<?xml version="1.0" encoding="UTF-8"?>
<svg width="10px" height="16px" viewBox="0 0 10 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Path</title>
    <defs>
        <filter x="-81.2%" y="-68.8%" width="262.5%" height="262.5%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="0" dy="5" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="10" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.168627451   0 0 0 0 0.247058823   0 0 0 0 0.129411765  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="Prijedlozi-poboljšanja-2024" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Hello-bar" transform="translate(-1239, -40)" fill="#FFFFFF" fill-rule="nonzero">
            <g id="Group-6" transform="translate(1220, 24)">
                <g id="Up" filter="url(#filter-1)" transform="translate(4, 4)">
                    <g id="Group-4" transform="translate(15, 12)">
                        <polygon id="Path" points="1.75 0.5 0 2.25 5.75 8 0 13.75 1.75 15.5 9.25 8"></polygon>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>