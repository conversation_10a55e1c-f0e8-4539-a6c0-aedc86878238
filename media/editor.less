@import "defaults.less";


/*------- helpers -------*/
.float-left { float: left; }
.float-right { float: right; }
.strong { font-weight: bold; }
.italic { font-style: italic; }
.uppercase { text-transform: uppercase; }

.first{margin-left:0 !important;}
.last{margin-right:0 !important;}

.image-left, .alignleft { float: left; margin: 5px 20px 10px 0px; }
.image-right, .alignright { float: right; margin: 5px 0px 10px 20px; }

.align-left {text-align:left;}
.align-right {text-align:right;}
.center {text-align:center;}

.underline { text-decoration:underline; }
.nounderline { text-decoration:none; }
.rounded { border-radius: var(--borderRadius); }

.red {color: @red;}
.green {color: @green;}
.orange {color: @orange;}

.first-title{margin-top: 0; padding-top: 0;}
.intro-text{font-size: 19px; line-height: 1.6;}
/*------- /helpers -------*/

/*------- selectors -------*/
* { margin: 0; padding: 0; border: none; }
body {background: #fff; padding: 10px 15px; color: @textColor; .font(var(--fontSize), var(--lineHeight), @font); }
table { border-spacing: 0; border: none; }
a {
	color: @linkColor;text-decoration: underline; .transition();
	&:hover { text-decoration: underline;color: @linkHoverColor; }
}
ul, ol {margin: 0px 0px 10px 35px;padding: 0px;}
ol { margin-left: 40px; }
h1, h2, h3, h4{
	font-weight: bold; line-height: 1.3; padding-bottom: 15px; padding-top: 20px; color: var(--green2);
	a, a:hover{text-decoration: none;}
}
h1{font-size: 44px; padding-top: 0;}
h2{font-size: 32px; line-height: 1.2;}
h3{font-size: 28px; line-height: 1.2;}
h4{font-size: 22px; line-height: 1.2;}
p{padding-bottom: 15px;}
/*------- /selectors -------*/

/*------- tables -------*/
.table { 
	width: 100%; border-spacing:0; margin: 10px 0px 20px; 
	th { font-weight: bold; font-size: 14px; text-align: left; padding: 6px 0; border-bottom: 1px solid @gray; }
	td { border-bottom: 1px solid @gray; padding: 6px 0; }
	&.stripe tbody tr:nth-child(even) { background: #E9E9E9; }
}
/*------- /tables -------*/

/*------- buttons -------*/
.btn, input[type=submit], button{
	position: relative; display: inline-flex; align-items: center; justify-content: center; padding: 0 40px; height: 54px; font-size: 16px; border-radius: @borderRadius; color: #fff; background: @blue; text-decoration: none; .gradient-green; position: relative; overflow: hidden;
	
	&:hover{
		color: #fff; text-decoration: none;
		/*&:after{opacity: 1;}*/
	}
	span{position: relative; z-index: 1;}
	&:after{.pseudo(100%,100%); .gradient-green-hover; opacity: 0; .transition(opacity);}
	&.flat{
		background: @green; .transition(all);
		&:after{display: none;}
	}
}
.btn-light-green.flat{
	background: @lightGreen;
	&:hover{background: @lightGreen/1.2;}
}
.btn-orange, .btn-yellow{
	.gradient(#E2926E,@orange);
	&:after{.gradient(#E2926E/1.1,@orange/1.1);}
}
.btn-white{
	border: 1px solid @borderColor; background: #fff; color: @darkGreen;
	&:after{display: none;}
	&:hover{color: @darkGreen;}
}
.btn-gray{
	background: #E9ECEB; color: @textColor; padding: 0 30px; font-size: 14px; .transition(all);
	&:hover{background: #E9ECEB/1.1; color: @textColor;}
	&:after{display: none;}
}
/*------- /buttons -------*/

.extra{
	background: @darkGreen url(images/bg-green.jpg); color: #fff; padding: 40px 50px; margin: 10px 0 45px; line-height: 1.7;
	a{
		color: #fff;
		&:hover{color: #fff;}
	}
}
.intro-text{font-size: 19px; line-height: 1.6;}