/*------- mixins -------*/
.transition (@element: color, @speed: .3s) {
	transition: @arguments;
	-webkit-backface-visibility: hidden;
	-webkit-tap-highlight-color: transparent;
}
.grayscale (@bw: 100%) {
	filter: grayscale(@bw);
	-webkit-filter: grayscale(@bw);
	-moz-filter: grayscale(@bw);
}
.text-shadow (@color, @x: 1px, @y: 1px, @blur: 0px) {
	text-shadow: @x @y @blur @color;
}
.gradient(@startColor: #eee, @endColor: white) {
	background: @startColor;
	background: linear-gradient(180deg, @startColor 0%, @endColor 100%);
	background-image: -moz-linear-gradient(@startColor 0%, @endColor 100%);
	background-image: -ms-linear-gradient(180deg, @startColor 0%, @endColor 100%);
}
.horizontal-gradient (@startColor: #eee, @endColor: white) {
 	background-color: @startColor;
	background-image: -webkit-linear-gradient(90deg, @startColor, @endColor);
	background-image: -ms-linear-gradient(90deg, @startColor, @endColor);
	background-image: linear-gradient(90deg, @startColor, @endColor);
}
.font(@fontSize, @fontLineHeight, @fontType) {
	font-size: @fontSize;
	line-height: @fontLineHeight; 
	font-family: @fontType;
}
.pseudo(@width, @height) {
	content:"";
	position: absolute;
	display: block;
	width:@width;
	height:@height;
}
.animation (@name, @duration: 300ms, @delay: 0, @ease: ease) {
	-webkit-animation: @name @duration @delay @ease;
	-moz-animation:    @name @duration @delay @ease;
	-ms-animation:     @name @duration @delay @ease;
}
.transform(@string){
	transform: @string;
	-webkit-transform: @string;
	-ms-transform: 		 @string;
}
.scale (@factor) {
	transform: scale(@factor);
	-webkit-transform: scale(@factor);
	-ms-transform: 		 scale(@factor);
}
.scaleX(@factor) {
	transform: scaleX(@factor);
	-webkit-transform: scaleX(@factor);
	-ms-transform: 		 scaleX(@factor);	
}
.scaleY (@factor) {
	transform: scaleY(@factor);
	-webkit-transform: scaleY(@factor);
	-ms-transform: scaleY(@factor);
}
.rotate (@deg) {
	transform: rotate(@deg);
	-webkit-transform: rotate(@deg);
	-ms-transform: 		 rotate(@deg);
}
.skew (@deg, @deg2) {
	transform: skew(@deg, @deg2);
	-webkit-transform: skew(@deg, @deg2);
	-ms-transform: skew(@deg, @deg2);
}
.translate (@x, @y:0) {
	transform: translate(@x, @y);
	-webkit-transform: translate(@x, @y);
	-ms-transform: translate(@x, @y);
}
.translate3d (@x, @y: 0, @z: 0) {
	transform: translate3d(@x, @y, @z);
	-webkit-transform: translate3d(@x, @y, @z);
	-ms-transform: translate3d(@x, @y, @z);
}
.perspective (@value: 1000) {
	perspective: 		@value;
	-webkit-perspective: 	@value;
}
.clear { 
	*zoom: 1; clear:both;
	&:before, &:after {content:""; display: table; }
	&:after { clear: both; }
}
.placeholder(@color,@focusColor) {
	&::-webkit-input-placeholder { color: @color; }
	&:-ms-input-placeholder { color: @color; }
	&::-moz-placeholder { color: @color; }
	&:focus {
		&::-webkit-input-placeholder { color: @focusColor; }
		&:-ms-input-placeholder { color: @focusColor; }
		&::-moz-placeholder { color: @focusColor; }
	}
}
.shadow { content:""; position: absolute; left: 35px; right: 35px; top: 20px; bottom: 5px; box-shadow: 0px 10px 65px rgba(0,0,0,.6); }
.lloader {
	background-image: url(images/loader.svg); background-color: #fff; background-repeat: no-repeat; background-position: center center; 
	img { opacity: 0; .transition(opacity); }
	&.loaded {
		background-color: #fff; background-image: none;
		img { opacity: 1; }
	}
}
.box-shadow-yellow{box-shadow: 0 15px 30px 0 rgba(228,197,113,0.2);}
.box-shadow-gray{box-shadow: 0 10px 30px 0 rgba(0,0,0,0.1);}

.gradient-green{.gradient(#ABC075,@green);}
.gradient-green-hover{.gradient(#ABC075/1.1,@green/1.1);}

.gradient-red{.gradient(@red*1.1,@red);}
.gradient-red-hover{.gradient(@red,@red/1.1);}

.gradient-orange{.gradient(#E2926E,#F4743B);}
/*------- /mixins -------*/


/*------- fonts -------*/
@font-face {
	font-family: 'Adelle';
	src: url('fonts/adellesans-regular.woff') format('woff'),  url('fonts/adellesans-regular.woff2') format('woff2');
	font-weight: normal;
	font-style: normal;
	font-display: block;
}
@font-face {
	font-family: 'Adelle';
	src: url('fonts/adellesans-bold.woff') format('woff'),  url('fonts/adellesans-bold.woff2') format('woff2');
	font-weight: bold;
	font-style: normal;
	font-display: block;
}
@font-face {
    font-family: 'icomoon';
    src: url('fonts/icomoon2.woff?4vq4o9') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}
/*------- /fonts -------*/

/*------- icons -------*/
.icon-pickup() {
  content: "\e934";
}
.icon-arrowR() {
  content: "\e931";
}
.icon-close2() {
  content: "\e932";
}
.icon-check2() {
  content: "\e933";
}
.icon-not-available() {
  content: "\e92f";
}
.icon-available() {
  content: "\e930";
}
.icon-danger-white2() {
  content: "\e92e";
}
.icon-webshop-only() {
  content: "\e92d";
}
.icon-clock3() {
  content: "\e92c";
}
.icon-calendar() {
  content: "\e92b";
}
.icon-organic() {
  content: "\e92a";
}
.icon-quote() {
  content: "\e929";
}
.icon-info() {
  content: "\e928";
}
.icon-barcode() {
  content: "\e927";
}
.icon-danger() {
  content: "\e926";
}
.icon-coupon() {
  content: "\e925";
}
.icon-phone2() {
  content: "\e924";
}
.icon-trash() {
  content: "\e923";
}
.icon-star() {
  content: "\e922";
}
.icon-tag() {
  content: "\e921";
}
.icon-clock2() {
  content: "\e91f";
}
.icon-phone() {
  content: "\e920";
}
.icon-arrow-left() {
  content: "\e91e";
}
.icon-portfolio() {
  content: "\e91c";
}
.icon-store() {
  content: "\e91d";
}
.icon-clock() {
  content: "\e91a";
}
.icon-ingredients() {
  content: "\e91b";
}
.icon-spoon() {
  content: "\e919";
}
.icon-eco() {
  content: "\e918";
}
.icon-heart-full() {
  content: "\e917";
}
.icon-cross() {
  content: "\e916";
}
.icon-check() {
  content: "\e915";
}
.icon-arrow-right() {
  content: "\e914";
}
.icon-shipping() {
  content: "\e913";
}
.icon-happiness() {
  content: "\e912";
}
.icon-arrow-down() {
  content: "\e911";
}
.icon-search() {
  content: "\e910";
}
.icon-ig() {
  content: "\e90f";
}
.icon-quickorder() {
  content: "\e90a";
}
.icon-user() {
  content: "\e90b";
}
.icon-cart() {
  content: "\e90c";
}
.icon-heart() {
  content: "\e90d";
}
.icon-help() {
  content: "\e90e";
}
.icon-shield() {
  content: "\e909";
}
.icon-marker() {
  content: "\e907";
}
.icon-symbol() {
  content: "\e908";
}
.icon-pin() {
  content: "\e906";
}
.icon-organic-food() {
  content: "\e905";
}
.icon-email() {
  content: "\e900";
}
.icon-envelope() {
  content: "\e901";
}
.icon-fb() {
  content: "\e902";
}
.icon-viber() {
  content: "\e903";
}
.icon-whatsapp() {
  content: "\e904";
}
/*------- /icons -------*/

/*------- site vars -------*/
:root{
	--fontSize: 16px;
	--lineHeight: 1.7;
	--pageWidth: 1480px;
	--contentSpacing: 125px;
	--brandItemHeight: 100px;
	--brandItemImgHeight: 38px;

	--circleSize: 60px;

	--pwTitle: 38px;
	--pwRecipesColWidth: 528px;
	--pwColWidth: 800px;
	--ppSmallImage: 230px;
	--ppSmallTitle: 16px;
	--ppFontSize: 14px;
	--ppHorizontalSpacing: 25px;

	--cpInstashopSpacing: 20px;
}

@media screen and (max-width: @l) {
	:root{
		--pageWidth: 88%;
		--contentSpacing: 70px;
		--brandItemHeight: 80px;
		--brandItemImgHeight: 30px;

		--circleSize: 54px;
		--pwTitle: 34px;
		--pwColWidth: 600px;
		--ppFontSize: 13px;
		--ppSmallImage: 175px;
		--ppSmallTitle: 15px;
		--ppHorizontalSpacing: 15px;

		--pwTitle: 26px;
	}
}

@media screen and (max-width: @t) {
	:root{
		--fontSize: 14px;
		--lineHeight: 1.5;
		--pageWidth: 100%;
		--contentSpacing: 30px;
		--ppHorizontalSpacing: 10px;
		--brandItemHeight: 63px;
		--brandItemImgHeight: 24px;
		--circleSize: 48px;
		--pwColWidth: 500px;

		--ppSmallImage: 145px;
		--ppSmallTitle: 14px;

		--wrapperOffset: 30px;
	}
}

@media screen and (max-width: @tp) {
	:root{
		--wrapperOffset: 20px;
		--brandItemHeight: 50px;
		--brandItemImgHeight: 20px;
		--pwTitle: 24px;
		--circleSize: 44px;
		--pwColWidth: 395px;
		--ppSmallImage: 115px;
		--ppSmallTitle: 12px;
	}
}

@media screen and (max-width: @m) {
	:root{
		--wrapperOffset: 15px;
		--contentSpacing: 15px;
		--brandItemHeight: 40px;
		--brandItemImgHeight: 18px;
		--pwTitle: 18px;
		--circleSize: 28px;
		--ppSmallImage: 140px;
	}
}

@l: 1550px;
@t: 1300px;
@tp: 950px;
@m: 900px;
@instashopL: 1400px;
@instashopT: 950px;
@instashopTp: 920px;

@pageWidth: 1480px;
@textColor: @darkGreen;
@linkColor: @darkGreen;
@linkHoverColor: @green;

@primaryColor: #ccc;
@secondaryColor: #74ad11;
@red: #BD444D;
@blue: #5B70F2;
@black: #000;
@gray: #e2e2e2;
@gray2: #959F90;
@orange: #F4743B;
@white: #fff;
@darkGreen: #2B3F21;
@lightGreen: #ABC075;
@green: #809941;
@yellow: #F8B600;
@lightOrange: #E4C571;
@borderColor: #e7e7e7;
@borderRadius: 3px;

@font: "Adelle", Arial, Helvetica, sans-serif;
@fonti: "icomoon";

@errorColor: @red;
@warningColor: @orange;
@successColor: @green;
/*------- /site vars -------*/


/*
	LEGEND
	screen sizes: 

	- normalize
	- helpers
	- tables
	- selectors
	- forms
	- info messages
	- buttons
	- navigation
*/