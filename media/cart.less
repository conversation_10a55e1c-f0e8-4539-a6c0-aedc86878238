@import "defaults.less";

:root{
	--pageWidth: 1200px;
}
@media screen and (max-width: @l) {
	:root{
		--pageWidth: 1000px;
	}
}
@media (max-width: @tp){
	:root{
		--pageWidth: auto;
	}	
} 
body{background: #fff!important;}
.page-checkout, .page-thankyou{
	.hello-bar{display: none!important;}
}
.footer-col1{
	flex-grow: 0; width: auto;
	@media (max-width: @m){position: relative; left: auto; bottom: auto;}
}
.footer-col2{
	display: none;
	@media (max-width: @t){display: block;}
	@media (max-width: @m){position: relative; left: auto; bottom: auto; margin-bottom: 5px;}
}
.page-wrapper{
	background: url(images/bg.jpg);
	@media (max-width: @m){background: none;}
}
.m-nav, .loyalty-quick, .m-nav-title{display: none!important;}
.main{
	.box-shadow-yellow;
	@media (max-width: @l){width: 1200px;}
	@media (max-width: @t){background: #fff; border-radius: @borderRadius; width: auto; margin-left: 30px; margin-right: 30px;}
	@media (max-width: @tp){margin-left: 20px; margin-right: 20px;}
	@media (max-width: @m){box-shadow: none;}
}

@media (max-width: @tp){
	body:not(.page-webshop-login){
		.main{margin-left: 0; margin-right: 0; border-radius: 0; box-shadow: none;}
		&.main-offset-sm{
			.main{margin-top: 0;}
			.header{padding-bottom: 0;}
		}
		.footer{margin-top: 0; border-top: 1px solid @borderColor;}
	}
}
.header{z-index: 1;}
.footer{
	margin-top: 70px;
	@media (max-width: @m){border-top: 1px solid @borderColor; margin-top: 25px;}
}
.wrapper-header{
	display: block; padding: 0; height: 120px;
	@media (max-width: @t){height: 100px;}
	@media (max-width: @m){height: 50px;}
}
.logo{
	position: absolute; left: 50px; top: 15px; width: 63px; height: 90px; margin: 0; z-index: 10;
	@media (max-width: @t){width: 50px; height: 70px;}
	@media (max-width: @tp){left: 23px;}
	@media (max-width: @m){
		left: 8px; height: 40px; top: 5px;
		&:before{font-size: 26px; line-height: 26px;}
	}
}
.checkout-return{
	position: absolute; font-size: 12px; left: 100px; top: 52px; z-index: 10;
	@media (max-width: 1600px){display: none;}
	@media (max-width: @m){display: block; left: auto; right: 7px; top: 7px;}
}
.btn-return-home{
	text-decoration: none; color: #fff; position: relative; padding: 0 0 0 23px;
	&:hover{text-decoration: underline; color: #fff;}
	&:before{.icon-arrow-left; font: 12px/10px @fonti; position: absolute; left: 0; top: 4px; color: #fff;}
	@media (max-width: @m){
		width: 35px; height: 35px; display: flex; align-items: center; justify-content: center; border-radius: @borderRadius; .gradient-orange; font-size: 0; padding: 0;
		&:before{.icon-cross; position: relative; top: auto; left: auto; font-size: 11px; line-height: 11px;}
	}
}
.checkout-title{
	padding: 0; position: absolute; left: 0; right: 0; top: 38px; font-size: 24px; line-height: 1.5;
	@media (max-width: @t){font-size: 20px;}
	@media (max-width: @m){font-size: 16px;}
	@media (max-width: @m){text-align: left; left: 65px; right: auto; top: 13px; font-size: 16px;}
}
.header-contact{
	position: absolute; top: 46px; right: 50px; font-weight: bold; z-index: 10;
	@media (max-width: @t){display: block;}
	@media (max-width: @tp){right: 20px;}
	@media (max-width: @m){position: relative; left: auto; top: auto; right: auto;}
}
.footer-col3{
	flex-grow: 1; justify-content: center;
	@media (max-width: @t){flex-grow: 0; width: auto;}
	@media (max-width: @m){justify-content: flex-start;}
}
@media (max-width: @m){
	.wc-row{display: block;}
	.footer-col3{position: relative; top: auto; margin: 15px 0 10px;}
}
.wc-col{
	width: 50%;
	@media (max-width: @m){width: 100%;}
}
.wc-col1{
	border-right: 1px solid @borderColor; display: flex; flex-direction: column;
	.wc-col-cnt{/*flex-grow: 1;*/ border-bottom: 1px solid @borderColor;}
	@media (max-width: @m){border-right: 0;}
}
@media (max-width: @m){
	.step2 .wc-col1{border-bottom: 1px solid @borderColor;}
}
.wc-step3-col1 .wc-col-cnt{
	padding-bottom: 35px;
	@media (max-width: @m){padding-bottom: 20px;}
}
.wc-title{
	font-size: 18px; line-height: 1.5; padding: 0 0 15px;
	@media (max-width: @tp){font-size: 16px;}
}
.wc-subtitle{font-size: 15px; line-height: 1.5; padding: 0 0 10px;}
.wc-subtitle-login{
	font-size: 20px; padding-bottom: 5px;
	@media (max-width: @t){font-size: 18px;}
	@media (max-width: @m){font-size: 16px; padding-bottom: 2px;}
}
.form-label{
	input[type=checkbox]+label, input[type=radio]+label{
		font-size: 14px;
		@media (max-width: @m){font-size: 12px;}
	}
	.field-b_same_as_shipping{padding-bottom: 10px;}
	.field-message{margin-bottom: 5px;}
	.field{padding-bottom: 10px;}
	.field-phone{
		margin-bottom: 20px; margin-top: 10px;
		@media (max-width: @m){margin-bottom: 5px; margin-top: 5px;}
	}
}
#field-b_company_oib{width: 100%!important;}
.wrapper-footer{
	padding-left: 50px; padding-right: 50px;
	@media (max-width: @t){padding-left: 0; padding-right: 0;}
	@media (max-width: @m){flex-direction: column-reverse; align-items: flex-start; padding-top: 15px; height: auto; padding-bottom: 15px;}
}

.page-checkout-login .wc-col{
	padding: 50px 70px 65px;
	@media (max-width: @t){padding: 35px 50px 40px;}
	@media (max-width: @tp){padding: 25px 30px 35px;}
	@media (max-width: @m){border-right: 0; padding: 15px 15px 20px;}
}
.wc-cnt{
	font-size: 14px;
	@media (max-width: @m){font-size: 12px;}
}
.wc-col-cnt{
	padding: 25px 50px;
	@media (max-width: @t){padding-left: 40px; padding-right: 40px;}
	@media (max-width: @tp){padding: 15px 20px 20px;}
	@media (max-width: @m){padding: 15px;}
}
.wc-col-shipping{
	border-bottom: 1px solid @borderColor; padding-bottom: 35px;
	@media (max-width: @tp){padding-bottom: 25px;}
	@media (max-width: @m){margin-bottom: 5px;}
}
.wc-col-cart{
	padding-top: 10px;
	@media (max-width: @m){padding-bottom: 20px;}
}
.w-cart-title{
	padding: 0;
	.counter{font-weight: normal; font-size: 12px; color: @green;}
}
.ww-cart-header{
	display: flex; justify-content: space-between; align-items: center; padding-bottom: 25px;
	@media (max-width: @m){padding-bottom: 5px;}
}
.w-btn-change{font-size: 12px; color: @green; text-decoration: underline;}

.ww-coupons{margin-bottom: 0;}
.wc-login-form{margin-top: 15px;}
.btn-checkout{
	padding: 0 20px; min-width: 220px;
	@media (max-width: @t){min-width: 160px;}
	@media (max-width: @tp){min-width: 140px;}
}
.a-btns{margin-top: 5px;}
.phone-tooltip{
	font-size: 12px; color: #959F90; display: block; padding: 5px 0 0 25px;
	@media (max-width: @m){padding-left: 15px;}
}
.btn-step2-submit{margin: 10px 0;}

.ww-coupons label{position: relative; top: auto; left: auto; margin-bottom: 10px; font-size: 14px; padding: 0 0 0 31px;}
.ww-coupons-label:before{font-size: 22px; line-height: 15px;}
.ww-btn-add{
	width: 130px;
	@media (max-width: @t){width: 80px;}
}
@media (max-width: @m){
	.wc-coupons{margin-top: 10px;}
}
.wp-title{font-size: 12px;}
.wp{
	border: 0; margin-bottom: 0;
	a{pointer-events: none;}
	@media (max-width: @m){padding-right: 0;}
}
@media (max-width: @tp){
	.wp-cnt{padding-right: 0;}
}
@media (max-width: @m){
	.wp-total{position: relative; bottom: auto; right: auto;}
}
.ww-cart-items{
	border-bottom: 1px solid @borderColor; overflow: auto; max-height: 265px; padding-right: 15px; margin-right: -15px;
	&::-webkit-scrollbar { -webkit-appearance: none; width: 3px; background: #E0E3DF; }
	&::-webkit-scrollbar-thumb {
		background-color: #9FA1A3; border-radius: 50px;
		box-shadow: 0 0 1px rgba(255,255,255,.5);
	}
	@media (max-width: @m){
		margin: 0; padding: 10px 0 0; border: 0; .transition(all);
		.wp{padding-right: 0; padding-left: 0; padding-top: 0;}
		&.active{max-height: 0; padding: 0;}
	}
}
.wc-col-totals{
	padding-top: 0;
	@media (max-width: @m){padding: 0 0 20px 0;}
}

.w-totals-value{flex-grow: 0;}
.ww-totals{
	padding: 0;
	&:before{display: none;}
	@media (max-width: @m){
		.w-totals-label{margin-left: 0; padding-right: 5px; flex-grow: 1; max-width: 70%;}
		.w-totals-value{width: auto; flex-grow: 1;}
	}
}

.step{
	border-bottom: 1px solid @borderColor; font-weight: bold; font-size: 18px; position: relative;
	a{text-decoration: none; color: #959F90; display: block; padding: 20px 50px;}
	@media (max-width: @t){
		font-size: 16px;
		a{padding-left: 40px; padding-right: 40px;}
	}
	@media (max-width: @tp){
		a{padding: 15px 20px;}
	}
	@media (max-width: @m){
		a{padding: 15px;}
	}
}
.completed-step{
	&:after{.pseudo(50px,50px); display: flex; align-items: center; justify-content: center; border-radius: @borderRadius; .gradient-green; top: 10px; right: 10px; .icon-check; font: 15px/15px @fonti; color: #fff;}
	a{color: @textColor; background: #F7F8F6; border-top-left-radius: @borderRadius;}
	@media (max-width: @tp){
		&:after{width: 40px; height: 40px; top: 7px;}
	}
	@media (max-width: @m){
		&:after{width: 26px; height: 26px; font-size: 12px; top: 15px; right: 15px;}
	}
}
.last-step{border-bottom: 0;}
.step3-step-shipping{border-top: 0; border-bottom: 1px solid @borderColor; border-top-left-radius: @borderRadius;}
.selected-shipping{
	display: block; width: 100%; font-size: 12px; font-weight: normal; line-height: 1.2; padding-top: 5px;
	.shipping_info{display: none!important;}
}
.step-selected-shipping{
	display: none;
	a{background: none;}
	.change-data{top: 42px;}
	&:after{top: 36px;}
	@media (max-width: @m){display: block;}
}
.change-data{
	color: @green; text-decoration: underline; font-size: 12px; position: absolute; top: 24px; right: 80px; .transition(color);
	&:hover{color: @textColor;}
	@media (max-width: @tp){top: 19px; right: 70px;}
	@media (max-width: @m){right: 53px; font-size: 11px;}
}
.field-payment{
	margin-bottom: 25px;
	&>label{display: none;}
	&>span{display: block; margin-bottom: 10px;}
	@media (max-width: @m){
		margin-bottom: 20px;
		input[type=radio]+label{font-size: 14px;}
	}
}
.wp-total{line-height: 1.2;}
.payment_info[data-payment_code=kekspay]{
	position: relative; max-width: none; padding-right: 95px;
	&:after{.pseudo(80px,35px); background: url(images/keks-logo-horizontal.svg) no-repeat left top; background-size: contain; position: absolute; right: 0; bottom: 0;}
}
.shipping-options{padding-bottom: 25px;}
.shipping-row{
	position: relative; padding-bottom: 10px;
	&:first-of-type{
		margin-top: 0; border-top: 0; padding-top: 0;
		.cart_info_total_extra_shipping{top: 4px;}
	}
	&.not-available{display: none;}
	&.active{display: block;}
	.cart_info_total_extra_shipping{position: absolute; top: 13px; right: 0; font-weight: bold; padding: 0; color: @textColor; font-size: 12px; display: none; display: none!important;}
	@media (max-width: @m){
		input[type=radio]+label{font-size: 14px;}
	}
}

.field-shipping{
	display: block; width: 100%; padding-bottom: 0!important;
	&>span{
		display: block; width: 100%; position: relative; border-top: 1px solid @borderColor; margin-top: 10px; padding-top: 10px;
		&:first-of-type{
			margin-top: 0; border-top: 0; padding-top: 0;
			.cart_info_total_extra_shipping{top: 4px;}
		}
	}
	&>label{display: none!important;}
}

.shipping-address{padding: 10px 0 5px;}
.btn-change-address{margin-top: 5px; display: inline-block;}
.step2 .btn-change-address{display: none;}
.shipping-note, .payment_info, .shipping_info{
	display: block!important; font-size: 12px; line-height: 1.3; color: @gray2; padding: 0 0 0 30px; max-width: 400px;
	a[href^=tel]{color: @gray2;}
}
.shipping_info select{
	margin-top: 10px; width: auto; font-size: 14px!important; color: @textColor;
	@media (max-width: @m){font-size: 12px!important;}
}
#field-shipping_pickup_location{padding-top: 0; width: 100%;}

.checkout-field-message{display: none!important;}
.step2{
	.shipping-address{display: none!important;}
}
input[type=radio]:checked ~ .cart_info_total_extra_shipping{color: @green;}
.shipping-location-address{
	font-size: 13px; color: @textColor; padding: 15px 0 0 0; display: none;
	&.active{display: block;}
	@media (max-width: @m){
		padding: 10px 0 0; font-size: 12px;
		strong{display: none;}
	}
}
.ww-cart-btns{
	display: none;
	@media (max-width: @m){display: flex; justify-content: space-between;}
}
.btn-toggle-cart{
	font-size: 12px; color: @green; display: inline;
	.s{display: none;}
	&.active{
		.s{display: inline;}
		.h{display: none;}
	}
}
.error-wolt{
	padding: 5px 0;
	@media (max-width: @m){padding: 5px 0;}
}
.wolt-delivery-checkbox{
	input[type=checkbox]+label, input[type=radio]+label{margin: 10px 0 0!important;}
}
.delivery-time-input{display: flex; align-items: center; margin-bottom: 5px; justify-content: space-between; padding-left: 30px;}
.select-wrapper{
	width: 50%; margin-right: 5px; position: relative;
	select{padding-left: 50px; width: 100%;}
	&:before{.icon-calendar(); color: #809941; display: block; font: 20px/20px @fonti; position: absolute; top: 27px; left: 20px; z-index: 50; pointer-events: none;}
	@media (max-width: @m){
		select{padding-left: 40px;}
		&:before{font-size: 18px; line-height: 18px; top: 24px; left: 15px;}
		&:after{top: 27px;}
	}
}
.input-wrapper{
	width: 50%; margin-left: 5px; position: relative;
	input{
		width: 100%!important; margin-top: 10px; padding-left: 50px; background: none;
	}
	&:before{.icon-clock3(); display: block; font: 20px/20px @fonti; color: #809941; position: absolute; top: 27px; left: 20px; z-index: 50; pointer-events: none;}
	&:after{content: ""; width: 12px; height: 12px; position: absolute; right: 15px; top: 29px; display: block; background-image: url(images/icons/arrow-down.svg); background-repeat: no-repeat; background-position: center center; background-size: contain; pointer-events: none;}
	
	@media (max-width: @m){
		input{padding-left: 40px; font-size: 12px;}
		&:before{font-size: 18px; line-height: 18px; top: 24px; left: 15px;}
		&:after{top: 27px;}
	}
}
.error-scheduled-time{padding-left: 30px;}
.cash-amount{
	padding-left: 30px;
	.label-wolt{display: block; padding-bottom: 10px; padding-top: 2px;}
}
.input-cash{
	position: relative; display: block;
	input{width: 165px; padding: 0 20px; color: #2B3F21;}
	&:before{content:"€"; font-size: 14px; line-height: 16px; position: absolute; color: #959F90; position: absolute; display: block; top: 19px; left: 136px; z-index: 50;}
	@media (max-width: @m){
		input{width: 125px;}
		&:before{top: 15px; left: 105px;}
	}
}

//Datepicker
.sw-datepicker{
	#ui-datepicker-div{
		left: 0!important; top: 80px!important; z-index: 50!important;
		@media (max-width: @tp){top: 75px!important;}
		@media (max-width: @m){right: 0; left: auto!important;}
	}
}
.ui-datepicker{
	width: 205px; background: #FFFFFF; padding: 20px; display: none; max-width: 205px; margin-left: -20px; border-radius: 2px; box-shadow: 0 0 30px 0 rgba(0,0,0,0.15);
	@media (max-width: @tp){margin-left: -30px;}
	@media (max-width: @m){margin-left: -80px;}
}
.ui-datepicker:before{content:""; display: block; position: absolute; left: 50%; margin-left: -4px; top: -5px; width: 10px; height: 10px; .rotate(45deg); background: white;}
.ui-datepicker-title{text-align: center; font-size: 14px; line-height: 16px; font-weight: bold; padding-bottom: 10px;}
.ui-datepicker-calendar td a.ui-state-hover,
.ui-datepicker-calendar td span.ui-state-hover,
.ui-datepicker-calendar td a.ui-state-active,
.ui-datepicker-calendar td span.ui-state-active{ background: linear-gradient(180deg, #ABC075 0%, #809941 100%); color:white; font-weight:normal; border-radius:4px; box-shadow:0 10px 15px 0 rgba(0,114,229,0.15)}
.ui-datepicker-calendar td a, .ui-datepicker-calendar td span {color: black;}
.ui-datepicker-current {display:none;}
.ui_tpicker_time_label, .ui_tpicker_time{display: none;}
.ui-datepicker-close{
	width: 100%; height: 44px; margin-top: 10px;
	&:hover{
		&:after{display: none;}
	}
}
.ui-timepicker-div{
	position: relative;
	dl{position: relative; height: 64px;}
	dt{width: 50%; top: 0; text-align: center; padding-right: 5px; font-size: 12px; line-height: 13px; color: #959F90; position: absolute;}
	.ui_tpicker_minute_label{right: 0; padding-right: 0; padding-left: 5px;}
	dd{
		width: 77px; height: 44px; position: absolute; bottom: 0;
		&:not(.ui_tpicker_minute):not(.ui_tpicker_hour){display: none;}
		select{height: 44px; width: 100%; padding: 0 20px; margin-top: 0;}
	}
	.ui_tpicker_minute{right: 0;}
}

/* .ui-datepicker.active-timepicker{
	display: block!important;
} */

.step3-footer{
	align-items: flex-start;
	.wc-col-totals{padding-left: 0; flex-grow: 1;}
	@media (max-width: @t){display: block;}
}
.page-webshop-shipping{
	.free-delivery-container{
		display: none!important;
		@media (max-width: @m){display: block!important; margin-top: 0;}
	}
}
.free-delivery-container{
	margin: 0 0 25px; padding-left: 50px; padding-right: 50px; font-size: 12px;
	@media (max-width: @t){padding-left: 40px; padding-right: 40px; margin-bottom: 20px;}
	@media (max-width: @tp){padding-right: 20px; padding-left: 20px;}
	@media (max-width: @m){padding: 0; margin: 15px 0 19px; text-align: left;}
}
.free-delivery-title{
	max-width: none;
	@media (max-width: @m){max-width: 100%; text-align: center;}
}
.free-delivery-scale{height: 28px;}
.free-delivery-scale-amount{font-size: 12px; top: 4px;}
.webshop-accept-terms{
	margin: 0 0 25px 10px; position: relative;
	label{font-weight: bold;}
	a{color: @green;}
	.error{
		position: relative; display: flex; align-items: center; padding: 2px 0 0 30px;
		&:before{.icon-danger(); position: absolute; font: 20px/20px @fonti; color: @red; top: 0; left: 1px;}
	}
}
.wc-accept-terms-tooltip{
	background: @darkGreen; color: #fff; height: 30px; padding: 0 13px; position: absolute; font-size: 12px; border-radius: 2px; z-index: 50; display: none; align-items: center; top: -2px; .translate(calc(~"-100% - 15px"));
	&:after{.pseudo(10px,10px); background: @darkGreen; .rotate(45deg); top: 10px; right: -3px;}
	&.active{display: flex;}
	@media (max-width: @t){display: none!important;}
}
.webshop-alert-terms{
	background: @red; color: #fff; color: #fff; font-size: 12px; border-radius: @borderRadius; padding: 5px 15px; margin-bottom: 15px; position: relative;
	&:after{.pseudo(10px,10px); background: @red; .rotate(45deg); left: 16px; bottom: -3px;}
}

.w-loyalty{
	margin-left: 50px; margin-right: 50px; margin-top: 10px; margin-bottom: 20px; padding-left: 25px; font-size: 14px; line-height: 1.5;
	&:after{display: none;}
	.loyalty-cnt, .loyalty-checkbox{max-width: none;
		@media (max-width: @tp){padding-right: 0;}
	}
	@media (max-width: @t){margin-left: 40px; margin-right: 40px;}
	@media (max-width: @tp){margin-left: 20px; margin-right: 20px;}
	@media (max-width: @m){margin-left: 0; margin-right: 0;}
}

.cart-totals-title{text-align: right;}
.step4{
	.ww-cart-items{
		border: 0; max-height: 350px;
		@media (max-width: @m){
			max-height: 1500px;
			&.active{max-height: 0;}
		}
	}
	.cart-totals{margin-bottom: 18px;}
	.cart-totals-title{display: none;}
	.cart-totals-top{margin-bottom: 0;}
	.wc-col1 .wc-col-cnt{
		padding-bottom: 50px;
		@media (max-width: @m){padding-bottom: 25px; border-bottom: 1px solid @borderColor;}
	}
}
.wc-custom-address{
	margin-bottom: 15px; font-size: 14px;
	@media (max-width: @m){font-size: 12px;}
}
.wc-custom-address-title{font-size: 15px; font-weight: bold;}
.wc-step4-col1 .wc-col-cnt{border-bottom: 0;}
.wc-terms{
	@media (max-width: @m){padding-top: 0;}
}
.wp{padding-bottom: 10px;}
.wp-image{width: 70px;}
.ct-review{
	position: relative; padding-left: 15px; font-size: 12px; line-height: 1.5; margin-bottom: 15px;
	&:before{.pseudo(3px,auto); left: 0; top: 0; bottom: 0; background: @borderColor;}
}
.ct-review-title-btn{color: @green; margin-left: 10px; font-size: 12px;}
.ct-review-title{font-size: 14px; font-weight: bold;}
.ct-row{margin-bottom: 0;}
.cart-totals{font-size: 14px;}
.priority-order{
	padding-bottom: 10px;
	span{padding-bottom: 4px;}
}
.priority-order-col-cnt{padding-top: 0; padding-bottom: 0;}

/*------- credit card payment -------*/
.cc_expire input, .cc_cvv input{padding: 0 5px; text-align: center;}
.cc_installments{max-width: 170px;
	.chzn-container-single .chzn-single span{color: #000;}
}
.cc_type{position: absolute; padding-left: 10px !important;}

.webshop_widget_payment_advanced{
	position: relative; padding-top: 6px;
	#field-error-cc{
		left: 100px;
		&:after{left: 7px;}
	}
	label{width: 100% !important; display: block !important; font-size: 16px !important;}
	div{padding-bottom: 8px; position: relative;}
	.error{left: 130px; right: auto;}
	.clear{padding: 0;}
	#field-cc_cvv{margin-right: 8px;}
	a{color: @textColor;}
	input[type=text]{
		&:hover, &:focus{background: #fff; color: #000;}
	}
}

/*------- /credit card payment -------*/