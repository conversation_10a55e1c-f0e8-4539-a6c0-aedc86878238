export default defineNuxtRouteMiddleware(async to => {
	if (process.server) return;

	const nuxtApp = useNuxtApp();
	if (nuxtApp.$isbot) return;

	const {getRedirects, findRedirect, isExternalRedirect} = useRedirects();

	// get list of redirects and if no redirects are found, exit
	const redirects = getRedirects();
	if (redirects?.length == 0) return;

	// find matching redirect and redirect if found
	const redirect = findRedirect(redirects, to.fullPath);
	if (redirect) {
		const newPath = redirect.new_path.trim().replace('(.*)', '');
		return navigateTo(newPath, {
			redirectCode: redirect?.type ? redirect.type : 301,
			external: isExternalRedirect(redirect),
		});
	}
});
