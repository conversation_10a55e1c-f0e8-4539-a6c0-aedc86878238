// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
	extends: ['../nuxt-base'],
	css: ['@/assets/style.css'],
	vite: {
		css: {
			preprocessorOptions: {
				less: {
					additionalData: `@import "@/assets/_vars.less"; @import "@/assets/_mixins.less";`,
				},
			},
		},
		define: {
			__LOCATIONS__: JSON.stringify(true),
			__PUBLISH_AUTHORS__: JSON.stringify(true),
		},
	},
	dir: {
		'public': 'media',
	},
	$production: {
		sourcemap: {
			server: false,
			client: false,
		},
		routeRules: {
			'*': {
				swr: 120,
				cache: {base: 'db'},
			},
		},
	},
	nitro: {
		minify: true,
		storage: {
			db: {
				driver: 'redis',
				host: 'redis_tzh_markerheadless_info_nuxt',
				//host: 'redis_beta_tvornicazdravehrane_com_nuxt',
				port: 6379,
				username: 'default',
				password: 'LqBKEDGSBM',
				base: 'tzh_dev',
			},
		},
		devStorage: {
			db: {
				driver: 'fs',
				base: '.app_cache_data',
			},
		},
	},
	compatibilityDate: '2024-07-23',
});
