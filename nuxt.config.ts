// https://nuxt.com/docs/api/nuxtapi/configuration/nuxt-config
export default defineNuxtConfig({
	devtools: {
		enabled: false,
	},
	compressPublicAssets: {
		gzip: true,
		brotli: true,
	},
	vite: {
		/*
		css: {
			preprocessorOptions: {
				less: {
					additionalData: `@import "~/assets/_vars.less"; @import "~/assets/_mixins.less";`,
				},
			},
		},
		build: {
			minify: true,
		},
		*/
		define: {
			__UNDER_DEVELOPMENT__: JSON.stringify(false),
			__PUBLISH__: JSON.stringify(true),
			__WEBSHOP__: JSON.stringify(true),
			__GTM_TRACKING__: JSON.stringify(false),
			__REMARKETING__: JSON.stringify(false),
			__FB_CAPI_TRACKING__: JSON.stringify(false),
			__HAPI_TRACKING__: JSON.stringify(false),
			__GENERATE_THUMBS__: JSON.stringify(false),
			__STRUCTURED_DATA__: JSON.stringify(false),
			__SELLERS__: JSON.stringify(false),
			__NEWSLETTER__: JSON.stringify(true),
			__WEBSHOP_VIEW_ORDER__: JSON.stringify(true),
			__AUTH__: JSON.stringify(true),
			__CATALOG_CATEGORY_LANDING__: JSON.stringify(false),
			__CATALOG_MAIN_LANDING__: JSON.stringify(false),
			__RECAPTCHA__: JSON.stringify(false),
			__CATALOG_SEO_URL__: JSON.stringify(false),
			__CATEGORY_WITHOUT_BASE_URL__: JSON.stringify(false),
			__KEKSPAY__: JSON.stringify(false),
			__ALT_PAYMENTS__: JSON.stringify(false), // Select new payment method if primary payment method fails
			__EVENTS__: JSON.stringify(false),
			__LOCATIONS__: JSON.stringify(false),
			__PUBLISH_AUTHORS__: JSON.stringify(false),
		},
	},
	vue: {
		compilerOptions: {
			isCustomElement: tag => ['gls-dpm-dialog'].includes(tag), // GLS parcel lockers custom element
		},
	},
	experimental: {
		asyncContext: true,
		checkOutdatedBuildInterval: 900000,
		emitRouteChunkError: 'automatic-immediate',
	},
	/*
	css: ['~/assets/style.css'],
	$development: {
		sourcemap: {
			server: true,
			client: true,
		},
	},
	$production: {
		sourcemap: {
			server: false,
			client: false,
		},
		// https://nuxt.com/docs/guide/concepts/rendering
		routeRules: {
			'*': {
				swr: 10 * 60,
				cache: {base: 'db'},
			},
		},
	},
	nitro: {
		minify: true,

		// https://nitro.unjs.io/guide/storage
		storage: {
			db: {
				driver: 'redis',
				host: '***********',
				port: 6380,
				username: 'default',
				password: '',
				base: '<naziv projekta>',
				ttl: 60 * 60, // 10 min
			},
		},
		devStorage: {
			db: {
				driver: 'redis',
				host: 'redis-16122.c300.eu-central-1-1.ec2.cloud.redislabs.com',
				username: 'nuxt',
				port: 16122,
				password: 'r&k*#TchB3',
			},
			db: {
				driver: 'fs',
				base: '.app_cache_data',
			}
		}		
	},
	*/
});
