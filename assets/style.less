/*------- normalize -------*/
*{margin: 0; padding: 0; border: 0; outline: none; -webkit-tap-highlight-color: transparent; box-sizing: border-box; -webkit-font-smoothing: antialiased; -webkit-backface-visibility: hidden;}
img{max-width: 100%; height: auto;}
html{font-family: sans-serif; /* 1 */ -ms-text-size-adjust: 100%; /* 2 */ -webkit-text-size-adjust: 100%; /* 2 */ scroll-behavior: smooth;}
article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary{display: block;}
audio, canvas, progress, video{display: inline-block; /* 1 */}
audio:not([controls]){display: none; height: 0;}
progress{vertical-align: baseline;}
[hidden], template{display: none;}
a{background-color: transparent; -webkit-text-decoration-skip: objects; /* 2 */}
a:active, a:hover{outline: 0; -webkit-tap-highlight-color: transparent;}
abbr[title]{border-bottom: 1px dotted;}
b, strong{font-weight: 600;}
dfn{font-style: italic;}
mark{background: #ff0; color: #000;}
small{font-size: 80%;}
sub, sup{font-size: 75%; line-height: 0; position: relative; vertical-align: baseline;}
sup{top: -0.5em;}
sub{bottom: -0.25em;}
svg:not(:root){overflow: hidden;}
hr{box-sizing: border-box; height: 0; border-bottom: 1px solid #ccc; margin-bottom: 10px; border-top-style: none; border-right-style: none; border-left-style: none;}
pre{overflow: auto;}
pre.debug{font-size: 14px !important;}
code, kbd, pre, samp{font-family: monospace, monospace; font-size: 1em;}
button, input, optgroup, select, textarea{color: inherit; /* 1 */ font: inherit; /* 2 */ margin: 0; /* 3 */ -webkit-appearance: none;}
button{overflow: visible;}
button, select{text-transform: none;}
button, html input[type="button"], input[type="reset"], input[type="submit"]{-webkit-appearance: button; /* 2 */ cursor: pointer; /* 3 */}
button[disabled], html input[disabled]{cursor: default;}
button::-moz-focus-inner, [type="button"]::-moz-focus-inner, [type="reset"]::-moz-focus-inner, [type="submit"]::-moz-focus-inner{border-style: none; padding: 0;}
input{line-height: normal; border-radius: 0; box-shadow: none;}
input[type="checkbox"], input[type="radio"]{box-sizing: border-box; /* 1 */ padding: 0; /* 2 */}
input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button{height: auto;}
[type="search"]::-webkit-search-cancel-button, [type="search"]::-webkit-search-decoration{-webkit-appearance: none;}
input[type=text], input[type=email], input[type=password], input[type=tel], input[type=search]{-webkit-appearance: none;}
input[type=number] {-moz-appearance:textfield; -webkit-appearance: textfield; -ms-appearance: textfield; -webkit-appearance: none;}
input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {-webkit-appearance: none;}
::-webkit-input-placeholder{color: inherit; opacity: 1;}
fieldset{border: none; margin: 0; padding: 0;}
textarea{overflow: auto; resize: vertical;}
optgroup{font-weight: 600;}
table{border-collapse: collapse; border-spacing: 0;}
input:-webkit-autofill, textarea:-webkit-autofill, select:-webkit-autofill{-webkit-box-shadow: 0 0 0 30px #fff inset; box-shadow: 0 0 0 30px #fff inset;} /* set browser autocomplete bg yellow color to white */
/*------- /normalize -------*/

/*------- fonts -------*/
/*
@font-face {
	font-family: 'lato';
	src: url('assets/fonts/lato-semibold.woff') format('woff'), url('fonts/lato-semibold.woff2') format('woff2');
	font-weight: 600;
	font-style: normal;
	font-display: block;
}
@font-face {
    font-family: 'icomoon';
    src: url('assets/fonts/icomoon.woff?v1') format('woff');
    font-weight: normal;
    font-style: normal;
}
*/
/*------- /fonts -------*/

/*------- vars -------*/
:root{
	// layout
	--borderRadius: 3px;

	// colors
	--primaryColor: #333333;
	--secondaryColor: #ccc;
	--textColor: #333333;
	--linkColor: #005DAC;
	--linkHoverColor: #005DAC;
	--borderColor: #ccc;
	--red: #E21937;
	
	// text
	--font: "Arial", sans-serif;
	--fonti: "icomoon";
	--fontSize: 18px;
	--lineHeight: 1.4;
	
	// warnings
	--errorColor: var(--red);
	--warningColor: #e26d19fb;
	--successColor: #13a01a;
	
	// forms
	--inputBg: #fff;
	--inputBorder: var(--borderColor);
	--inputBorderHover: var(--borderColor);
}
/*------- /vars -------*/

/*------- selectors -------*/
/*
::-webkit-scrollbar { -webkit-appearance: none; width: 5px; }
::-webkit-scrollbar-thumb {
	background-color: @lightGray; border-radius: 5px;
	box-shadow: 0 0 1px rgba(255,255,255,.5);
}
*/
body{background: #fff; color: var(--textColor); font: var(--fontSize)/var(--lineHeight) var(--font);}
a{
	color: var(--linkColor); text-decoration: underline; .transition();
	&:hover{text-decoration: underline; color: var(--linkHoverColor);}
}
ul, ol{margin: 0; padding: 0;}
h1, h2, h3, h4{
	font-weight: 600; padding: 30px 0 15px; font-family: var(--font); font-weight: 600; line-height: 1.2;
	a, a:hover{text-decoration: none;}
}
h1{font-size: 48px; padding-top: 0;}
h2{font-size: 36px;}
h3{font-size: 28px;}
h4{font-size: 20px;}
p{padding-bottom: 10px;}
/*------- /selectors -------*/

/*------- forms -------*/
label{padding: 0 0 4px 0; display: inline-block;}
select{-moz-appearance: none; -o-appearance:none; -webkit-appearance: none; -ms-appearance: none;}
select::-ms-expand {display: none;}
input, textarea, select{-webkit-appearance: none; background-color: var(--inputBg); border-radius: 0; padding: 0 20px; border: 1px solid var(--inputBorder); font-size: 16px; width: 100%; height: 54px; font-family: var(--font); line-height: normal; .transition(border-color);}
input:disabled, textarea:disabled, input:disabled+label, .disabled{cursor: not-allowed !important; color: #ccc;}
input:hover, textarea:hover, select:hover, input:focus, textarea:focus, select:focus{border-color: var(--inputBorderHover); outline: 0;}
input[type=submit], button{border: none; display: inline-block;}
input[type=checkbox], input[type=radio]{padding: 0; height: auto; border: none;}
textarea{height: 130px; padding-top: 10px; padding-bottom: 10px; line-height: 19px;}
legend{
	font-size: 16px; line-height: 18px; font-weight: 600;
	a{text-decoration: none;}
}
input[type=checkbox], input[type=radio]{position: absolute; left: -9999px; display: inline;}
input[type=checkbox] + label, input[type=radio] + label{cursor: pointer; position: relative; padding: 1px 0 4px 32px; min-height: 20px; line-height: 20px; font-size: 14px; text-align: left;}
input[type=radio] + label{width: 100%;}

input[type=checkbox] + label:before{.pseudo(18px, 18px); text-indent: 1px; background: var(--inputBg); color: #fff; border: 1px solid var(--inputBorder); left: 0; text-align: center; top: 0; font: 8px/20px var(--fonti); .transition(all);}
input[type=radio] + label:before{.pseudo(20px, 20px); border-radius: 200px; background: var(--inputBg); color: #fff; border: 1px solid var(--inputBorder); left: 0; text-align: center; top: 0; .transition(all);}

input[type=checkbox]:checked + label:before{background: #ccc; border-color: #ccc;}
input[type=radio]:checked + label{font-weight: 600;}
input[type=radio]:checked + label:before{background: #ccc; border-color: #ccc; box-shadow: inset 0px 0px 0px 3px #fff;}

select{background: var(--inputBg); background-size: 10px; background-position: right 23px top 23px; padding-right: 40px;}

.form-label{
	p, .field{position: relative;padding-bottom: 10px;}
	label{position: absolute; top: 16px; left: 20px; padding: 0; cursor: text; z-index: 10; .transition(all); font-size: 16px; line-height: 22px;}
	.global-error{width: 100%;}
	.focus,.ffl-floated,.floating-label{
		label{top: 7px; font-size: 12px; line-height: 16px; font-weight: normal;}
		input,select{padding-top: 15px; font-size: 16px;}
		textarea{padding-top: 25px;}
	}
	input[type=checkbox] + label, input[type=radio] + label{position: relative; left: auto; top: auto; cursor: pointer; padding: 1px 0 4px 32px; min-height: 24px; line-height: 20px; font-size: 14px; color: var(--textColor); text-align: left;}
	input[type=radio] + label{font-size: 16px;}

}
/*------- /forms -------*/

/*------- tables -------*/
.table{
	width: 100%; border-spacing: 0; margin: 10px 0px 20px;
	th{font-weight: 600; font-size: 14px; text-align: left; padding: 6px 0; border-bottom: 1px solid var(--borderColor);}
	td{border-bottom: 1px solid var(--borderColor); padding: 6px 10px 6px 0;}
	&.stripe tbody tr:nth-child(even){background: #E9E9E9;}
}
.table-row{display: table; width: 100%;}
.table-col{display: table-cell;}
.table-wrapper {
	overflow-x: scroll; -webkit-overflow-scrolling:touch; width: 100%; position: relative; padding-bottom: 35px;
	&:before{ .pseudo(30px,28px); /*background: url(assets/images/arrow.gif) no-repeat;*/ background-size: cover; bottom: 0; right: 4px;}
	&.ios{
		padding-bottom: 40px;
		&:before{.pseudo(30px,28px); /*background: url(assets/images/arrow.gif) no-repeat;*/ background-size: cover; bottom: 0; right: 4px;}
	}
}
/*------- /tables -------*/

/*------- info messages -------*/
.error{color: var(--errorColor); display: block; padding: 5px 0 0 20px; font-size: 12px; line-height: 17px;}
.global-error, .global-success, .global-warning{display: flex; align-items: center; font-size: 16px; margin: 0 0 15px 0; line-height: 22px; padding: 11px 15px 10px 50px; min-height: 44px; background: #F0F2F4; background-position: left 15px top 10px; background-size: 24px auto; color: var(--textColor); position: relative;}
.global-success{background-color: #F0F2F4; background-repeat: no-repeat; background-size: 24px auto; background-position: left 15px top 10px;}
.field_error_input, .field_error_input_radio{border-color: var(--errorColor);}
#field-error-accept_terms{padding-left: 32px;}
/*------- /info messages -------*/

/*------- buttons -------*/
.btn, input[type=submit], button{
	background: var(--primaryColor);
}
/*------- /buttons -------*/