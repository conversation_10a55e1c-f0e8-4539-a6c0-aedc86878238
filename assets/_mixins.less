.transition (@element: color, @speed: .3s) {
	transition: @arguments;
	-webkit-backface-visibility: hidden;
	-webkit-tap-highlight-color: transparent;
}
.grayscale (@bw: 100%) {
	filter: grayscale(@bw);
	-webkit-filter: grayscale(@bw);
	-moz-filter: grayscale(@bw);
}
.gradient(@startColor: #eee, @endColor: white) {
	background: @startColor;
	background-image: linear-gradient(180deg, @startColor 0%, @endColor 100%);
	background-image: -webkit-linear-gradient(180deg, @startColor 0%, @endColor 100%);
	background-image: -moz-linear-gradient(@startColor 0%, @endColor 100%);
	background-image: -ms-linear-gradient(180deg, @startColor 0%, @endColor 100%);
}
.horizontal-gradient (@startColor: #eee, @endColor: white) {
 	background-color: @startColor;
	background-image: -webkit-linear-gradient(90deg, @startColor, @endColor);
	background-image: -ms-linear-gradient(90deg, @startColor, @endColor);
	background-image: linear-gradient(90deg, @startColor, @endColor);
}
.font(@fontSize, @fontLineHeight, @fontType) {
	font-size: @fontSize;
	line-height: @fontLineHeight; 
	font-family: @fontType;
}
.pseudo(@width, @height) {
	content:"";
	position: absolute;
	display: block;
	width:@width;
	height:@height;
}
.animation (@name, @duration: 300ms, @delay: 0, @ease: ease) {
	-webkit-animation: @name @duration @delay @ease;
	-moz-animation:    @name @duration @delay @ease;
	-ms-animation:     @name @duration @delay @ease;
}
.transform(@string){
	transform: @string;
	-webkit-transform: @string;
	-ms-transform: 		 @string;
}
.scale (@factor) {
	transform: scale(@factor);
	-webkit-transform: scale(@factor);
	-ms-transform: 		 scale(@factor);
}
.scaleX(@factor) {
	transform: scaleX(@factor);
	-webkit-transform: scaleX(@factor);
	-ms-transform: 		 scaleX(@factor);	
}
.scaleY (@factor) {
	transform: scaleY(@factor);
	-webkit-transform: scaleY(@factor);
	-ms-transform: scaleY(@factor);
}
.rotate (@deg) {
	transform: rotate(@deg);
	-webkit-transform: rotate(@deg);
	-ms-transform: 		 rotate(@deg);
}
.translate (@x, @y:0) {
	transform: translate(@x, @y);
	-webkit-transform: translate(@x, @y);
	-ms-transform: translate(@x, @y);
}
.translate3d (@x, @y: 0, @z: 0) {
	transform: translate3d(@x, @y, @z);
	-webkit-transform: translate3d(@x, @y, @z);
	-ms-transform: translate3d(@x, @y, @z);
}
.clear() { 
	/**zoom: 1;*/ clear:both;
	&:before, &:after {content:""; display: table; }
	&:after { clear: both; }
}
.placeholder(@color,@focusColor) {
	&::-webkit-input-placeholder { color: @color; }
	&:-ms-input-placeholder { color: @color; }
	&::-moz-placeholder { color: @color; }
	&:focus {
		&::-webkit-input-placeholder { color: @focusColor; }
		&:-ms-input-placeholder { color: @focusColor; }
		&::-moz-placeholder { color: @focusColor; }
	}
}


/*------- icons -------*/
.icon-video() {
  content: "\e942";
}
.icon-image-gallery() {
  content: "\e941";
}
.icon-surprise() {
  content: "\e940";
}
.icon-calendar2() {
  content: "\e93f";
}
.icon-reply() {
  content: "\e93e";
}
.icon-thumbsup() {
  content: "\e93d";
}
.icon-fb2() {
  content: "\e93a";
}
.icon-link() {
  content: "\e93b";
}
.icon-viber2() {
  content: "\e93c";
}
.icon-package-box() {
  content: "\e938";
}
.icon-loyalty() {
  content: "\e939";
}
.icon-payment-protection() {
  content: "\e937";
}
.icon-location() {
  content: "\e936";
}
.icon-faq() {
  content: "\e935";
}
.icon-pickup() {
  content: "\e934";
}
.icon-arrowR() {
  content: "\e931";
}
.icon-close2() {
  content: "\e932";
}
.icon-check2() {
  content: "\e933";
}
.icon-not-available() {
  content: "\e92f";
}
.icon-available() {
  content: "\e930";
}
.icon-danger-white2() {
  content: "\e92e";
}
.icon-webshop-only() {
  content: "\e92d";
}
.icon-clock3() {
  content: "\e92c";
}
.icon-calendar() {
  content: "\e92b";
}
.icon-organic() {
  content: "\e92a";
}
.icon-quote() {
  content: "\e929";
}
.icon-info() {
  content: "\e928";
}
.icon-barcode() {
  content: "\e927";
}
.icon-danger() {
  content: "\e926";
}
.icon-coupon() {
  content: "\e925";
}
.icon-phone2() {
  content: "\e924";
}
.icon-trash() {
  content: "\e923";
}
.icon-star() {
  content: "\e922";
}
.icon-tag() {
  content: "\e921";
}
.icon-clock2() {
  content: "\e91f";
}
.icon-phone() {
  content: "\e920";
}
.icon-arrow-left() {
  content: "\e91e";
}
.icon-portfolio() {
  content: "\e91c";
}
.icon-store() {
  content: "\e91d";
}
.icon-clock() {
  content: "\e91a";
}
.icon-ingredients() {
  content: "\e91b";
}
.icon-spoon() {
  content: "\e919";
}
.icon-eco() {
  content: "\e918";
}
.icon-heart-full() {
  content: "\e917";
}
.icon-cross() {
  content: "\e916";
}
.icon-check() {
  content: "\e915";
}
.icon-arrow-right() {
  content: "\e914";
}
.icon-shipping() {
  content: "\e913";
}
.icon-happiness() {
  content: "\e912";
}
.icon-arrow-down() {
  content: "\e911";
}
.icon-search() {
  content: "\e910";
}
.icon-ig() {
  content: "\e90f";
}
.icon-quickorder() {
  content: "\e90a";
}
.icon-user() {
  content: "\e90b";
}
.icon-cart() {
  content: "\e90c";
}
.icon-heart() {
  content: "\e90d";
}
.icon-help() {
  content: "\e90e";
}
.icon-shield() {
  content: "\e909";
}
.icon-marker() {
  content: "\e907";
}
.icon-symbol() {
  content: "\e908";
}
.icon-pin() {
  content: "\e906";
}
.icon-organic-food() {
  content: "\e905";
}
.icon-email() {
  content: "\e900";
}
.icon-envelope() {
  content: "\e901";
}
.icon-fb() {
  content: "\e902";
}
.icon-viber() {
  content: "\e903";
}
.icon-whatsapp() {
  content: "\e904";
}
/*------- /icons -------*/
