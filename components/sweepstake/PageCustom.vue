<template>
	<BaseMetaSeo :data="data" v-if="seo" />
	<slot :page="data" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const endpoints = useEndpoints();
	const lang = useLang();


	const emit = defineEmits(['load']);
	const props = defineProps({
		seo: {
			type: Boolean,
			default: true,
		},
	});


	async function fetch(config = {}) {
	       const urlParams = route.params.slug
			.slice(1)
			.filter(value => value !== '')
			.join('-');

	       let reqUrl = `${endpoints.get('_get_hapi_sweepstake_pages')}?lang=${lang.get()}&slug=` + urlParams;

	       if(route?.query?.webshoporder_code){
	           reqUrl += '&webshoporder_code='+route?.query?.webshoporder_code;
	       }

		return await useApi(
			reqUrl,
			{
				method: 'GET',
			},
			{cache: true}
		);
	}

	let data = await fetch();
	data = data.data[0];

	onMounted(() => {
		emit('load', data ? data : null);
	});
</script>
