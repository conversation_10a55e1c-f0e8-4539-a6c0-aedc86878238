<template>
	<div class="cd-not-available cd-unavailable" v-if="productId">
		<BaseCmsLabel code="not_available" class="cd-not-available-header" tag="h2" v-if="status == 'not-available'" />
		<BaseCmsLabel code="not_available_subtitle" tag="div" class="cdu-subtitle" />
		<div class="cd-notifyme-form cd-notify-form-container">
			<BaseFeedbackNotificationForm class="form-notifyme cd-form-notifyme cd-notify-form form-animated-label" v-slot="{fields, status, loading}">
				<div class="cd-form-notifyme-cnt cd-notify-form-fields" v-if="!status?.success">
					<BaseFormField v-for="field in fields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel}">
						<template v-if="field.type != 'hidden'">
							<BaseFormInput :id="`notification-${field.name}`" :placeholder="labels.get('email')" />
							<span class="error" v-show="errorMessage" v-html="errorMessage" />
						</template>
						<BaseFormInput v-else :id="`notification-${field.name}`" :value="productId" />
					</BaseFormField>
					<button :disabled="loading" class="btn btn-orange btn-notify btn-notifyme-form" type="submit"><BaseCmsLabel code="notifyme" tag="span" /></button>
				</div>
				<div class="notifyme-success" v-show="status?.success">
					<BaseCmsLabel code="notifyme_catalog_ty" class="notifyme-success-content" tag="div" />
				</div>
			</BaseFeedbackNotificationForm>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['item', 'status']);
	const productId = computed(() => (props.item?.widget_code) ? props.item.widget_code.replace(/_/g, "-") : null);
	const labels = useLabels();
</script>
