<template>
	<BaseFeedbackComments :items="item?.feedback_comment_widget?.items ? item?.feedback_comment_widget?.items : {}" sort="desc" v-slot="{items: comments}">
		<div :class="containerClass">
			<div class="comment-form-container">
				<template v-if="mode != 'catalogDetail'">
					<BaseCmsLabel v-if="mode == 'pd'" tag="div" class="comments-title" code="add_article_comment" />
					<BaseCmsLabel v-else-if="mode == 'pdRecipes'" tag="div" class="comments-title" code="add_recipe_comment" />
					<BaseCmsLabel v-else tag="div" class="comments-title" code="comments" />
				</template>
				<template v-else>
					<div class="comments-filters-container">
						<div class="btn btn-green comment-button" @click="modal.open('comment')"><BaseCmsLabel code="comment_button_label" tag="span" /></div>

						<template v-if="item.feedback_comment_widget.comments > 1">
							<div class="comments-filters" :class="{loading: loading}">
								<UiLoader color="black" v-if="loading" />
								<div v-for="(filter, key) in filters" :key="key" @click="toggleFilter(key)" class="comment-filter" :class="['filter-'+ key, {active: filter.active}]">
									<span class="comment-filter-title">{{filter.label}}</span>
									<span class="comment-btn"></span>
								</div>

								<select v-model="selectedSort" @change="sort" class="comment-select">
									<option value="" selected disabled><BaseCmsLabel code="comments_sort" /></option>
									<option value="new"><BaseCmsLabel code="comments_sort_new" /></option>
									<option value="old"><BaseCmsLabel code="comments_sort_old" /></option>
									<option value="rate_up"><BaseCmsLabel code="comments_sort_rate_up" /></option>
									<option value="rate_down"><BaseCmsLabel code="comments_sort_rate_down" /></option>
								</select>
							</div>
						</template>
					</div>
				</template>
				<div class="comment-form-inner" :class="{'active': activeCommentForm}">
					<BaseFeedbackCommentsForm class="clear comment-form form-inline form-label" v-slot="{fields, status, onReset}">
						<div class="comments-close" @click="activeCommentForm = !activeCommentForm" />
						<template v-if="!status?.success">
							<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
								<BaseFormInput v-if="item.type == 'hidden'" />
								<p v-else class="field comment-field" :class="['comment-field-' + item.name, {'ffl-floated': floatingLabel, 'hidden': item.type == 'hidden'}]">
									<label v-if="item.name === 'rate'" class="label">
										<BaseCmsLabel :code="mode === 'pdRecipes' ? 'your_rate_recipe' : 'your_rate'" />
									</label>
									<label v-else class="label" :for="item.name">
										<BaseCmsLabel :code="'form_comments_' + item.name" :default="item.name" />
									</label>
									<BaseFormInput />
									<span class="error" v-show="errorMessage" v-html="errorMessage" />
								</p>
							</BaseFormField>
							<div class="buttons comment-buttons">
								<button class="btn btn-primary btn-send-comment" type="submit">
									<span><BaseCmsLabel code="send_comment" /></span>
								</button>
								<BaseCmsLabel code="comment_note" class="comment-note" tag="div" />
							</div>
						</template>
						<div class="comment-success" v-if="status?.success">
							<div class="comment-success-message"><BaseCmsLabel code="comment_success" /></div>
							<div class="btn btn-comment-new" @click="onReset">
								<span><BaseCmsLabel code="comment_add_new" /></span>
							</div>
						</div>
					</BaseFeedbackCommentsForm>
				</div>
			</div>
		</div>

		<div v-if="bestReviewedComment">
			<BaseCmsLabel code="best_reviewed_commet" class="comments-titles" tag="div" />
			<FeedbackCommentEntry :key="bestReviewedComment.id" :item="bestReviewedComment" class="cd-comment cd-best-comment" />
		</div>

		<!-- FIXME - lista komentara se ne prikazuje, vraća prazni array ispisa comments -->
		<div id="comment-list" class="comment-list" :class="{'cd-comment-list': mode == 'catalogDetail'}">
			<div class="comments-title comments-list-title">
				<BaseCmsLabel code="other_comments_and_reviews" /> <span class="comments-counter">({{ comments.length }})</span>
			</div>
			<div class="comment-list-cnt">
				<FeedbackCommentEntry v-for="comment in comments" :key="comment.id" :item="comment" class="cd-comment" :mode="mode" />
				<button class="comments-load-more" type="button" v-if="pagination?.next" @click="loadMore" :disabled="loading"><BaseCmsLabel code="load_mode_comments" tag="span" /></button>
			</div>
		</div>
	</BaseFeedbackComments>
</template>

<script setup>
	const props = defineProps(['item', 'mode']);
	const containerClass = (props.mode == 'catalogDetail') ? 'catalog-comments' : 'publish-comments';
	const modal = useModal();
	const labels = useLabels();
	const endpoints = useEndpoints();
	const lang = useLang();
	const activeCommentForm = ref(false);

	const loading = ref(false)
	const comments = ref([]);
	const bestReviewedComment = ref(null)
	const selectedSort = ref('');
	const currentPage = ref(1);
	const pagination = ref(null);

	const filters = ref({
		verified: { label: labels.labels.commetns_real_customer, query: 'verified_users_only=1', active: false },
		media: { label: labels.labels.photo_video_review, query: 'with_media=1', active: false },
	});

	const queryOptions = computed(() => {
		const queryParts = Object.entries(filters.value).filter(([_, filter]) => filter.active).map(([_, filter]) => filter.query);

		if (selectedSort.value) {
			queryParts.push(`sort=${selectedSort.value}`);
		}

		queryParts.push(`page=${currentPage.value}`);

		return '?' + queryParts.join('&');
	});

	function toggleFilter(filterKey) {
		filters.value[filterKey].active = !filters.value[filterKey].active;
		currentPage.value = 1;
		fetchComments();
	}

	function sort() {
		currentPage.value = 1;
		fetchComments();
	}

	function loadMore() {
		if (pagination.value?.next) {
			currentPage.value = pagination.value.next;
			fetchComments(true);
		}
	}

	async function fetchComments(append = false) {
		loading.value = true;

		let ep = endpoints.get('_get_hapi_feedback_comments');
		ep = ep.replace('%CONTENT_TYPE%', 'catalogproduct').replace('%ID%', props.item.id).replace('%LANG%', lang.get())+queryOptions.value;
		const res = await useApi(
			ep,
			{method: 'GET'}
		);

		if (res.success) {
			if(!bestReviewedComment.value){
				bestReviewedComment.value = res.data.items.reduce((max, comment) => {
					return comment.review > max.review ? comment : max;
				}, res.data.items[0]);
			}

			if (append) {
				comments.value.push(...res.data.items);
			} else {
				comments.value = res.data.items;
			}

			pagination.value = res.data.meta_data.pagination.page ? res.data.meta_data.pagination.page : null;

		}
		loading.value = false;
	}


	//comments form images & video
	const images = ref({});
	const imageInput = ref({});

	const video = ref({});
	const videoInput = ref({});

	const handleImageChange = (event) => {
		let files = Array.from(event.target.files)
		images.value = files.slice(0, 5)
	}

	function clearImages() {
		images.value = {};
		imageInput.value[0].value = '';
	}

	function clearVideo() {
		video.value = {};
		videoInput.value[0].value = '';
	}
	//comments form images & video

	onMounted(() => {
		if(props.item?.feedback_comment_widget?.comments > 0 && props.item?.feedback_comment_widget?.model == 'catalogproduct'){
			fetchComments();
		}
	})
</script>

<style scoped lang="less">
	.comment-field-rate {
		.label {
			position: relative;
			top: auto;
			left: auto;
			font-size: 14px;
			color: @textColor;
			padding-right: 15px;
		}
	}

	.comments-filters-container{position: relative; display: flex; flex-flow: column; gap: 16px; width: 100%; margin-bottom: 32px;}
	.comment-button{width: 100%;}
	.comments-filters{
		display: flex; gap: 8px; position: relative;
		.base-loader{background: rgba(255,255,255,0.5); display: flex; align-items: center; justify-content: center; position: absolute; left: 0; right: 0; height: 54px; z-index: 1;}
	}
	.comment-filter{
		flex-grow: 1; flex-shrink: 1; display: flex; align-items: center; gap: 7px; font-size: 14px; line-height: 1.5; padding: 0 20px; height: 54px; border: 1px solid @borderColor; border-radius: 2px; cursor: pointer;
		&.active{
			.comment-btn{
				background: @green;
				&:after{left: auto; right: 0;}
			}
		}
	}
	.comment-btn{
		position: relative; width: 30px; height: 10px; border-radius: 5px; background: #898989; display: flex; align-items: center;
		&:after{.pseudo(20px,20px); background: #fff; border-radius: 50%; left: 0; box-shadow: 0 1px 2px rgba(0,0,0,0.1);}
	}
	.comment-select{width: 270px; flex-grow: 0; flex-shrink: 0;}
	.comments-titles{display: block; font-size: 22px; line-height: 1.5; font-weight: 700;}
	.cd-best-comment{margin-top: 10px; background: rgba(149, 159, 144, 0.06); border: 1px solid @borderColor; padding: 20px 24px 16px; margin-bottom: 32px;}
</style>
