<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}" v-bind="$attrs">
		<slot :errors="errors" :meta="meta" :values="values" :onReset="onReset" :fields="fields" :loading="loading" :status="status" :onSubmit="onSubmit" />
	</BaseForm>
</template>

<script setup>
	const lang = useLang();
	   const feedback = useFeedback();
	const labels = useLabels();
	const props = defineProps({
		parentCommentId: [Number, String],
		images: Object,
		video: Object,
	});

	let recaptcha;
	if (__RECAPTCHA__) recaptcha = useRecaptcha();

	// fetch form fields
	const fields = ref([]);

	onMounted(async () => {
		fields.value = await feedback.fetchCommentsForm().then(res => (res?.data ? res.data : []));
		if (fields.value?.length && props.parentCommentId) {
			fields.value = toRaw(fields.value).map(field => {
				if (field.name == 'parent_id') return {...field, value: props.parentCommentId};
				return field;
			});
		}
	});

	const loading = ref(false);
	const status = ref(null);

	// on form submit
	async function onSubmit({values, actions}) {
	       loading.value = true;
	       const formData = new FormData();

	       values.lang = lang.get();

		// If recaptcha is enabled, fetch the token and add it to the data
	       if (recaptcha) {
			const recaptchaToken = await recaptcha.fetchRecaptchaToken({action: 'submit-comment'});
			if (recaptchaToken) values['g-recaptcha-response'] = recaptchaToken;
		}

		Object.entries(values).forEach(([key, value]) => {
			formData.append(key, value)
		})

	       Object.entries(props.images).forEach(([index, image]) => {
	           const fieldName = `image_${Number(index) + 1}`
	           formData.append(fieldName, image)
		})

		if(props.video.name){
			formData.append('video', props.video)
		}

		const res = await feedback.submitComment(formData);
		status.value = res;

		// reset form values if success
		if (res.success) actions.resetForm();

		// set field errors if response contains api errors
		if (res?.data?.errors?.length) {
			res.data.errors.forEach(error => {
				actions.setFieldError(error.field, labels.get(error.error, error.error));
			});
		}

		loading.value = false;
		return res;
	}

	// reset form to initial values
	function onReset() {
		status.value = {};
	}
</script>
