<template>
	<div class="publish-comments" :class="[mode]">
		<div class="cd-comments-header" v-if="mode == 'catalogDetail'">
			<BaseCmsLabel tag="div" class="comments-title" code="reviews_and_comments" />
			<div class="cd-rates-container">
				<FeedbackRating :rating="item.feedback_rate_widget.rates" />
				<FeedbackRates :rates="item.feedback_rate_widget.rates" mode="cd-rates cd-comment-rates" v-if="item.feedback_rate_widget.rates_votes > 0" />
			</div>
			<div class="cd-comments-note" v-html="labels.get('comments_counter_label').replace('%c%', item.feedback_comment_widget?.comments)"></div>
		</div>
		<template v-else>
			<BaseCmsLabel v-if="mode == 'pd'" tag="div" class="comments-title" code="add_article_comment" />
			<BaseCmsLabel v-else-if="mode == 'pdRecipes'" tag="div" class="comments-title" code="add_recipe_comment" />
			<BaseCmsLabel v-else tag="div" class="comments-title" code="comments" />
		</template>

		<BaseThemeUiModal name="comment" v-slot="{item: parentId}" :mask-closable="true">
			<div class="clear comment-form-container">
				<!-- <div class="pd-comments-title"><BaseCmsLabel code="comments" /></div> -->
				<!-- <div v-if="mode == 'pd'" class="pd-comments-intro"><BaseCmsLabel code="publish_comment_intro" /></div> -->
				<div class="cd-comments-intro comments-title" v-if="mode == 'catalogDetail'"><BaseCmsLabel code="product_comment_intro" /></div>
				<div class="cd-comments-intro comments-title" v-else><BaseCmsLabel code="comment_intro" /></div>
				<div class="comment-form-inner">
					<FeedbackCommentsFormCustom :images="images" :video="video" class="form-comment form-label form-animated-label" :parent-comment-id="parentId.parent_comment_id ? parentId.parent_comment_id : ''" v-slot="{fields, status, onReset}">
						<template v-if="!status?.success">
							<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
								<p v-if="item.name == 'image_1'" class="field comment-field comment-field-images" :class="['comment-field-' + item.name, {'ffl-floated': floatingLabel}]">
									<input type="file" :name="item.name" :id="item.name" multiple ref="imageInput" @change="handleImageChange" />
									<label :for="item.name"><BaseCmsLabel code="field_add_photos" /><BaseCmsLabel code="field_add_photos_max" tag="span" /></label>
									<!--BaseCmsLabel code="field_add_photos_max_mb" class="field-s-note" tag="span" /-->
									<span class="uploaded-images" v-if="images.length">
										<span class="uploaded-images-items">
											<div v-for="(image, index) in images" :key="index">{{image.name}}</div>
										</span>
										<span class="uploaded-delete" @click="clearImages">Obriši fotografije</span>
									</span>
								</p>
								<p v-else-if="item.name == 'video'" class="field comment-field" :class="['comment-field-' + item.name, {'ffl-floated': floatingLabel}]">
									<input type="file" :name="item.name" :id="item.name" ref="videoInput" @change="(event) => {video = event.target.files?.[0]}" />
									<label :for="item.name"><BaseCmsLabel code="field_add_video" /></label>
									<!--BaseCmsLabel code="field_add_video_max_mb" class="field-s-note" tag="span" /-->

									<span class="uploaded-images" v-if="video.name">
										<span class="uploaded-images-items">
											{{ video.name }}
										</span>
										<span class="uploaded-delete" @click="clearVideo">Obriši video</span>
									</span>
								</p>
								<BaseFormInput v-else-if="item.type == 'hidden'" />
								<p v-else-if="!item.name.includes('image')" class="field comment-field" :class="['comment-field-' + item.name, {'ffl-floated': floatingLabel, 'hidden': item.type == 'hidden'}]">
									<BaseFormInput :id="item.name" />
									<span v-if="item.name == 'rate'" class="label"><BaseCmsLabel :code="mode === 'pdRecipes' ? 'your_rate_recipe' : 'your_rate'" /></span>
									<label v-else :for="item.name"><BaseCmsLabel :code="'inquiry_' + item.name" :default="item.name" /></label>
									<span class="error" v-show="errorMessage" v-html="errorMessage" />
								</p>
							</BaseFormField>
							<div class="buttons comment-button-cnt">
								<button class="btn btn-primary btn-send-comment" type="submit">
									<span><BaseCmsLabel code="send_comment" /></span>
								</button>
								<BaseCmsLabel code="comment_form_note" class="comment-form-note" tag="div" />
							</div>
						</template>
						<div class="comment-success" v-if="status?.success">
							<div class="comment-success-message"><BaseCmsLabel code="comment_success" /></div>
							<div class="btn btn-primary btn-comment-new" @click="onReset">
								<span><BaseCmsLabel code="comment_add_new" /></span>
							</div>
						</div>
					</FeedbackCommentsFormCustom>
				</div>
			</div>
		</BaseThemeUiModal>
	</div>

	<div id="comment-list" class="comment-list">
		<div class="comment-filters-sections">
			<div class="btn btn-green comment-button" @click="modal.open('comment')"><BaseCmsLabel code="comment_button_label" tag="span" /></div>

			<template v-if="item.feedback_comment_widget.comments > 1 && mode != 'pd'">
				<div class="comments-filters" :class="{loading: loading}">
					<UiLoader color="black" v-if="loading" />
					<div v-for="(filter, key) in filters" :key="key" @click="toggleFilter(key)" class="comment-filter" :class="['filter-'+ key, {active: filter.active}]">
						<span class="comment-filter-title" v-html="filter.label"></span>
						<span class="comment-btn"></span>
					</div>

					<select v-model="selectedSort" @change="sort" class="comment-select">
						<option value="" selected disabled><BaseCmsLabel code="comments_sort" /></option>
						<option value="new"><BaseCmsLabel code="comments_sort_new" /></option>
						<option value="old"><BaseCmsLabel code="comments_sort_old" /></option>
						<option value="rate_up"><BaseCmsLabel code="comments_sort_rate_up" /></option>
						<option value="rate_down"><BaseCmsLabel code="comments_sort_rate_down" /></option>
					</select>
				</div>
			</template>

			<template v-if="item.feedback_comment_widget.comments > 1 && mode != 'pd'">
				<div v-if="bestReviewedComment">
					<BaseCmsLabel code="best_reviewed_commet" class="comments-titles" tag="div" />
					<FeedbackCommentEntry :key="bestReviewedComment.id" :item="bestReviewedComment" class="cd-comment cd-best-comment" />
				</div>
			</template>
		</div>

		<BaseFeedbackComments v-if="mode == 'pd' || mode == 'pdRecipes'" :items="item?.feedback_comment_widget?.items ? item?.feedback_comment_widget?.items : {}" sort="desc" v-slot="{items: commentsPublish}">
			<template v-if="commentsPublish?.length > 0">
				<div class="comments-list-main">
					<div class="comments-title comments-list-title">
						<BaseCmsLabel code="comments" /> <span class="comments-counter">({{ commentsPublish.length }})</span>
					</div>
					<div v-if="item.feedback_comment_widget.comments > 0" class="comment-list-cnt">
						<template v-for="comment in commentsPublish" :key="comment.id">
							<FeedbackCommentEntry :item="comment" class="cd-comment" :mode="[mode]" />
						</template>
					</div>
				</div>
			</template>
		</BaseFeedbackComments>
		<template v-else>
			<div class="comments-title comments-list-title">
				<BaseCmsLabel code="other_comments_and_reviews" /> <span class="comments-counter">({{ comments.length }})</span>
			</div>
			<div v-if="item.feedback_comment_widget.comments > 0" class="comment-list-cnt">
				<template v-for="comment in comments" :key="comment.id">
					<FeedbackCommentEntry :item="comment" class="cd-comment" v-if="Number(comment.parent_id) == 0" :mode="mode" />
					<div class="comment-replies-container" v-if="comment?.comment_replies">
						<FeedbackCommentEntry v-for="commentChild in comment.comment_replies" :key="commentChild.id" :item="commentChild" class="cd-comment" :mode="mode" />
					</div>
				</template>
				<button class="comments-load-more" type="button" v-if="pagination?.next" @click="loadMore" :disabled="loading"><BaseCmsLabel code="load_mode_comments" tag="span" /></button>
			</div>
			<div v-else class="no-comments"><BaseCmsLabel code="no_comments" /></div>
		</template>
	</div>
</template>

<script setup>
	const modal = useModal();
	const labels = useLabels();
	const props = defineProps(['item', 'mode']);
	const endpoints = useEndpoints();
	const lang = useLang();

	const loading = ref(false)
	const comments = ref([]);
	const bestReviewedComment = ref(null)
	const selectedSort = ref('');
	const currentPage = ref(1);
	const pagination = ref(null);

	const filters = ref({
		verified: { label: labels.labels.commetns_real_customer, query: 'verified_users_only=1', active: false },
		media: { label: labels.labels.photo_video_review, query: 'with_media=1', active: false },
	});

	const queryOptions = computed(() => {
		const queryParts = Object.entries(filters.value).filter(([_, filter]) => filter.active).map(([_, filter]) => filter.query);

		if (selectedSort.value) {
			queryParts.push(`sort=${selectedSort.value}`);
		}

		queryParts.push(`page=${currentPage.value}`);

		return '?' + queryParts.join('&');
	});

	function toggleFilter(filterKey) {
		filters.value[filterKey].active = !filters.value[filterKey].active;
		currentPage.value = 1;
		fetchComments();
	}

	function sort() {
		currentPage.value = 1;
		fetchComments();
	}

	function loadMore() {
		if (pagination.value?.next) {
			currentPage.value = pagination.value.next;
			fetchComments(true);
		}
	}

	async function fetchComments(append = false) {
		loading.value = true;

		let ep = endpoints.get('_get_hapi_feedback_comments');
		ep = ep.replace('%CONTENT_TYPE%', 'catalogproduct').replace('%ID%', props.item.id).replace('%LANG%', lang.get())+queryOptions.value;
		const res = await useApi(
			ep,
			{method: 'GET'}
		);

		if (res.success) {
			if(!bestReviewedComment.value){
				bestReviewedComment.value = res.data.items.reduce((max, comment) => {
					return comment.review > max.review ? comment : max;
				}, res.data.items[0]);
			}

			if (append) {
				comments.value.push(...res.data.items);
			} else {
				comments.value = res.data.items;
			}

			pagination.value = res.data.meta_data.pagination.page ? res.data.meta_data.pagination.page : null;

		}
		loading.value = false;
	}


	//comments form images & video
	const images = ref({});
	const imageInput = ref({});

	const video = ref({});
	const videoInput = ref({});

	const handleImageChange = (event) => {
		let files = Array.from(event.target.files)
		images.value = files.slice(0, 5)
	}

	function clearImages() {
		images.value = {};
		imageInput.value[0].value = '';
	}

	function clearVideo() {
		video.value = {};
		videoInput.value[0].value = '';
	}
	//comments form images & video

	onMounted(() => {
		if(props.item?.feedback_comment_widget?.comments > 0 && props.item?.feedback_comment_widget?.model == 'catalogproduct'){
			fetchComments();
		}
	})
</script>

<style lang="less" scoped>
	.comments-title{
		font-size: 20px; font-weight: bold; padding: 0 0 16px; color: #244538;
		@media (max-width: @t){font-size: 18px;}
		@media (max-width: @m){font-size: 16px;}
	}
	.pd-comments{
		.comment-field-rate, .comment-rate{display: none!important;}
		@media (max-width: @m){padding-bottom: 35px;}
	}
	.comment-form{display: flex; flex-wrap: wrap;}
	.comment-field{
		width: 100%; padding-bottom: 8px;
		:deep(input), :deep(textarea){padding: 0 20px;}
		&.ffl-floated{
			:deep(input){padding-top: 16px;}
			:deep(textarea){padding-top: 25px;}
		}
		label{font-size: 14px; left: 20px;}
		//@media (max-width: @tp){width: calc(~"50% - 5px");}
		//@media (max-width: @m){width: 100%;}
	}
	.comment-field-rate{
		width: 100%; display: flex; align-items: center; flex-flow: row-reverse; justify-content: flex-end; padding-left: 25px; margin: 8px 0; gap: 10px;
		@media (max-width: @m){padding-left: 15px;}
	}
	.comment-button-cnt{
		display: flex; flex-flow: column; align-items: center; gap: 16px; margin-top: 7px;
		.btn{width: 100%;}
		@media (max-width: @m){flex-flow: column; gap: 8px;}
	}
	.comment-form-note{font-size: 12px; text-align: center; color: @gray2; max-width: 270px;}
	.comment-field-email{margin-left: auto;}
	.comment-field-message{
		width: 100%;
		textarea{width: 100%; display: block;}
	}
	:deep(.form-rate-item){
		display: inline-flex; margin-right: 5px; width: 20px; height: 21px;
		input[type=radio]+label{
			padding: 0; font-size: 0;
			&:before{.icon-star(); color: #D5D9D3; font: 20px/20px @fonti; border: 0; box-shadow: none!important; background: none!important;}
			&:after{display: none;}
		}
		&.active, &.active-hover{
			input[type=radio]+label:before{color: @yellow;}
		}
	}
	:deep(.form-field-rate-items){
		display: flex;
		@media (max-width: @m){.scale(0.8); transform-origin: left center;}
	}
	.comment-field-images, .comment-field-video{
		label{
			position: absolute; display: flex; align-items: center; background: #fff; top: 1px; left: 1px; right: 1px; height: 52px; border-radius: @borderRadius; padding: 0 20px;
			&:before{.icon-image-gallery(); font: 24px/1 @fonti; color: @lightGreen; margin-right: 8px;}
			span{font-size: 12px; line-height: 1.2; color: @gray2; flex-grow: 1; flex-shrink: 1; text-align: right;}
			@media (max-width: @m){height: 45px;}
		}
	}
	.comment-field-video label:before{.icon-video();}
	.uploaded-images{position: relative; display: flex; flex-flow: column; padding: 5px 20px 0;}
	.uploaded-images-items{display: flex; flex-flow: column; gap: 5px; font-size: 12px; line-height: 1.2; color: @lightGreen;}
	.uploaded-delete{
		display: block; font-size: 12px; line-height: 1.2; color: @red; margin-top: 5px; font-weight: bold; cursor: pointer; .transition(color);
		@media (min-width: @h){
			&:hover{color: @red*1.2;}
		}
	}
	.error{padding-left: 20px; font-size: 12px; line-height: 1.2;}

	.btn-send-comment{
		min-width: 140px;
		//@media (max-width: @t){min-width: 150px; font-size: 16px;}
		@media (max-width: @tp){min-width: 120px; font-size: 14px;}
		@media (max-width: @m){width: 100%;}
	}
	.cd-comments .comment-note{
		text-align: left; padding-left: 20px; max-width: 310px; margin-right: 0;
		@media (max-width: @m){padding: 15px 15px 0; max-width: 300px; margin: auto; text-align: center;}
	}
	.comment-buttons{
		display: flex; width: 100%; align-items: center;
		@media (max-width: @m){flex-wrap: wrap;}
	}
	.comment-note{
		font-size: 12px; font-style: italic; line-height: 1.5; color: @gray2; max-width: 270px; margin-right: 25px; margin-left: auto; text-align: center;
		@media (max-width: @t){margin-right: 0;}
		@media (max-width: @tp){font-size: 11px;}
		@media (max-width: @m){margin: 15px auto 0;}
	}
	.comment-success .btn{margin-top: 15px;}
	.comments-list{
		margin-bottom: 60px;
		@media (max-width: @tp){margin-bottom: 30px;}
		@media (max-width: @m){margin-bottom: 0;}
		&.active{
			.comments-load-more{display: none;}
			.comment-items .comment:nth-of-type(1n + 11){display: block;}
		}
	}
	.comments-list-title{padding-bottom: 0;}
	.comment-items{
		.comment:nth-of-type(1n + 11){display: none;}
	}
	.comments-load-more{margin-top: 8px;}


	.comments-title{
		font-size: 22px; line-height: 1.5; padding-bottom: 12px;
		@media (max-width: @m){font-size: 14px; padding-bottom: 8px;}
	}
	.cd-rates-container{
		display: flex; align-items: center; margin-bottom: 8px; font-size: 34px; line-height: 1.2; gap: 12px;
		@media (max-width: @m){font-size: 16px;}
	}

	.cd-comments-note{
		display: block; font-size: 14px; line-height: 1.5; margin-bottom: 16px;
		:deep(strong){color: @green;}
		@media (max-width: @m){font-size: 12px; margin-bottom: 12px;}
	}
	.comment-button{
		width: 100%; margin-bottom: 16px; cursor: pointer;
		@media (max-width: @m){margin-bottom: 8px;}
	}
	.comments-filters{
		display: flex; gap: 8px; position: relative; margin-bottom: 32px;
		.base-loader{background: rgba(255,255,255,0.5); display: flex; align-items: center; justify-content: center; position: absolute; left: 0; right: 0; height: 54px; z-index: 1;}
		@media (max-width: 1500px){flex-wrap: wrap;}
		@media (max-width: @m){margin-bottom: 12px;}
	}
	.comment-filter{
		flex-grow: 1; flex-shrink: 1; display: flex; align-items: center; gap: 7px; font-size: 14px; line-height: 1.5; padding: 0 20px; height: 54px; border: 1px solid @borderColor; border-radius: 2px; cursor: pointer;
		&.active{
			.comment-btn{
				background: @green;
				&:after{left: auto; right: 0;}
			}
		}
		@media (max-width: @m){
			flex-grow: 0; flex-shrink: 0; width: calc(~"50% - 4px"); font-size: 12px; height: 47px; gap: inherit; justify-content: space-between;
			:deep(.comment-filter-title) span{display: none;}
		}

	}
	.comment-btn{
		position: relative; width: 30px; height: 10px; border-radius: 5px; background: #898989; display: flex; align-items: center;
		&:after{.pseudo(20px,20px); background: #fff; border-radius: 50%; left: 0; box-shadow: 0 1px 2px rgba(0,0,0,0.1);}
	}
	.comment-select{
		width: 270px; flex-grow: 0; flex-shrink: 0;
		@media (max-width: @l){width: 210px;}
		@media (max-width: 1500px){width: 100%;}
	}
	.comments-titles{
		display: block; font-size: 22px; line-height: 1.5; font-weight: 700;
		@media (max-width: @m){font-size: 14px;}
	}
	.cd-comment-rates{
		display: inline-flex;
		:deep(.icon-star), :deep(.icon-star-empty){
			position: relative; margin-right: 2px;
			&:before{
				.icon-star(); font: 28px/1 @fonti; color: #d5d9d3;
				@media (max-width: @m){font-size: 14px;}
			}
		}
		:deep(.icon-star:before){color: @yellow;}
	}

	//publish comments
	.comments-list-main{
		margin-bottom: 60px;
		@media (max-width: @tp){margin-bottom: 30px;}
		@media (max-width: @m){margin-bottom: 0;}
	}
	.pd-comments,.pd-recipe-comments{
		.comments-counter{color: @green; font-weight: normal; font-size: 16px;}
		.comment-filters-sections{margin-bottom: 50px;}
	}


	// modal comments
	.publish-comments{
		:deep(.base-modal-cnt){width: 412px; padding: 32px 40px; border-radius: 0;}
		:deep(.base-modal-cnt-close){
			position: absolute; width: 40px; height: 40px; background: @red; display: flex; align-items: center; justify-content: center; border-radius: 0 @borderRadius 0 0; top: 0; right: 0; cursor: pointer; border: none; border-radius: 0; .transition(background);
			&:before{.icon-close2; font: 16px/1 @fonti; color: #fff;}
			svg{display: none;}
			@media (min-width: @h){
				&:hover{background: darken(@red,5%)}
			}
		}
		@media (max-width: @m){
			:deep(.base-modal-cnt){width: 100vw; max-width: 100vw; padding: 24px 16px; height: 100svh; border-radius: 0;}
			:deep(.base-modal-cnt-close){
				border-radius: 0; width: 32px; height: 32px;
				&:before{font-size: 10px;}
			}
		}
	}
	.form-comment{display: flex; flex-flow: column;}

	//comment replies
	.comment-replies-container{
		position: relative; display: flex; flex-flow: column; border: 1px solid #E9ECEB; background: #f9f9f8; border-radius: @borderRadius; margin-top: -1px; margin-bottom: -1px; margin-left: -2px; margin-right: -2px; padding: 0 24px;
		&:after{.pseudo(14px,14px); .rotate(45deg); background: #f9f9f8; top: -7px; right: 52px; border-left: 1px solid #E9ECEB; border-top: 1px solid #E9ECEB;}
		@media (max-width: @m){
			&:after{width: 10px; height: 10px; top: -6px; right: 59px;}
		}
	}
	/*
	:deep(.comment-message){line-height: 1.6;}
	:deep(.comment-rate){
		line-height: 0; height: 12px;
		.icon-star{line-height: 0; height: 12px;}
	}
	:deep(.comments-list-title){font-size: 22px; line-height: 32px; padding-bottom: 8px;}
	:deep(.comment-form-container){margin-bottom: 0;}
	:deep(.comment-form-inner){
		position: fixed; top: 0; right: 0; bottom: 0; left: 0; display: none; align-items: center; justify-content: center; background: rgba(28,34,25,0.75); z-index: 1000;
		&.active{display: flex;}
	}
	:deep(.comment-form){background: #fff; position: relative; width: 595px; height: auto; padding: 40px; border-radius: @borderRadius;}
	:deep(.comments-close){
		position: absolute; width: 40px; height: 40px; background: @red; display: flex; align-items: center; justify-content: center; border-radius: 0 @borderRadius 0 0; top: 0; right: 0; cursor: pointer; .transition(background);
		&:before{.icon-close2; font: 16px/1 @fonti; color: #fff;}
		@media (min-width: @h){
			&:hover{background: darken(@red,5%)}
		}
	}
	:deep(.btn-send-comment){min-width: 140px;}
	:deep(.comment-note){max-width: 295px;}
	*/
</style>
