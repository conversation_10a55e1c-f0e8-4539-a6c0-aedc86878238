<template>
	<article class="comment" :class="[mode, {'comment-manager': Number(item.manager) == 1}, {'comment-child': Number(item.parent_id) != 0}]">
		<div class="comment-col comment-content">
			<div class="comment-header">
				<span v-if="item.rate && Number(item.parent_id) == 0" class="cp-rate comment-rate fz0">
					<span v-for="i in 5" :key="i" class="icon-star-empty" :class="{'icon-star': i <= item.rate}"></span>
				</span>
				<span class="comment-username">{{ item.display_name }}</span>
				<BaseCmsLabel v-if="item.verified_buyer" code="comment_verified_buyer" class="comment-verified-buyer" tag="div" />
				<span class="comment-date"><BaseUtilsFormatDate :date="item.datetime_created" format="DD. MMMM YYYY." /></span>
			</div>
			<div class="comment-message">{{ item.message }}</div>
			<div class="comment-attachments" v-if="mode == 'catalogDetail'">
				<div class="comment-images" v-if="item.image_1_upload_path" v-interpolation>
					<a :href="item.image_1_upload_path" class="fancybox"><BaseUiImage :src="item.image_1_upload_path" /></a>
					<a v-if="item.image_2_upload_path" :href="item.image_2_upload_path" class="fancybox"><BaseUiImage :src="item.image_2_upload_path" /></a>
					<a v-if="item.image_3_upload_path" :href="item.image_3_upload_path" class="fancybox"><BaseUiImage :src="item.image_3_upload_path" /></a>
					<a v-if="item.image_4_upload_path" :href="item.image_4_upload_path" class="fancybox"><BaseUiImage :src="item.image_4_upload_path" /></a>
					<a v-if="item.image_5_upload_path" :href="item.image_5_upload_path" class="fancybox"><BaseUiImage :src="item.image_5_upload_path" /></a>
				</div>
				<div class="comment-video" @click="videoActive = !videoActive" :class="{'play': videoActive}">
					<video controls="true" playsinline v-if="item.video_upload_path">
						<source :src="config.host + item.video_upload_path" type="video/mp4" />
					</video>
				</div>
			</div>

			<div class="comment-footer">
				<BaseFeedbackCommentReview :comment="item" v-slot="{status, rateUp, rateDown, positiveReviews, negativeReviews}">
					<div class="comment-footer-top" v-if="mode != 'pd'">
						<BaseCmsLabel code="comment_review_useful" class="comment-review-useful" tag="div" />
						<div class="review-rates up" @click="rateUp" :class="{'empty': positiveReviews == 0}">{{positiveReviews}}</div>
						<div class="review-rates down" @click="rateDown" :class="{'empty': negativeReviews == 0}">{{negativeReviews}}</div>
						<div class="review-rate-status" v-if="status"><BaseCmsLabel :code="status" /></div>
						<div v-if="canReplay" class="comment-reply" @click="modal.open('comment', {parent_comment_id: item.id})"><BaseCmsLabel code="comment_reply" tag="span" default="Odgovori" /></div>
					</div>
				</BaseFeedbackCommentReview>
			</div>
		</div>
	</article>
</template>

<script setup>
	const props = defineProps(['item', 'mode']);
	const modal = useModal();
	const {getUser} = useAuth();
	const config = useAppConfig();
	const canReplay = computed(() => {
		const user = getUser();
		return (Number(props.item.parent_id) == 0 && (user?.staff || user?.superuser || user?.developer));
	})

	const videoActive = ref(true);
</script>

<style lang="less" scoped>
	.comment{
		font-size: 14px; border-top: 1px solid @borderColor; padding: 15px 0 19px;
		@media (max-width: @m){font-size: 12px; padding: 10px 0 12px;}
	}
	.cd-best-comment{
		margin-top: 10px; background: rgba(149, 159, 144, 0.06); border: 1px solid @borderColor; padding: 20px 24px 16px; margin-bottom: 32px;
		@media (max-width: @m){margin-top: 8px; margin-bottom: 12px; padding: 10px 10px 12px;}
	}
	.comment-header{padding-bottom: 5px; display: flex; align-items: center;}
	.comment-date{color: @gray2; width: 100%; flex-grow: 1; flex-shrink: 1; text-align: right;}
	.comment-rate, .add_rate{
		display: inline-flex;
		.icon-star, .icon-star-empty{
			position: relative; margin-right: 2px;
			&:before{.icon-star(); font: 12px/12px @fonti; color: #d5d9d3;}
		}
		.icon-star:before{color: @yellow;}
	}
	.comment-rate{margin-right: 12px; flex-grow: 0; flex-shrink: 0;}
	.comment-rate .icon-star-active:before, .add_rate .icon-star:before{color: @yellow;}
	.comments-counter{color: @green; font-weight: normal; font-size: 16px;}
	.comment-username{font-weight: 700; margin-right: 10px; flex-grow: 0; flex-shrink: 0;}
	.comment-verified-buyer{
		position: relative; display: flex; align-items: center; font-size: 12px; line-height: 16px; text-transform: uppercase; font-weight: 700; color: @green; padding-top: 2px; flex-grow: 0; flex-shrink: 0;
		&:before{.icon-available(); font: 20px/1 @fonti; color: @green; margin-right: 6px; margin-top: -2px;}
	}
	.comment-footer{position: relative; display: flex; align-items: center; justify-content: space-between; margin-top: 12px;}
	.comment-footer-top{
		position: relative; display: flex; align-items: center; gap: 10px; width: 100%;
		@media (max-width: @m){flex-wrap: wrap;}
	}
	.comment-review-useful{font-size: 12px;}
	.review-rates{
		position: relative; display: flex; align-items: flex-end; font-size: 14px; line-height: 1; padding-left: 22px;
		&.empty{color: @gray2;}
		&:before{.pseudo(16px,16px); .icon-thumbsup(); font: 16px/1 @fonti; color: @green; bottom: 0; left: 0;}
		&.down{
			&:before{color: @red; .rotate(180deg); bottom: auto; top: 0;}
		}
	}
	.comment-reply{
		flex-grow: 1; flex-shrink: 1; display: flex; justify-content: flex-end; text-align: right; text-decoration: underline; font-size: 12px; line-height: 16px; color: @darkGreen; cursor: pointer; transition: text-decoration-color 0.3s; padding-left: 22px;
		span{
			position: relative; display: flex; align-items: center; padding-left: 28px; text-align: right; flex-grow: 0; flex-shrink: 0;
			&:before{.pseudo(20px,17px); .icon-reply(); font: 17px/1 @fonti; color: @green; left: 0;}
		}
		@media (min-width: @h){
			&:hover{text-decoration-color: transparent;}
		}
		@media (max-width: @m){width: 100%;}
	}

	//child comment
	.comment-child{
		border: none; border-bottom: 1px solid #E9ECEB; padding: 16px 0;
		&:last-child{border-bottom: 0;}
	}

	// comment attachments
	.comment-attachments{position: relative; display: flex; flex-flow: column; gap: 10px; margin-top: 10px;}
	.comment-images{
		position: relative; display: flex; flex-wrap: wrap; gap: 10px;
		a{
			display: block; width: calc(~"20% - 8px"); border-radius: @borderRadius; border: 1px solid #fff; .transition(border-color);
			@media (min-width: @h){
				&:hover{border-color: @green;}
			}
		}
		:deep(img){display: block; width: auto; height: auto; max-width: 100%;  border-radius: @borderRadius;}
	}
	.comment-video{
		line-height: 0; position: relative;
		&.play{
			&:before{.pseudo(100%,100%); background: url('assets/images/icons/youtube-big.png') center no-repeat;}
		}
		video{width: 100%;}
		@media (max-width: @l){
			video{max-width: 100%;}
		}
	}
</style>
