<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}">
		<slot :errors="errors" :meta="meta" :values="values" :fields="fields" :gdprFields="gdprFields" :content="formContent" :status="status" :loading="loading" />
	</BaseForm>
</template>

<script setup>
	const props = defineProps({
		list: {
			type: String,
			default: 'list',
		},
		event: {
			type: String,
			default: '',
		},
		dataKey: {
			type: String,
			default: null,
		},
	});
	const newsletter = useNewsletter();
	const labels = useLabels();
	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();

	let recaptcha;
	if (__RECAPTCHA__) recaptcha = useRecaptcha();

	const loading = ref(false);
	const status = ref(null);

	const formContent = useState('newsletterFormContent', () => null);
	const formFields = useState('newsletterFormFields', () => []);
	const fields = computed(() => {
		if (formFields.value?.length) return formFields.value.filter(el => !el.name.startsWith('gdpr_'));
	});
	const gdprFields = computed(() => {
		if (formFields.value?.length) return formFields.value.filter(el => el.name.startsWith('gdpr_'));
	});

	onMounted(async () => {
		await fetchForm();
	});

	// fetch form fields
	async function fetchForm() {
		if (formContent.value && formFields.value) return; // return cached form fields

		return await newsletter.fetchForm(props.list, {dataKey: props.dataKey}).then(res => {
			if (!res?.success) return useLog(['Non existing list: ' + props.list], 'debug');
			formFields.value = res?.data?.form ? res.data.form : [];
			formContent.value = res?.data?.content ? res.data.content : null;
			return res.data;
		});
	}

	// on form submit
	async function onSubmit({values, actions}) {
		if (loading.value) return false; // prevent multiple submits

		let data = {...values};
		status.value = null;
		loading.value = true;

		// format gdpr consent fields to new array so they can be correctly processed by the API. https://hapi.marker.hr/#/Newsletter/post_v1_newsletter_subscribe_
		data.gdpr_template_api = [];
		if (gdprFields.value?.length) {
			Object.entries(data).forEach(([key, value]) => {
				const elName = key.split('-')[1];
				if (elName && value) {
					data.gdpr_template_api.push(elName);
				}
			});
		}

		// where newsletter signup is triggered from (regular signup, leaving popup, etc.)
		data.event = props.event;

		// If recaptcha is enabled, fetch the token and add it to the data
		if (recaptcha) {
			const recaptchaToken = await recaptcha.fetchRecaptchaToken({action: 'newsletter-signup'});
			if (recaptchaToken) data['g-recaptcha-response'] = recaptchaToken;
		}

		// subscribe user. Return API errors if any
		const res = await newsletter.subscribe(data);
		if (!res?.success) {
			let fieldError = '';
			if (res?.data?.errors?.length) fieldError = res.data.errors[0].error;
			if (res?.data?.label_name) fieldError = res.data.label_name;
			if (fieldError == 'error_atleast_one_gdpr_newsletter_checkbox_must_be_checked') fieldError = 'error_newsletter_gdpr'; // rename default error message because label identifier (admin) is character limited
			if (fieldError == 'error_newsletter_gdpr_accept_should_be_checked') fieldError = 'error_newsletter_gdpr_accept';
			actions.setFieldError('email', labels.get(fieldError, fieldError));
		}
		status.value = res;
		loading.value = false;

		// send event to Facebook CAPI
		if (res?.success && fbCapi) {
			fbCapi.sendEvent('subscribe', {
				subscription_type: 'newsletter',
			});
		}
	}
</script>
