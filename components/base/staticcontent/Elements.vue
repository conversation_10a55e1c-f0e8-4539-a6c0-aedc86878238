<template>
	<slot :items="items" :layout="item.layout" :position="item.position" :data="item" :menu="menu" />
</template>

<script setup>
	const {generateThumbs} = useImages();
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
		thumbPreset: String,
	});
	const emit = defineEmits(['load']);

	// get data provided by parent CatalogPosts component
	const {menu} = inject('baseStaticcontentPageData');

	// get item elements
	const items = computed(() => {
		emit('load', props.item?.elements);
		return props.item.elements ? props.item.elements : [];
	});

	await generateImages();
	watch(items, async () => await generateImages());

	// generate thumbs
	async function generateImages() {
		if (items.value?.length && props.thumbPreset) {
			const data = items?.value[0]?.elements ? items?.value[0]?.elements : items.value;
			await generateThumbs({
				data: data,
				preset: props.thumbPreset,
			});
		}
	}
</script>
