<template>
	<BaseMetaSeo :data="page" />
	<slot :item="page" />
</template>

<script setup>
	const route = useRoute();
	const landing = useStaticContent();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();

	const emit = defineEmits(['data', 'load']);
	const page = await landing.fetch().then(res => (res?.data?.length ? res.data[0] : null));

	// generate menu from page items
	const menu = computed(() => {
		if (!page.value) return [];

		let menuItems = [];
		if (page.value?.items) {
			page.value.items.forEach(item => {
				if (item.elements) {
					item.elements.forEach(element => {
						if (!element.menu_visible || element.menu_visible == '0') return;
						menuItems.push({
							title: element.menu_title ? element.menu_title : element.title,
							id: element.id,
							position: item.position,
							layout: element.layout_code,
						});
					});
				}
			});
		}

		// append menu items to page object
		page.menu = menuItems;
		emit('data', page.value);

		return menuItems;
	});

	// provide data to child components
	provide('baseStaticcontentPageData', {
		menu: menu.value,
	});

	onMounted(() => {
		if (gtm) gtm.gtmTrack('pageView', {url: route.fullPath, title: page.value?.title});
		if (fbCapi) fbCapi.sendEvent('pageView', {title: page.value?.title});
		emit('load', page.value);
	});
</script>
