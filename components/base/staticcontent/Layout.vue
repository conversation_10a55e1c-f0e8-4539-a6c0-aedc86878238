<template>
	<component :is="component" :item="item" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
	});
	const {templates} = inject('page');

	// replace special characters in layout name and convert to PascalCase
	const component = computed(() => {
		const layout = props.item.layout
			.replace(/[^a-zA-Z0-9]/g, ' ')
			.replace(/(?:^\w|[A-Z]|\b\w)/g, word => word.toUpperCase())
			.replace(/\s+/g, '');

		if (!templates) return null;
		const template = templates.get('StaticcontentLayouts' + layout);
		return template ? defineAsyncComponent(template) : null;
	});
</script>
