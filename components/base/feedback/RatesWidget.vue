<template>
	<slot :rate="Number(data?.rates || rate).toFixed(1)" :stars="stars" :counter="data?.rates_votes || 0" v-if="data?.rates_status > 1 || rate" />
</template>

<script setup>
	const props = defineProps(['data', 'rate']);

	let stars = '';
	for (let i = 1; i <= 5; i++) {
		const active = i <= Math.round(props.data?.rates || props.rate) ? ' icon-star active' : '';
		stars += '<span class="icon-star-empty' + active + '"></span>';
	}
</script>
