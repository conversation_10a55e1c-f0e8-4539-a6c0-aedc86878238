<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}" v-bind="$attrs">
		<slot :errors="errors" :meta="meta" :values="values" :onReset="onReset" :fields="fields" :loading="loading" :status="status" :onSubmit="onSubmit" />
	</BaseForm>
</template>

<script setup>
	const feedback = useFeedback();
	const labels = useLabels();
	const arrayUtils = useArrayUtils();
	const props = defineProps({
		parentCommentId: [Number, String],
		fieldsConfig: Object,
	});

	let recaptcha;
	if (__RECAPTCHA__) recaptcha = useRecaptcha();

	// fetch form fields
	const data = shallowRef([]);
	onMounted(async () => {
		data.value = await feedback.fetchCommentsForm().then(res => (res?.data ? res.data : []));
	});

	const fields = computed(() => {
		if (!data.value?.length) return [];

		const fields = data.value;

		// If parentCommentId is set, set it as value for parent_id field
		if (props.parentCommentId) {
			fields.map(field => {
				if (field.name == 'parent_id') return {...field, value: props.parentCommentId};
				return field;
			});
		}

		// Use fieldConfig.order if set to override the default fields order
		if (props.fieldsConfig?.order) {
			arrayUtils.sortByField(fields, props.fieldsConfig.order);
		}

		return fields;
	});

	const loading = ref(false);
	const status = ref(null);

	// on form submit
	async function onSubmit({values, actions}) {
		loading.value = true;

		// If recaptcha is enabled, fetch the token and add it to the data
		if (recaptcha) {
			const recaptchaToken = await recaptcha.fetchRecaptchaToken({action: 'submit-comment'});
			if (recaptchaToken) values['g-recaptcha-response'] = recaptchaToken;
		}

		const res = await feedback.submitComment(values);
		status.value = res;

		// reset form values if success
		if (res.success) actions.resetForm();

		// set field errors if response contains api errors
		if (res?.data?.errors?.length) {
			res.data.errors.forEach(error => {
				actions.setFieldError(error.field, labels.get(error.error, error.error));
			});
		}

		loading.value = false;
		return res;
	}

	// reset form to initial values
	function onReset() {
		status.value = {};
	}
</script>
