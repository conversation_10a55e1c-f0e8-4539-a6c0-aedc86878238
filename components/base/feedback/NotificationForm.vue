<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}" v-bind="$attrs">
		<slot :loading="loading" :errors="errors" :meta="meta" :values="values" :fields="fields" :status="status" />
	</BaseForm>
</template>

<script setup>
	const feedback = useFeedback();
	const labels = useLabels();

	// fetch form fields
	const fields = ref([]);

	onMounted(async () => {
		fields.value = await feedback.fetchNotifymeForm().then(res => (res?.data ? res.data : []));
	});

	const loading = ref(false);
	const status = ref(null);

	// on form submit
	async function onSubmit({values, actions}) {
		loading.value = true;
		const res = await feedback.submitNotifyme(values);

		// set field errors if response contains api errors
		if (!res.success) {
			loading.value = false;
			if (res.data?.label_name) {
				actions.setFieldError('email', labels.get(res.data.label_name, res.data.label_name));
				return res;
			}
		}

		if (res.success) {
			actions.resetForm();
		}

		status.value = res;
		loading.value = false;
	}
</script>
