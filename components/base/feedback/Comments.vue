<template>
	<slot :items="items" :loadMore="loadMore" :currentPage="currentPage" :totalPages="totalPages" />
</template>

<script setup>
	const {sortByField} = useArrayUtils();
	const props = defineProps({
		items: [Array, Object],
		perPage: [String, Number],
		sort: String,
		hierarchy: {
			type: Boolean,
			default: false,
		},
	});
	const items = ref([]);
	const totalPages = ref(1);
	const currentPage = ref(1);

	// Nest and sort comments if defined in props
	const allComments = Object.values(props.items) || [];
	let filteredComments = [];
	if (allComments.length) {
		if (props.hierarchy) {
			// Filter only top level comments
			filteredComments = allComments.filter(item => Number(item.parent_id) == 0);

			// Add nested comments
			filteredComments = filteredComments.map(item => {
				item.items = allComments.filter(i => Number(i.parent_id) == item.id);
				if (props.sort) item.items = sortByField(item.items, 'datetime_created', {sortType: props.sort});
				return item;
			});
		} else {
			filteredComments = allComments;
		}
	}
	if (props.sort) filteredComments = sortByField(filteredComments, 'datetime_created', {sortType: props.sort});
	if (props.perPage) totalPages.value = Math.ceil(filteredComments?.length / props.perPage);
	items.value = props.perPage && filteredComments?.length ? filteredComments.slice(0, props.perPage) : filteredComments;

	function sort(data, mode) {
		if (mode == 'new') {
			return data.sort((a, b) => new Date(parseInt(b.datetime_created) * 1000) - new Date(parseInt(a.datetime_created) * 1000));
		}

		if (mode == 'old') {
			return data.sort((a, b) => new Date(parseInt(a.datetime_created) * 1000) - new Date(parseInt(b.datetime_created) * 1000));
		}
	}

	function loadMore() {
		currentPage.value++;
		if (currentPage.value > totalPages.value) {
			currentPage.value = totalPages.value;
			return;
		}

		const appendComments = filteredComments.slice((currentPage.value - 1) * props.perPage, currentPage.value * props.perPage);
		if (appendComments?.length) items.value.push(...appendComments);
	}
</script>
