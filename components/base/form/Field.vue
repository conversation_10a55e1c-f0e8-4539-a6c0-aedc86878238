<template>
	<slot :floatingLabel="floatingLabel" :value="value" :errorMessage="errorMessage" :isTouched="isTouched" :required="required" />
</template>

<script setup>
	import {useFieldError, useFieldValue} from 'vee-validate';
	const model = defineModel();
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
	});

	if (!props.item) {
		throw new Error('Missing item prop in BaseFormField component');
	}

	// provide data to child components
	const item = computed(() => props.item);
	const errorMessage = useFieldError(item.value.name);
	const isTouched = ref(false);
	const value = useFieldValue(item.value.name);
	const floatingLabel = ref(false);
	const required = props.item.validation?.length && props.item.validation.some(rule => rule.type === 'not_empty');
	model.value = {
		item,
		value,
		errorMessage,
	};
	provide('baseFormFieldData', {floatingLabel, isTouched, item});
</script>
