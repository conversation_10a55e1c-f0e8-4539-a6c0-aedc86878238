<template>
	<ClientOnly>
		<div class="form-field-rate-items" :class="props.class">
			<span class="form-rate-item" :class="[{'active': rate >= 1 && !rateHover}, {'active-hover': rateHover >= 1}]" @mouseover="rateHover = 1" @mouseout="rateHover = 0">
				<input type="radio" :name="name" :id="props.id ? props.id + '-rate1' : 'rate1'" value="1" @click="setRate(1)" />
				<label :for="props.id ? props.id + '-rate1' : 'rate1'">1</label>
			</span>
			<span class="form-rate-item" :class="[{'active': rate >= 2 && !rateHover}, {'active-hover': rateHover >= 2}]" @mouseover="rateHover = 2" @mouseout="rateHover = 0">
				<input type="radio" :name="name" :id="props.id ? props.id + '-rate2' : 'rate2'" value="2" @click="setRate(2)" />
				<label :for="props.id ? props.id + '-rate2' : 'rate2'">2</label>
			</span>
			<span class="form-rate-item" :class="[{'active': rate >= 3 && !rateHover}, {'active-hover': rateHover >= 3}]" @mouseover="rateHover = 3" @mouseout="rateHover = 0">
				<input type="radio" :name="name" :id="props.id ? props.id + '-rate3' : 'rate3'" value="3" @click="setRate(3)" />
				<label :for="props.id ? props.id + '-rate3' : 'rate3'">3</label>
			</span>
			<span class="form-rate-item" :class="[{'active': rate >= 4 && !rateHover}, {'active-hover': rateHover >= 4}]" @mouseover="rateHover = 4" @mouseout="rateHover = 0">
				<input type="radio" :name="name" :id="props.id ? props.id + '-rate4' : 'rate4'" value="4" @click="setRate(4)" />
				<label :for="props.id ? props.id + '-rate4' : 'rate4'">4</label>
			</span>
			<span class="form-rate-item" :class="[{'active': rate >= 5 && !rateHover}, {'active-hover': rateHover >= 5}]" @mouseover="rateHover = 5" @mouseout="rateHover = 0">
				<input type="radio" :name="name" :id="props.id ? props.id + '-rate5' : 'rate5'" value="5" @click="setRate(5)" />
				<label :for="props.id ? props.id + '-rate5' : 'rate5'">5</label>
			</span>
		</div>
	</ClientOnly>
</template>

<script setup>
	const props = defineProps(['name', 'id', 'class']);
	const emit = defineEmits(['update']);
	const rate = ref(0);
	const rateHover = ref(0);

	// rating field
	function setRate(value) {
		rate.value = value;
		emit('update', value);
	}
</script>
