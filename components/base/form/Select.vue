<template>
	<div :class="['base-select-menu', {'has-label': floatingLabel}, {'has-selected-item': selectedItem}, props.theme ? 'theme-' + props.theme : '', {'active': isDropdownOpen}]" ref="menu">
		<div class="base-select-menu-header" @click="isDropdownOpen = !isDropdownOpen">
			<template v-if="floatingLabel">
				<label class="base-select-menu-label" v-if="props.floatingLabel">{{ label }}</label>
				<div class="base-select-menu-selected" v-html="selectedItem"></div>
			</template>
			<template v-else>
				<div class="base-select-menu-selected" v-html="selectedItem ? selectedItem : label"></div>
			</template>
		</div>
		<div class="base-select-menu-body">
			<div class="base-select-search" v-if="props.search">
				<input type="text" :placeholder="props.searchPlaceholder" v-model="searchTerm" />
			</div>
			<div class="base-select-menu-items">
				<slot />
			</div>
		</div>
	</div>
</template>

<script setup>
	const {onClickOutside} = useDom();
	const slots = useSlots();
	const model = defineModel();
	const props = defineProps({
		label: String,
		floatingLabel: {
			type: Boolean,
			default: false,
		},
		theme: String,
		html: {
			type: Boolean,
			default: false,
		},
		search: {
			type: Boolean,
			default: false,
		},
		searchPlaceholder: String,
	});

	const isDropdownOpen = ref(false);
	const selectedItem = ref(null);
	const selectedValue = ref('');
	const searchTerm = ref('');

	watchEffect(() => {
		model.value = selectedValue.value;
	});

	const menu = ref(null);
	onClickOutside(menu, event => {
		isDropdownOpen.value = false;
	});

	provide('baseSelectMenuData', {selectedItem, selectedValue, isDropdownOpen, searchTerm, html: props.html});
</script>

<style scoped lang="less">
	.base-select-menu {
		position: relative;
	}
	.base-select-menu-body {
		display: none;
		position: absolute;
		top: 100%;
		left: 0;
		background: #fff;
		z-index: 1000;
	}
	.base-select-menu.active .base-select-menu-body {
		display: block;
	}
	.base-select-menu-items {
		max-height: 200px;
		overflow: auto;
	}
	.theme-base {
		.base-select-menu-header {
			border: 1px solid #ccc;
			border-radius: 4px;
			height: 55px;
			display: flex;
			align-items: center;
			padding: 0 20px;
			cursor: pointer;
			&:after {
				content: '▾';
				display: block;
				position: absolute;
				top: 0;
				right: 20px;
				width: 10px;
				height: 100%;
				line-height: 1;
				display: flex;
				align-items: center;
			}
		}
		.base-select-menu-body {
			border: 1px solid #ccc;
			width: 100%;
			position: absolute;
			top: calc(~'100% - 1px');
			border-radius: 0 0 4px 4px;
			padding: 10px;
		}
		.base-select-search {
			margin-bottom: 10px;
		}
		&.active {
			.base-select-menu-header {
				border-bottom-left-radius: 0;
				border-bottom-right-radius: 0;
			}
		}
		:deep(.base-select-menu-item) {
			padding: 3px 7px;
			cursor: pointer;
			&:hover {
				background: #ccc;
			}
		}
		.base-select-menu-label {
			position: absolute;
		}
		&.has-label.has-selected-item {
			.base-select-menu-label {
				font-size: 13px;
				top: 5px;
			}
			.base-select-menu-selected {
				position: absolute;
				top: 24px;
			}
		}
	}
</style>
