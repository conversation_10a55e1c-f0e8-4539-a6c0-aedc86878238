<template>
	<div class="base-select-menu-item" :data-value="props.value" :class="{'active': active}" v-show="matchSearch" @click="onSelect(), $emit('select', {value: props.value})" ref="item">
		<slot />
	</div>
</template>

<script setup>
	const emit = defineEmits(['select']);
	const props = defineProps({
		value: {
			type: [String, Number],
			required: true,
		},
		selected: {
			type: Boolean,
			default: false,
		},
	});

	const {selectedItem, selectedValue, isDropdownOpen, searchTerm, html} = inject('baseSelectMenuData');
	const item = ref(null);

	// Check if item is selected based on value prop
	const active = computed(() => {
		return props.value == selectedValue.value;
	});

	// Check if item title matches search term
	const matchSearch = computed(() => {
		if (!searchTerm?.value) return true;
		return item.value.textContent.toLowerCase().includes(searchTerm.value.toLowerCase()) ? true : false;
	});

	// Set selected item on component mount
	let initTimeout;
	onMounted(() => {
		if (initTimeout) clearTimeout(initTimeout);
		initTimeout = setTimeout(() => {
			if (props.selected) onSelect();
		}, 100);
	});

	function onSelect() {
		selectedValue.value = props.value;
		selectedItem.value = html ? item.value.innerHTML : item.value.textContent.trim();
		isDropdownOpen.value = false;
		searchTerm.value = '';
	}
</script>
