<template>
	<slot :items="searchResults" :searchTerm="searchTerm" :searchContent="searchContent" :loading="loading" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const config = useAppConfig();
	const catalog = useCatalog();
	const publish = usePublish();
	const pages = usePage();
	const endpoints = useEndpoints();
	const labels = useLabels();
	const {getAppUrl} = useApiRoutes();
	const {searchResults, setSearchProductsCount} = useSearch();

	const props = defineProps({
		searchModules: Array, // [{module: 'catalog', title: 'Module title'}]
		log: {
			type: Boolean,
			default: false,
		},
	});

	const searchTerm = ref(route.query.search_q);
	const searchContent = computed(() => {
		return route.query.search_content ? route.query.search_content : 'search';
	});
	const loading = ref(false);

	// inject data provided by parent Category component. If no data is provided, set category to null
	const {category} = inject('baseCatalogCategoryData', {category: null});

	// Set catalog fetch options (used only if default tab is not products)
	let catalogFetchOptions = {};
	catalogFetchOptions.mode = 'index';
	if (config.catalog?.productsResponseFields?.length) catalogFetchOptions.response_fields = config.catalog.productsResponseFields;
	catalogFetchOptions.search_q = searchTerm.value?.trim();
	catalogFetchOptions._search_id = true;
	if (category?.value?.position_h) catalogFetchOptions.category_position = category.value.position_h;

	// run all search modules
	async function search() {
		if (!props.searchModules || !route.query.search_q) return;

		loading.value = true;
		const results = {};

		// Fetch results for all modules
		const searchModules = props.searchModules?.length ? props.searchModules.map(item => item.module.replace('|', '.')) : ['catalog'];
		const searchData = await useApi(`${endpoints.get('_post_hapi_search_autocomplete')}`, {
			method: 'POST',
			body: {
				'search_q': searchTerm.value,
				'controller': 'catalog',
				'advanced': true,
				'content': 'all',
				'result_per_page': 1,
				'allow_models': searchModules,
			},
		});

		if (!searchData?.data) {
			console.error('BaseSearchResults - No data returned from API. Check if search modules are correctly configured.', searchData);
		}

		// Remap each module to return only needed data
		for (const item of props.searchModules) {
			const searchModule = item.module.split('|');

			let moduleTitle = item.title;
			if (!moduleTitle && searchModule[1]) moduleTitle = labels.get('search_' + searchModule[0] + '_' + searchModule[1]);
			if (!moduleTitle) moduleTitle = labels.get('search_' + searchModule[0]);

			if (searchModule[0] == 'cms') {
				results['cms'] = {
					id: 'cms',
					title: moduleTitle,
					url: `${getAppUrl('search')}?search_q=${route.query.search_q}&search_content=${searchModule[0]}`,
					total: searchData.data?.cms_show_all?.total || 0,
				};
			}

			if (searchModule[0] == 'publish') {
				const publishKey = searchModule[1] ? 'publish.' + searchModule[1] : 'publish';
				results[publishKey] = {
					id: publishKey,
					title: moduleTitle,
					url: searchData.data?.[publishKey + '_show_all']?.url_without_domain || '',
					total: searchData.data?.[publishKey + '_show_all']?.total || 0,
				};
			}

			if (searchModule[0] == 'catalog') {
				results['catalog'] = {
					id: 'catalog',
					title: moduleTitle,
					url: searchData.data?.catalogproduct_show_all?.url_without_domain || '',
					total: searchData.data?.catalogproduct_show_all?.total || 0,
				};
				setSearchProductsCount(searchData.data?.catalogproduct_show_all?.total || 0);
			}
		}

		searchResults.value = results;
		loading.value = false;

		if (props.log) {
			console.log('BaseSearchResults', {
				results: searchResults.value,
				term: searchTerm.value,
			});
		}
	}

	onMounted(async () => {
		await search();
	});

	// watch for search term change
	watch(
		() => route.query.search_q,
		async (newData, oldData) => {
			await new Promise(resolve => setTimeout(resolve, 500));
			searchTerm.value = newData;
			if (route.meta.contentType === 'search' && !loading.value) {
				await search();
			}
		}
	);

	// provide data to child components
	provide('baseSearchResultsData', {
		searchTerm,
		loading,
	});
</script>
