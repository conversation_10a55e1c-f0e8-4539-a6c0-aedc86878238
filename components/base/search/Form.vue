<template>
	<slot
		:searchResults="searchResults"
		:totalSearchResults="totalSearchResults"
		:loading="loading"
		:updateValue="updateValue"
		:handleInput="handleInput"
		:resetAutocomplete="resetAutocomplete"
		:resetInput="resetInput"
		:onReset="onReset"
		:searchTerm="searchTerm"
		:onBlur="onBlur"
		:selectedIndex="selectedIndex"
		:onSubmit="onSubmit" />
</template>

<script setup>
	const searchResultsModel = defineModel();
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const config = useAppConfig();
	const {generateThumbs} = useImages();
	const {getAppUrl} = useApiRoutes();

	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();

	const {emit: eventBusEmit} = useEventBus();
	const emit = defineEmits(['beforeSearch', 'afterSearch', 'afterSubmit', 'afterReset', 'afterEscape']);

	const props = defineProps({
		fetch: Object,
		submit: {
			type: Boolean,
			default: true,
		},
		log: {
			type: Boolean,
			default: false,
		},
		resetOnEmptyInput: {
			type: Boolean,
			default: true,
		},
	});

	const searchTerm = ref('');
	const loading = ref(0);
	const searchModules = ref(props.fetch?.allow_models ? props.fetch.allow_models : ['catalogproduct']);
	const searchResults = ref(null);
	const totalSearchResults = ref(0);
	const selectedIndex = ref(0);
	const selectedItem = ref(null);

	let typingTimer;
	async function handleInput(event) {
		// clear timer on each keypress
		clearTimeout(typingTimer);

		if (!/^[a-zA-Z0-9 ]*$/.test(event.key) && !['Backspace', 'ArrowDown', 'ArrowUp', 'Enter', 'Escape'].includes(event.key)) {
			return false;
		}

		// if arrow down or arrow up key is pressed go to next or previous item and set input value to selected item title
		if (event.key == 'ArrowDown' || event.key == 'ArrowUp') {
			if (event.key == 'ArrowDown') {
				selectedIndex.value++;
				if (selectedIndex.value > totalSearchResults.value) {
					selectedIndex.value = 1;
				}
			}

			if (event.key == 'ArrowUp') {
				selectedIndex.value--;
				if (selectedIndex.value < 1) {
					selectedIndex.value = totalSearchResults.value;
				}
			}

			selectedItem.value = findItemByIndex(selectedIndex.value);
			searchTerm.value = selectedItem.value ? selectedItem.value.title : searchTerm.value;
			return false;
		}

		// if enter key is pressed and item is selected, navigate to selected item. Else navigate to search results page
		if (event.key == 'Enter' && props.submit) {
			if (selectedItem.value) {
				await navigateTo(selectedItem.value.url_without_domain);
				emit('afterSubmit', {searchTerm: searchTerm.value});
				return resetAutocomplete();
			}

			if (searchTerm.value && searchTerm.value.length > 1) {
				return onSubmit();
			}
		}

		// reset autocoplete results if search term is empty or escape key is pressed
		if ((props.resetOnEmptyInput && !searchTerm.value) || event.key == 'Escape') {
			resetAutocomplete();
			emit('afterEscape', {searchTerm: searchTerm.value});
			return;
		}

		// run search function only if typing has stopped for 300ms
		typingTimer = setTimeout(search, 300);
	}

	// fetch data
	async function search() {
		// search only if search term is at least 2 characters long and
		if (!searchTerm.value || searchTerm.value.length < 2) return false;

		emit('beforeSearch', {searchTerm: searchTerm.value});

		// reset total search results and selected index and start loading
		totalSearchResults.value = 0;
		selectedIndex.value = 0;
		let index = 1;
		loading.value = 1;

		// run all search modules
		const searchData = await useApi(nuxtApp.$appData.endpoints['_post_hapi_search_autocomplete'], {
			method: 'POST',
			body: {
				'search_q': searchTerm.value,
				'controller': 'catalog',
				'advanced': true,
				'content': 'all',
				'allow_models': ['catalogproduct'],
				'result_fields': {
					'catalogproduct': ['image', 'price', 'price_custom', 'manufacturer_title', 'code', 'category', 'basic_price', 'basic_price_custom', 'discount_percent', 'discount_percent_custom'],
				},
				'result_per_page': {
					'_default': 5,
				},
				'result_image': '70x70_r',
				...props.fetch,
			},
		});

		totalSearchResults.value = 0;

		// add index to each module item
		searchModules.value.forEach(model => {
			if (searchData.data?.[model]?.length) {
				totalSearchResults.value += searchData.data[model].length;
				searchData.data[model].forEach(item => {
					item.index = index;
					index++;
				});
			}
		});

		searchResults.value = searchData?.data ? searchData.data : null;
		searchResultsModel.value = searchResults.value;
		if (props.log) useLog(searchResults.value);
		loading.value = 0;
		emit('afterSearch', {searchTerm: searchTerm.value});
	}

	// navigate to search results page
	let submitTimeout;
	let fbTrackingTimeout;
	async function onSubmit(options) {
		if (submitTimeout) clearTimeout(submitTimeout);
		submitTimeout = setTimeout(async () => {
			let redirectUrl = route.path == getAppUrl('catalog') && !route.query?.search_q ? getAppUrl('search') : getAppUrl('catalog'); // use /search/ if url is /products/
			let searchQuery = {
				search_q: searchTerm.value.trim(),
			};

			await navigateTo({
				path: redirectUrl,
				query: searchQuery,
			});

			eventBusEmit('catalogProductsUpdate', route.query);
			emit('afterSubmit', {searchTerm: searchTerm.value});
		}, 50);

		if (fbTrackingTimeout) clearTimeout(fbTrackingTimeout);
		if (fbCapi && config.facebook?.conversionApi?.events?.includes('search')) {
			fbTrackingTimeout = setTimeout(() => {
				const fbPayload = {search_string: searchTerm.value};
				fbPayload.content_ids = fbCapi.getData();
				fbCapi.sendEvent('search', fbPayload);
				fbCapi.setData(null);
			}, 2000);
		}

		await new Promise(resolve => setTimeout(resolve, 500));
		resetAutocomplete();
	}

	// update search term value
	function updateValue(event) {
		searchTerm.value = event.target.value;
	}

	// reset search results
	function onReset() {
		resetInput();
		resetAutocomplete();
	}

	// reset search results on blur. Timeout is needed to prevent search results from disappearing when clicking on them
	function onBlur() {
		setTimeout(() => {
			resetAutocomplete();
		}, 500);
	}

	function resetInput() {
		searchTerm.value = '';
	}

	function resetAutocomplete() {
		searchResults.value = null;
		totalSearchResults.value = 0;
		selectedIndex.value = 0;
		selectedItem.value = null;
		searchResultsModel.value = null;
		emit('afterReset');
	}

	// find item by index
	function findItemByIndex(index) {
		if (!searchResults.value || !searchModules.value?.length) return null;
		for (const key of searchModules.value) {
			if (!searchResults.value?.[key]) continue;
			const item = searchResults.value[key].find(i => i.index === index);
			if (item) {
				return item;
			}
		}

		return null;
	}

	// expose methods to parent component
	defineExpose({resetAutocomplete, onReset});

	onBeforeUnmount(() => {
		clearTimeout(submitTimeout);
		clearTimeout(typingTimer);
		clearTimeout(fbTrackingTimeout);
	});
</script>
