<template>
	<Body
		:class="{
			'gdpr-active': !gdprCookie || activeConfigurator,
			'gdpr-active-configurator': activeConfigurator,
		}" />
	<slot :cookie="gdprCookie" :checkboxValues="checkboxValues" :activeConfigurator="activeConfigurator" :cookieWarning="cookieWarning" :configurator="configurator" :message="message" :onToggleConfigurator="toggleConfigurator" :onSubmit="submit" :handleInput="handleInput" :loading="loading" />
</template>

<script setup>
	const gdpr = useGdpr();
	const consents = useState('gdprConsents', () => null);
	const cookieWarning = useState('gdprCookieWarning', () => null);
	const configurator = useState('gdprConfigurator', () => null);
	const checkboxValues = reactive({});
	const loading = ref(true);
	const props = defineProps({
		enableConsents: {
			type: Boolean,
			default: false,
		},
	});

	const gdprCookie = useCookie('gdpr_cookie', {
		maxAge: 60 * 60 * 24 * 365, // 1 year
		secure: process.env.NODE_ENV == 'development' ? false : true,
		encode(value) {
			return value;
		},
		decode(value) {
			return value;
		},
	});

	onMounted(async () => {
		await fetchGdprData();

		// set checkbox values
		const objects = configurator.value?.objects || [];
		if (objects) {
			objects.forEach(item => {
				// if enableConsents prop is enabled, all consents will be initially checked
				if (props.enableConsents && !gdprCookie.value) {
					checkboxValues[item.code] = true;
				} else {
					const isApproved = (consents.value?.approved || []).includes(item.code);
					checkboxValues[item.code] = isApproved || item.type == 'r' ? true : false;
				}
			});
		}

		// set global click event listener
		document.addEventListener('click', handleGlobalClick);
		loading.value = false;
	});

	function handleGlobalClick(event) {
		// check if the clicked element or its parent has a specific class
		const acceptAllBtn = event.target.closest('.gdpr_submit_all_button');
		const acceptBasicBtn = event.target.closest('.gdpr_submit_request_button');
		const configBtn = event.target.closest('.gdpr_configurator_button');

		if (acceptAllBtn) {
			acceptAllHandler();
		} else if (acceptBasicBtn) {
			acceptBasicHandler();
		} else if (configBtn) {
			toggleConfigurator();
		}
	}

	async function fetchGdprData() {
		if (consents.value && cookieWarning.value && configurator.value) return; // return state cached gdpr data

		return await Promise.all([gdpr.fetchConsents(), gdpr.fetchTemplate({template: 'cookie'}), gdpr.fetchTemplate({template: 'popup'})])
			.then(([consentsData, cookieWarningData, configuratorData]) => {
				consents.value = consentsData?.data ? consentsData.data : null;
				cookieWarning.value = cookieWarningData?.data ? cookieWarningData.data : null;
				configurator.value = configuratorData?.data ? configuratorData.data : null;
			})
			.catch(error => {
				useLog(error, 'error');
			});
	}

	// handle checkbox input
	function handleInput(event) {
		if (!event?.target?.name) return;
		checkboxValues[event.target.name] = event.target.checked;
	}

	function setGdprCookie() {
		let cookieValue = [];
		Object.entries(checkboxValues).forEach(([key, value]) => {
			if (value && key != 'lang') {
				cookieValue.push(key);
			}
		});
		gdprCookie.value = cookieValue.join('|');
	}

	// submit gdpr consents
	const message = ref(null);
	async function submit(mode) {
		loading.value = true;
		message.value = null;

		// set all values to true if mode is set to 'all'
		if (mode == 'all') {
			Object.entries(checkboxValues).forEach(([key, value]) => {
				checkboxValues[key] = true;
			});
		}

		// set only neccessary values to true if mode is set to 'necessary'
		if (mode == 'necessary') {
			if (!configurator.value?.objects) {
				return useLog('No objects found in gdpr configurator', 'debug');
			}
			configurator.value?.objects.forEach(item => {
				checkboxValues[item.code] = item.type == 'r' ? true : false;
			});
		}

		await gdpr.submit({...checkboxValues}).then(res => {
			message.value = res.data;
		});

		setGdprCookie();
		loading.value = false;
		activeConfigurator.value = false;
	}

	// toggle configurator visibility
	const activeConfigurator = ref(false);
	function toggleConfigurator() {
		activeConfigurator.value = !activeConfigurator.value;
	}
	async function acceptAllHandler() {
		return await submit('all');
	}
	async function acceptBasicHandler() {
		return await submit('necessary');
	}

	onBeforeUnmount(() => {
		document.removeEventListener('click', handleGlobalClick);
	});
</script>
