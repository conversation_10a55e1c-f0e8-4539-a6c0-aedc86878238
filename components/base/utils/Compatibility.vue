<template>
	<div class="compatibilityalert" v-if="alert">
		<template v-if="alert == 'notdefined'">
			Potrebno <a href="https://frontend.marker.hr/vue-docs/#basecompatibility" target="_blank">definirati <PERSON> <i>base</i> verziju u app.baseCompatibility!</a>
		</template>
		<template v-if="alert == 'lower'">
			<p>Trenutna <i>base</i> verzija (v{{ config.version }}) je veća od podržane <i>base</i> verzije na projektu (v{{ config.baseCompatibility }}).</p>
			<p>
				Provjeriti da li sve ispravno radi s trenutnom verzijom i <a href="https://frontend.marker.hr/vue-docs/#basecompatibility" target="_blank">ažurirati <i>app.baseCompatibility</i></a> ili smanjiti <i>base</i> verziju.
			</p>
		</template>
	</div>
</template>

<script setup>
	const config = useAppConfig();

	function compareVersions(v1, v2) {
		const v1Parts = v1.split('.').map(Number);
		const v2Parts = v2.split('.').map(Number);

		for (let i = 0; i < 3; i++) {
			if (v1Parts[i] > v2Parts[i]) return 1;
			if (v1Parts[i] < v2Parts[i]) return -1;
		}

		return 0; // Versions are equal
	}

	// Usage examples
	const baseVersion = config.version;
	const baseCompatibility = config.baseCompatibility;
	const alert = computed(() => {
		if (!baseCompatibility) return 'notdefined';
		const compare = compareVersions(baseVersion, baseCompatibility);
		if (compare === 1) return 'lower';
		return false;
	});
</script>

<style scoped>
	.compatibilityalert {
		background: #e83b3b;
		text-align: center;
		padding: 20px;
		font: 15px/1.5 Arial, sans-serif;
		color: #fff;
		font-weight: bold;
		border-bottom: 1px solid #cc3232;
		position: fixed;
		top: 0;
		right: 0;
		left: 0;
		z-index: 999999999999999;
	}
	a {
		color: #fff;
		text-decoration: underline;
	}
	a:hover {
		text-decoration: none;
		color: #fff;
	}
	p {
		padding: 0;
		margin: 0;
	}
</style>
