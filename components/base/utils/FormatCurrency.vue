<template>
	<span v-if="wrap" class="p-w" v-html="formatCurrency(price, {showCurrency: showCurrency, wrap: true})" />
	<template v-else>
		{{ formatCurrency(price, {showCurrency: showCurrency}) }}
	</template>
</template>

<script setup>
	const {formatCurrency} = useCurrency();
	const props = defineProps({
		price: {
			type: [String, Number],
			required: true,
		},
		showCurrency: {
			type: Boolean,
			default: true,
		},
		wrap: {
			type: Boolean,
			default: false,
		},
	});
</script>
