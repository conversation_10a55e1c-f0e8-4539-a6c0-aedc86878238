<template>
	<slot :items="lists" :loading="loading" />
</template>

<script setup>
	const config = useAppConfig();
	const catalog = useCatalog();
	const emit = defineEmits(['load']);
	const {generateThumbs} = useImages();
	const props = defineProps({
		fetch: {
			type: Object,
			required: true,
		},
		thumbPreset: String,
		dataKey: {
			type: String,
			default: null,
		},
		log: {
			type: Boolean,
			default: false,
		},
		watchFetch: {
			type: Boolean,
			default: false,
		},
	});

	const lists = ref([]);
	const loading = ref(true);

	async function fetchData() {
		loading.value = true;
		const fetchOptions = {
			...props.fetch,
		};
		if (config?.catalog?.listsResponseFields?.length) fetchOptions.response_fields = config.catalog.listsResponseFields;
		const res = await catalog.fetchLists(fetchOptions, {dataKey: props.dataKey});
		lists.value = res?.data?.length ? res.data : [];
		loading.value = false;
		await generateImages();
	}

	await fetchData();

	onMounted(async () => {
		// emit event with list data
		emit('load', {items: lists.value});

		// log lists data to console
		if (props.log) useLog(['BaseCatalogProductsWidget', lists.value, props.fetch]);
	});

	// generate thumbs
	async function generateImages() {
		if (lists.value?.length && props.thumbPreset) {
			await generateThumbs({
				data: lists.value,
				preset: props.thumbPreset,
				dataKey: props.dataKey ? props.dataKey + '-thumbs' : null,
			});
		}
	}

	// watch if fetch prop has changed and refetch data
	if (props.fetch && props.watchFetch) {
		watch(
			() => props.fetch,
			async () => await fetchData()
		);
	}
</script>
