<template>
	<slot :counter="counter" :wishlistUrl="getAppUrl('wishlist')" />
</template>

<script setup>
	const catalog = useCatalog();
	const {getAppUrl} = useApiRoutes();
	const isFetched = useState('wishlistIsFetched', () => false);

	onMounted(async () => {
		if (!isFetched.value) {
			await catalog.fetchWishlistProducts();
			isFetched.value = true;
		}
	});

	// get compared products
	const counter = computed(() => {
		const res = catalog.getWishlistProducts();
		return res?.items?.length;
	});
</script>
