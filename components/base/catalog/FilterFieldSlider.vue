<template>
	<div class="cf-range" v-if="item?.options_config">
		<div class="cf-range-input">
			<input type="text" :name="item.filter_url" v-model="inputValue[0]" @input="onChange" />
			<span class="cf-range-separator">-</span>
			<input type="text" :name="item.filter_url" v-model="inputValue[1]" @input="onChange" />
		</div>
		<Slider class="cf-range-slider" v-model="value" :options="{margin: 20}" :step="Number(item.options_config?.step)" :min="minValue" :max="maxValue" :tooltips="false" @slide="onSlide" />
		<button class="cf-range-button" @click="submit" v-if="!props.autoSubmit">{{ labels.get('confirm_range_filters') }}</button>
	</div>
</template>

<script setup>
	import Slider from '@vueform/slider';
	import '@vueform/slider/themes/default.css';
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {emit} = useEventBus();
	const {formatNumber} = useText();
	const props = defineProps({
		item: Object,
		autoSubmit: {
			type: Boolean,
			default: true,
		},
	});
	const labels = useLabels();

	// min & max values
	const minValue = Math.round(props.item.options_config?.min_value);
	const maxValue = Math.round(props.item.options_config?.max_value);

	if (props.item?.layout == 'sf' && !props.item?.options_config) {
		useLog(`FilterFieldSlider: ${props.item.code} options_config is undefined`, 'debug');
	}

	// if there are initial values, use them. Otherwise use min & max values
	const initialValues = props.item?.options_selected?.length ? props.item.options_selected[0].split('-') : null;
	const initialMin = initialValues ? initialValues[0] : minValue;
	const initialMax = initialValues ? initialValues[1] : maxValue;

	// set initial slider and input values
	const inputValue = ref([formatNumber(initialMin), formatNumber(initialMax)]);
	const value = ref([Math.round(initialMin), Math.round(initialMax)]);

	// on slider update
	function onSlide(v) {
		inputValue.value = [formatNumber(v[0]), formatNumber(v[1])];
	}

	// on input update. Value is debounced to prevent multiple updates
	let debounceTimer;
	function onChange(v) {
		clearTimeout(debounceTimer);

		debounceTimer = setTimeout(function () {
			let start = sanitizeInput(inputValue.value[0]);
			let end = sanitizeInput(inputValue.value[1]);

			if (start < minValue) start = minValue;
			if (start >= end) start = end - 100;
			if (start < 0) start = 0;
			if (end > maxValue || end < 0) end = maxValue;

			// set slider values
			value.value = [start, end];

			// update input values
			inputValue.value = [formatNumber(start), formatNumber(end)];
		}, 300);
	}

	// if filter option is cleared, reset slider and input values
	watch(
		() => props.item.options_selected,
		val => {
			if (!val) {
				value.value = [minValue, maxValue];
				inputValue.value = [formatNumber(minValue), formatNumber(maxValue)];
			}
		}
	);

	// submit selected price range
	async function submit() {
		let price = `${value.value[0]}-${value.value[1]}`;

		// if price values are equal to min & max values, remove price filter
		if (value.value[0] == minValue && value.value[1] == maxValue) {
			price = undefined;
		}

		await navigateTo({query: {...route.query, [props.item.filter_url]: price, to_page: undefined, page: undefined}});
		emit('catalogProductsUpdate', route.query);
	}

	// get data provided by parent Filter component
	const {globalSelectedFilters} = inject('baseFiltersData');

	// update global filters on input value change
	watch(
		() => value.value,
		async () => {
			if (props.autoSubmit) await submit();
			updateGlobalFilters();
		}
	);

	// update global filters to enable manual submit
	function updateGlobalFilters() {
		const globalFilters = globalSelectedFilters.value;

		setTimeout(() => {
			// if price values are equal to min & max values and price filter is not set, remove price filter from global filters
			if (value.value[0] !== minValue || value.value[1] !== maxValue || route.query.cena !== undefined) {
				return (globalFilters[props.item.filter_url] = [`${value.value[0]}-${value.value[1]}`]);
			}

			delete globalFilters[props.item.filter_url];
		}, 100);
	}

	// sanitize input fields
	function sanitizeInput(value) {
		let v = value.toString();
		v = v.replace(/[a-zA-ZŠĐČĆŽšđčćž,.]/g, '');
		return +v;
	}

	onBeforeUnmount(() => {
		clearTimeout(debounceTimer);
	});
</script>
