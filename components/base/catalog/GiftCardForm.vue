<template>
	<BaseForm @submit="onSubmit" v-slot="{errors, meta, values}" :loading="loading" v-bind="$attrs">
		<slot :loading="loading" :errors="errors" :meta="meta" :values="values" :emailFields="emailFields" :postFields="postFields" :onRemove="onRemove" :status="status" :product="product" />
	</BaseForm>
</template>

<script setup>
	const webshop = useWebshop();
	const props = defineProps({
		values: Object,
		number: {
			type: Number,
			default: 0,
		},
		code: String,
	});

	const status = ref(null);
	const loading = ref(false);

	// get gift product data
	const cart = computed(() => {
		if (props.code) return webshop.getCartData();
	});
	const product = computed(() => {
		if (!props.code) return null;
		if (!cart.value?.parcels?.length) return null;
		const cartItem = cart.value.parcels[0].items.find(item => item.shopping_cart_code === props.code);
		return cartItem ? cartItem : null;
	});

	// FIXME PROG brisanje gift carda ako proizvod više ne postoji https://markerdoo.eu.teamwork.com/app/tasks/26250731

	// edit gift card
	let statusTimeout;
	async function onSubmit({values, actions}) {
		if (!props.code) return useLog('Missing code', 'error');
		if (statusTimeout) clearTimeout(statusTimeout);
		loading.value = true;
		const request = {
			couponrecipient: {
				code: props.code,
				number: props.number,
				type: values?.type || 'email',
				email: values?.email || '',
				fullname: values?.fullname || '',
				address: values?.address || '',
				zipcode: values?.zipcode || '',
				city: values?.city || '',
				message: values?.message || '',
			},
		};
		const res = await webshop.submitCustomerData(request, {type: false});
		status.value = res;
		loading.value = false;

		await new Promise(resolve => {
			statusTimeout = setTimeout(() => {
				status.value = null;
				resolve();
			}, 3000);
		});
		await webshop.fetchCustomer();
	}

	// remove gift card
	async function onRemove() {
		if (!props.code && !props.number) return useLog('Missing code or number');
		const request = {
			couponrecipient: {
				code: props.code,
				number: props.number,
				type: '',
			},
		};
		await webshop.submitCustomerData(request, {type: false});
		await webshop.fetchCustomer();
	}

	const emailFields = [
		{
			'name': 'type',
			'type': 'radio',
			'value': 'email',
			'selected': props.values?.type === 'address' ? false : true,
			'validation': [],
		},
		{
			'name': 'email',
			'type': 'email',
			'value': props.values?.email ? props.values.email : '',
			'related_field': 'type',
			'validation': [
				{
					'type': 'not_empty',
					'value': null,
					'error': 'error_not_empty',
				},
				{
					'type': 'max_length',
					'value': 60,
					'error': 'error_max_length',
				},
				{
					'type': 'email',
					'value': null,
					'error': 'error_email',
				},
				{
					'type': 'email_domain',
					'value': null,
					'error': 'error_email_domain',
				},
			],
		},
		{
			'name': 'email_confirm',
			'type': 'email',
			'value': props.values?.email ? props.values.email : '',
			'validation': [
				{
					'type': 'not_empty',
					'value': null,
					'error': 'error_not_empty',
				},
				{
					'type': 'max_length',
					'value': 60,
					'error': 'error_max_length',
				},
				{
					'type': 'email',
					'value': null,
					'error': 'error_email',
				},
				{
					'type': 'email_domain',
					'value': null,
					'error': 'error_email_domain',
				},
				{
					'type': 'matches',
					'value': 'email',
					'error': 'error_matches',
				},
			],
		},
		{
			'name': 'message',
			'type': 'textarea',
			'value': props.values?.message ? props.values.message : '',
			'validation': [],
		},
	];

	const postFields = [
		{
			'name': 'type',
			'type': 'radio',
			'value': 'address',
			'selected': props.values?.type === 'address' ? true : false,
			'validation': [],
		},
		{
			'name': 'fullname',
			'value': props.values?.fullname ? props.values.fullname : '',
			'type': 'text',
			'validation': [
				{
					'type': 'not_empty',
					'value': null,
					'error': 'error_not_empty',
				},
				{
					'type': 'max_length',
					'value': 45,
					'error': 'error_max_length',
				},
			],
		},
		{
			'name': 'address',
			'value': props.values?.address ? props.values.address : '',
			'type': 'text',
			'validation': [
				{
					'type': 'not_empty',
					'value': null,
					'error': 'error_not_empty',
				},
				{
					'type': 'max_length',
					'value': 100,
					'error': 'error_max_length',
				},
			],
		},
		{
			'name': 'zipcode',
			'value': props.values?.zipcode ? props.values.zipcode : '',
			'type': 'text',
			'validation': [
				{
					'type': 'not_empty',
					'value': null,
					'error': 'error_not_empty',
				},
				{
					'type': 'number',
					'value': null,
					'error': 'error_number',
				},
				{
					'type': 'max_length',
					'value': 24,
					'error': 'error_max_length',
				},
			],
		},
		{
			'name': 'city',
			'value': props.values?.city ? props.values.city : '',
			'type': 'text',
			'validation': [
				{
					'type': 'not_empty',
					'value': null,
					'error': 'error_not_empty',
				},
				{
					'type': 'max_length',
					'value': 50,
					'error': 'error_max_length',
				},
			],
		},
	];
</script>
