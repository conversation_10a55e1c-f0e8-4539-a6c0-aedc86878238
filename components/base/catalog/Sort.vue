<template>
	<slot :onSort="onSort" :items="props.sortOptions" :selected="selectedSort" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const labels = useLabels();
	const {emit} = useEventBus();
	const props = defineProps({
		sortOptions: {
			type: Array,
			default: () => ['priority', 'new', 'old', 'expensive', 'cheaper', 'az', 'za', 'discount_priority', 'fastest_first', 'rates'],
		},
		defaultSort: String,
		mode: {
			type: String,
			default: 'catalog',
		},
	});

	// if sort is defined in query, use it as default value. Otherwise use first option from sortOptions
	const selectedSort = ref(route.query.sort ? route.query.sort : props.sortOptions[0]);

	// if defaultSort prop is defined and sort is not already defined in query, execute onSort on mount
	onMounted(() => {
		if (!route.query.sort && props.defaultSort) {
			selectedSort.value = props.defaultSort;
			onSort();
		}
	});

	// if sort is changed, update query and emit event. If sort is set to 'priority', remove it from query
	async function onSort(evt) {
		selectedSort.value = evt?.target?.value || evt;
		const sort = selectedSort.value == 'priority' || !selectedSort.value ? undefined : selectedSort.value;

		await navigateTo({query: {...route.query, sort, to_page: undefined, page: undefined}});

		// emit event based on mode prop
		let emitModule = 'catalogProductsUpdate';
		if (props.mode == 'sellers') emitModule = 'catalogSellersUpdate';
		if (props.mode == 'publish') emitModule = 'publishPostsUpdate';
		if (emitModule) emit(emitModule, route.query);
	}
</script>
