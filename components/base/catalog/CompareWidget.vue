<template>
	<slot :counter="counter" :compareUrl="getAppUrl('compare')" />
</template>

<script setup>
	const catalog = useCatalog();
	const {getAppUrl} = useApiRoutes();
	const isFetched = useState('compareIsFetched', () => false);

	onMounted(async () => {
		if (!isFetched.value) {
			await catalog.fetchComparedProducts();
			isFetched.value = true;
		}
	});

	// get compared products
	const counter = computed(() => {
		const res = catalog.getComparedProducts();
		return res?.length;
	});
</script>
