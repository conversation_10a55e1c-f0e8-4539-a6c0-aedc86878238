<template>
	<slot :items="products" :searchFields="searchFields" :searchFieldsMeta="searchFieldsMeta" :loading="loading" :pagination="pagination" :next-page="pagination.page?.next" :loadMore="loadMore" />
</template>

<script setup>
	const emit = defineEmits(['loadProducts', 'filterProducts']);
	const {fetchProducts, updateProductsPriceQty} = useCatalog();
	const config = useAppConfig();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	let remarketing;
	if (__REMARKETING__) remarketing = useRemarketing();

	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const router = useRouter();
	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();
	const {emit: emitGlobal, bus} = useEventBus();
	const {generateThumbs} = useImages();
	const {onElementVisibility, onScroll} = useDom();
	const props = defineProps({
		fetch: Object,
		thumbPreset: String,
		infiniteScroll: Number,
		responseFields: Array,
	});

	const products = ref([]);
	const searchFields = shallowRef([]);
	const searchFieldsMeta = shallowRef({});
	const pagination = ref({});
	const loading = ref(true);
	let fetchOptions = {};

	// get data provided by parent Category component
	const {category, contentType} = inject('baseCatalogCategoryData');

	// set fetch options
	_setFetchOptions();

	// fetch initial products
	if (route.query?.page) fetchOptions.page = route.query.page;
	if (route.query?.to_page) fetchOptions.to_page = route.query.to_page;

	const productsData = await fetchProducts(fetchOptions);
	products.value = productsData?.data?.items || [];
	searchFields.value = productsData?.data?.search_fields || [];
	searchFieldsMeta.value = productsData?.data?.search_fields_meta_data || {};

	onMounted(async () => {
		// update products price and qty (user specific prices)
		if (config?.catalog?.updatePriceQty) {
			const p = await updateProductsPriceQty(products.value);
			if (p?.length) products.value = [...p];
		}

		if (gtm) gtm.gtmTrack('viewItemList', {items: products.value, item_list_id: category.value?.id, item_list_name: category.value?.title});
		if (remarketing) {
			const remarketingEvent = contentType.value === 'search' ? 'searchresults' : 'category';
			remarketing.sendEvent(remarketingEvent, {items: products.value});
		}

		// emit event with initial products
		emit('loadProducts', {items: products.value, contentType: contentType.value});

		// Send search event to Facebook Conversion API
		_provideFbTrackingData();

		// listen for back navigation
		window.addEventListener('popstate', handleBackNavigation);
	});

	// listen for back/forward navigation and refresh products
	let isPopState = false;
	function handleBackNavigation() {
		isPopState = true;
	}
	router.afterEach(to => {
		if (isPopState) {
			emitGlobal('catalogProductsUpdate', {
				page: to.query?.page || null,
				toPage: to.query?.to_page || null,
			});
			isPopState = false;
		}
	});

	// update pagination data for initial products
	if (productsData?.data?.meta_data) {
		_updatePagination(productsData.data);
	}

	// if provided page is greater than total pages, redirect to last page
	if (pagination.value?.page?.to_page > pagination.value?.page?.total) {
		await navigateTo({query: {...route.query, 'to_page': undefined}});
	}

	// finish loading
	loading.value = false;

	// generate thumbs
	if (products.value?.length && props.thumbPreset) {
		await generateThumbs({
			data: products.value,
			preset: props.thumbPreset,
		});
	}

	// load more products event
	async function loadMore(mode = 'next') {
		// do not load more posts if previous or next page is not available
		if (mode === 'next' && !pagination.value.page.next) return false;
		if (mode === 'prev' && !pagination.value.page.previous) return false;

		// start loading
		loading.value = true;

		// get next page as default fetch option
		let page = pagination.value.page.next;

		// if mode is "next" + "page" and "to_page" query params are set, use "to_page" as next page
		if (mode === 'next' && route.query?.page && route.query?.to_page) {
			page = parseInt(route.query.to_page) + 1;
		}

		// if mode is "prev", use previous page
		if (mode === 'prev') {
			if (route.query?.page && parseInt(route.query.page) > 1) {
				page = parseInt(route.query.page) - 1;
			} else {
				page = pagination.value.page.previous;
			}
		}

		// fetch new products
		const newProducts = await fetchProducts({...fetchOptions, page: page});
		let appendProducts = newProducts?.data?.items?.length ? newProducts.data.items : [];

		// update products price and qty (user specific prices)
		if (config?.catalog?.updatePriceQty) {
			const p = await updateProductsPriceQty(appendProducts);
			if (p?.length) appendProducts = p;
		}

		// generate thumbs
		if (props.thumbPreset && appendProducts.length) {
			await generateThumbs({
				data: appendProducts,
				preset: props.thumbPreset,
			});
		}

		// gtm tracking
		if (gtm) gtm.gtmTrack('viewItemList', {items: appendProducts, item_list_id: category.value?.id, item_list_name: category.value?.title});

		// if mode is "prev", prepend new products
		if (mode === 'prev') {
			products.value.unshift(...appendProducts);

			// if query param "to_page" is not set, set it to current page
			const toPage = route.query?.to_page ? route.query.to_page : pagination.value.page.current;
			await navigateTo({query: {...route.query, 'page': page, 'to_page': toPage}});
		} else {
			products.value.push(...appendProducts);
			await navigateTo({query: {...route.query, 'to_page': page}});
		}

		emit('loadProducts', {items: appendProducts});

		// update pagination data
		_updatePagination(newProducts?.data);

		// finish loading
		loading.value = false;
	}

	const infiniteScroll = props.infiniteScroll ? props.infiniteScroll : 0;
	if (infiniteScroll > 0) {
		let scrollDirection;
		let infiniteScrollTriggered = 0;
		const scrollTrigger = ref(null);

		onMounted(async () => {
			await nextTick();
			scrollTrigger.value = document.querySelector('[data-products-scroll-trigger]'); // infinite scroll auto trigger
		});

		onScroll({
			debounce: 50,
			callback: ({direction}) => {
				scrollDirection = direction;
			},
		});

		onElementVisibility({
			target: scrollTrigger,
			callback: async ({isVisible}) => {
				if (isVisible && scrollDirection == 'down' && infiniteScrollTriggered < infiniteScroll) {
					await loadMore();
					infiniteScrollTriggered++;
				}
			},
		});
	}

	// watch for filter or pagination updates and fetch new products
	let fetchTimeout;
	watch(
		() => bus.value,
		async () => {
			// do not fetch/filter products if event is not "catalogProductsUpdate"
			if (bus.value.event != 'catalogProductsUpdate') return false;

			// start loading
			loading.value = true;

			if (fetchTimeout) clearTimeout(fetchTimeout);
			fetchTimeout = setTimeout(async () => {
				// set fetch options
				_setFetchOptions();

				// if bus data contains pagination data, use it
				const page = bus.value.data?.page ? bus.value.data.page : null;
				const toPage = bus.value.data?.toPage ? bus.value.data.toPage : null;

				// fetch products
				const productsData = await fetchProducts({...fetchOptions, page: page, to_page: toPage});
				let productItems = productsData?.data?.items?.length ? [...productsData?.data?.items] : [];

				// update products price and qty (user specific prices)
				if (config?.catalog?.updatePriceQty) {
					const p = await updateProductsPriceQty(productItems);
					if (p?.length) productItems = p;
				}

				products.value = productItems;
				searchFields.value = productsData?.data?.search_fields ? [...productsData.data.search_fields] : [];
				searchFieldsMeta.value = productsData?.data?.search_fields_meta_data ? {...productsData.data.search_fields_meta_data} : {};

				emit('filterProducts', {items: products});

				// update pagination data
				_updatePagination(productsData?.data);

				// generate thumbs
				if (products.value?.length && props.thumbPreset) {
					await generateThumbs({
						data: products.value,
						preset: props.thumbPreset,
					});
				}

				// Send search event to Facebook Conversion API
				_provideFbTrackingData();

				// finish loading
				loading.value = false;
			}, 100);
		}
	);

	// fetch options
	function _setFetchOptions() {
		// set initial fetch options
		fetchOptions = {
			...props.fetch,
			mode: 'index',
		};

		if (config?.catalog?.productsResponseFields?.length && !fetchOptions.response_fields) fetchOptions.response_fields = config.catalog.productsResponseFields;
		if (props.responseFields) fetchOptions.response_fields = props.responseFields;

		// set category options (used by backend)
		if (contentType.value == 'category') {
			if (category.value?.position_h) fetchOptions.category_position = category.value.position_h;
			if (category.value?.id && !category.value?.pageId) fetchOptions._category_id = category.value.id;
			if (category.value?.search_id) fetchOptions._search_id = category.value.search_id;

			// to avoid missing products, skip products on first page if category has rotator elements
			if (category.value?.rotator_elements?.length) {
				fetchOptions._rotator_elements = category.value.rotator_elements.length;
			}
		}

		// if content type is "manufacturer", fetch manufacturer by slug and set manufacturer_id fetch option
		if (contentType.value == 'manufacturer' && category.value?.id && category.value?.position_h) {
			fetchOptions.manufacturer_id = [category.value.id];
			fetchOptions._manufacturer_id = category.value.id;
			fetchOptions.manufacturer_position = category.value.position_h;
		}

		// if content type is "list", fetch list by slug and set list_id fetch option
		if (contentType.value == 'list' && category.value?.id && category.value?.code) {
			fetchOptions.list_code = [category.value.code];
			fetchOptions._list_id = category.value.id;
		}

		// if content type is "seller", fetch seller products
		if (__SELLERS__ && contentType.value == 'seller' && category.value?.id && category.value?.position_h) {
			if (category.value?.products_required_select_category == true) {
				const firstId = Object.keys(category.value.products_categories)[0];
				fetchOptions.category_id = firstId;
				fetchOptions.seller_position = [category.value.position_h];
				fetchOptions._seller_id = category.value.id;
			} else {
				fetchOptions.seller_position = [category.value.position_h];
				fetchOptions._seller_id = category.value.id;
			}
		}

		// if content type is "search"
		if (contentType.value == 'search') {
			fetchOptions._search_id = true;
			if (category.value?.position_h) fetchOptions.category_position = category.value.position_h;
		}

		// restructure filter query params
		if (route.query) {
			Object.entries(route.query).forEach(([key, value]) => {
				if (value) {
					const formattedValue = Array.isArray(value) ? value.join(',') : value;
					fetchOptions[key] = formattedValue;
				}
			});
		}
	}

	// update pagination data
	function _updatePagination(data) {
		pagination.value = data?.meta_data?.pagination ? data.meta_data.pagination : {};
		if (pagination.value.items) pagination.value.items.current = products.value?.length;
	}

	// provide found products to Facebook Conversion API
	function _provideFbTrackingData() {
		if (fbCapi && contentType.value == 'search' && config.facebook?.conversionApi?.events?.includes('search')) {
			const contentIds = products.value.map(product => product.id);
			fbCapi.setData(contentIds);
		}
	}

	// provide data to child components
	provide('baseCatalogProductsData', {searchFieldsMeta, searchFields, loading, pagination});
	provide('pagination', {pagination, mode: 'catalog'});

	// reset data on unmount
	onBeforeUnmount(() => {
		products.value = [];
		searchFields.value = [];
		searchFieldsMeta.value = {};
		pagination.value = {};
		window.removeEventListener('popstate', handleBackNavigation);
	});
</script>
