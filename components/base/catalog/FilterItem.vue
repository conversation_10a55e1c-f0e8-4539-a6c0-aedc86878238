<template>
	<slot :fields="fields" :totalFields="totalFields" :onFilter="onFilter" :onClear="onClear" :selectedFilters="selectedFilters" :active="active" :onToggle="onToggle" :visibleFields="visibleFields" :onShowMore="onShowMore" :onShowLess="onShowLess" :onSearch="onSearch" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {emit, subscribe} = useEventBus();
	const {debounce} = useUtils();
	const {compareArrays} = useArrayUtils();
	const {normalizeString} = useText();
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
		visibleFields: Number,
		active: {
			type: Boolean,
			default: props => props.item?.options_show,
		},
		autoSubmit: {
			type: Boolean,
			default: true,
		},
		group: String,
	});

	// get data provided by parent Filter component
	const {globalSelectedFilters} = inject('baseFiltersData');
	const selectedFilters = ref([]);
	const active = ref(props.active);
	const visibleFields = ref(props.visibleFields);
	const searchTerm = ref('');
	const totalFields = computed(() => props.item?.options?.length);

	const fields = computed(() => {
		let items = props.item.options;
		if (!items?.length) return null;

		if (!searchTerm.value) {
			// show only number of fields defined by visibleFields prop
			items = props.visibleFields ? items.slice(0, visibleFields.value) : items;
		} else {
			const normalizedSearch = normalizeString(searchTerm.value.toLowerCase());
			items = items.filter(item => normalizeString(item.title.toLowerCase()).includes(normalizedSearch));
		}

		/* 
		Group filtered items by alphabet
		{
			alphabet: 'A',
			items: []
		}
		*/
		if (props.group == 'alphabet') {
			items = items.reduce((acc, item) => {
				const firstChar = item.title.charAt(0); // Uzmi prvi znak

				let group = acc.find(g => g.alphabet === firstChar);
				if (!group) {
					group = {alphabet: firstChar, items: []};
					acc.push(group);
				}
				group.items.push(item);

				return acc;
			}, []);
			items.sort((a, b) => a.alphabet.localeCompare(b.alphabet));
		}

		return items;
	});

	function onShowMore() {
		visibleFields.value = props.item.options.length;
	}

	function onShowLess() {
		visibleFields.value = props.visibleFields;
	}

	// when search input is changed, update searchTerm value and visibleFields value
	function onSearch(event) {
		searchTerm.value = event.target.value;
		visibleFields.value = event.target.value ? null : props.visibleFields;
	}

	async function onFilter(event) {
		// check if event name and value are defined
		if (!event.target.name || !event.target.value || event.target.value == 'on') {
			return useLog('Filter name or value is not defined', 'error');
		}

		// add or remove selected filter if checkbox is checked or unchecked
		if (event.target.checked && !selectedFilters.value.includes(event.target.value)) {
			selectedFilters.value.push(event.target.value);
		}
		if (!event.target.checked) {
			selectedFilters.value = selectedFilters.value.filter(item => item.toString() !== event.target.value);
		}

		// select or unselect all sublevel checkboxes if parent checkbox is checked
		const checkboxes = document.querySelectorAll(`input[name="${event.target.name}"][value^="${event.target.value}"]`);
		if (checkboxes?.length > 1) {
			checkboxes.forEach(checkbox => {
				if (checkbox.value.includes('/')) {
					if (event.target.checked) {
						checkbox.checked = true;
						if (!selectedFilters.value.includes(checkbox.value)) {
							selectedFilters.value.push(checkbox.value);
						}
					} else {
						checkbox.checked = false;
						selectedFilters.value = selectedFilters.value.filter(item => item.toString() !== checkbox.value);
					}
				}
			});
		}

		await debounceSubmit();
	}

	// toggle filter visibility
	function onToggle(event, options = {}) {
		if (options?.all) emit('catalogFilterItemToggle', props.item);
		searchTerm.value = '';
		active.value = !active.value;
	}

	// close other active filters when filter item is toggled
	subscribe(
		'catalogFilterItemToggle',
		data => {
			if (!data || props.item.id != data?.id) active.value = false;
		},
		{deep: false}
	);

	// when clear button is clicked, remove all selected filters and submit
	async function onClear() {
		selectedFilters.value = [];
		await submit('clear');
	}

	// update selected filters on component mount and when filter is removed from currently active filters
	watchEffect(() => {
		selectedFilters.value = props.item.options?.length ? props.item.options.filter(item => item.selected).map(item => item.filter_url) : [];
	});

	// debounce submit (prevent rage clicks)
	const debounceSubmit = debounce(() => submit(), 500);

	// if auto submit is enabled, update route query and emit event to update products
	async function submit(mode) {
		let currentRouteFilters = route.query?.[props.item.filter_url] || [];
		if (!Array.isArray(currentRouteFilters)) currentRouteFilters = [currentRouteFilters];
		if (compareArrays(currentRouteFilters, selectedFilters.value)) return; // do not submit if filters are the same
		if (props.autoSubmit || mode) {
			await navigateTo({query: {...route.query, [props.item.filter_url]: selectedFilters.value, to_page: undefined, page: undefined}});
			emit('catalogProductsUpdate', route.query);
		}

		updateGlobalFilters();
	}

	// update global filters to enable manual submit
	function updateGlobalFilters() {
		const globalFilters = globalSelectedFilters.value;

		// check if global filters object contain current props.item.filter_url. if it does, remove it
		if (globalFilters[props.item.filter_url]) {
			delete globalFilters[props.item.filter_url];
		}

		// if selectedFilters value is not empty add it to global filters
		if (selectedFilters.value.length > 0) {
			globalFilters[props.item.filter_url] = selectedFilters.value;
		}
	}

	// update global selected filters on component mount
	updateGlobalFilters();

	// expose values to parent component
	defineExpose({active});
</script>
