<template>
	<slot :onFilter="onClick" :selected="selected">
		<div :class="'cf-' + filter">
			<input type="checkbox" :name="filter" :id="'filter-' + filter" @click="onClick" :value="value" v-model="selected" />
			<label :for="'filter-' + filter">{{ labels.get('filter_' + filter) }}</label>
		</div>
	</slot>
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {emit, bus} = useEventBus();
	const labels = useLabels();

	const props = defineProps({
		filter: {
			type: String,
			required: true,
		},
		value: {
			type: [String, Number],
			default: 1,
		},
	});

	// initial selected value
	const selected = ref(false);

	// watch for route changes and if query contains filter, set selected to true
	watchEffect(() => {
		selected.value = route.query[props.filter] && route.query[props.filter] != 0 ? true : false;
		//emit('catalogProductsUpdate', route.query);
	});

	// if value is changed, update query and emit event. If checkbox is unchecked, remove filter from query
	async function onClick() {
		const filter = selected.value ? undefined : props.value;

		await navigateTo({query: {...route.query, [props.filter]: filter, to_page: undefined, page: undefined}});
		emit('catalogProductsUpdate', route.query);
	}
</script>
