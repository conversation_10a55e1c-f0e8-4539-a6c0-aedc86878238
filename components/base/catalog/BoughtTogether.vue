<template>
	<slot :total="totalPrice" :saving="savingPrice" :addToCartData="addToCartItems" :onResetSelectedVariation="resetSelectedVariations" />
</template>

<script setup>
	const {removeEmptyElements} = useArrayUtils();
	const model = defineModel();
	const props = defineProps({
		items: {
			type: Array,
			required: true,
		},
		meta: {
			type: Object,
			required: true,
		},
		selected: String,
	});

	/* 
		By default, select all available items without variations.
		If selected is set to 'main', select only the first item.
		If selected is set to 'none', select nothing.
	*/
	if (props.items?.length) {
		const mainItem = props.items[0];
		if (props.selected == 'none') model.value = [];
		if (props.selected == 'main') model.value = !mainItem.is_available || +mainItem.variation_request ? [] : [mainItem];
		if (!props.selected) model.value = props.items.filter(item => item.is_available && !+item.variation_request);
	}

	const checkedItems = computed(() => {
		return [...model.value];
	});

	const addToCartItems = computed(() => {
		return checkedItems.value.map(checkedItem => {
			let modalData = checkedItem;
			const shoppingCartCode = removeEmptyElements(checkedItem.shopping_cart_code.split('_'));

			// Append variation data to product modalData
			if (shoppingCartCode?.length && shoppingCartCode[1] != 0) {
				const productId = shoppingCartCode[0];
				const product = props.items.find(item => item.id == productId);

				// Extract variation attributes
				product.variation = {
					attributes: {},
				};
				const productVariationAttributes = removeEmptyElements(checkedItem.attributes_ids.split(','));
				if (productVariationAttributes.length && product.variation_attributes_all) {
					Object.values(product.variation_attributes_all).forEach(el => {
						if (productVariationAttributes.includes(el.id)) product.variation.attributes[el.id] = el;
					});
				}

				// Apend data (remove empty elements)
				Object.keys(checkedItem).forEach(key => {
					if (checkedItem[key]) product[key] = checkedItem[key];
				});

				modalData = product;
			}

			return {
				modalData,
				shopping_cart_code: checkedItem.shopping_cart_code,
				quantity: checkedItem.quantity ? checkedItem.quantity : 1,
				relatedlist_id: props.meta?.extra?.list_id,
			};
		});
	});

	// discount percent added to list
	const discountPercent = props.meta?.extra?.related_discount_percent ? props.meta?.extra?.related_discount_percent / 100 : 0;

	const totalPrice = computed(() => {
		const subtotal = checkedItems.value.reduce((total, item) => {
			const qty = item.quantity ? item.quantity : 1;

			// if all items are selected, apply discount
			if (checkedItems.value?.length >= props.items?.length && discountPercent > 0) {
				// calculate the discount for one item
				const discountAmount = item.price_custom * discountPercent;

				// apply discount to one item, full price for the rest
				if (qty > 1) {
					return total + item.price_custom * (qty - 1) + (item.price_custom - discountAmount);
				} else {
					return total + (item.price_custom - discountAmount);
				}
			}

			// all items are not selected, no discount. Return full price
			return total + item.price_custom * qty;
		}, 0);

		return subtotal;
	});

	const savingPrice = computed(() => {
		const subtotal = checkedItems.value.reduce((total, item) => {
			// if all items are selected, get total discount amount
			if (checkedItems.value?.length >= props.items?.length && discountPercent > 0) {
				const discountAmount = item.price_custom * discountPercent;
				return total + discountAmount;
			}

			// all items are not selected, no discount
			return 0;
		}, 0);

		return subtotal;
	});

	// reset selected variations when user select another option
	function resetSelectedVariations(vars) {
		if (!vars) return;
		const shoppingCartCodes = Object.values(vars).map(item => item.shopping_cart_code);
		model.value = checkedItems.value.filter(item => !shoppingCartCodes.includes(item.shopping_cart_code));
	}
</script>
