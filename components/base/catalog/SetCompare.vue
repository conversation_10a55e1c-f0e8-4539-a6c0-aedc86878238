<template>
	<slot :onToggleCompare="onToggleCompare" :active="active" :message="message" :loading="loading" :compareUrl="compareUrl" :limit="compareLimit">
		<span class="base-setcompare">
			<span class="base-setcompare-btn" :class="{'loading': loading, 'active': active}" @click="onToggleCompare">{{ active ? '-' : '+' }}</span>
			<span class="base-setcompare-message" v-show="message" v-html="labels.get(message).replace('%view_compare_url%', compareUrl)" />
		</span>
	</slot>
</template>

<script setup>
	const config = useAppConfig();
	const {getAppUrl} = useApiRoutes();
	const labels = useLabels();
	const catalog = useCatalog();
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
	});

	// set initial active state
	const comparedProducts = computed(() => catalog.getComparedProducts());
	const active = computed(() => {
		if (!comparedProducts.value?.length) return false;
		return comparedProducts.value?.find(el => el.id == props.item?.id) ? true : false;
	});
	const compareLimit = config?.compare?.limit ? config.compare.limit : null;

	const message = ref('');
	const loading = ref(false);
	const compareUrl = getAppUrl('compare');

	let messageTimeout;
	async function onToggleCompare() {
		if (loading.value) return; // prevent multiple clicks

		clearTimeout(messageTimeout);
		loading.value = true;
		message.value = '';

		let res;
		if (active.value) {
			res = await catalog.removeFromCompare({shopping_cart_code: props.item.shopping_cart_code});
		} else {
			if (!compareLimit || (compareLimit && comparedProducts.value.length < compareLimit)) {
				res = await catalog.addToCompare({shopping_cart_code: props.item.shopping_cart_code});
			} else {
				res = 'compare_error_limit';
			}
		}

		// if limit is reached, show message
		if (res == 'compare_error_limit') {
			loading.value = false;
			message.value = res || '';
			messageTimeout = setTimeout(() => (message.value = ''), 4000);
			return;
		}

		await catalog.fetchComparedProducts({retry: active.value ? 'remove' : 'add', itemId: props.item.id});
		loading.value = false;
		message.value = res?.data?.label_name || '';
		messageTimeout = setTimeout(() => (message.value = ''), 4000);
	}
</script>
