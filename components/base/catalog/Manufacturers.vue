<template>
	<slot :items="filteredItems" :loading="loading" :onSearch="onSearch" />
</template>

<script setup>
	const config = useAppConfig();
	const emit = defineEmits(['load']);
	const catalog = useCatalog();
	const {generateThumbs} = useImages();
	const {normalizeString} = useText();
	const props = defineProps({
		fetch: Object,
		thumbPreset: String,
		cache: {
			type: Boolean,
			default: true,
		},
		dataKey: String,
		log: {
			type: Boolean,
			default: false,
		},
	});

	const items = ref([]);
	const loading = ref(true);
	async function fetchData() {
		const fetchOptions = {
			...props.fetch,
			cache: props.cache,
		};
		if (config?.catalog?.manufacturersResponseFields?.length && !fetchOptions.response_fields) fetchOptions.response_fields = config.catalog.manufacturersResponseFields;

		const fetchConfig = {};
		if (props.dataKey) {
			fetchConfig.dataKey = props.dataKey;
		}

		const res = await catalog.fetchManufacturers(fetchOptions, {...fetchConfig});
		items.value = res?.data?.length ? res.data : [];

		loading.value = false;
		await generateImages();
	}

	await fetchData();

	const searchTerm = ref('');
	function onSearch(event) {
		searchTerm.value = event.target.value;
	}

	// Filter items if search term is provided. Supports different data structures (alphabet group or flat list)
	const filteredItems = computed(() => {
		if (!searchTerm.value.trim()) return items.value;

		const normalizedSearch = normalizeString(searchTerm.value.toLowerCase());
		if (items.value.length && typeof items.value[0]?.alphabet === 'string') {
			return items.value
				.map(group => ({
					...group,
					items: group.items.filter(item => normalizeString(item.title.toLowerCase()).includes(normalizedSearch)),
				}))
				.filter(group => group.items.length > 0);
		}

		return items.value.filter(item => normalizeString(item.title.toLowerCase()).includes(normalizedSearch));
	});

	onMounted(async () => {
		// emit event with data
		emit('load', {items: items.value});

		// log categories data to console
		if (props.log) console.log('BaseCatalogManufacturers', items.value, props.fetch);
	});

	async function generateImages() {
		if (items.value?.length && props.thumbPreset) {
			await generateThumbs({
				data: items.value,
				preset: props.thumbPreset,
				dataKey: props.dataKey ? props.dataKey + '-thumbs' : null,
			});
		}
	}
</script>
