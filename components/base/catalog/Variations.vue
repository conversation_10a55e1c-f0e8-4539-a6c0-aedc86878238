<template>
	<slot :items="variations" :attributes="attributes" :selectedAttributes="selectedAttributes" :selectedVariation="selectedVariation" :variationAddToCartData="addToCartData" :onSelect="onSelect" />
</template>

<script setup>
	const route = useRoute();
	const {compareArrays, removeEmptyElements} = useArrayUtils();
	const {getCurrency} = useCurrency();
	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
		activeVariation: {
			type: [Object, Boolean, String],
			default: props => (props.item.active_variation ? props.item.active_variation : null),
		},
	});

	// get all variations from item and process them
	const variations = ref([]);
	if (props.item.variations) variations.value = Object.values(props.item.variations);

	// if variations data is not provided, try to get variations from variation_quickdata
	if (!props.item.variations && props.item.variation_quickdata) {
		variations.value = Object.values(props.item.variation_quickdata);

		if (props.item.variation_attributes_all) {
			const allAttributes = Object.values(props.item.variation_attributes_all);

			// add attributes to each variation quickdata if not present
			variations.value = variations.value.map(item => {
				if (item.attributes?.length) return;
				const attributeIds = removeEmptyElements(item.attributes_ids.split(','));

				// check if allAttributes contain attribute from attributeIds. if it does, append attribute to item
				const attributes = {};
				attributeIds.forEach(attributeId => {
					const attr = allAttributes.find(attr => attr.id == attributeId);
					if (attr) attributes[attr.id] = toRaw(attr);
				});

				return {
					...item,
					attributes,
				};
			});
		} else {
			useLog('Missing variation_attributes_all data', 'error');
		}
	}

	const attributes = computed(() => {
		if (!props.item.variation_attributes_all) return [];

		let attrs = [];
		Object.values(props.item.variation_attributes_all).forEach(item => {
			const existingItem = attrs.find(existing => existing.attribute_code === item.attribute_code);
			if (existingItem) return;

			attrs.push({
				id: item.id,
				attribute_position: item.attribute_position,
				attribute_title: item.attribute_title,
				attribute_code: item.attribute_code,
				attribute_ordering: item.attribute_ordering,
				field_type: item.field_type,
				attribute_image: item.attribute_image,
				active: false,
			});
		});

		// sort attributes by attribute_position
		attrs = Object.values(attrs).sort((a, b) => {
			return a.attribute_position - b.attribute_position;
		});

		// add index for each attribute
		attrs.forEach((item, itemIndex) => {
			item.index = itemIndex;
		});

		return attrs;
	});

	const attributeItems = computed(() => (props.item?.variation_attributes_all ? Object.values(props.item.variation_attributes_all) : []));
	const selectedAttributes = ref([]);

	// set initialy selected variation / attributes
	let activeVariation = props.activeVariation;

	// if prop is set to 'singleActive' and there is only one variation, set it as active
	if (props.activeVariation == 'onlySingle') {
		if (variations.value?.length == 1) {
			activeVariation = variations.value[0];
		}
	}

	if (activeVariation?.attributes) {
		setSelectedAttributes(activeVariation.attributes);
	}

	// watch for changes in selectedAttributes and update active state for attribute items
	watch(
		selectedAttributes,
		data => {
			if (!data?.length || !attributes.value?.length) return;

			// reset active state for all items
			attributes.value.forEach(el => (el.active = false));

			// set active state for items in selectedAttributes. First item is always active. The rest are active if previous item is selected
			attributes.value[0].active = true;
			for (let i = 0; i < data.length; i++) {
				if (attributes.value[i + 1]) attributes.value[i + 1].active = true;
			}
		},
		{immediate: true}
	);

	// add selected attributes based on url hash
	watch(
		() => route.hash,
		data => {
			// selected attribute
			if (data?.startsWith('#variationattribute')) {
				const attrId = data.replace('#variationattribute', '');
				if (attrId) {
					const attr = props.item.variation_attributes_all[attrId];
					if (attr) setSelectedAttributes([attr]);
				}
			}

			// selected variation
			if (data?.startsWith('#variationdetail')) {
				const variationCode = data.replace('#variationdetail', '');
				const selectedVariation = variations.value?.find(variation => variation.shopping_cart_code === variationCode);
				if (selectedVariation) setSelectedAttributes(selectedVariation.attributes);
			}
		},
		{immediate: true}
	);

	const selectedModel = defineModel('selected');
	const selectedShoppingCartCodeModel = defineModel('selectedShoppingCartCode');
	const selectedCodeModel = defineModel('selectedCode');
	const isSelectedModel = defineModel('isSelected');
	const selectedVariation = computed(() => {
		if (!selectedAttributes.value?.length) return null;

		// get ids of selected attributes
		const selectedAttributesIds = selectedAttributes.value.map(attr => attr.id);
		let selectedVar = null;

		// find variation that matches selected attributes
		variations.value.forEach(variation => {
			const variationAttributesIds = variation.attributes_ids.split(',');
			if (compareArrays(variationAttributesIds, selectedAttributesIds, {removeEmptyElements: true})) selectedVar = variation;
		});

		selectedModel.value = selectedVar ? selectedVar : null;
		selectedShoppingCartCodeModel.value = selectedVar?.shopping_cart_code ? selectedVar.shopping_cart_code : null;
		selectedCodeModel.value = selectedVar?.code ? selectedVar.code : null;
		isSelectedModel.value = selectedVar ? true : false;

		// send facebook capi event
		if (fbCapi && selectedVar) {
			fbCapi.sendEvent('customizeProduct', {
				id: selectedVar.code,
				content_type: 'product',
				value: selectedVar.price_custom,
				currency: getCurrency()?.code,
			});
		}

		return selectedVar;
	});

	const addToCartData = computed(() => {
		if (!selectedVariation.value) return null;
		const item = {...props.item};
		const selectedVar = selectedVariation.value;
		const selectedVariationAttributes = {
			attributes: selectedVariation.value?.attributes,
		};
		item.variation = selectedVariationAttributes;
		delete selectedVar.title;
		return {
			...item,
			...selectedVar,
		};
	});

	// set selected attributes based on selected variation
	function onSelect(e) {
		const variationCartCode = e.target.value;
		const variation = variations.value?.find(variation => variation.shopping_cart_code === variationCartCode);

		if (variation) {
			setSelectedAttributes(variation.attributes);
		}
	}

	// sort attributes by attribute_position and save to selectedAttributes
	function setSelectedAttributes(attrs) {
		const attributes = Object.values(attrs).sort((a, b) => {
			return a.attribute_position - b.attribute_position;
		});
		selectedAttributes.value = attributes || [];
	}

	provide('BaseCatalogVariationsData', {
		attributes,
		attributeItems,
		variations,
		selectedAttributes,
	});
</script>
