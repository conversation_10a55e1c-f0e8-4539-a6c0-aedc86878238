<template>
	<slot :searchResults="searchResults" :totalSearchResults="totalSearchResults" :loading="loading" :updateValue="updateValue" :handleInput="handleInput" :onReset="onReset" :searchTerm="searchTerm" />
</template>

<script setup>
	const catalog = useCatalog();
	const searchResultsModel = defineModel();
	const labels = useLabels();
	const nuxtApp = useNuxtApp();
	const {generateThumbs} = useImages();
	const {emit} = useEventBus();
	const props = defineProps({
		fetch: Object,
	});

	const searchTerm = ref('');
	const loading = ref(0);
	const searchResults = ref(null);
	const totalSearchResults = ref(0);

	let typingTimer;
	async function handleInput(event) {
		// clear timer on each keypress
		clearTimeout(typingTimer);

		if (!/^[a-zA-Z0-9 ]*$/.test(event.key) && !['Backspace', 'ArrowDown', 'ArrowUp', 'Enter', 'Escape'].includes(event.key)) {
			return false;
		}

		// run search function only if typing has stopped for 300ms
		typingTimer = setTimeout(search, 300);
	}

	// fetch data
	async function search() {
		// reset total search results and selected index and start loading
		totalSearchResults.value = 0;
		loading.value = 1;
		let searchData = [];

		// search only if search term is at least 2 characters long and
		if (searchTerm.value && searchTerm.value.length > 1) {
			searchData = await catalog.fetchWishlistProducts({search_q: searchTerm.value});
		} else {
			searchData = await catalog.fetchWishlistProducts();
		}

		totalSearchResults.value = 0;
		searchResultsModel.value = searchData;
		loading.value = 0;
	}

	// update search term value
	function updateValue(event) {
		searchTerm.value = event.target.value;
	}

	// reset search results
	function onReset() {
		searchResults.value = null;
		totalSearchResults.value = 0;
		searchResultsModel.value = null;
		searchTerm.value = '';
		search();
	}

	onBeforeUnmount(() => {
		clearTimeout(typingTimer);
	});
</script>
