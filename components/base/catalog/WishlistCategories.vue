<template>
	<slot :items="items" :loading="loading" />
</template>

<script setup>
	const catalog = useCatalog();
	const {generateThumbs} = useImages();
	const props = defineProps({
		levelFrom: String,
		levelTo: String,
		limit: Number,
		thumbPreset: String,
	});
	const loading = ref(false);
	const items = ref(null);

	onMounted(async () => {
		loading.value = true;
		const res = await catalog.fetchWishlistCategories({'level_from': props.levelFrom, 'level_to': props.levelTo});
		if (res.success) {
			items.value = Object.values(res.data);
			if (props.limit) {
				items.value = items.value.slice(0, props.limit);
			}

			if (items.value && props.thumbPreset) {
				generateThumbs({
					data: items.value,
					preset: props.thumbPreset,
				});
			}
		}
		loading.value = false;
	});
</script>
