<template>
	<slot :items="items" :total="total" :addProduct="addProduct" :removeProduct="removeProduct" :loading="loading" :addToCartData="addToCartData" :hasVariations="hasVariations" :valid="valid" />
</template>

<script setup>
	const catalog = useCatalog();
	const {removeEmptyElements} = useArrayUtils();
	const {generateThumbs} = useImages();
	const props = defineProps({
		fetch: Object,
		thumbPreset: String,
	});

	const model = defineModel();
	const quickOrderCookie = useCookie('quick_order_products', {
		encode(value) {
			return value;
		},
		decode(value) {
			return value;
		},
	});
	const selectedItems = ref([]);
	const items = ref([]);
	const loading = ref(false);

	// set selected items from cookie
	onMounted(async () => {
		if (quickOrderCookie.value) {
			selectedItems.value = quickOrderCookie.value.split(',');
			if (selectedItems.value.length) {
				const data = await fetchProducts(selectedItems.value);
				items.value = data;
				items.value = items.value.sort((a, b) => selectedItems.value.indexOf(a.id) - selectedItems.value.indexOf(b.id));
				model.value = items.value;
			}
		}
	});

	// set add to cart data from items
	const addToCartData = computed(() => {
		return items.value.map(item => {
			return {
				modalData: item,
				shopping_cart_code: item.shopping_cart_code,
				quantity: item.quantity ? item.quantity : 1,
			};
		});
	});

	// calculate total
	const total = computed(() => {
		return items.value.reduce((total, item) => total + item.price_custom * item.quantity, 0);
	});

	// fetch products by ids
	async function fetchProducts(ids) {
		if (!ids?.length) return;
		loading.value = true;
		const res = await catalog.fetchProducts({mode: 'widget', id: ids, ...props.fetch});
		const data = res.data?.items;
		if (data.length && props.thumbPreset) {
			await generateThumbs({
				data: data,
				preset: props.thumbPreset,
			});
		}
		loading.value = false;
		return data;
	}

	// remove product from items and update cookie
	function removeProduct(data) {
		if (!data) {
			return useLog('Quick order removeProduct() - missing product data', 'error');
		}
		items.value = items.value.filter(item => item.id != data.id);
		selectedItems.value = selectedItems.value.filter(item => item != data.id);
		setCookie();
		model.value = items.value;
	}

	// fetch new product, add it to items if not already selected and update cookie
	async function addProduct(data) {
		if (!data) {
			return useLog('Quick order addProduct() - missing product data', 'error');
		}

		if (!selectedItems.value.includes(data.id)) {
			selectedItems.value.push(data.id);
			const res = await fetchProducts([data.id]);
			if (res?.length) items.value.push(res[0]);
			setCookie();
		}
		model.value = items.value;
	}

	function setCookie() {
		quickOrderCookie.value = selectedItems.value.join(',');
	}

	// check if any of the items has variations
	const hasVariations = computed(() => {
		return items.value.some(item => item.variation_request == '1');
	});

	// check if all items with variations have selected variation
	const valid = computed(() => {
		// not valid if there are no items
		if (!items.value?.length) {
			return false;
		}

		let isValid = true;
		addToCartData.value.forEach(item => {
			if (item.modalData?.variation_request == '1' && !item.modalData?.selectedVariation) {
				isValid = false;
			}
		});
		return isValid;
	});
</script>
