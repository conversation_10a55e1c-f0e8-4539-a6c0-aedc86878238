<template>
	<slot :item="attributeItem" :items="items" :onSelect="onSelect" :selected="selectedItem" />
</template>

<script setup>
	const {removeEmptyElements, compareArrays} = useArrayUtils();
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
	});

	// data provided by parent
	const {attributeItems, variations, selectedAttributes} = inject('BaseCatalogVariationsData');

	const attributeItem = computed(() => {
		const attr = props.item;
		attr.empty = items.value?.length ? false : true;
		return attr;
	});

	// attribute items
	const items = computed(() => {
		let attrs = [];

		if (props.item.index == 0) {
			attrs = Object.values(attributeItems.value).filter(attr => attr.attribute_code === props.item.attribute_code);
		}

		if (selectedAttributes.value?.length) {
			// get attributes higher in the hierarchy than the current attribute
			const matchingAttributes = selectedAttributes.value.filter(attr => attr.attribute_position < props.item.attribute_position);

			// extract all matched attributes from variations into one array
			variations.value.forEach(variation => {
				const attributes = Object.values(variation.attributes);
				if (attributes?.length) {
					if (containsAllItems(attributes, matchingAttributes, 'code')) {
						const attr = attributes.find(attr => attr.attribute_code === props.item.attribute_code);
						if (attr) attrs.push(attr);
					}
				}
			});
		}

		// remove duplicate items
		attrs = attrs.filter((item, index, self) => index === self.findIndex(t => t.code === item.code));

		// add attribute index to each item
		attrs.forEach((item, itemIndex) => {
			item.attribute_index = props.item.index;
		});

		return attrs;
	});

	function onSelect(item) {
		// do nothing if item is already selected
		if (item.selected) return;

		let attributeItem = item;

		// if item is a <select> element
		if (item.target?.value) attributeItem = attributeItems.value[item.target.value];

		// remove attribute from array if attribute_position is less than the selected attribute_position (reset selected attributes)
		selectedAttributes.value = selectedAttributes.value.filter(attr => attr.attribute_position <= attributeItem.attribute_position);

		// add attribute to array if it doesn't already exist, or replace it if it does
		const existingAttribute = selectedAttributes.value.find(attr => attr.attribute_code === attributeItem.attribute_code);
		if (!existingAttribute) {
			selectedAttributes.value.push(attributeItem);
		} else {
			const index = selectedAttributes.value.indexOf(existingAttribute);
			selectedAttributes.value.splice(index, 1, attributeItem);
		}
	}

	// currently selected attribute item
	const selectedItem = computed(() => {
		return selectedAttributes.value.find(attr => attr.attribute_code === props.item.attribute_code);
	});

	// watch for changes in selectedAttributes and update selected state for attribute items
	watch(
		selectedAttributes,
		data => {
			// reset selected state for all attribute items
			items.value.forEach(el => (el.selected = false));

			// set selected state for selected attribute items
			const selectedAttribute = data[props.item.index];
			if (selectedAttribute) {
				const attr = items.value.find(el => el.code == selectedAttribute.code);
				if (attr) attr.selected = true;
			}
		},
		{immediate: true}
	);

	// get available variations based on selected attributes
	const availableVariations = computed(() => {
		if (!items.value?.length) return {};

		const matchingVars = {};
		items.value.forEach(item => {
			matchingVars[item.code] = [];

			// get all selected attributes and add current attribute to the array
			let selectedAttributesIds = [];
			if (item.attribute_index == 0) {
				selectedAttributesIds.push(item.id);
			} else {
				selectedAttributesIds = selectedAttributes.value.map(attr => {
					if (attr.attribute_position < item.attribute_position) return attr.id;
				});
				if (!selectedAttributesIds.includes(item.id)) selectedAttributesIds.push(item.id);
			}

			// get all variations that match the selected attributes
			let match = [];
			variations.value.forEach(variation => {
				const variationAttributesIds = variation.attributes_ids?.split(',');
				if (compareArrays(variationAttributesIds, selectedAttributesIds, {removeEmptyElements: true, mode: 'match'})) {
					match.push(variation.is_available);
				}
			});

			matchingVars[item.code] = match.some(element => element === true) ? true : false;
		});

		return matchingVars;
	});

	// watch for changes in availableVariations and update available state for attribute items
	watch(
		availableVariations,
		data => {
			items.value.forEach(item => {
				item.available = data[item.code];
			});
		},
		{immediate: true}
	);

	// check if arr1 contains the same items as arr2 based on a property
	function containsAllItems(arr1, arr2, prop) {
		return arr2.every(item2 => arr1.some(item1 => item1[prop] === item2[prop]));
	}
</script>
