<template>
	<slot :items="categories" :loading="loading" />
</template>

<script setup>
	const emit = defineEmits(['load']);
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const catalog = useCatalog();
	const {generateThumbs} = useImages();

	const props = defineProps({
		fetch: Object,
		thumbPreset: String,
		dataKey: {
			type: String,
			default: null,
		},
		log: {
			type: Boolean,
			default: false,
		},
	});

	const categories = ref([]);
	const loading = ref(true);
	const res = await catalog.fetchCategories(props.fetch, {dataKey: props.dataKey});
	categories.value = res?.data?.length ? res.data : [];
	loading.value = false;
	await generateImages();

	onMounted(async () => {
		// emit event with data
		emit('load', {items: categories.value});

		// log categories data to console
		if (props.log) useLog(['BaseCatalogCategoriesWidget', categories.value, props.fetch]);
	});

	async function generateImages() {
		// generate thumbs
		if (categories.value?.length && props.thumbPreset) {
			await generateThumbs({
				data: categories.value,
				preset: props.thumbPreset,
				dataKey: props.dataKey ? props.dataKey + '-thumbs' : null,
			});
		}
	}
</script>
