<template>
	<slot :items="items" :counter="counter" :attributes="attributes" :onRemove="onRemove" :loading="loading" :differentAttributes="differentAttributes" :onToggleMode="toggleMode" :mode="mode" />
</template>

<script setup>
	const catalog = useCatalog();
	const {generateThumbs} = useImages();
	const emit = defineEmits(['loadProductsWidget', 'remove']);
	const props = defineProps({
		thumbPreset: String,
		syncRowHeight: {
			type: Boolean,
			default: false,
		},
	});
	const counter = ref(0);
	const loading = ref(false);
	const productsHeight = ref([]);
	const attributes = ref([]);
	const differentAttributes = ref([]);
	const mode = ref('all');
	const items = ref([]);

	// process items when data changes
	watch(
		() => catalog.getComparedProducts(),
		async (newItems, oldItems) => {
			items.value = newItems?.length ? processItems(newItems) : [];

			// generate thumbs
			if (items.value?.length && props.thumbPreset) {
				await generateThumbs({
					data: items.value,
					preset: props.thumbPreset,
				});
			}
		},
		{immediate: true}
	);

	// set current mode
	function toggleMode(value) {
		mode.value = value;
	}

	onMounted(async () => {
		if (!items.value?.length) {
			loading.value = true;
			await catalog.fetchComparedProducts();
			loading.value = false;
		}

		// synchronize row heights when data changes
		if (props.syncRowHeight) {
			watch(
				items,
				() => {
					setTimeout(() => {
						equalRowHeight();
						equalProductHeight();
					}, 200);
				},
				{immediate: true}
			);
		}

		// emit event with initial list
		emit('loadProductsWidget', {items: items.value});
	});

	function processItems(items) {
		const products = [...items];
		counter.value = products.length;
		attributes.value = [];
		productsHeight.value = [];
		differentAttributes.value = [];

		// if there are no products return empty array
		if (!products.length) return [];

		// get all attributes from products and remove duplicates
		products.forEach(item => {
			productsHeight.value.push(item);

			if (item.attributes?.length) {
				item.attributes.forEach(attr => {
					if (!attributes.value.find(a => a.attribute_code === attr.attribute_code)) {
						attributes.value.push(attr);
					}
				});
			}
		});

		// set attributes for each product
		products.forEach(item => {
			let newAttributes = [];
			if (item.attributes?.length) {
				attributes.value.forEach(attr => {
					const foundAttribute = item.attributes.find(a => a.attribute_code === attr.attribute_code);
					newAttributes.push({
						...attr,
						title: foundAttribute ? foundAttribute.title : '',
					});
				});
			}
			item.attributes = newAttributes;
		});

		// set array of different attributes
		products.forEach(item => {
			let differenceItems = [];
			if (item.attributes?.length) {
				item.attributes.forEach(attr => {
					if (!differenceItems.includes(attr.attribute_code)) {
						const foundAttribute = products.find(i => i.attributes.find(a => a.attribute_code === attr.attribute_code && a.title !== attr.title));
						if (foundAttribute) {
							differenceItems.push(attr.attribute_code);
						}
					}
				});
			}
			differentAttributes.value = differenceItems;
		});

		return products;
	}

	// synchronize row heights. :data-compare-attribute="attribute.attribute_code" property needs to be set on each attribute row
	function equalRowHeight() {
		if (attributes.value.length) {
			attributes.value.forEach(item => {
				const rows = document.querySelectorAll(`[data-compare-attribute="${item.attribute_code}"]`);
				if (!rows.length) {
					useLog(`data-compare-attribute="..." property is missing for each attribute row!`, 'warn');
					return;
				}

				let minHeight = 0;
				for (let j = 0; j < rows.length; j++) {
					minHeight = Math.max(minHeight, rows[j].offsetHeight);
				}

				for (let j = 0; j < rows.length; j++) {
					rows[j].style.height = minHeight + 'px';
				}
			});
		}
	}

	// synchronize product heights. :data-compare-product="item.id" property needs to be set on each product
	function equalProductHeight() {
		if (productsHeight.value.length) {
			let maxHeight = 0;
			productsHeight.value.forEach(item => {
				const container = document.querySelectorAll(`[data-compare-product="${item.id}"]`);

				for (let j = 0; j < container.length; j++) {
					maxHeight = Math.max(maxHeight, container[j].offsetHeight);
				}
			});

			productsHeight.value.forEach(item => {
				const container = document.querySelectorAll(`[data-compare-product="${item.id}"]`);

				for (let j = 0; j < container.length; j++) {
					container[j].style.height = maxHeight + 'px';
				}
			});
		}
	}

	// Remove item from compare and refetch products. If item is not provided, remove all items
	async function onRemove(item) {
		// If no item is provided, remove all items
		if (!item) {
			if (!items.value?.length) {
				return useLog('No items to remove', 'warn');
			}

			loading.value = true;

			// Remove all items one by one
			for (const compareItem of items.value) {
				if (compareItem?.shopping_cart_code) {
					await catalog.removeFromCompare({shopping_cart_code: compareItem.shopping_cart_code});
				}
			}

			items.value = [];
			catalog.comparedProducts.value = [];
			loading.value = false;
			return;
		}

		// Remove single item
		if (!item?.shopping_cart_code || !item?.id) {
			return useLog('Shopping cart code or ID is undefined', 'error');
		}

		loading.value = true;
		await catalog.removeFromCompare({shopping_cart_code: item.shopping_cart_code});
		await catalog.fetchComparedProducts({retry: 'remove', itemId: item.id});
		emit('remove', {item, items: items.value});
		loading.value = false;
	}

	// provide data to child components
	provide('baseCatalogCompareData', {loading});
</script>
