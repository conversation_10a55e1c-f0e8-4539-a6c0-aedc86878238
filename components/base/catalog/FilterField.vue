<template>
	<div class="cf-row" :class="level">
		<input type="checkbox" :name="name" :id="'filter-' + name + '-' + item.id" :value="item.filter_url" />
		<label :for="'filter-' + name + '-' + item.id">
			<slot>
				<span class="cf-row-title">{{ item.title }}</span>
				<span class="cf-row-counter">{{ item.total_available }}</span>
			</slot>
		</label>
	</div>
</template>

<script setup>
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
		name: {
			type: String,
			required: true,
		},
	});

	// level css class
	const level = computed(() => (props.item.level ? 'cf-row-level' + props.item.level : ''));
</script>
