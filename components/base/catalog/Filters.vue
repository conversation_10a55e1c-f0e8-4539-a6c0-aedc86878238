<template>
	<slot :onSubmit="onSubmit" :searchFields="searchFields" :selectedFiltersCounter="counter" :productsCounter="productsCounter" :liveProductsCounter="liveProductsCounter" :onClear="onClear" :loading="loading" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {emit, subscribe} = useEventBus();
	const {fetchProducts} = useCatalog();
	const model = defineModel();
	const props = defineProps({
		liveCounter: {
			type: Boolean,
			default: false,
		},
		exclude: {
			type: Array,
			required: false,
		},
		scrollToElement: String,
	});

	// get data from parent components
	const {pagination} = inject('pagination');
	const {category} = inject('baseCatalogCategoryData');
	let {searchFields, loading} = inject('baseCatalogProductsData');

	const globalSelectedFilters = ref({});
	const productsCounter = computed(() => pagination.value?.items?.total || 0);
	const liveProductsCounter = ref(pagination.value?.items?.total || 0);

	// exclude attributes
	if (searchFields.value && props.exclude) {
		searchFields.value = searchFields.value.filter(obj => !props.exclude.includes(obj.code));
	}

	model.value = {
		searchFields,
	};

	// count selected filters
	const counter = computed(() => {
		let count = 0;

		// count selected filters
		for (const [key, value] of Object.entries(globalSelectedFilters.value)) {
			count += value.length;
		}

		return count;
	});

	// if live counter is enabled, watch for changes in selected filters. If changes are detected, count products
	if (props.liveCounter) {
		watch(counter, () => countProducts(), {deep: true});
	}

	// count products for selected filters
	async function countProducts() {
		if (counter.value) {
			let fetchOptions = {
				mode: 'index',
				category_position: category.value.position_h,
				_category_id: category.value.id,
				_search_id: category.value.search_id,
			};
			Object.entries(globalSelectedFilters.value).forEach(el => {
				const value = typeof el[1] != 'string' ? el[1].join(',') : el[1];
				if (el[0] != 'to_page') fetchOptions[el[0]] = value;
			});

			const productsData = await fetchProducts({...fetchOptions, page: null, to_page: null});
			console.log('count products', productsData);
			liveProductsCounter.value = productsData.data?.meta_data?.items_total;
			return liveProductsCounter.value;
		}

		liveProductsCounter.value = pagination.value?.items?.total;
	}

	// submit selected filters
	async function onSubmit() {
		await navigateTo({query: {...route.query, ...globalSelectedFilters.value, to_page: undefined, page: undefined}});
		emit('catalogProductsUpdate', route.query);
	}

	// clear selected filters
	async function onClear() {
		globalSelectedFilters.value = {};
		await navigateTo({query: {to_page: undefined, page: undefined}});
		emit('catalogProductsUpdate', route.query);
	}

	let scrollTimer;
	function scrollToElement() {
		const element = document.querySelector(props.scrollToElement);
		if (scrollTimer) clearTimeout(scrollTimer);
		if (element) {
			scrollTimer = setTimeout(() => {
				element.scrollIntoView({behavior: 'smooth'});
			}, 300);
		}
	}

	if (props.scrollToElement) {
		watch(
			() => globalSelectedFilters.value,
			() => scrollToElement(),
			{deep: true}
		);
	}

	// reset globalSelectedFilters if all filters are cleared from ActiveFilters component
	subscribe('catalogProductsUpdate', data => {
		if (data?.clearAllFilters) globalSelectedFilters.value = {};
	});

	// provide data to child components
	provide('baseFiltersData', {globalSelectedFilters});

	onBeforeUnmount(() => {
		clearTimeout(scrollTimer);
	});
</script>
