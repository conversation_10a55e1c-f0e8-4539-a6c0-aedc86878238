<template>
	<slot :items="products" :meta="meta" :loading="loading" />
</template>

<script setup>
	const config = useAppConfig();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	const catalog = useCatalog();
	const emit = defineEmits(['loadProductsWidget']);
	const model = defineModel();
	const {generateThumbs} = useImages();
	const props = defineProps({
		fetch: Object,
		items: Array,
		transformProducts: Function,
		thumbPreset: String,
		dataKey: {
			type: String,
			default: null,
		},
		gtmTracking: Object,
		log: {
			type: Boolean,
			default: false,
		},
		watchFetch: {
			type: Boolean,
			default: false,
		},
		cache: {
			type: Boolean,
			default: true,
		},
	});

	const products = ref([]);
	const meta = shallowRef(null);
	const loading = ref(true);

	// Check if fetch.list_code has multiple lists because of different data structure
	const isMultiList = Array.isArray(props.fetch?.list_code) && props.fetch.list_code.length > 1;

	// if items are provided, use them and ignore fetch prop. Otherwise fetch items from API
	if (props.items) {
		products.value = props.items;
		model.value = props.items;
	} else {
		await fetchData();
	}

	async function fetchData() {
		loading.value = true;
		const fetchOptions = {
			mode: 'widget',
			...props.fetch,
		};

		if (config?.catalog?.productsResponseFields?.length && !fetchOptions.response_fields) fetchOptions.response_fields = config.catalog.productsResponseFields;

		const productData = await catalog.fetchProducts(fetchOptions, {dataKey: props.dataKey, cache: props.cache});

		// Handle single or multiple list_code
		if (isMultiList) {
			products.value = productData?.data ? productData.data : null;
			meta.value = null;
		} else {
			products.value = productData?.data?.items?.length ? productData.data.items : [];
			meta.value = productData?.data?.meta_data ? productData.data.meta_data : null;
		}

		if (products.value?.length && props.thumbPreset) {
			let productsCopy = [...products.value];
			await generateThumbs({
				data: productsCopy,
				preset: props.thumbPreset,
				dataKey: props.dataKey ? props.dataKey + '-thumbs' : null,
			});
			products.value = productsCopy;
		}

		// Transform products data if transformProducts prop is provided
		if (props.transformProducts) {
			products.value = props.transformProducts(products.value);
		}

		model.value = products.value;
		loading.value = false;
	}

	// update products price and qty (user specific prices)
	async function updatePriceQty() {
		if (!config?.catalog?.updatePriceQty) return;
		if (isMultiList && products.value && typeof products.value === 'object') {
			const updated = {};
			for (const [key, arr] of Object.entries(products.value)) {
				if (Array.isArray(arr) && arr.length) {
					const p = await catalog.updateProductsPriceQty(arr);
					updated[key] = Array.isArray(p) ? [...p] : arr;
				} else {
					updated[key] = arr;
				}
			}
			products.value = updated;
			return;
		}

		const p = await catalog.updateProductsPriceQty(products.value);
		if (p?.length) products.value = [...p];
	}

	onMounted(async () => {
		await updatePriceQty();

		// gtm tracking
		if (isMultiList && products.value && gtm && props.gtmTracking) {
			Object.entries(products.value).forEach(([key, items]) => {
				if (Array.isArray(items) && items.length) {
					gtm.gtmTrack('viewItemList', {items, list: key, ...props.gtmTracking});
				}
			});
		} else if (products.value?.length && props.gtmTracking && gtm) {
			gtm.gtmTrack('viewItemList', {items: products.value, ...props.gtmTracking});
		}

		// emit event with initial list
		emit('loadProductsWidget', {items: products.value, data: props.items ? props.items : props.fetch});

		// log products data to console
		if (props.log) useLog(['BaseCatalogProductsWidget', products.value, props.fetch]);
	});

	// watch if fetch prop has changed and refetch data
	if (props.fetch && props.watchFetch) {
		watch(
			() => props.fetch,
			async () => {
				await fetchData();
				await updatePriceQty();
			}
		);
	}
</script>
