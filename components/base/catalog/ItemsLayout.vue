<template>
	<slot :itemsLayout="layout" :changeItemsLayout="changeItemsLayout" />
</template>

<script setup>
	const props = defineProps({
		default: String,
	});

	const layout = ref(props.default ? props.default : 'grid');

	//add the value to the local storage
	function changeItemsLayout(value) {
		localStorage.setItem('itemsLayout', value);
		layout.value = value;
	}

	onMounted(() => {
		//get initial value from local storage if exist
		if (localStorage && localStorage.getItem('itemsLayout')) {
			layout.value = localStorage.getItem('itemsLayout');
		}
	});

	provide('baseCatalogItemsLayoutData', {layout, changeItemsLayout});
</script>
