<template>
	<slot :onSubmit="onSubmit" :value="inputValue" :onInput="onInput">
		<input type="text" :name="item.filter_url" v-model="inputValue" @keyup="onInput" />
		<button @click="onSubmit">
			<BaseCmsLabel code="confirm_filter" />
		</button>
	</slot>
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {emit} = useEventBus();
	const props = defineProps({
		item: Object,
	});

	const inputValue = ref(null);

	function onInput(e) {
		inputValue.value = e.target.value;
	}

	// submit selected price range
	async function onSubmit() {
		await navigateTo({query: {...route.query, [props.item.filter_url]: inputValue.value, to_page: undefined, page: undefined}});
		emit('catalogProductsUpdate', route.query);
	}
</script>
