<template>
	<slot :items="searchResults" :loading="loading" :onAddToCompare="onAddToCompare" :handleInput="handleInput" :onBlur="onBlur" :selectedIndex="selectedIndex" />
</template>

<script setup>
	const catalog = useCatalog();
	const {generateThumbs} = useImages();
	const props = defineProps({
		item: Object,
		thumbPreset: String,
		limit: {
			type: Number,
			default: 5,
		},
	});
	const searchResults = ref([]);
	const loading = ref(false);
	const searchTerm = defineModel();
	const totalSearchResults = ref(0);
	const selectedIndex = ref(null);
	const selectedItem = ref(null);

	// get data provided by parent component
	const {loading: loadingCompareProducts} = inject('baseCatalogCompareData');

	let typingTimer;
	async function handleInput(event) {
		// clear timer on each keypress
		clearTimeout(typingTimer);

		// proceed only if key is letter, number, backspace, arrow down, arrow up or enter
		if (!/^[a-zA-Z0-9 ]*$/.test(event.key) && !['Backspace', 'ArrowDown', 'ArrowUp', 'Enter', 'Escape'].includes(event.key)) {
			return false;
		}

		// if arrow down or arrow up key is pressed go to next or previous item and set input value to selected item title
		if (event.key == 'ArrowDown' || event.key == 'ArrowUp') {
			if (!searchResults.value?.length) return;

			if (event.key == 'ArrowDown') {
				selectedIndex.value = selectedIndex.value == null ? 0 : selectedIndex.value + 1;
				if (selectedIndex.value > totalSearchResults.value - 1) {
					selectedIndex.value = 0;
				}
				scrollToLocation();
			}

			if (event.key == 'ArrowUp') {
				selectedIndex.value--;
				if (selectedIndex.value < 0) {
					selectedIndex.value = totalSearchResults.value - 1;
				}
				scrollToLocation();
			}

			selectedItem.value = searchResults.value[selectedIndex.value];
			searchTerm.value = selectedItem.value ? selectedItem.value.title : searchTerm.value;
			return false;
		}

		// if enter key is pressed and item is selected, replace compared product with new one
		if (event.key == 'Enter') {
			if (selectedItem.value) onAddToCompare(selectedItem.value);
		}

		// reset autocoplete results if escape key is pressed
		if (!searchTerm.value || event.key == 'Escape') {
			resetAutocomplete();
		}

		// run search function only if typing has stopped for 300ms
		typingTimer = setTimeout(search, 300);
	}

	// fetch data
	async function search() {
		// search only if search term is at least 2 characters
		if (!searchTerm.value || searchTerm.value.trim().length < 2 || / {2,}/.test(searchTerm.value)) return false;

		totalSearchResults.value = 0;
		selectedIndex.value = null;
		loading.value = true;

		const fetchOptions = {
			search_q: searchTerm.value.trim(),
			limit: props.limit,
			mode: 'search',
		};

		// exclude from search products that are already in compare
		const comparedProducts = catalog.getComparedProducts();
		if (comparedProducts?.length) {
			const exclude = comparedProducts.map(item => item.id);
			fetchOptions.id_exclude = exclude;
		}

		const res = await catalog.fetchProducts(fetchOptions);
		totalSearchResults.value = res?.data?.items?.length;
		searchResults.value = res?.data?.items;

		// generate thumbs
		if (searchResults.value && props.thumbPreset) {
			await generateThumbs({
				data: searchResults.value,
				preset: props.thumbPreset,
			});
		}

		loading.value = false;
	}

	// add product to compare
	async function onAddToCompare(item) {
		if (!item) return false;
		loadingCompareProducts.value = true;

		// if item is already in compare, replace it with new one
		if (props.item) {
			await catalog.removeFromCompare({shopping_cart_code: props.item.shopping_cart_code});
		}
		await catalog.addToCompare({shopping_cart_code: item.shopping_cart_code});
		await catalog.fetchComparedProducts({retry: 'add', itemId: item.id});
		resetAutocomplete();
		loadingCompareProducts.value = false;
	}

	// close search results
	function onBlur() {
		setTimeout(() => {
			resetAutocomplete();
		}, 300);
	}

	// scroll to selected item if it is not visible
	function scrollToLocation() {
		const el = document.querySelector('li[data-compare-search-index="' + selectedIndex.value + '"]');
		if (el) el.scrollIntoView({block: 'nearest', inline: 'nearest'});
	}

	function resetAutocomplete() {
		searchTerm.value = '';
		searchResults.value = null;
		totalSearchResults.value = 0;
		selectedIndex.value = null;
		selectedItem.value = null;
	}
</script>
