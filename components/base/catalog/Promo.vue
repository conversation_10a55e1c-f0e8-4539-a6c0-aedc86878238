<template>
	<slot :items="items" />
</template>

<script setup>
	const {generateThumbs} = useImages();
	const {category} = inject('baseCatalogCategoryData');
	const props = defineProps({
		thumbPreset: String,
	});

	const items = ref([]);
	watch(
		category,
		async newVal => {
			if (!newVal) return;
			items.value = newVal?.rotator_elements?.length ? newVal.rotator_elements : [];
			if (items.value?.length && props.thumbPreset) {
				await generateThumbs({
					data: items.value,
					preset: props.thumbPreset,
				});
			}
		},
		{immediate: true}
	);
</script>
