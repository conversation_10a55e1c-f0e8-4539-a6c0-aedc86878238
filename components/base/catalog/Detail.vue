<template>
	<BaseMetaSeo :data="item" />
	<slot :item="item" />
</template>

<script setup>
	const catalog = useCatalog();
	const config = useAppConfig();
	const {generateThumbs} = useImages();
	const {wrapImages} = useText();
	const emit = defineEmits(['load']);

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	let hapi;
	if (__HAPI_TRACKING__) hapi = useHapiTracking();

	let remarketing;
	if (__REMARKETING__) remarketing = useRemarketing();

	const {getCurrency} = useCurrency();
	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();
	const props = defineProps({
		thumbPreset: String,
		wrapImages: Object,
		log: {
			type: Boolean,
			default: false,
		},
	});

	// get product data already fetched on server (app.global)
	const item = useState('product');

	// generate thumbnails
	if (item.value && props.thumbPreset) {
		await generateThumbs({
			data: item.value,
			preset: props.thumbPreset,
		});
	}

	// return product data through v-model
	const model = defineModel();
	model.value = item.value;

	onMounted(async () => {
		// update products price and qty (user specific prices)
		if (config?.catalog?.updatePriceQty) {
			const p = await catalog.updateProductsPriceQty(item.value);
			if (p) item.value = {...p};
		}

		// wrap images (product content) with titles
		wrapImages(props.wrapImages);

		if (item.value && gtm) gtm.gtmTrack('viewItem', {items: item.value});
		if (item.value && remarketing) remarketing.sendEvent('offerdetail', {items: item.value});
		if (item.value?.id && hapi) hapi.sendEvent('catalogproduct', item.value.id);
		if (fbCapi && item.value) {
			fbCapi.sendEvent('viewContent', {
				content_ids: [item.value.code],
				content_category: item.value.category_title,
				content_name: item.value.title,
				content_type: 'product',
				currency: getCurrency()?.code,
				value: item.value.price_custom || 0,
			});
		}

		emit('load', {item: item.value});
		if (props.log) useLog(['BaseCatalogDetail', item.value]);
	});
</script>
