<template>
	<BaseMetaSeo :data="post" />
	<slot :item="post" :locationsUrl="getAppUrl('location')" />
</template>

<script setup>
	const {getAppUrl} = useApiRoutes();
	const post = shallowRef(null);
	const route = useRoute();
	const {getLastUrlSegment} = useUrl();
	const props = defineProps({
		id: Number,
		log: {
			type: Boolean,
			default: false,
		},
	});

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	let hapi;
	if (__HAPI_TRACKING__) hapi = useHapiTracking();

	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();

	// Destructure id from url
	let id = getLastUrlSegment(route.path);
	id = id ? id.split('-').pop() : null;

	// Fetch location point
	const point = await useApi(
		'/api/nuxtapi/location/point/',
		{
			method: 'POST',
			body: {id: props.id || id},
		},
		{cache: true}
	);

	// Send tracking events
	if (gtm) gtm.gtmTrack('pageView', {url: route.fullPath, title: post.value?.title});
	if (fbCapi) fbCapi.sendEvent('pageView', {title: post.value?.title});

	// Set point data
	post.value = point?.data ? point.data : null;

	onMounted(() => {
		if (id && hapi) hapi.sendEvent('location', id);
		if (props.log) useLog(['BaseLocationPoint', post.value]);
	});
</script>
