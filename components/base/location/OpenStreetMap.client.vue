<template>
	<div class="map-container" ref="mapContainer" :class="{'gestures-alert': gesturesAlert}">
		<div class="mapgestures">{{ gesturesAlertMessage }}</div>
		<div :id="props.id" :style="{height: props.height || ''}"></div>
		<Link href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.css" rel="stylesheet" />
	</div>
</template>
<script setup>
	import {renderToString} from '@vue/server-renderer';
	const {addScript, waitForWindowProperty} = useMeta();
	const slots = useSlots();
	const props = defineProps({
		locations: {
			type: Array,
			required: true,
		},
		id: {
			type: String,
			default: 'map',
		},
		height: String,
		pinOptions: Object, // https://leafletjs.com/reference.html#icon
		mapOptions: Object, // https://leafletjs.com/reference.html#map-option
		popupOptions: Object, // https://leafletjs.com/reference.html#popup
		zoomOnMarkerClick: {
			type: Boolean,
			default: true,
		},
		geolocation: {
			type: Boolean,
			default: false,
		},
	});

	let map;
	const markers = new Map();
	const mapContainer = ref(null);
	const gesturesAlert = ref(false);

	function initMap() {
		if (!window.L) return;
		map = L.map(props.id, {scrollWheelZoom: false, ...props.mapOptions});

		L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
			attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>',
		}).addTo(map);
	}

	async function createMarkers(locations) {
		if (!locations?.length || !window.L) return;
		const bounds = L.latLngBounds([]);
		const markerPromises = locations.map(async location => {
			if (!location.gmap?.lat || !location?.gmap.lon) {
				return useLog(['Location does not have latitude or longitude data', location], 'warn');
			}

			// Create marker
			const marker = L.marker([location.gmap.lat, location.gmap.lon], props.pinOptions ? {icon: L.icon(props.pinOptions)} : {}).addTo(map);

			// Get popup content template from slot and add to marker
			const slotContent = slots.default ? slots.default({item: location}) : [];
			const popupContent = slotContent.length > 0 ? await renderToString(slotContent[0]) : '';
			if (popupContent) {
				const popupOptions = props.popupOptions;
				if (props.popupOptions?.offset?.length) popupOptions.offset = L.point(props.popupOptions.offset[0] || 0, props.popupOptions.offset[1] || 0); //offset: L.point(0, -50),
				marker.bindPopup(popupContent, popupOptions);
			}

			if (location.code) markers.set(location.code, marker);

			// Add marker to bounds
			bounds.extend([location.gmap.lat, location.gmap.lon]);

			// Zoom map to marker
			if (props.zoomOnMarkerClick) {
				marker.on('click', () => {
					map.setView(marker.getLatLng(), 15, {animate: true});
				});
			}
		});

		await Promise.all(markerPromises);
		if (bounds.isValid()) {
			map.fitBounds(bounds, {padding: [20, 20]});
		}
	}

	function enableGeolocation() {
		if (!window.L) return;
		if (!navigator.geolocation) {
			return useLog('Geolocation is disabled or not supported by this browser.', 'warn');
		}

		navigator.geolocation.getCurrentPosition(
			position => {
				const userLat = position.coords.latitude;
				const userLon = position.coords.longitude;
				L.marker([userLat, userLon]).addTo(map); // Add a marker for the user location
				map.setView([userLat, userLon], 13); // Center and zoom the map to the user location
			},
			error => {
				useLog(`Failed to retrieve geolocation: ${error.message}`, 'warn');
			}
		);
	}

	// Open the popup for a specific marker
	function openPopup(code) {
		const marker = markers.get(code);
		if (!marker) return useLog(`Marker with code ${code} not found`, 'error');
		marker.openPopup();
		map.setView(marker.getLatLng(), 15, {animate: true}); // Center on the marker
	}

	// Handle gestured on desktop and mobile devices (prevent map dragging scrolling)
	const gesturesAlertMessage = computed(() => {
		const isMobile = /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent);
		return isMobile ? 'Use two fingers to move the map' : 'Use CTRL + scroll to zoom the map';
	});

	function gestures() {
		if (mapContainer.value && window) {
			window.addEventListener('keydown', handleZoom);
			window.addEventListener('keyup', handleZoom);
			mapContainer.value.addEventListener('wheel', handleWheel, {passive: true});
			mapContainer.value.addEventListener('touchstart', handleTouch, {passive: true});
			mapContainer.value.addEventListener('touchend', handleTouch, {passive: true});
		}
	}

	let gestureTimeout;
	function handleWheel() {
		if (!gesturesAlert.value) gesturesAlert.value = true;
		if (gestureTimeout) clearTimeout(gestureTimeout);
		gestureTimeout = setTimeout(() => {
			gesturesAlert.value = false;
		}, 1000);
	}

	let t;
	function handleTouch(event) {
		if (t) clearTimeout(t);
		t = setTimeout(() => {
			if (event.touches.length >= 2) {
				map.dragging.enable();
				gesturesAlert.value = false;
				return;
			}

			map.dragging.disable();
			if (event.touches.length == 1) gesturesAlert.value = true;
			if (!event.touches.length) gesturesAlert.value = false;
		}, 100);
	}

	function handleZoom(event) {
		if (!map) return;
		gesturesAlert.value = false;
		event.ctrlKey || event.metaKey ? map.scrollWheelZoom.enable() : map.scrollWheelZoom.disable();
	}

	// Remove registered event listeners
	onBeforeUnmount(() => {
		window.removeEventListener('keyup', handleZoom);
		window.removeEventListener('keydown', handleZoom);
		mapContainer.value.removeEventListener('wheel', handleWheel);
		mapContainer.value.removeEventListener('touchstart', handleTouch);
		mapContainer.value.removeEventListener('touchend', handleTouch);
	});

	// Install the Leaflet and create markers when the script is ready
	onMounted(async () => {
		addScript({
			src: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.js',
			key: 'leaflet',
		});

		waitForWindowProperty(
			'L',
			L => {
				initMap();
				createMarkers(props.locations);
				if (props.geolocation) enableGeolocation();
				gestures();
			},
			{
				onFailure: () => {
					useLog('Failed to load Leaflet', 'error');
				},
			}
		);
	});

	// Expose the openPopup function to the parent component
	defineExpose({
		openPopup,
	});
</script>

<style lang="less">
	.map-container {
		position: relative;
	}
	.mapgestures {
		display: none;
		position: absolute;
		background: rgba(0, 0, 0, 0.5);
		cursor: grab;
		align-items: center;
		justify-content: center;
		color: #fff;
		font: 20px/1 Arial, sans-serif;
		font-weight: bold;
		z-index: 1001;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		user-select: none;
		-webkit-user-select: none;
		-ms-user-select: none;
		text-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
	}
	.gestures-alert .mapgestures {
		display: flex;
	}
</style>
