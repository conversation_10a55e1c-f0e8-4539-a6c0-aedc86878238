<template>
	<div ref="mapRef" class="map" />
</template>

<script setup>
	import {Loader} from '@googlemaps/js-api-loader';
	import {renderToString} from '@vue/server-renderer';
	const slots = useSlots();
	const {getInfo} = useInfo();

	let mapRef = ref(null);
	let map;
	let markers = [];
	let currentInfoWindow;
	let markersMap = new Map(); // Store markers with their IDs
	const activeMarker = defineModel();

	const props = defineProps({
		locations: {
			type: Array,
			required: true,
		},
		pin: String,
		mapOptions: Object,
		geolocation: {
			type: Boolean,
			default: false,
		},
		apiKey: {
			type: String,
			default: '',
		},
		infoWindow: {
			type: Boolean,
			default: true,
		},
		infoWindowOptions: Object,
		watchLocations: {
			type: Boolean,
			default: false,
		},
	});

	const mapsApiKey = computed(() => {
		if (props.apiKey) {
			if (props.apiKey == 'dev') return '';
			return props.apiKey;
		}

		const key = getInfo('__maps_googleapis_key');
		if (key) return key;

		return '';
	});

	// load google maps api and init map on component mount
	onMounted(async () => {
		const loader = new Loader({
			apiKey: mapsApiKey.value,
			version: 'weekly',
		});

		await loader.load();
		await initMap();
		createMarkers(props.locations);
	});

	// map options: https://developers.google.com/maps/documentation/javascript/reference/map#MapOptions
	async function initMap() {
		const {Map} = await google.maps.importLibrary('maps');
		const {AdvancedMarkerElement} = await google.maps.importLibrary('marker');

		map = new Map(mapRef.value, {
			zoom: props.mapOptions?.zoom ? props.mapOptions.zoom : 15,
			mapId: 'googleMap',
			...props.mapOptions,
		});

		// center map to user location if geolocation is enabled
		if (props.geolocation) centerMapToUserLocation();
	}

	// marker options: https://developers.google.com/maps/documentation/javascript/reference/advanced-markers
	async function createMarkers(locations) {
		const bounds = new google.maps.LatLngBounds();

		// Clear existing markers and info windows
		if (currentInfoWindow) {
			currentInfoWindow.close();
			currentInfoWindow = null;
		}

		// Clear markers from the map and reset collections
		markersMap.forEach(marker => {
			marker.map = null;
		});
		markersMap.clear();

		// create new markers
		await Promise.all(
			locations.map(async location => {
				if (!location.gmap) return useLog(['Location does not have a gmap object', location], 'error');
				const markerConfig = {
					map,
					position: new google.maps.LatLng(location.gmap.lat, location.gmap.lon),
				};
				if (props.pin) {
					const markerImage = document.createElement('img');
					markerImage.src = props.pin;
					markerConfig.content = markerImage;
				}
				const marker = new google.maps.marker.AdvancedMarkerElement(markerConfig);

				// Store marker with its ID
				if (location.code) {
					markersMap.set(location.code, marker);
				}

				// render slot content to string
				const slotContent = slots.default ? slots.default({item: location}) : [];
				const infoWindowContent = await renderToString(slotContent[0]);

				if (props.infoWindow) {
					createInfoWindow(marker, location, infoWindowContent);
				}

				bounds.extend(marker.position);
			})
		);

		// Handle map zoom and center
		if (locations.length === 1) {
			// For single marker, use the marker's position and specified zoom
			const zoomLevel = locations[0].gmap?.zoom || props.mapOptions?.zoom || 15;
			map.setCenter(new google.maps.LatLng(locations[0].gmap.lat, locations[0].gmap.lon));
			map.setZoom(zoomLevel);
		} else {
			// For multiple markers, fit the map to the bounds of all markers
			map.fitBounds(bounds);
		}
	}

	// infoWindow options: https://developers.google.com/maps/documentation/javascript/reference/info-window#InfoWindowOptions
	function createInfoWindow(marker, location, infoWindowContent) {
		// create new InfoWindow
		const infoWindow = new google.maps.InfoWindow({
			content: `<div class="infoBox-window"><div class="infoBox-close">X</div>${infoWindowContent}</div>`,
			...props.infoWindowOptions,
		});

		marker.addListener('gmp-click', () => {
			// close active InfoWindow
			if (currentInfoWindow) {
				currentInfoWindow.close();
			}

			// open new InfoWindow
			infoWindow.open({
				anchor: marker,
				map,
				shouldFocus: false,
			});

			// set the new InfoWindow as the current InfoWindow
			currentInfoWindow = infoWindow;

			// center the map and zoom to the marker
			map.setCenter(marker.position);
			const zoomLevel = location.gmap?.zoom ? Number(location.gmap.zoom) : 15;
			map.setZoom(zoomLevel);

			// Update active marker
			if (location) {
				activeMarker.value = location;
			}
		});

		// align the InfoWindow with the marker
		google.maps.event.addListener(infoWindow, 'domready', () => {
			const infoBoxElement = document.querySelector('.infoBox-window');
			const offsetX = props.infoWindowOptions?.offsetX !== undefined ? props.infoWindowOptions.offsetX : infoBoxElement.offsetWidth / 2;
			const offsetY = props.infoWindowOptions?.offsetY !== undefined ? props.infoWindowOptions.offsetY : infoBoxElement.offsetHeight + 10;

			infoWindow.setOptions({
				pixelOffset: new google.maps.Size(offsetX, offsetY),
			});

			const infoBoxCloseButton = document.querySelector('.infoBox-close');
			infoBoxCloseButton.addEventListener('click', () => {
				infoWindow.close();
				activeMarker.value = null;
			});
		});

		// Add listener for info window close event
		google.maps.event.addListener(infoWindow, 'closeclick', () => {
			activeMarker.value = null;
		});
	}

	// use geolocation to center the map to the user's location
	const centerMapToUserLocation = () => {
		if (navigator.geolocation) {
			navigator.geolocation.getCurrentPosition(
				position => {
					const userLocation = {
						lat: position.coords.latitude,
						lng: position.coords.longitude,
					};
					map.setCenter(userLocation);
				},
				error => {
					useLog(['Error obtaining user location:', error], 'error');
				}
			);
		} else {
			useLog('Geolocation is not supported by this browser.', 'error');
		}
	};

	// Watch for changes in the locations prop if watchLocations is enabled
	if (props.watchLocations) {
		watch(
			() => props.locations,
			async (newLocations, oldLocations) => {
				if (map) {
					// Only update if the locations have actually changed
					const hasChanged = JSON.stringify(newLocations) !== JSON.stringify(oldLocations);
					if (hasChanged) {
						await createMarkers(newLocations);
					}
				}
			}
		);
	}

	// Expose function to open specific marker
	defineExpose({
		openPopup: locationCode => {
			const marker = markersMap.get(locationCode);
			if (marker) {
				// Simulate marker click
				google.maps.event.trigger(marker, 'gmp-click');
			}
		},
	});
</script>

<style lang="less" scoped>
	:deep(.gm-style:has(.infoBox-window)) {
		.gm-style-iw-d,
		.gm-style-iw-c {
			padding: 0 !important;
			border-radius: 0 !important;
			background: none !important;
			box-shadow: none !important;
			max-width: none !important;
			max-height: none !important;
			overflow: visible !important;
		}
		.gm-style-iw-tc {
			display: none;
		}
		.gm-style-iw > button {
			display: none !important;
		}
	}
</style>
