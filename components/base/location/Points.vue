<template>
	<slot :places="places" :items="items" :pageUrl="getAppUrl('location')" />
</template>

<script setup>
	const {getAppUrl} = useApiRoutes();
	const locations = useLocations();
	const {generateThumbs} = useImages();
	const props = defineProps({
		fetch: Object,
		thumbPreset: String,
		dataKey: {
			type: String,
			default: null,
		},
		place: Object,
	});

	const data = ref([]);

	// fetch locations
	const points = await locations.fetch({...props.fetch}, {dataKey: props.dataKey});
	data.value = points.data?.length ? points.data : [];

	// generate thumbs
	if (data.value?.length && props.thumbPreset) {
		await generateThumbs({
			data: data.value,
			preset: props.thumbPreset,
			dataKey: props.dataKey ? props.dataKey + '-thumbs' : null,
		});
	}

	// Return all items or items filtered by place if place prop is defined
	const items = computed(() => {
		if (!props.place) return data.value;
		return data.value.filter(item => item.location_code === props.place.location_code);
	});

	const places = computed(() => {
		if (!data.value?.length) return [];

		return data.value.reduce((accumulator, currentItem) => {
			// check if location already exists
			const existingItem = accumulator.find(item => item.title === currentItem.location_title);

			// if location doesn't exist, create it and add items
			if (!existingItem) {
				accumulator.push({
					id: currentItem.location_id,
					title: currentItem.location_title,
					location_code: currentItem.location_code,
					location_position_h: currentItem.location_position_h,
					location_level: currentItem.location_level,
					url_without_domain: currentItem.url_without_domain,
					items: data.value.filter(item => item.location_code === currentItem.location_code),
				});
			}

			return accumulator;
		}, []);
	});
</script>
