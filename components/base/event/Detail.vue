<template>
	<BaseMetaSeo :data="post" />
	<slot :item="post" />
</template>

<script setup>
	const route = useRoute();
	const {getUrlSegments} = useUrl();
	const {wrapImages} = useText();
	const props = defineProps({
		wrapImages: Object,
		log: {
			type: Boolean,
			default: false,
		},
	});

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	let hapi;
	if (__HAPI_TRACKING__) hapi = useHapiTracking();

	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();

	const post = shallowRef(null);
	const loading = ref(true);

	const eventId = getUrlSegments(route.path, {ignoreLang: true, limit: 1, reverse: true, stringify: true});
	const res = await useApi(
		'/api/nuxtapi/event/event/',
		{
			method: 'POST',
			body: {
				id: eventId,
			},
		},
		{cache: true}
	);
	post.value = res?.data ? res.data : null;
	loading.value = false;

	if (gtm) gtm.gtmTrack('pageView', {url: route.fullPath, title: post.value?.title});
	if (fbCapi) fbCapi.sendEvent('pageView', {title: post.value?.title});

	// wrap images with titles
	onMounted(() => {
		wrapImages(props.wrapImages);
		if (post.value?.id && hapi) hapi.sendEvent('event', post.value.id);
		if (props.log) useLog(['BaseEventDetail', post.value]);
	});
</script>
