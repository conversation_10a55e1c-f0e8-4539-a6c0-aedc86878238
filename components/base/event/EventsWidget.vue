<template>
	<slot :items="posts" :loading="loading" />
</template>

<script setup>
	const posts = shallowRef([]);
	const loading = ref(true);
	const props = defineProps({
		fetch: Object,
	});

	const postsData = await useApi(
		'/api/nuxtapi/event/events/',
		{
			method: 'POST',
			body: {
				mode: 'index',
				...props.fetch,
			},
		},
		{cache: true}
	);
	posts.value = postsData?.data?.items?.length ? [...postsData.data.items] : [];
	loading.value = false;

	// reset data on unmount
	onBeforeUnmount(() => {
		posts.value = [];
	});
</script>
