<template>
	<div class="base-boxnow-parcel-lockers">
		<slot :selectedLocker="selectedLocker" :meta="meta" :errorMessage="errorMessage" />
		<input type="hidden" name="boxnowlocker" v-model="inputValue" />
		<teleport to="body">
			<div id="boxnowmap"></div>
		</teleport>
	</div>
</template>

<script setup>
	import {useField} from 'vee-validate';
	const {addScript, uninstallScript} = useMeta();
	const webshop = useWebshop();
	const emit = defineEmits(['select']);
	const labels = useLabels();

	// define vee input field
	const {
		value: inputValue,
		errorMessage,
		meta,
	} = useField(
		'boxnowlocker',
		value => {
			if (!value || !String(value).trim().length) return labels.get('error_not_empty', 'error_not_empty');
			return true;
		},
		{
			initialValue: getData()?.parcel_locker_id || '',
		}
	);

	function getData() {
		const cartData = webshop.getCartData();
		const selectedShipping = cartData?.parcels[0]?.shipping?.selected;
		if (['boxnow_locker'].includes(selectedShipping?.widget) && selectedShipping?.parcel_locker_info) {
			return selectedShipping.parcel_locker_info;
		}
		return null;
	}
	const selectedLocker = computed(() => getData());

	let timeout = null;
	onMounted(async () => {
		clearTimeout(timeout);

		// Make onLockerSelect globally available
		window.onLockerSelect = onLockerSelect;

		timeout = setTimeout(() => {
			addScript({
				innerHTML: `
				var _bn_map_widget_config = {
					type: "popup",
					parentElement: "#boxnowmap",
					afterSelect: function(selected){
						window.onLockerSelect(selected);
					}
				};
			`,
				key: 'boxnowscript',
				async: true,
				defer: true,
			});

			addScript({
				src: 'https://widget-cdn.boxnow.hr/map-widget/client/v5.js',
				key: 'boxnow',
				async: true,
				defer: true,
			});
		}, 400);
	});

	// Clean up global function on component unmount
	onUnmounted(() => {
		delete window.onLockerSelect;
		uninstallScript({key: 'boxnowscript'});
		uninstallScript({key: 'boxnow'});
	});

	// Save selected locker location
	async function onLockerSelect(event) {
		const data = {
			parcel_locker_type: 'boxnow',
			parcel_locker_id: event?.boxnowLockerId,
			parcel_locker_title: event?.boxnowLockerName,
			parcel_locker_address: event?.boxnowLockerAddressLine1,
			parcel_locker_city: event?.boxnowLockerAddressLine2,
			parcel_locker_zipcode: event?.boxnowLockerPostalCode,
		};
		inputValue.value = event?.boxnowLockerId || '';
		console.log('onLockerSelect', data);
		emit('select', data || null);
	}
</script>
