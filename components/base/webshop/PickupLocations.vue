<template>
	<slot :fields="pickupLocations" :selectedLocation="selectedPickupLocation" :onSelectLocation="onSelectLocation" />
</template>

<script setup>
	const locations = useLocations();
	const labels = useLabels();

	// inject data provided by parent component
	const {parcel, selectedPickupLocation} = inject('baseWebshopShippingFormData');

	// set initial selected pickup location
	const points = ref([]);
	onMounted(async () => {
		const pointsRes = await locations.fetch();
		points.value = pointsRes?.data || []; // fetch all locations to extract additional data (address, phone...)
		if (parcel.value?.shipping?.pickup_location?.selected) {
			selectedPickupLocation.value = parcel.value.shipping.pickup_location.selected;
			appendLocationData();
		}
	});

	const pickupLocations = computed(() => {
		let fields = [
			{
				name: `parcel_${parcel.value.number}_pickup_location`,
				type: 'radio',
				id: '9999',
				value: '',
				title: labels.get('select_pickup_location', '-'),
				selected: !selectedPickupLocation.value?.id ? true : false,
				validation: [
					{
						type: 'not_empty',
						value: null,
						error: 'error_not_empty',
					},
				],
			},
		];

		if (parcel.value?.shipping?.pickup_location?.available) {
			const t = parcel.value.shipping.pickup_location.available.map(item => {
				return {
					...item,
					name: `parcel_${parcel.value.number}_pickup_location`,
					type: 'radio',
					id: item.id,
					value: item.id,
					selected: selectedPickupLocation.value?.id == item.id ? true : false,
					validation: [
						{
							type: 'not_empty',
							value: null,
							error: 'error_not_empty',
						},
					],
				};
			});

			fields = fields.concat(t);
		}

		return fields;
	});

	// selected location
	function onSelectLocation(event) {
		selectedPickupLocation.value = event.target.value ? pickupLocations.value.find(location => location.id == event.target.value) : null;
		appendLocationData();
	}

	// append additional location data to selected pickup location
	function appendLocationData() {
		let tempData = selectedPickupLocation.value;
		if (tempData && points.value?.length) {
			const point = points.value.find(point => point.id == tempData.id);
			if (point) {
				tempData = {
					...tempData,
					...point,
				};
			}
		}

		selectedPickupLocation.value = tempData;
		return tempData;
	}
</script>
