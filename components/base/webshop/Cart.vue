<template>
	<slot :cart="cartData" :counter="counter" :cartUrl="cartUrl" :urls="getAppUrls()" :total="total" :parcels="parcels" :loading="loading" :onFinishShopping="finishShopping" />
</template>

<script setup>
	const emit = defineEmits(['removeProduct', 'updateProduct']);
	const config = useAppConfig();
	const {cart, fetchCart, getCartData} = useWebshop();
	const {getAppUrls} = useApiRoutes();
	const {generateThumbs} = useImages();
	const cartUrl = getAppUrls().webshop_shopping_cart;
	const {getCurrency} = useCurrency();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	let remarketing;
	if (__REMARKETING__) remarketing = useRemarketing();

	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();

	const props = defineProps({
		thumbPreset: String,
		fetchOnMount: {
			type: Boolean,
			default: true,
		},
		gtmTracking: {
			type: Boolean,
			default: false,
		},
		remarketing: {
			type: Boolean,
			default: false,
		},
	});

	const fetchingCart = useState('fetchingCart', () => false);
	const cartData = shallowRef(null);
	const counter = ref(0);
	const total = shallowRef(null);
	const parcels = shallowRef([]);
	const events = ref(null);
	const loading = ref(true);
	const cartViewed = ref(false);

	// fetch initial cart data
	onMounted(async () => {
		if (fetchingCart.value || cart?.value?.cart) return;

		fetchingCart.value = true;
		await fetchCart();
		fetchingCart.value = false;
	});

	// gtm tracking
	function finishShopping() {
		if (gtm) gtm.gtmTrack('beginCheckout', {cart: getCartData()});
		if (fbCapi) {
			const cartProducts = getCartData('products');
			fbCapi.sendEvent('initiateCheckout', {
				content_ids: cartProducts.map(el => el.code),
				contents: cartProducts.map(el => {
					return {
						id: el.code,
						quantity: el.quantity,
						item_price: el.total,
					};
				}),
				value: getCartData().total.total_items_total || 0,
				currency: getCurrency()?.code,
				num_items: getCartData().total.items || 0,
			});
		}
	}

	// watch for events sent from child components and emit. Needed when subcomponent is removed from the DOM.
	watch(
		() => events.value,
		eventValue => {
			if (eventValue?.event) emit(eventValue.event, eventValue);
			events.value = null;
		}
	);

	watch(
		cart,
		async newData => {
			if (newData) {
				cartData.value = newData.cart;
				counter.value = newData.total?.items;
				total.value = newData.total;

				// copy parcels to avoid watch infinite loop by modifying the original data
				const parcelsCopy = newData?.parcels?.length ? JSON.parse(JSON.stringify(newData.parcels)) : [];

				// generate thumbs for all parcel images
				if (parcelsCopy?.length) {
					for (let i = 0; i < parcelsCopy.length; i++) {
						let parcel = parcelsCopy[i];
						if (parcel.items?.length) {
							// move image to parent object so generateThumbs can find it
							parcel.items.map(el => {
								el.image = el.item?.image ? el.item.image : null;
							});

							if (props.thumbPreset) {
								await generateThumbs({
									data: parcel.items,
									preset: props.thumbPreset,
								});
							}
						}
					}
				}
				parcels.value = parcelsCopy;

				// gtm and remarketing tracking only on cart page
				if (!cartViewed.value && (gtm || remarketing)) {
					if (props.gtmTracking && gtm) {
						gtm.gtmTrack('viewCart', {cart: newData});
					}
					if (props.remarketing && remarketing) {
						remarketing.sendEvent('conversionintent', {items: newData?.parcels[0]?.items || [], total: newData.total?.total_items_total || 0});
					}
					cartViewed.value = true;
				}
				loading.value = false;
			}
		},
		{immediate: true, deep: true}
	);

	onUnmounted(() => {
		cartViewed.value = false;
	});

	// provide data to child components
	provide('baseWebshopCartData', {cartData, counter, cartUrl, total, parcels, events});
</script>
