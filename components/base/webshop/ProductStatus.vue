<template>
	<slot :item="status" />
</template>

<script setup>
	const {bus, clearEventBus} = useEventBus();
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
		autoClose: {
			type: Number,
			default: 4000,
		},
	});

	const status = ref();

	function updateStatus() {
		if (bus.value.event != 'webshopAddedToCart' || !props.item.shopping_cart_code) return;

		const productShoppingCartCode = props.item?.shopping_cart_code;
		const productStatus = bus.value.data?.status?.data || null;

		// if multiple products are added to cart
		if (productStatus?.labels_name) {
			status.value = {
				label: productStatus.labels_name[productShoppingCartCode],
			};
		}

		// if only one product is added
		if (productStatus?.label_name) {
			const addedProduct = bus.value.data?.products[0].shopping_cart_code;
			if (productShoppingCartCode == addedProduct) {
				status.value = {
					label: productStatus.label_name,
				};
			}
		}
	}

	onMounted(() => {
		updateStatus();

		if (props.autoClose > 0) {
			setTimeout(() => {
				status.value = null;
				clearEventBus();
			}, props.autoClose);
		}
	});
</script>
