<template>
	<div class="base-gls-parcel-lockers">
		<slot :selectedLocker="selectedLocker" :showMap="showMap" :meta="meta" :errorMessage="errorMessage" />
		<input type="hidden" name="locker" v-model="inputValue" />
		<teleport to="body">
			<gls-dpm-dialog id="gls-parcel-lockers-map" @change="onLockerSelect"></gls-dpm-dialog>
		</teleport>
	</div>
</template>

<script setup>
	import {useField} from 'vee-validate';
	const {addScript} = useMeta();
	const lang = useLang();
	const webshop = useWebshop();
	const currentLanguage = computed(() => lang.get());
	const emit = defineEmits(['select']);
	const labels = useLabels();

	// define vee input field
	const {
		value: inputValue,
		errorMessage,
		meta,
	} = useField(
		'locker',
		value => {
			if (!value || !String(value).trim().length) return labels.get('error_not_empty', 'error_not_empty');
			return true;
		},
		{
			initialValue: getData()?.parcel_locker_id || '',
		}
	);

	function getData() {
		const cartData = webshop.getCartData();
		const selectedShipping = cartData?.parcels[0]?.shipping?.selected;
		if (['gls_locker', 'gls_locker2'].includes(selectedShipping?.widget) && selectedShipping?.parcel_locker_info) {
			return selectedShipping.parcel_locker_info;
		}
		return null;
	}
	const selectedLocker = computed(() => getData());

	// Add GLS DPM script
	let initialValueTimeout = null;
	onMounted(async () => {
		addScript({
			src: 'https://map.gls-hungary.com/widget/gls-dpm.js',
			type: 'module',
			key: 'gls-dpm',
			async: true,
		});
	});

	// Show GLS DPM map modal
	function showMap() {
		const map = document.getElementById('gls-parcel-lockers-map');
		if (map) {
			map.setAttribute('country', currentLanguage.value);
			map.setAttribute('language', currentLanguage.value);
			map.showModal();
		} else {
			console.error('GLS map element not found');
		}
	}

	// Save selected locker location
	async function onLockerSelect(event) {
		const data = {
			parcel_locker_type: 'gls',
			...event.detail,
		};
		inputValue.value = data.id;
		emit('select', data || null);
	}

	defineExpose({
		showMap,
	});
</script>
