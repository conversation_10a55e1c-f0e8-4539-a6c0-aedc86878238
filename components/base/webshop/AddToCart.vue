<template>
	<slot :onAddToCart="addToCart" :loading="loading" :addInProgress="addInProgress" />
</template>

<script setup>
	const webshop = useWebshop();
	const {fetchProducts} = useCatalog();
	const {getAppUrl} = useApiRoutes();
	const eventBus = useEventBus();
	const emit = defineEmits(['addingToCart', 'addedToCart']);

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	const {getCurrency} = useCurrency();
	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();
	const config = useAppConfig();
	const props = defineProps({
		data: {
			type: [Object, Array],
			required: true,
		},
		redirect: [String, Boolean],
		addToCartModal: {
			type: Boolean,
			default: true,
		},
		disabled: {
			type: Boolean,
			default: false,
		},
	});
	const loading = ref(false);

	// watch for adding to cart event
	const addInProgress = ref(false);
	watch(
		() => eventBus.bus.value,
		busValue => (addInProgress.value = busValue.event == 'webshopAddingToCart' ? true : false)
	);

	// add product to cart and emit events
	async function addToCart() {
		if (props.disabled) return;

		// idicate that product is being added to cart (for disabling button on other products)
		if (props.addToCartModal) eventBus.emit('webshopAddingToCart');
		emit('addingToCart');

		// add products to cart
		loading.value = true;
		let products = Array.isArray(props.data) ? props.data : [props.data];

		// if product contains fetchId property, fetch product data
		const productIds = products.filter(product => product.fetchId).map(product => product.fetchId);
		if (productIds?.length) {
			const productsRes = await fetchProducts({mode: 'widget', id: productIds});
			if (productsRes?.data?.items?.length) {
				products = products.map(product => {
					const productData = productsRes.data.items.find(res => res.id == product.fetchId);
					return {
						modalData: productData,
						shopping_cart_code: productData?.shopping_cart_code,
						...product,
					};
				});
			}
		}

		// remove modalData from add to cart request. Add to cart endpoint does not accept modalData. It is used only for modal
		const payload = products.map(product => {
			const productCopy = {
				...product,
				quantity: product.quantity || 1,
			};
			delete productCopy.fetchId;
			delete productCopy.modalData;
			return productCopy;
		});
		const res = await webshop.addProduct(payload);

		// gtm event
		if (res?.success && gtm) {
			const gtmData = products.map(product => {
				return {
					...product.modalData,
					quantity: product.quantity,
				};
			});
			gtm.gtmTrack('addProduct', {items: gtmData});
		}

		// fb capi event
		if (res?.success && fbCapi && config.facebook?.conversionApi?.events?.includes('addToCart')) {
			products.forEach(product => {
				if (product.modalData) {
					fbCapi.sendEvent('addToCart', {
						content_ids: [product.modalData.code],
						content_name: product.modalData.title,
						content_type: 'product',
						currency: getCurrency()?.code,
						quantity: product.quantity,
						value: parseFloat(product.modalData.price_custom || 0) * product.quantity,
					});
				}
			});
		}

		// emit event with added products
		const emitData = {
			products,
			status: res,
		};
		if (props.addToCartModal) eventBus.emit('webshopAddedToCart', emitData);
		emit('addedToCart', emitData);
		loading.value = false;

		// redirect
		if (props.redirect) {
			const redirectUrl = typeof props.redirect == 'string' ? props.redirect : 'webshop_shopping_cart';
			return navigateTo(getAppUrl(redirectUrl));
		}
	}
</script>
