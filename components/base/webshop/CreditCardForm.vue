<template>
	<slot :fields="fields" :selectedCard="selectedCreditCard" />
</template>

<script setup>
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
	});
	const webshop = useWebshop();
	const cartData = computed(() => webshop.getCartData());
	const labels = useLabels();

	// get number of possible installments from cart. If no installments are available, default to 1
	const installments = computed(() => {
		const possibleInstallments = cartData.value?.cart?.payments?.selected?.length ? cartData.value.cart.payments.selected[0].possible_installments : [1];
		return possibleInstallments?.length ? possibleInstallments : [1];
	});

	// get selected number of installmentments from cart
	const selectedInstallment = computed(() => {
		return cartData.value?.cart?.payments?.selected?.length ? cartData.value.cart.payments.selected[0].installments : 1;
	});

	// get selected credit card from cart
	const selectedCreditCard = computed(() => {
		if (cartData.value?.cart?.payments?.selected?.length && cartData.value.cart.payments.selected[0].is_credit_card) {
			const cardOptions = cartData.value.cart.payments.selected[0].options;
			if (cardOptions?.length) {
				return cardOptions.find(option => option.selected);
			}
		}

		return null;
	});

	const singlePayment = labels.get('installments_1');
	const fields = computed(() => {
		let cardFields = [];
		if (props.item?.options?.length) {
			cardFields = props.item.options.map(option => {
				return {
					title: option.payment_title,
					key: option.id,
				};
			});
		}

		const installmentFields = installments.value.map((installment, index) => {
			return {
				title: installment == 1 && singlePayment ? singlePayment : installment, // show label for single payment if available
				key: installment,
			};
		});

		return [
			{
				name: 'payment_option_id',
				type: 'select',
				value: selectedCreditCard.value ? selectedCreditCard.value.id : '',
				options: [
					{
						key: '',
						title: '-',
					},
					...cardFields,
				],
				validation: [
					{
						type: 'not_empty',
						value: null,
						error: 'error_not_empty',
					},
				],
			},
			{
				name: 'cc_installments',
				type: 'select',
				value: selectedInstallment.value,
				options: installmentFields,
				validation: [
					{
						type: 'not_empty',
						value: null,
						error: 'error_not_empty',
					},
				],
			},
		];
	});
</script>
