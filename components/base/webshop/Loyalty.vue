<template>
	<slot :item="loyalty" :loading="loading" :onSubmit="onSubmit" />
</template>

<script setup>
	const {getCartData, submitCustomerData} = useWebshop();
	const endpoints = useEndpoints();
	const {absolute} = useUrl();
	const cartData = computed(() => getCartData());
	const loading = ref(false);
	const barcodePath = useState('baseLoyaltyBarcode', () => null);
	const fetchedBarcode = useState('baseFetchedLoyaltyBarcode', () => false);
	const props = defineProps({
		fetchBarcode: {
			type: Boolean,
			default: false,
		},
	});

	// Combine all loyalty data
	const loyalty = computed(() => {
		const loyaltyData = cartData.value?.cart?.loyalty ? cartData.value?.cart?.loyalty : null;

		// Add totals and barcodePath properties to loyalty data
		if (loyaltyData) {
			const result = {
				...loyaltyData,
				totals: {
					total_extra_loyalty: Number(cartData.value?.total?.total_extra_loyalty) || 0,
					total_extra_loyalty_discount_percent: Number(cartData.value?.total?.total_extra_loyalty_discount_percent) || 0,
					total_extra_loyalty_new: Number(cartData.value?.total?.total_extra_loyalty_new) || 0,
					total_extra_loyalty_new_discount_percent: Number(cartData.value?.total?.total_extra_loyalty_new_discount_percent) || 0,
				},
			};

			// Add barcodePath if defined
			if (barcodePath.value) {
				result.barcode = barcodePath.value;
			}

			return result;
		}

		return null;
	});

	// Fetch barcode image if fetchBarcode prop is true and barcode is not already fetched
	if (props.fetchBarcode && !barcodePath.value) {
		const stopWatcher = watch(
			loyalty,
			async newData => {
				if (newData?.code && !fetchedBarcode.value) {
					fetchedBarcode.value = true;
					loading.value = true;
					const res = await useApi(
						endpoints.get('_post_hapi_misc_barcode'),
						{
							method: 'POST',
							body: {
								code: newData.code,
							},
						},
						{
							cache: true,
						}
					);
					barcodePath.value = res.data?.barcode_path ? absolute(res.data.barcode_path) : null;
					loading.value = false;
					stopWatcher();
				}
			},
			{immediate: true}
		);
	}

	const usePointsActive = computed(() => {
		return cartData.value?.total?.total_extra_loyalty == cartData.value?.cart?.loyalty?.all_points_discount * -1;
	});

	async function onSubmit(options) {
		if (loading.value) return;
		loading.value = true;

		let body = {
			loyalty_request_new: !cartData.value?.cart?.loyalty?.use_loyalty_checked || false, // New user requesting loyalty discount (loyalty discount checkbox)
		};

		if (options.usePoints) {
			body = {use_loyalty_point: !usePointsActive.value};
		}

		const res = await submitCustomerData(body, {type: false});
		loading.value = false;
		return res;
	}
</script>
