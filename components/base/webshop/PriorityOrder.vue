<template>
	<slot :onSubmit="onSubmit" :loading="loading" :isActive="isActive" />
</template>

<script setup>
	const {getCartData, submitCustomerData} = useWebshop();
	const loading = ref(false);

	const isActive = computed(() => {
		const cartTotal = getCartData()?.total;
		return cartTotal?.extraitems?.some(el => el.type === 'extraitem_priority_order' && el.total > 0) || false;
	});

	async function onSubmit() {
		if (loading.value) return; // Prevent double click
		loading.value = true;
		const res = await submitCustomerData(
			{
				priority: !isActive.value,
			},
			{
				type: false,
			}
		);
		loading.value = false;
		return res;
	}
</script>
