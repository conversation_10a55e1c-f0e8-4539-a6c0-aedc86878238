<template>
	<slot :errors="errors" :errorsItems="errorsItems" :warnings="warnings" :warningsItems="warningsItems" />
</template>

<script setup>
	const {getCartData} = useWebshop();
	const cartData = computed(() => getCartData());
	const errors = computed(() => {
		return cartData.value?.cart?.errors?.cart?.length ? cartData.value.cart.errors.cart : null;
	});
	const errorsItems = computed(() => {
		return cartData.value?.cart?.errors?.items?.length ? cartData.value.cart.errors.items : null;
	});
	const warnings = computed(() => {
		return cartData.value?.cart?.warnings?.cart?.length ? cartData.value.cart.warnings.cart : null;
	});
	const warningsItems = computed(() => {
		return cartData.value?.cart?.warnings?.items?.length ? cartData.value.cart.warnings.items : null;
	});
</script>
