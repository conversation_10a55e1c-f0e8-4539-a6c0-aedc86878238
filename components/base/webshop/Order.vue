<template>
	<slot :order="order" :payment="payment" :shipping="shipping" :pickupLocation="pickupLocation" :termsPdf="termsPdf" :invoicePdf="invoicePdf" :paymentTransferUrl="paymentTransferUrl" />
</template>

<script setup>
	const props = defineProps({
		dev: {
			type: Boolean,
			default: false,
		},
	});
	const emit = defineEmits(['load', 'submit']);
	const config = useAppConfig();
	const auth = useAuth();
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const locations = useLocations();
	const {getAppUrl} = useApiRoutes();
	const {order, fetchOrder} = useWebshop();
	const point = ref([]);

	let remarketing;
	if (__REMARKETING__) remarketing = useRemarketing();

	onMounted(async () => {
		if (route?.query?.order_identificator) {
			// cover cases if order_identificator has multiple values separated by '|' (?order_identificator=111|xyz|webshoporder)
			const urlOrderIdentificator = route.query.order_identificator.split('|');
			const orderIdentificator = urlOrderIdentificator?.length > 1 ? `${urlOrderIdentificator[0]}-${urlOrderIdentificator[1]}` : urlOrderIdentificator[0];
			const orderRes = await fetchOrder({code: orderIdentificator});

			// fetch all locations to extract additional data (address, phone...)
			if (orderRes.data?.parcels[0]?.shipping?.pickup_location?.selected) {
				const pointRes = await locations.fetch({id: orderRes.data?.parcels[0]?.shipping?.pickup_location?.selected.id});
				point.value = pointRes?.data;
			}

			if (remarketing) remarketing.sendEvent('conversion', {items: orderRes?.data?.parcels[0]?.items || [], total: orderRes?.data?.total?.total || 0});
		}

		// emit event when form is loaded. Can be used to trigger analytics event or similar
		emit('load', order.value);
	});

	const payment = computed(() => {
		return order.value?.cart?.payments?.selected?.length ? order.value.cart.payments.selected[0] : null;
	});

	const shipping = computed(() => {
		return order.value?.parcels[0]?.shipping?.selected ? order.value.parcels[0].shipping.selected : null;
	});

	const pickupLocation = computed(() => {
		let location = order.value?.parcels[0]?.shipping?.pickup_location?.selected ? order.value.parcels[0].shipping.pickup_location.selected : null;

		// append additional location data
		if (location) {
			location = {
				...location,
				...point.value[0],
			};
		}

		return location;
	});

	const termsPdf = computed(() => order.value?.cart?.terms_pdf_url);
	const invoicePdf = computed(() => order.value?.cart?.invoice_pdf_url);
	const paymentTransferUrl = '/webshop/print-payment-transfer/?order_identificator=' + route?.query?.order_identificator;
</script>
