<template>
	<slot :items="countries" :submitCountry="submitCountry" :selected="selectedCountry" :loading="loading" />
</template>

<script setup>
	const endpoints = useEndpoints();
	const {submitCustomerData, getCartData} = useWebshop();
	const {detectCountryByIP} = useUtils();
	const lang = useLang();
	const props = defineProps({
		ipDetect: {
			type: Boolean,
			default: false,
		},
		init: {
			type: Boolean,
			default: false,
		},
		default: String,
	});

	const loading = ref(false);
	const countriesList = useState('shippingCountries', () => []);

	// Fetch countries from API
	if (props.init) {
		onMounted(async () => {
			loading.value = true;
			const countriesData = await useApi(`${endpoints.get('_get_hapi_webshop_country')}`, null, {cache: true});
			countriesList.value = countriesData?.data?.length ? countriesData.data : [];
			loading.value = false;
		});

		// Set initial country only if customer has no country selected
		watch(
			() => getCartData('customer'),
			async newData => {
				if (!newData) return;
				if (!newData?.address?.country) await setInitialCountry();
			},
			{immediate: true}
		);
	}

	/* 
		Set initial country based on IP detection or default country code
		Use IP detection to initially select country if no country is already selected. If IP detection fails, use default country code
	*/
	async function setInitialCountry() {
		let countryCode = props.default || lang.get('code');

		if (props.ipDetect) {
			const detectedCountryCode = await detectCountryByIP();
			if (detectedCountryCode) countryCode = detectedCountryCode;
		}

		const matchingCountry = countriesList.value.find(country => country.code.toLowerCase() === countryCode);
		if (matchingCountry) await submitCountry({id: matchingCountry.id});
	}

	// List of countries with selected flag
	const countries = computed(() => {
		const selectedCountry = getCartData('customer')?.address?.country;
		return countriesList.value.map(country => {
			return {
				...country,
				selected: selectedCountry == country.id ? true : false,
			};
		});
	});

	// Selected country
	const selectedCountry = computed(() => {
		if (!countries.value?.length) return null;
		return countries.value.find(country => country.selected) || null;
	});

	// Submit country with country ID or code
	async function submitCountry(options) {
		if (!options?.id && !options?.code) {
			return console.error('Country ID or code not provided');
		}
		loading.value = true;

		const countryId = options?.id ? options.id : countries.value.find(country => country.code == options.code)?.id;
		const res = await submitCustomerData(
			{
				country: countryId,
			},
			{
				type: 'webshop.customer-country',
			}
		);
		loading.value = false;
		return res;
	}
</script>
