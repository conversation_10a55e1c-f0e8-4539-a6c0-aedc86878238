<template>
	<slot :data="data" :reload="reload" />
</template>

<script setup>
	const route = useRoute();
	const {getAppUrl} = useApiRoutes();
	const {getUrlSegments} = useUrl();
	const endpoints = useEndpoints();
	const {fetchOrderStatus} = useWebshop();
	const data = ref(null);

	function reload() {
		if (process.client) window.location.reload();
	}

	/* 
		Try to fetch order status every 2 minutes.
		If order status is "thank_you", redirect to thank you page (webshop/ThankYou).
		If order status is "failed_payment", redirect to failed payment page (webshop/FailedPayment).
	*/
	onMounted(async () => {
		const orderId = getUrlSegments(route.path, {reverse: true, limit: 1, stringify: true});
		if (!orderId) {
			console.error('Order ID is not available');
			return;
		}

		const paymentUrl = endpoints.get('_get_hapi_webshop_order_specific_paymentgateway_data').replace('%ID%-%CODE%', orderId);
		const paymentRes = await useApi(paymentUrl, null, {appendLang: true});
		data.value = paymentRes?.data || null;
		if (!data.value) return;

		const status = await fetchOrderStatus({code: orderId});
		if (status.data.next_view == 'thank_you') {
			return await navigateTo({path: getAppUrl('webshop_thank_you'), query: {order_identificator: orderId}});
		}
		if (status.data.next_view == 'failed_payment') {
			return await navigateTo({path: getAppUrl('webshop_failed_payment'), query: {order_identificator: orderId}});
		}
	});
</script>
