<template>
	<slot :order="orderData" :selected="selectedPayment" :fields="fields" :loading="loading" :onSelect="onSelect" :onCancelOrder="onCancelOrder" :onSubmit="onSubmit" :invoiceUrl="invoiceUrl" />
</template>

<script setup>
	const route = useRoute();
	const endpoints = useEndpoints();
	const orderData = ref(null);
	const {fetchOrder, handleOrder} = useWebshop();
	const loading = ref(false);
	const {getAppUrl} = useApiRoutes();
	const selectedPayment = ref(null);

	// Extract payment fields from order
	const fields = computed(() => {
		if (orderData.value?.data?.cart?.payments?.available?.length) {
			return orderData.value.data.cart.payments.available.map(item => {
				return {
					...item,
					name: 'payment',
					type: 'radio',
					value: item.id,
					selected: false,
				};
			});
		}

		return [];
	});

	// Get order id from url. Cover cases if order_identificator has multiple values separated by '|' (?order_identificator=111|xyz|webshoporder)
	let orderId = route.query.order_identificator.split('|');
	orderId = orderId?.length > 1 ? `${orderId[0]}-${orderId[1]}` : orderId[0];

	// Fetch order data
	onMounted(async () => {
		orderData.value = await fetchOrder({code: orderId, newPayment: true});
	});

	const invoiceUrl = computed(() => {
		if (orderData.value?.data?.cart?.invoice_pdf_url) {
			const url = new URL(orderData.value.data.cart.invoice_pdf_url, 'http://example.com');
			return url.pathname;
		}

		return null;
	});

	async function onSubmit() {
		if (!selectedPayment.value || loading.value) return;

		loading.value = true;
		const updateOrderEp = endpoints.get('_put_hapi_webshop_order').replace('%ID%-%CODE%', orderId);

		const requestBody = {
			payment: selectedPayment.value?.id,
			payment_option_id: selectedPayment.value?.payment_option_id ? selectedPayment.value.payment_option_id : 0,
		};
		if (selectedPayment.value?.cc_installments) {
			requestBody.cc_installments = selectedPayment.value?.cc_installments ? selectedPayment.value.cc_installments : 1;
		}

		await useApi(
			updateOrderEp,
			{
				method: 'PUT',
				body: requestBody,
			},
			{lang: false}
		);

		await new Promise(resolve => setTimeout(resolve, 6000));
		orderData.value = await fetchOrder({code: orderId});
		await handleOrder({
			order: orderData.value,
			orderId,
		});

		loading.value = false;
	}

	async function onCancelOrder() {
		if (loading.value) return;
		loading.value = true;

		const updateOrderEp = useEndpoints().get('_put_hapi_webshop_order').replace('%ID%-%CODE%', orderId);
		const order = await useApi(updateOrderEp, {method: 'PUT', body: {status: 'otkazano'}});
		if (order) {
			await new Promise(resolve => setTimeout(resolve, 3000));
			if (order.success) {
				return navigateTo(getAppUrl('webshop_canceled'));
			}
		}

		loading.value = false;
	}

	function onSelect(item) {
		selectedPayment.value = item;
	}
</script>
