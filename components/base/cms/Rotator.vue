<template>
	<slot :items="items" :featuredItems="featured" :loading="loading" />
</template>

<script setup>
	const rotator = useRotator();
	const {generateThumbs} = useImages();
	const config = useAppConfig();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	const emit = defineEmits(['loadRotatorItems', 'fetch']);
	const model = defineModel();
	const props = defineProps({
		fetch: {
			type: Object,
			required: true,
		},
		transformItems: Function,
		transformFeaturedItems: Function,
		featured: Number,
		thumbPreset: String,
		featuredThumbPreset: String,
		ga4: {
			type: String,
			default: '',
		},
		gtmTracking: Object,
		dataKey: {
			type: String,
			default: null,
		},
		log: {
			type: Boolean,
			default: false,
		},
	});

	const loading = ref(true);
	const items = ref([]);
	const featured = ref([]);
	const res = await rotator.fetch(props.fetch, {dataKey: props.dataKey});
	emit('fetch', {items: res?.data?.length ? res.data[0].items : []});

	items.value = res?.data?.length ? res.data[0].items : [];
	if (items.value?.length && props.featured) {
		let d = res.data[0].items;
		featured.value = d.slice(0, props.featured);
		if (props.transformFeaturedItems) featured.value = props.transformFeaturedItems(featured.value);

		items.value = d.slice(props.featured);
	}
	if (props.transformItems) items.value = props.transformItems(items.value);
	loading.value = false;

	await generateImages();

	async function generateImages() {
		const thumbsPromises = [];

		if (items.value?.length && props.thumbPreset) {
			const thumbPromise = generateThumbs({
				data: items.value,
				preset: props.thumbPreset,
				dataKey: props.dataKey ? props.dataKey + '-thumbs' : null,
			});
			thumbsPromises.push(thumbPromise);
		}

		if (featured.value?.length && props.featuredThumbPreset) {
			const featuredThumbPromise = generateThumbs({
				data: featured.value,
				preset: props.featuredThumbPreset,
				dataKey: props.dataKey ? props.dataKey + '-thumbs-featured' : null,
			});
			thumbsPromises.push(featuredThumbPromise);
		}

		if (!thumbsPromises.length) return;

		try {
			await Promise.all(thumbsPromises);
		} catch (error) {
			useLog(['Error generating rotator thumbnails', error], 'error');
		}
	}

	onMounted(async () => {
		// gtm tracking
		if (props.gtmTracking && gtm) gtm.gtmTrack('viewPromotion', props.gtmTracking);

		// emit event with initial list
		emit('loadRotatorItems', {items: res.data?.length ? res.data[0].items : [], creativeSlot: props.ga4});

		// log
		if (props.log) console.log(`BaseCmsRotator ${props.fetch.code}`, items.value);
	});

	model.value = {
		allItems: res.data?.length ? res.data[0].items : [],
		items: items.value,
		featured: featured.value,
	};
</script>
