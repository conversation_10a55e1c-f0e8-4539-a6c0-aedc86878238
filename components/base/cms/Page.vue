<template>
	<BaseMetaSeo :data="data" v-if="seo" />
	<slot :page="data" />
</template>

<script setup>
	const {_route} = useNuxtApp();
	const {getUrlSegments} = useUrl();
	const page = usePage();
	const {generateThumbs} = useImages();
	const {wrapImages} = useText();
	const {bus} = useEventBus();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	let remarketing;
	if (__REMARKETING__) remarketing = useRemarketing();

	let hapi;
	if (__HAPI_TRACKING__) hapi = useHapiTracking();

	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();

	const config = useAppConfig();
	const emit = defineEmits(['load']);
	const props = defineProps({
		fetch: Object,
		fetchSlugSegments: Number,
		seo: {
			type: Boolean,
			default: true,
		},
		thumbPreset: String,
		wrapImages: Object,
		gtmTracking: {
			type: Boolean,
			default: true,
		},
		remarketing: {
			type: Boolean,
			default: true,
		},
		fbTracking: {
			type: Boolean,
			default: true,
		},
	});

	const fetchOptions = {
		...props.fetch,
	};

	// fetch page by defined number of slug segments
	if (props.fetchSlugSegments) {
		fetchOptions.slug = getUrlSegments(_route.path, {ignoreLang: true, limit: props.fetchSlugSegments, stringify: true, addSlashes: true});
	}

	const data = shallowRef(null);
	const pageData = await page.fetch(fetchOptions);
	data.value = pageData;
	_route.meta.pageId = pageData?.id;

	await generateImages();

	// generate thumbs
	async function generateImages() {
		if (data?.value && props.thumbPreset) {
			let dataCopy = {...data.value};
			await generateThumbs({
				data: dataCopy,
				preset: props.thumbPreset,
			});
			data.value = dataCopy;
		}
	}

	watch(
		() => bus?.value,
		async () => {
			if (bus?.value?.event != 'cmsPagesUpdate') return false;
			setTimeout(async () => {
				const pageData = await page.fetch(fetchOptions);
				data.value = pageData;
			}, 100);
		}
	);

	// wrap images with titles
	onMounted(() => {
		wrapImages(props.wrapImages);
		if (props.gtmTracking && gtm) gtm.gtmTrack('pageView', {url: _route.fullPath, title: data.value?.title});
		if (props.remarketing && remarketing) {
			const remarketingEvent = data.value?.template == 'cms/homepage' ? 'home' : 'other';
			remarketing.sendEvent(remarketingEvent, {items: {}});
		}
		if (props.fbTracking && fbCapi) {
			let event = 'pageView';
			if (data.value?.template == 'cms/contact') event = 'contact';
			if (data.value?.template == 'location/index') event = 'findLocation';
			fbCapi.sendEvent(event, {title: data.value?.title});
		}
		if (data.value?.id && hapi) hapi.sendEvent('page', data.value?.id);
		emit('load', data.value ? data.value : null);
	});

	// provide data to child components
	provide('baseCmsPageData', {data: data.value});
</script>
