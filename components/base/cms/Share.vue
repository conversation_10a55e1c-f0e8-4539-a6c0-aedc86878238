<template>
	<slot :items="items" :onShare="onShare" :linkCopy="linkCopy" />
</template>

<script setup>
	const modal = useModal();
	const props = defineProps({
		networks: {
			type: Array,
			default: () => ['facebook', 'whatsapp', 'viber', 'email', 'link'],
		},
		title: String,
		description: String,
		image: String,
	});

	// default network list
	const networksData = {
		facebook: {
			code: 'facebook',
			title: 'Facebook',
		},
		viber: {
			code: 'viber',
			title: 'Viber',
		},
		whatsapp: {
			code: 'whatsapp',
			title: 'WhatsApp',
		},
		linkedin: {
			code: 'linkedin',
			title: 'Linkedin',
		},
		twitter: {
			code: 'twitter',
			title: 'Twitter',
		},
		pinterest: {
			code: 'pinterest',
			title: 'Pinterest',
		},
		email: {
			code: 'email',
			title: 'Email',
		},
		emailPopup: {
			code: 'emailPopup',
			title: 'Email',
		},
		link: {
			code: 'link',
			title: 'Link',
		},
	};

	// generate list of networks to share based on props.networks
	const items = computed(() => {
		let items = [];
		props.networks.forEach(element => {
			if (networksData[element]) items.push(networksData[element]);
		});

		return items;
	});

	// share content event
	async function onShare(network) {
		let networkUrl = '';
		let url = window.location.href;

		// title order: 1. component prop, 2. og:title, 3. meta title
		let title = document.title;
		if (props.title) {
			title = props.title;
		} else if (document.querySelector('meta[property="og:title"]')) {
			title = document.querySelector('meta[property="og:title"]').content;
		}

		// description order: 1. component prop, 2. og:description, 3. meta description
		let description = '';
		if (props.description) {
			description = props.description;
		} else if (document.querySelector('meta[property="og:description"]')) {
			description = document.querySelector('meta[property="og:description"]').content;
		} else if (document.querySelector('meta[name="description"]')) {
			description = document.querySelector('meta[name="description"]').content;
		}

		// image order: 1. component prop, 2. og:image
		let image = '';
		if (props.image) {
			image = props.image;
		} else if (document.querySelector('meta[property="og:image"]')) {
			image = document.querySelector('meta[property="og:image"]').content;
		}

		let domain = encodeURIComponent(window.location.hostname);
		let popup = true;

		if (network.code == 'facebook') {
			networkUrl = 'https://www.facebook.com/sharer/sharer.php?u=' + url + '&title=' + title + '&description=' + description + '&picture=' + image;
		}
		if (network.code == 'twitter') {
			const limit = 130 - decodeURIComponent(url).length - 1;
			title = title.length > limit ? title.substr(0, limit - 7) + '... ' : title + ' - ';
			networkUrl = 'https://twitter.com/intent/tweet?status=' + title + url + '&related=micropat';
		}
		if (network.code == 'linkedin') {
			networkUrl = 'https://www.linkedin.com/shareArticle?mini=true&url=' + url + '&title=' + title + '&summary=' + description + '&source=' + domain;
		}
		if (network.code == 'pinterest') {
			networkUrl = 'https://www.pinterest.com/pin/create/button?url=' + url + '&media=' + image + '&description=' + description;
		}
		if (network.code == 'viber') {
			networkUrl = 'viber://forward?text=' + title + ' ' + url;
			popup = false;
		}
		if (network.code == 'whatsapp') {
			networkUrl = 'whatsapp://send?text=' + title + ' ' + url;
			popup = false;
		}
		if (network.code == 'email') {
			const subject = encodeURIComponent(title);
			const body = encodeURIComponent(`${description}\n\n${url}`);
			networkUrl = `mailto:?subject=${subject}&body=${body}`;
			popup = false;
		}
		if (network.code == 'emailPopup') {
			networkUrl = '';
			modal.open('shareemail');
		}
		if (network.code == 'link') {
			networkUrl = '';
			await copyToClipboard(url);
			popup = false;
		}

		if (networkUrl) {
			popup ? window.open(networkUrl, network, 'menubar=no,width=500,height=495,toolbar=no') : (window.location = networkUrl);
		}

		// GA tracking
		/*
		if (shareConfig.tracking && typeof ga === 'function') {
			ga('send', 'social', network, 'share', url);
		}
		*/
	}

	const linkCopy = ref(false);
	let timer;
	async function copyToClipboard(payload) {
		clearTimeout(timer);
		try {
			await navigator.clipboard.writeText(payload);
			linkCopy.value = true;
			timer = setTimeout(() => {
				linkCopy.value = false;
			}, 3000);
		} catch (err) {
			useLog(['Failed to copy text: ', err], 'error');
		}
	}
</script>
