<template>
	<slot :items="items" :currentLanguage="getLang()" />
</template>

<script setup>
	const config = useAppConfig();
	const {getInfo} = useInfo();
	const route = useRoute();
	const {getLang} = useLang();
	const props = defineProps({
		ignore: Array,
	});

	const items = computed(() => {
		const langs = getInfo('languages');

		// Filter out ignored languages
		if (props.ignore?.length) {
			return langs.filter(lang => !props.ignore.includes(lang.code));
		}

		// Return href lang url if available
		return langs.map(lang => {
			return {
				...lang,
				url: route.meta?.languageUrls?.find(el => el.lang === lang.code)?.url || lang.base_url,
			};
		});
	});
</script>
