<template>
	<template v-for="(part, index) in contentParts" :key="'part-' + index">
		<template v-if="part.type === 'shortcode'">
			<slot name="catalogProduct" :params="part.params"></slot>
		</template>
		<div v-else v-html="part.content" v-interpolation></div>
	</template>
</template>

<script setup>
	const props = defineProps({
		content: {
			type: String,
			default: '',
		},
	});

	// Parse content into parts (text and shortcodes)
	const contentParts = computed(() => {
		if (!props.content) return [];

		const parts = [];
		let lastIndex = 0;
		const regex = /\[CATALOGPRODUCT(?:\s+([^\]]+))?\]/g;

		// Find all instances of CATALOGPRODUCT shortcode
		let match;
		while ((match = regex.exec(props.content)) !== null) {
			// Add text before the shortcode
			if (match.index > lastIndex) {
				parts.push({
					type: 'text',
					content: props.content.substring(lastIndex, match.index),
				});
			}

			// Add the shortcode
			parts.push({
				type: 'shortcode',
				params: parseParams(match[1] || ''),
			});

			lastIndex = match.index + match[0].length;
		}

		// Add remaining text after the last shortcode
		if (lastIndex < props.content.length) {
			parts.push({
				type: 'text',
				content: props.content.substring(lastIndex),
			});
		}

		// If no shortcodes were found, return the original content
		if (parts.length === 0) {
			return [
				{
					type: 'text',
					content: props.content,
				},
			];
		}

		return parts;
	});

	// Parse parameters from shortcode
	function parseParams(paramsString) {
		if (!paramsString) return {};

		const params = {};
		// Match key=value pairs
		const regex = /(\w+)=["']?([^"'\s]+)["']?/g;
		let match;

		while ((match = regex.exec(paramsString)) !== null) {
			params[match[1]] = match[2];
		}

		return params;
	}
</script>
