<template>
	<slot :items="menuItems" :currentUrl="route.path" />
</template>

<script setup>
	const config = useAppConfig();
	const menus = useMenus();
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {generateThumbs} = useImages();
	const props = defineProps({
		fetch: Object,
		code: {
			type: String,
			required: true,
		},
		levelRange: String,
		startPosition: String,
		thumbPreset: String,
		dataKey: {
			type: String,
			default: null,
		},
	});

	// get menu items from api
	const fetchOptions = {
		code: props.code,
	};
	if (props.levelRange) fetchOptions.level_range = props.levelRange;
	if (props.startPosition) fetchOptions.start_position = props.startPosition;

	const menuItems = shallowRef([]);
	const menuData = await menus.fetch(fetchOptions, {dataKey: props.dataKey});
	menuItems.value = menuData?.data?.length ? menuData.data[0].items : [];

	// generate thumbs
	if (menuItems.value?.length && props.thumbPreset) {
		let menuItemsCopy = [...menuItems.value];
		await generateThumbs({
			data: menuItemsCopy,
			preset: props.thumbPreset,
			dataKey: props.dataKey ? props.dataKey + '-thumbs' : null,
		});
		menuItems.value = menuItemsCopy;
	}
</script>
