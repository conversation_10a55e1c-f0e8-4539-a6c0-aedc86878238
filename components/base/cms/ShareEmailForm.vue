<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}">
		<slot :loading="loading" :errors="errors" :meta="meta" :values="values" :fields="fields" :status="status" />
	</BaseForm>
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const config = useAppConfig();
	const form = useSiteform();

	// form submit handler
	const status = ref(null);
	const loading = ref(false);
	async function onSubmit({values}) {
		loading.value = true;
		values.url = config.host + route.fullPath;
		const res = await form.submitForm({type: 'tellafriend', values});
		status.value = res;
		loading.value = false;
	}

	// share email form fields
	const fields = ref([
		{
			name: 'fullname',
			value: '',
			type: 'text',
			validation: [
				{
					type: 'not_empty',
					value: null,
					error: 'error_not_empty',
				},
			],
		},
		{
			name: 'email',
			value: '',
			type: 'email',
			validation: [
				{
					type: 'not_empty',
					value: null,
					error: 'error_not_empty',
				},
				{
					type: 'max_length',
					value: 60,
					error: 'error_max_length',
				},
				{
					type: 'email',
					value: null,
					error: 'error_email',
				},
			],
		},
		{
			name: 'friend_email',
			value: '',
			type: 'email',
			validation: [
				{
					type: 'not_empty',
					value: null,
					error: 'error_not_empty',
				},
				{
					type: 'max_length',
					value: 60,
					error: 'error_max_length',
				},
				{
					type: 'email',
					value: null,
					error: 'error_email',
				},
			],
		},
		{
			name: 'message',
			value: '',
			type: 'textarea',
		},
	]);
</script>
