<template>
	<component v-if="tag" :is="tag" :innerHTML="label" />
	<template v-else>
		{{ label }}
	</template>
</template>

<script setup>
	const labels = useLabels();
	const attrs = useAttrs();
	const {stripHtml} = useText();
	const props = defineProps({
		code: {
			type: String,
			required: true,
		},
		tag: String,
		replace: Array,
		default: {
			type: String,
			default: 'code',
		},
		stripHtml: {
			type: Boolean,
			default: false,
		},
	});

	// use span as default tag if tag prop is not defined
	const tag = computed(() => {
		if (props.tag) return props.tag;
		if (attrs.class) return 'span';
		return null;
	});

	const label = computed(() => {
		let l = labels.get(props.code, props.default);

		// Replace content within label
		if (props.replace) {
			props.replace.forEach(r => {
				Object.entries(r).forEach(([key, value]) => {
					l = l.replaceAll(key, value);
				});
			});
		}

		// Strip HTML if stripHtml prop is true
		if (props.stripHtml) {
			l = stripHtml(l);
		}

		return l;
	});
</script>
