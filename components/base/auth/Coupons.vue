<template>
	<slot :couponsData="couponsData" :items="items" :counter="counter" :onActivate="onActivate" :onDeactivate="onDeactivate" :onRemove="onRemove" :status="status" :loading="loading" :activatedCoupons="activatedCoupons" />
</template>

<script setup>
	const auth = useAuth();
	const webshop = useWebshop();
	const cartData = computed(() => webshop.getCartData());
	const isLoggedIn = computed(() => auth.isLoggedIn());
	const {subscribe} = useEventBus();
	const props = defineProps({
		onlyActive: {
			type: Boolean,
			default: false,
		},
		multipleCoupons: {
			type: Boolean,
			default: false,
		},
	});

	const items = ref([]);
	const couponsData = ref(null);
	const counter = ref(0);
	const loading = ref(true);
	const status = ref();

	// return array of activated coupons (codes)
	const activatedCoupons = computed(() => {
		if (!cartData.value?.total?.extraitems?.length) return;

		let coupons = [];
		const cartExtraItems = cartData.value?.total?.extraitems;
		cartExtraItems.filter(el => {
			if (el.type == 'coupon') coupons.push(el.code);
		});
		return coupons;
	});

	// fetch coupons on component mount
	onMounted(async () => {
		await fetchCoupons();
	});

	// clear status message after 4 seconds
	watch(
		() => status.value,
		() => {
			setTimeout(() => {
				status.value = '';
			}, 4000);
		}
	);

	// listen for couponAdded event and fetch coupons again
	subscribe('couponAdded', async () => {
		await fetchCoupons();
	});

	// fetch coupons from API and set items and counter
	async function fetchCoupons() {
		if (!isLoggedIn.value) return;

		loading.value = true;
		return await auth.fetchCoupons().then(res => {
			// if onlyActive is true, filter out all coupons that are not active by datetime_expire field
			if (props.onlyActive) {
				items.value = res?.data?.items?.filter(el => {
					return el.datetime_expire > new Date().toISOString() || !el.datetime_expire;
				});
			} else {
				items.value = res?.data?.items;
			}
			counter.value = res?.data?.items_total;
			couponsData.value = res?.data;
			loading.value = false;
			return res.data;
		});
	}

	// activate coupon
	async function onActivate(code) {
		loading.value = true;

		// If there are active coupons, remove them and apply the new one. Only if multipleCoupons prop is disabled
		if (!props.multipleCoupons && activatedCoupons.value?.length) {
			for (const couponCode of activatedCoupons.value) {
				await webshop.removeCoupon({code: couponCode});
			}
		}

		// Add the new coupon
		const res = await webshop.addCoupon({code});
		status.value = res.data;
		loading.value = false;
	}

	// deactivate coupon
	async function onDeactivate(code) {
		loading.value = true;
		const res = await webshop.removeCoupon({code});
		status.value = res.data;
		loading.value = false;
	}

	// remove coupon from user profile
	async function onRemove(code) {
		const res = await auth.removeCoupon({code});
		status.value = res.data;
		await fetchCoupons();
	}
</script>
