<template>
	<slot :status="data" :loading="loading" />
</template>

<script setup>
	const auth = useAuth();
	const {_route} = useNuxtApp();
	const {getUrlSegments} = useUrl();
	const {getAppUrl} = useApiRoutes();
	const data = ref();
	const loading = ref(true);
	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();
	const props = defineProps({
		redirect: String,
		redirectTimeout: Number,
	});

	const id = getUrlSegments(_route.path, {ignoreLang: true, limit: 1, offset: 2});
	const code = getUrlSegments(_route.path, {ignoreLang: true, limit: 1, offset: 3});
	let timeoutId;

	if (id && code) {
		try {
			const res = await auth.confirmSignup({id, code});
			data.value = res.data;

			if (res.success && props.redirect) {
				if (props.redirectTimeout) {
					timeoutId = setTimeout(async () => {
						await navigateTo(getAppUrl(props.redirect));
					}, props.redirectTimeout);
				} else {
					await navigateTo(getAppUrl(props.redirect));
				}
			}

			if (fbCapi && res.success) {
				fbCapi.sendEvent('completeRegistration', {
					registration_status: 'confirmed',
				});
			}

			loading.value = false;
		} catch (err) {
			useLog(err);
		}

		loading.value = false;
	} else {
		useLog('Missing signup id or code');
	}

	onBeforeUnmount(() => {
		if (timeoutId) clearTimeout(timeoutId);
	});
</script>
