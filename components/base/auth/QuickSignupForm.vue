<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}">
		<slot :loading="loading" :errors="errors" :meta="meta" :fields="fields" :values="values" :status="status" />
	</BaseForm>
</template>

<script setup>
	const auth = useAuth();
	const webshop = useWebshop();
	const loading = ref(false);
	const status = ref(null);
	const labels = useLabels();
	const props = defineProps({
		showPassword: {
			type: Boolean,
			default: false,
		},
	});

	// fetch form fields
	const fields = ref([]);
	onMounted(async () => {
		const res = await auth.fetchForm({type: 'auth.signup-quick'});
		if (res.data?.length) {
			fields.value = res.data;

			// Append show password field
			if (props.showPassword) {
				const index = fields.value.findIndex(item => item.name === 'password');
				if (index !== -1) {
					fields.value.splice(index + 1, 0, {name: 'show_password', type: 'checkbox', value: false});
				}
			}
		}
	});

	// handle form submit
	async function onSubmit({values, actions}) {
		loading.value = true;

		const customerData = await webshop.fetchCustomer();
		const data = Object.fromEntries(Object.entries(values).map(([key, value]) => [key.replace('gdpr_template-', ''), value]));
		if (props.showPassword) delete data.showPassword;
		const res = await auth.quickSignup({...customerData.data, ...data});
		status.value = res;

		// set field errors if response contains api errors
		if (res?.data?.errors?.length) {
			loading.value = false;
			res.data.errors.forEach(error => {
				actions.setFieldError(error.field, labels.get(error.error, error.error));
			});
			return res;
		}

		loading.value = false;
	}
</script>
