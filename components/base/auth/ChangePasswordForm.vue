<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}">
		<slot :loading="loading" :errors="errors" :meta="meta" :values="values" :apiErrors="apiErrors" :fields="fields" :status="status" />
	</BaseForm>
</template>

<script setup>
	const config = useAppConfig();
	const auth = useAuth();
	const status = ref(null);
	const apiErrors = ref([]);
	const labels = useLabels();
	const loading = ref(false);

	let recaptcha;
	if (__RECAPTCHA__) recaptcha = useRecaptcha();

	// fetch form fields
	const fields = shallowRef([]);

	onMounted(async () => {
		fields.value = await auth.fetchForm({type: 'auth.change-password'}).then(res => res.data);
	});

	// handle form submit
	async function onSubmit({values, actions}) {
		loading.value = true;

		// If recaptcha is enabled, fetch the token and add it to the data
		if (recaptcha) {
			const recaptchaToken = await recaptcha.fetchRecaptchaToken({action: 'auth-change-password'});
			if (recaptchaToken) values['g-recaptcha-response'] = recaptchaToken;
		}

		const res = await auth.changePassword(values);
		apiErrors.value = res.data?.errors ? res.data.errors : [];
		status.value = res;

		// set field errors if response contains api errors
		if (res?.data?.errors?.length) {
			loading.value = false;
			res.data.errors.forEach(error => {
				actions.setFieldError(error.field, labels.get(error.error, error.error));
			});
			return res;
		}

		loading.value = false;
	}
</script>
