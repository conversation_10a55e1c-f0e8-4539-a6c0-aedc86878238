<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}">
		<slot :loading="loading" :formError="error" :errors="errors" :meta="meta" :fields="items" :values="values" :urls="getAppUrls()" />
	</BaseForm>
</template>

<script setup>
	const auth = useAuth();
	const {getAppUrl, getAppUrls} = useApiRoutes();
	const arrayUtils = useArrayUtils();
	const props = defineProps({
		fieldsConfig: Object,
		submitUrl: {
			type: String,
			default: 'auth',
		},
	});
	const config = useAppConfig();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	const error = ref('');
	const loading = ref(false);

	// fetch form fields
	const fields = ref();
	onMounted(async () => {
		fields.value = await auth.fetchForm({type: 'auth.login'}).then(res => res.data);
	});

	const items = computed(() => {
		if (!fields.value) return [];
		const items = fields.value;

		// if fieldConfig.order is set, use it to override the default fields order
		if (props.fieldsConfig?.order) arrayUtils.sortByField(items, props.fieldsConfig.order);
		return items;
	});

	// handle form submit
	async function onSubmit({values}) {
		loading.value = true;

		const userData = {
			email: values.email,
			password: values.password,
			remember: values.remember_me,
		};

		await auth.login(userData).then(async res => {
			// if login was not successful, show error message
			error.value = !res.user_id ? 'login_form_error' : '';

			// if login was successful, redirect to profile page
			if (res.user_id) {
				clearNuxtData(); // Reset api cache to prevent stale data

				if (gtm) gtm.gtmTrack('login', {user_id: res.user_id});
				setTimeout(() => {
					return navigateTo(getAppUrl(props.submitUrl));
				}, 500);
			}
		});

		loading.value = false;
	}
</script>
