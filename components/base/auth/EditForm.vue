<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}">
		<slot :errors="errors" :apiErrors="apiErrors" :meta="meta" :values="values" :loading="loading" :fields="items" :status="status" />
	</BaseForm>
</template>

<script setup>
	const auth = useAuth();
	const arrayUtils = useArrayUtils();
	const props = defineProps({
		fieldsConfig: Object,
	});
	const loading = ref(false);
	const apiErrors = ref([]);
	const status = ref(null);
	const labels = useLabels();
	const emit = defineEmits(['load', 'submit']);

	let recaptcha;
	if (__RECAPTCHA__) recaptcha = useRecaptcha();

	// fetch form fields
	const data = shallowRef([]);

	onMounted(async () => {
		data.value = await auth.fetchForm({type: 'auth.edit'}).then(res => res.data);
	});

	const items = computed(() => {
		if (!data.value) return [];

		const fields = data.value;

		// if fieldConfig.order is set, use it to override the default fields order
		if (props.fieldsConfig?.order) arrayUtils.sortByField(fields, props.fieldsConfig.order);

		// if fieldConfig is set, use it to override the default field config
		if (props.fieldsConfig) {
			fields.map(field => {
				if (props.fieldsConfig?.[field.name]?.type) field.type = props.fieldsConfig[field.name].type;
			});
		}

		emit('load', {fields});
		return fields;
	});

	// handle form submit
	async function onSubmit({values, actions}) {
		loading.value = true;
		let data = {...values};

		// format gdpr consent fields to new array so they can be correctly processed by the API. https://hapi.marker.hr/#/Newsletter/post_v1_newsletter_subscribe_
		data.gdpr_template_api = [];
		Object.entries(data).forEach(([key, value]) => {
			if (key?.startsWith('gdpr_template_api-')) {
				const elName = key.split('-')[1];
				if (elName && value) {
					data.gdpr_template_api.push(elName);
					delete data[key];
				}
			}
		});

		// If recaptcha is enabled, fetch the token and add it to the data
		if (recaptcha) {
			const recaptchaToken = await recaptcha.fetchRecaptchaToken({action: 'auth-edit-profile'});
			if (recaptchaToken) data['g-recaptcha-response'] = recaptchaToken;
		}

		const res = await auth.updateProfile(data);
		apiErrors.value = res.data?.errors ? res.data.errors : [];

		// update user state if success
		if (res?.success) {
			status.value = res;
			await auth.fetchUser();
		}

		// set field errors if response contains api errors
		if (res?.data?.errors?.length) {
			loading.value = false;
			res.data.errors.forEach(error => {
				actions.setFieldError(error.field, labels.get(error.error, error.error));
			});
			return res;
		}

		loading.value = false;
		emit('submit', {values});
	}
</script>
