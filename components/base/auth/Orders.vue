<template>
	<slot :items="items" :counter="counter" :onLoadMore="onLoadMore" :loading="loading" :pagination="pagination" :ordersUrl="getAppUrl('auth_my_webshoporder')" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const {getAppUrl} = useApiRoutes();
	const auth = useAuth();
	const route = nuxtApp._route;
	const {generateThumbs} = useImages();
	const items = ref([]);
	const counter = ref(0);
	const pagination = ref(null);
	const loading = ref(true);
	const props = defineProps({
		thumbPreset: String,
		limit: Number,
	});

	const nextPage = computed(() => {
		return pagination.value?.current_page < pagination.value?.total_pages ? pagination.value.current_page + 1 : '';
	});

	onMounted(async () => {
		await auth.fetchOrders().then(res => {
			if (!res?.data?.items?.length) return (loading.value = false);

			items.value = res?.data?.items;
			if (props.limit && items.value?.length > props.limit) {
				items.value = items.value.slice(0, props.limit);
			}
			counter.value = res?.data?.items_total;
			pagination.value = res.data.pagination;
			pagination.value.next_page = nextPage.value;
			loading.value = false;
		});

		// generate thumbs
		processImages(items.value);
	});

	// fetch more orders
	async function onLoadMore() {
		if (!nextPage.value) return;

		loading.value = true;
		let newItems = [];
		await auth.fetchOrders({page: nextPage.value}).then(res => {
			newItems = res?.data?.items;
			pagination.value = res.data.pagination;
			pagination.value.next_page = nextPage.value;
		});

		processImages(newItems);
		items.value.push(...newItems);
		loading.value = false;
	}

	async function processImages(items) {
		// generate thumbs for each order products
		if (props.thumbPreset && items.length) {
			items.map(item => {
				if (item.order_items) {
					generateThumbs({
						data: item.order_items,
						preset: props.thumbPreset,
					});
				}
			});
		}
	}
</script>
