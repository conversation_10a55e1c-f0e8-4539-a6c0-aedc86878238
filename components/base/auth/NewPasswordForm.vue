<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}">
		<slot :loading="loading" :errors="errors" :meta="meta" :fields="fields" :values="values" :status="status" />
	</BaseForm>
</template>

<script setup>
	const config = useAppConfig();
	const auth = useAuth();
	const {_route} = useNuxtApp();
	const {getAppUrl} = useApiRoutes();
	const {getUrlSegments} = useUrl();
	const loading = ref(false);
	const status = ref(null);

	// check if user that is trying to change password is valid. If not, redirect back to forgotten password page
	const id = getUrlSegments(_route.path, {ignoreLang: true, limit: 1, offset: 2});
	const code = getUrlSegments(_route.path, {ignoreLang: true, limit: 1, offset: 3});
	const validUser = await auth.confirmForgottenPassword({id, code}).then(res => res.success);
	if (!validUser) await navigateTo(getAppUrl('auth_forgotten_password'));

	// redirect to external api url if set
	if (config?.keycloak) {
		const res = await auth.forgottenPasswordApi();
		if (res.data?.external_api_data?.url) {
			await navigateTo(res.data.external_api_data.url, {external: true});
		}
	}

	// fetch form fields if user is valid
	const fields = ref();
	onMounted(async () => {
		fields.value = await auth.fetchForm({type: 'auth.new-password'}).then(res => res.data);
	});

	// handle form submit
	async function onSubmit({values, actions}) {
		loading.value = true;

		// generate new password
		const res = await auth
			.generateNewPassword({
				id: id,
				forgotten_password_code: code,
				password: values.password,
				password_confirm: values.password_confirm,
			})
			.then(res => {
				status.value = res;
				loading.value = false;
				return res;
			});

		// if password is successfully changed, redirect to login page
		if (res.success) {
			setTimeout(() => {
				return navigateTo(getAppUrl('auth_login'));
			}, 5000);
		}
	}
</script>
