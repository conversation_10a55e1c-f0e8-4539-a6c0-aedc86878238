<template>
	<slot :items="items" :counter="counter" />
</template>

<script setup>
	const auth = useAuth();
	const {generateThumbs} = useImages();
	const props = defineProps({
		thumbPreset: String,
		separateByModule: {
			type: Boolean,
			default: false,
		},
	});
	const items = ref([]);
	const counter = ref(0);

	const data = await auth.fetchComments().then(res => res.data);
	counter.value = data?.items_total;
	items.value = data?.items;

	// generate thumbs
	if (items.value.length && props.thumbPreset) {
		await generateThumbs({
			data: items.value,
			preset: props.thumbPreset,
		});
	}

	// separate comments by contenttype
	if (props.separateByModule) {
		const comments = items.value.reduce((acc, item) => {
			const key = item.contenttype;
			if (!acc[key]) acc[key] = [];
			acc[key].push(item);
			return acc;
		}, {});
		items.value = comments;
	}
</script>
