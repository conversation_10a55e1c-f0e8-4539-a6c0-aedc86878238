<template>
	<slot :status="data" :loading="loading" />
</template>

<script setup>
	const {_route} = useNuxtApp();
	const {getUrlSegments} = useUrl();
	const auth = useAuth();
	const data = ref();
	const loading = ref(true);

	const id = getUrlSegments(_route.path, {ignoreLang: true, limit: 1, offset: 2});
	const code = getUrlSegments(_route.path, {ignoreLang: true, limit: 1, offset: 3});

	if (id && code) {
		try {
			await auth.confirmForgottenPassword({id, code}).then(res => {
				data.value = res.data;
				loading.value = false;
			});
		} catch (err) {
			useLog(err);
		}

		loading.value = false;
	} else {
		useLog('Missing signup id or code');
	}
</script>
