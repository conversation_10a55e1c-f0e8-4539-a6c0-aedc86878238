<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}">
		<slot :loading="loading" :errors="errors" :meta="meta" :values="values" :fields="fields" :status="status" :urls="getAppUrls()" />
	</BaseForm>
</template>

<script setup>
	const config = useAppConfig();
	const auth = useAuth();
	const {getAppUrls} = useApiRoutes();
	const status = ref(null);
	const loading = ref(false);
	const labels = useLabels();

	// fetch form fields
	const fields = shallowRef([]);
	onMounted(async () => {
		fields.value = await auth.fetchForm({type: 'auth.forgotten-password'}).then(res => res.data);
	});

	// handle form submit
	async function onSubmit({values, actions}) {
		loading.value = true;
		const res = await auth.forgottenPassword(values);
		status.value = res;

		// set field errors if response contains api errors
		if (!res.success && res.data?.label_name) {
			loading.value = false;
			actions.setFieldError('email', labels.get(res.data?.label_name, res.data?.label_name));
			return res;
		}

		loading.value = false;
	}
</script>
