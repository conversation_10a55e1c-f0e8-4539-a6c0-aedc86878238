<template>
	<slot :socialLogin="socialLogin" :loading="loading" :loadingFacebook="loadingFacebook" :loadingGoogle="loadingGoogle" />
</template>

<script setup>
	const auth = useAuth();
	const loading = ref(false);
	const loadingFacebook = ref(false);
	const loadingGoogle = ref(false);

	// Redirect to provider login page. Providers: Facebook, Google
	async function socialLogin(provider) {
		loading.value = true;
		if (provider === 'Facebook') loadingFacebook.value = true;
		if (provider === 'Google') loadingGoogle.value = true;

		const res = await auth.socialLogin({provider});

		if (res.data?.redirect_url) {
			window.location.href = res.data.redirect_url;
			return res;
		}

		loading.value = false;
		loadingFacebook.value = false;
		loadingGoogle.value = false;
	}
</script>
