<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}">
		<slot :loading="loading" :errors="errors" :meta="meta" :fields="items" :values="values" :apiErrors="apiErrors" :status="status" :contentType="contentType" />
	</BaseForm>
</template>

<script setup>
	const auth = useAuth();
	const nuxtApp = useNuxtApp();
	const arrayUtils = useArrayUtils();
	const props = defineProps({
		fieldsConfig: Object,
	});
	const loading = ref(false);
	const apiErrors = ref([]);
	const status = ref(null);
	const contentType = computed(() => nuxtApp.$appGlobalData.contentType);
	const labels = useLabels();
	const config = useAppConfig();
	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();

	let recaptcha;
	if (__RECAPTCHA__) recaptcha = useRecaptcha();

	const emit = defineEmits(['load', 'submit']);

	// fetch form fields
	const data = shallowRef(null);

	onMounted(async () => {
		data.value = await auth.fetchForm({type: 'auth.signup'});
	});

	const items = computed(() => {
		if (!data.value?.data) return [];

		const fields = data.value.data;

		// if fieldConfig.order is set, use it to override the default fields order
		if (props.fieldsConfig?.order) arrayUtils.sortByField(fields, props.fieldsConfig.order);

		// if fieldConfig is set, use it to override the default field config
		if (props.fieldsConfig) {
			fields.map(field => {
				if (props.fieldsConfig?.[field.name]?.type) field.type = props.fieldsConfig[field.name].type;
			});
		}

		emit('load', {fields});
		return fields;
	});

	// handle form submit
	async function onSubmit({values, actions}) {
		loading.value = true;
		let data = {...values};

		// format gdpr consent fields to new array so they can be correctly processed by the API. https://hapi.marker.hr/#/Newsletter/post_v1_newsletter_subscribe_
		data.gdpr_template_api = [];
		Object.entries(data).forEach(([key, value]) => {
			if (key?.startsWith('gdpr_template_api-')) {
				const elName = key.split('-')[1];
				if (elName && value) {
					data.gdpr_template_api.push(elName);
					delete data[key];
				}
			}
		});

		// If recaptcha is enabled, fetch the token and add it to the data
		if (recaptcha) {
			const recaptchaToken = await recaptcha.fetchRecaptchaToken({action: 'auth-signup'});
			if (recaptchaToken) data['g-recaptcha-response'] = recaptchaToken;
		}

		const res = await auth.signup(data);
		apiErrors.value = res.data?.errors ? res.data.errors : [];
		status.value = res;

		// set field errors if response contains api errors
		if (res?.data?.errors?.length) {
			loading.value = false;
			res.data.errors.forEach(error => {
				actions.setFieldError(error.field, labels.get(error.error, error.error));
			});
			return res;
		}

		if (fbCapi && res.success) {
			fbCapi.sendEvent('completeRegistration', {
				registration_status: 'pending',
			});
		}

		loading.value = false;
		emit('submit', {values});
	}
</script>
