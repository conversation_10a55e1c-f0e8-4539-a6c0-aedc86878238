<template>
	<slot :items="items" />
</template>

<script setup>
	const faq = useFaq();
	const {generateThumbs} = useImages();
	const props = defineProps({
		fetch: Object,
		hierarchy: {
			type: Boolean,
			default: false,
		},
		thumbPreset: String,
	});

	// fetch categories
	const data = await faq.fetchCategories(props.fetch).then(res => res.data);

	// generate thumbs
	if (data?.length && props.thumbPreset) {
		await generateThumbs({
			data: data,
			preset: props.thumbPreset,
		});
	}

	// return items
	const items = computed(() => {
		if (!data?.length) return [];
		if (!props.hierarchy) return data;
		return createHierarchy(data, '');
	});

	// create hierarchy
	function createHierarchy(data, parentPosition) {
		if (!data?.length) return [];
		const hierarchy = [];

		// Check if position_h property exists in the data
		const hasPositionH = data.every(item => item.position_h !== undefined);
		if (!hasPositionH) {
			if (props.hierarchy) {
				console.warn('BaseFaqCategories: hierarchy prop is set but position_h property is missing. Hierarchy cannot be created. Hint: check response_fields data.');
			}
			return data;
		}

		data.filter(item => item.position_h.startsWith(parentPosition)).forEach(item => {
			const childPosition = item.position_h.slice(parentPosition.length);
			if (childPosition.indexOf('.') === -1) {
				hierarchy.push({
					...item,
					children: createHierarchy(data, item.position_h + '.'),
				});
			}
		});

		return hierarchy.length ? hierarchy : data;
	}
</script>
