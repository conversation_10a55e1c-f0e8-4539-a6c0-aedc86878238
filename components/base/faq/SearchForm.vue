<template>
	<slot :onSubmit="onSubmit" :onUpdateValue="onUpdateValue" :searchTerm="searchTerm">
		<form class="faq-search-form" @submit="onSubmit">
			<input class="faq-search-input" name="search_q" type="text" v-model="searchTerm" />
			<button class="faq-search-btn" type="submit" :disabled="!searchTerm"><BaseCmsLabel code="faq_search_submit" /></button>
		</form>
	</slot>
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const searchTerm = ref('');
	const emit = defineEmits(['submit']);

	// update search term value
	function onUpdateValue(event) {
		searchTerm.value = event.target.value;
	}

	// submit search
	async function onSubmit(event) {
		event.preventDefault();
		await navigateTo(route.path + '?search_q=' + searchTerm.value);
		emit('submit', {searchTerm: searchTerm.value});
	}
</script>
