<template>
	<slot :items="items" :loading="loading" />
</template>

<script setup>
	const faq = useFaq();
	const props = defineProps({
		fetch: Object,
		mode: String,
		watchFetch: {
			type: Boolean,
			default: false,
		},
	});

	const loading = ref(false);
	const items = shallowRef(null);

	const fetchData = async () => {
		loading.value = true;
		if (props.mode == 'product') {
			// fetch questions for product. If no questions found, fetch questions for procut category. If no questions found, fetch all questions from specific faq category
			const limit = props.fetch?.limit || 100;
			items.value = await faq.fetchQuestions({catalogproduct_id: props.fetch?.catalogproduct_id, limit: limit}).then(res => res.data);
			if (!items.value?.length) {
				items.value = await faq.fetchQuestions({catalogcategory_id: props.fetch?.catalogcategory_id, limit: limit}).then(res => res.data);
			}
			if (!items.value?.length) {
				items.value = await faq.fetchQuestions({category_code: props.fetch?.category_code, limit: limit}).then(res => res.data);
			}
			if (!items.value?.length && props.fetch?.all) {
				items.value = await faq.fetchQuestions({limit: limit}).then(res => res.data);
			}
		} else {
			items.value = await faq.fetchQuestions(props.fetch).then(res => res.data);
		}
		loading.value = false;
	};

	await fetchData();

	// Watch if fetch prop has changed and refetch data
	if (props.fetch && props.watchFetch) {
		watch(
			() => props.fetch,
			async () => {
				await fetchData();
			}
		);
	}
</script>
