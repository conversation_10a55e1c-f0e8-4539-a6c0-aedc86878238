<template>
	<BaseMetaSeo :data="item" v-if="seo" />
	<slot :item="item" :contentType="contentType" :searchTerm="searchTerm" />
</template>

<script setup>
	const faq = useFaq();
	const page = usePage();
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const emit = defineEmits(['load']);
	const {getUrlSegments} = useUrl();
	const {generateThumbs} = useImages();
	const props = defineProps({
		seo: {
			type: Boolean,
			default: true,
		},
		fetch: Object,
		thumbPreset: String,
	});

	const currentUrl = getUrlSegments(route.path, {ignoreLang: true});
	const contentType = ref(null);
	const searchTerm = ref('');
	let item = null;

	// if url contains only one slug segment, fetch page content
	if (!props.fetch && currentUrl.length === 1) {
		item = await page.fetch();
	}

	const fetchOptions = {
		mode: 'full',
		include_subcategories: true,
		hierarhy_by_position: true,
	};

	// remove root slug from url
	if (!props.fetch?.slug) {
		const slug = getUrlSegments(route.path, {ignoreLang: true, offset: 1, stringify: true});
		if (slug) fetchOptions.slug = slug;
	}

	// if url contains more than one slug segment, fetch category
	if (currentUrl.length > 1) {
		const data = await faq.fetchCategories(fetchOptions).then(res => res.data);

		// generate thumbs
		if (data.length && props.thumbPreset) {
			await generateThumbs({
				data: data,
				preset: props.thumbPreset,
			});
		}

		item = data[0];

		// add children to category
		item.children = data.length > 1 ? data.slice(1) : [];
	}

	// set content type based on url
	watch(
		() => route.query.search_q,
		() => {
			searchTerm.value = route.query.search_q ? route.query.search_q : '';
			if (route.query.search_q) contentType.value = 'search';
			if (!route.query.search_q && currentUrl.length === 1) contentType.value = 'page';
			if (!route.query.search_q && currentUrl.length > 1) contentType.value = 'category';
		},
		{immediate: true}
	);

	onMounted(() => {
		emit('load', item ? item : null);
	});
</script>
