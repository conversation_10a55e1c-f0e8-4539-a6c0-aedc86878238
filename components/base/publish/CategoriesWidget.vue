<template>
	<slot :items="categories" />
</template>

<script setup>
	const publish = usePublish();
	const {generateThumbs} = useImages();
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const props = defineProps({
		fetch: Object,
		items: Array,
		thumbPreset: String,
		dataKey: {
			type: String,
			default: null,
		},
	});

	// get categories. Ignore fetch if props.items is provided
	const categories = ref([]);

	if (props.items) {
		categories.value = props.items;
		await generateImages();
	} else {
		const publishCategories = await publish.fetchCategories({...props.fetch}, {dataKey: props.dataKey});
		categories.value = publishCategories?.data?.length ? publishCategories.data : [];
		await generateImages();
	}

	async function generateImages() {
		// generate thumbs
		if (categories?.value?.length && props.thumbPreset) {
			await generateThumbs({
				data: categories.value,
				preset: props.thumbPreset,
				dataKey: props.dataKey ? props.dataKey + '-thumbs' : null,
			});
		}
	}
</script>
