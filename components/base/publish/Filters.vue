<template>
	<slot :onSubmit="onSubmit" :searchFields="searchFields" :selectedFiltersCounter="counter" :postsCounter="postsCounter" :onClear="onClear" :loading="loading" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {emit, subscribe} = useEventBus();
	const model = defineModel();
	const props = defineProps({
		scrollToElement: String,
	});

	// get data from parent components
	const {pagination} = inject('pagination');
	const {category} = inject('basePublishCategoryData', {category: null});
	let {searchFields, loading} = inject('basePublishPostsData');

	const globalSelectedFilters = ref({});
	const postsCounter = computed(() => pagination.value?.items?.total || 0);

	model.value = {
		searchFields,
	};

	// count selected filters
	const counter = computed(() => {
		let count = 0;

		// count selected filters
		for (const [key, value] of Object.entries(globalSelectedFilters.value)) {
			count += value.length;
		}

		return count;
	});

	// submit selected filters
	async function onSubmit() {
		await navigateTo({query: {...route.query, ...globalSelectedFilters.value, to_page: undefined, page: undefined}});
		emit('publishPostsUpdate', route.query);
	}

	// clear selected filters
	async function onClear() {
		globalSelectedFilters.value = {};
		await navigateTo({query: {to_page: undefined, page: undefined}});
		emit('publishPostsUpdate', route.query);
	}

	let scrollTimer;
	function scrollToElement() {
		const element = document.querySelector(props.scrollToElement);
		if (scrollTimer) clearTimeout(scrollTimer);
		if (element) {
			scrollTimer = setTimeout(() => {
				element.scrollIntoView({behavior: 'smooth'});
			}, 300);
		}
	}

	if (props.scrollToElement) {
		watch(
			() => globalSelectedFilters.value,
			() => scrollToElement(),
			{deep: true}
		);
	}

	// reset globalSelectedFilters if all filters are cleared from ActiveFilters component
	subscribe('publishPostsUpdate', data => {
		if (data?.clearAllFilters) globalSelectedFilters.value = {};
	});

	// provide data to child components
	provide('basePublishFiltersData', {globalSelectedFilters});

	onBeforeUnmount(() => {
		clearTimeout(scrollTimer);
	});
</script>
