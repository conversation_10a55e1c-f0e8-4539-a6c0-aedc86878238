<template>
	<BaseMetaSeo :data="post" />
	<slot :item="post" />
</template>

<script setup>
	const publish = usePublish();
	const {generateThumbs} = useImages();
	const {wrapImages} = useText();
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const config = useAppConfig();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	let hapi;
	if (__HAPI_TRACKING__) hapi = useHapiTracking();

	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();
	const emit = defineEmits(['data']);
	const props = defineProps({
		fetch: Object,
		thumbPreset: String,
		rootCategory: {
			type: Boolean,
			default: false,
		},
		wrapImages: Object,
		log: {
			type: Boolean,
			default: false,
		},
	});

	// fetch post data
	const post = ref({});

	if (props.fetch) {
		const data = await publish.fetchPost();
		post.value = data;
	} else {
		const p = useState('publishPost');
		post.value = p.value;
		if (gtm) gtm.gtmTrack('pageView', {url: route.fullPath, title: post.value?.title});
		if (fbCapi) fbCapi.sendEvent('pageView', {title: post.value?.title});
	}

	// generate thumbnails if needed
	if (props.thumbPreset) {
		await generateThumbs({
			data: post.value,
			preset: props.thumbPreset,
		});
	}

	// wrap images with titles
	onMounted(() => {
		wrapImages(props.wrapImages);
		if (post.value?.id && hapi) hapi.sendEvent('publish', post.value.id);
		if (props.log) useLog(['BasePublishDetail', post.value]);
	});

	// provide root category data
	if (props.rootCategory && post.value.category_position_h) {
		const rootCategoryPosition = post.value?.category_position_h?.split('.')[0] || null;
		if (rootCategoryPosition) {
			const rootCategory = await publish.fetchCategories({
				position: rootCategoryPosition,
				single: true,
			});
			post.value.root_category = rootCategory?.data?.length ? rootCategory.data[0] : null;
		}
	}

	// emit data to parent
	emit('data', post);
</script>
