<template>
	<slot :items="items" :onRemove="onRemove" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {emit} = useEventBus();
	const items = shallowRef([]);

	// get data provided by parent CatalogPosts component
	const {searchFieldsMeta} = inject('basePublishPostsData');

	// update items when searchFieldsMeta changes
	watchEffect(() => {
		items.value = searchFieldsMeta.value?.selected ? searchFieldsMeta.value.selected : [];
	});

	// remove filter from url and emit event to update products
	async function onRemove(item) {
		let query = {};

		// remove selected item from query. If item is not provided, remove all filters
		if (item) {
			const fieldTitle = item.attribute_slug;

			// clone query object
			query = route?.query ? JSON.parse(JSON.stringify(route.query)) : {};

			if (query.hasOwnProperty(fieldTitle)) {
				if (typeof query[fieldTitle] == 'string') {
					query[fieldTitle] = undefined;
				} else if (Array.isArray(query[fieldTitle])) {
					const index = query[fieldTitle].indexOf(item.slug);
					if (index > -1) {
						query[fieldTitle].splice(index, 1);
					}
				}
			}
		}

		// reset pagination
		query.to_page = undefined;
		query.page = undefined;

		// keep sort and search term
		if (route.query?.sort) query.sort = route.query.sort;
		if (route.query?.search_q) query.search_q = route.query.search_q;

		// update url
		await navigateTo({query});

		// emit event to update products
		emit('publishPostsUpdate', {
			...query,
			clearAllFilters: item ? false : true, // this will trigger globalSelectedFilters reset
		});
	}
</script>
