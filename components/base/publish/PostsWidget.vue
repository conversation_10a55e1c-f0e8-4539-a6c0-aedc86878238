<template>
	<slot :items="posts" :featuredPosts="featuredPosts" />
</template>

<script setup>
	const config = useAppConfig();
	const {fetchPosts} = usePublish();
	const {generateThumbs} = useImages();

	const props = defineProps({
		fetch: Object,
		items: Array,
		thumbPreset: String,
		featuredPosts: [String, Number],
		featuredThumbPreset: String,
		watchFetch: {
			type: Boolean,
			default: false,
		},
		dataKey: {
			type: String,
			default: null,
		},
		log: {
			type: Boolean,
			default: false,
		},
	});

	// fetch posts
	let fetchOptions = {
		mode: 'widget',
		...props.fetch,
	};
	if (config?.publish?.postsResponseFields?.length && !fetchOptions.response_fields) fetchOptions.response_fields = config.publish.postsResponseFields;

	const postsData = shallowRef([]);
	postsData.value = await fetchPosts(fetchOptions, {dataKey: props.dataKey});

	// posts
	const posts = computed(() => {
		if (!props.items && !postsData.value?.data) return [];

		const data = props.items ? props.items : postsData.value?.data?.items;
		return props.featuredPosts ? data.slice(props.featuredPosts) : data;
	});

	// featured posts
	const featuredPosts = computed(() => {
		if (!props.featuredPosts && (!props.items || !postsData.value?.data)) return [];

		const data = props.items ? props.items : postsData.value?.data?.items;
		return data?.length ? data.slice(0, props.featuredPosts) : [];
	});

	await generateImages();
	await generateFeaturedImages();

	onMounted(() => {
		if (props.log) useLog(['BasePublishPostsWidget', postsData.value.data, fetchOptions]);
	});

	// Watch if fetch prop has changed and refetch data
	if (props.fetch && props.watchFetch) {
		watch(
			() => props.fetch,
			async (newFetch, oldFetch) => {
				postsData.value = await fetchPosts({mode: 'widget', ...newFetch}, {dataKey: props.dataKey});
			}
		);
	}

	async function generateImages() {
		if (posts.value?.length && props.thumbPreset) {
			await generateThumbs({
				data: posts.value,
				preset: props.thumbPreset,
				dataKey: props.dataKey ? props.dataKey + '-thumbs' : null,
			});
		}
	}

	async function generateFeaturedImages() {
		if (featuredPosts.value?.length && props.featuredThumbPreset) {
			await generateThumbs({
				data: featuredPosts.value,
				preset: props.featuredThumbPreset,
				dataKey: props.dataKey ? props.dataKey + '-featured-thumbs' : null,
			});
		}
	}
</script>
