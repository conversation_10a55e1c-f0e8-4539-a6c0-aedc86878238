<template>
	<slot :items="authors" />
</template>

<script setup>
	const {getUrlSegments} = useUrl();
	const route = useRoute();
	const props = defineProps({
		fetch: Object,
		useSlug: {
			type: Boolean,
			default: false,
		},
	});

	const fetchOptions = {};
	if (props.useSlug) {
		const slug = getUrlSegments(route.path, {ignoreLang: true, limit: 1, reverse: true, stringify: true});
		fetchOptions.slug = slug;
	}

	const data = await useApi('/api/nuxtapi/publish/authors/', {
		method: 'POST',
		body: fetchOptions,
	});

	const authors = data?.data?.length ? data.data : [];
</script>
