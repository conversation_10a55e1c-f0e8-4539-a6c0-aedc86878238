<template>
	<Html :lang="language.get('locale')" />
	<Title v-if="title">{{ title }}</Title>
	<Meta name="format-detection" content="telephone=no" />
	<Meta name="viewport" :content="config.meta.viewport" />
	<Meta name="author" content="Marker.hr" />
	<Meta v-if="seoDescription" name="description" :content="seoDescription" />
	<Meta v-if="seoKeywords" name="keywords" :content="seoKeywords" />
	<Meta v-if="ogTitle" property="og:title" :content="ogTitle.trim()" />
	<Meta property="og:type" content="website" />
	<Meta v-if="siteName" property="og:site_name" :content="siteName" />
	<Meta v-if="url" property="og:url" :content="url" />
	<Meta v-if="ogDescription" property="og:description" :content="ogDescription.trim()" />
	<Meta v-if="image" property="og:image" :content="image" />
	<Meta v-if="image" property="twitter:image" :content="image" />
	<Link v-if="image" rel="image_src" :href="image" />
	<Meta v-if="url" property="twitter:url" :content="url" />
	<Meta v-if="robots" name="robots" :content="robots" />
	<Link v-if="canonicalUrl" rel="canonical" :href="canonicalUrl" />
	<template v-if="config.multilanguage && languageUrls?.length">
		<Link v-for="langItem in languageUrls" :key="langItem.lang" rel="alternate" :hreflang="langItem.lang" :href="langItem.url" />
	</template>
</template>

<script setup>
	const {absolute} = useUrl();
	const {getInfo} = useInfo();
	const language = useLang();
	const nuxtApp = useNuxtApp();
	const config = useAppConfig();
	const route = nuxtApp._route;
	const props = defineProps(['data']);

	const siteName = computed(() => {
		const siteUrl = getInfo('site_url');
		return siteUrl.split('.').slice(-2).join('.');
	});

	// if seo_title is not set, use title
	const title = computed(() => {
		if (props.data?.seo_title) return props.data.seo_title;
		if (props.data?.title) return props.data.title;
		return '';
	});

	const url = computed(() => {
		if (props.data?.url) return props.data.url;
		return '';
	});

	const seoDescription = computed(() => {
		if (props.data?.seo_description) return props.data.seo_description.trim();
		if (props.data?.description) return props.data.description.trim();
		return '';
	});

	const seoKeywords = computed(() => {
		if (props.data?.seo_keywords) return props.data.seo_keywords.trim();
		if (props.data?.keywords) return props.data.keywords.trim();
		return '';
	});

	const ogTitle = computed(() => {
		if (props.data?.seo_og_title) return props.data.seo_og_title;
		if (props.data?.seo_title) return props.data.seo_title;
		if (props.data?.title) return props.data.title;
		return '';
	});

	const ogDescription = computed(() => {
		if (props.data?.seo_og_description) return props.data.seo_og_description;
		if (props.data?.seo_description) return props.data.seo_description;
		return '';
	});

	const image = computed(() => {
		if (props.data?.seo_og_image) return absolute(props.data.seo_og_image);
		if (props.data?.main_image_upload_path) return absolute(props.data.main_image_upload_path);
		return getInfo('site_url') + '/images/logo-big.png';
	});

	const canonicalUrl = computed(() => {
		if (route.query && Object.keys(route.query).length > 0) return route.path;
		if (props.data?.seo_canonical_url) return props.data.seo_canonical_url;
		return '';
	});

	const robots = computed(() => {
		if (props.data?.seo_robots) return props.data.seo_robots.replace(['no', 'nf'], ['noindex', 'nofollow']);
		return '';
	});

	const languageUrls = computed(() => {
		const links = props.data?.languages_urls;
		if (links) {
			const data = Object.entries(links).map(lang => {
				return {
					lang: lang[0],
					url: lang[1],
				};
			});
			return data.filter(d => d.url);
		}
		return [];
	});
</script>
