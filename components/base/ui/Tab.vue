<template>
	<div :class="['tab-content', {'active': isActive}, {'accordion': rwd}]">
		<div class="tab-title" v-show="rwd" v-html="title" @click="setActiveTab(identifier)" />
		<div class="tab-desc">
			<slot />
		</div>
	</div>
</template>

<script setup>
	const props = defineProps({
		title: {
			type: String,
			default: '',
			required: true,
		},
		id: {
			type: [String, Number],
			default: null,
		},
		active: {
			type: Boolean,
			default: false,
		},
	});

	// get data provided by parent component
	const {activeTab, rwd, toggleAccordion} = inject('baseUiTabsData');

	// Use id if provided, fallback to title
	const identifier = computed(() => props.id ?? props.title);
	const activeAccordion = ref(activeTab.value == identifier.value ? true : false);

	// send active tab data back to parent
	function setActiveTab(id) {
		activeAccordion.value = !activeAccordion.value;
		activeTab.value = id;
	}

	const isActive = computed(() => {
		if (rwd.value && toggleAccordion) {
			return activeAccordion.value;
		}
		return activeTab.value == identifier.value;
	});
</script>

<style lang="less" scoped>
	.tab-content:not(.accordion) {
		display: none;
	}
	.tab-content.active {
		display: block;
	}
	.accordion {
		.tab-desc {
			display: none;
		}
		&.active .tab-desc {
			display: block;
		}
	}
</style>
