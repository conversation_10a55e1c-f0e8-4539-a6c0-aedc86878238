<template>
	<div
		:class="{
			'scroll': navScroll,
			'scroll-left': isScrollBeginning,
			'scroll-right': isScrollEnd,
		}">
		<ul class="tabs-nav" v-show="!rwd" @scroll="onNavScroll" ref="navRef">
			<li :class="['tab-nav', tab.props.class, {'active': activeTab == getTabIdentifier(tab)}]" v-for="(tab, index) in tabs" :key="index" @click="activeTab = getTabIdentifier(tab)">
				<span v-html="tab.props.title" />
			</li>
		</ul>
		<div class="tabs-content">
			<slot />
		</div>
	</div>
</template>

<script setup>
	const emit = defineEmits(['tabChange']);
	const props = defineProps({
		closed: {
			// all tabs closed by default
			type: Boolean,
			default: false,
		},
		breakpoint: [String, Boolean],
		toggleAccordion: {
			// allow multiple tabs to be open at the same time
			type: Boolean,
			default: false,
		},
	});
	const slots = useSlots();

	// Filter out non-rendered tabs (v-if false, comments, fragments)
	const getAllTabs = () => {
		const slotContent = slots.default()[0].children.default ? slots.default() : slots.default()[0].children;
		return slotContent.filter(tab => {
			// Only include actual Tab components that are rendered
			// Filter out comment nodes, fragments, and components without props
			return tab && tab.type && tab.props && typeof tab.props === 'object' && tab.props.title !== undefined;
		});
	};

	const tabs = computed(() => getAllTabs());
	const activeTab = ref();
	const navRef = ref();
	const navScroll = ref(false);
	const isScrollBeginning = ref(true);
	const isScrollEnd = ref(false);

	// Helper function to get tab identifier. Use id if provided, fallback to title
	function getTabIdentifier(tab) {
		return tab.props.id ?? tab.props.title;
	}

	// emit event on tab change
	watch(
		() => activeTab.value,
		() => emit('tabChange', activeTab.value)
	);

	// set active tab - use watchEffect to react to tabs changes
	watchEffect(() => {
		const currentTabs = tabs.value;
		if (!props.closed && currentTabs.length > 0) {
			// Set first tab as active if no active tab is set
			if (!activeTab.value) {
				activeTab.value = getTabIdentifier(currentTabs[0]);
			}

			// Check if any tab has active prop and set it as active
			currentTabs.forEach(el => {
				if (el.props.active || el.props.active == '') {
					activeTab.value = getTabIdentifier(el);
				}
			});

			// If current active tab is no longer available, set first available tab as active
			const activeTabExists = currentTabs.some(tab => getTabIdentifier(tab) === activeTab.value);
			if (!activeTabExists && currentTabs.length > 0) {
				activeTab.value = getTabIdentifier(currentTabs[0]);
			}
		}
	});

	// rwd
	const rwd = ref();
	if (props.breakpoint) {
		const {onMediaQuery} = useDom();
		onMediaQuery({
			query: props.breakpoint,
			enter: () => (rwd.value = true),
			leave: () => (rwd.value = false),
		});
	}

	// provide current active tab to child component
	provide('baseUiTabsData', {
		activeTab,
		rwd,
		toggleAccordion: props.toggleAccordion,
	});

	// Expose function to set active tab
	function setActiveTab(tabIdentifier) {
		activeTab.value = tabIdentifier;
	}

	function onNavScroll() {
		const nav = navRef.value;
		if (!nav) return;

		const scrollLeft = nav.scrollLeft;
		const scrollWidth = nav.scrollWidth;
		const clientWidth = nav.clientWidth;

		// Check if scrolled
		navScroll.value = scrollLeft > 0;

		// Check if at beginning
		isScrollBeginning.value = scrollLeft === 0;

		// Check if at end (accounting for potential decimal differences)
		isScrollEnd.value = Math.ceil(scrollLeft + clientWidth) >= scrollWidth;
	}

	defineExpose({
		setActiveTab,
	});
</script>
