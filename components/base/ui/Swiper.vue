<template>
	<div class="swiper-container">
		<div class="swiper" ref="swiperContainer">
			<div class="swiper-wrapper">
				<slot />
			</div>
		</div>
		<slot name="navigation">
			<div class="swiper-navigation" :class="[props.name ? `${props.name}-swiper-navigation` : '', navigation?.wrapperClass, {'swiper-navigation-lock': totalSlides <= swiper?.slidesPerViewDynamic()}]" v-if="navigation?.enabled">
				<div ref="navPrev" class="swiper-button swiper-button-prev" :class="[props.name ? `${props.name}-swiper-button-prev` : '', navigation?.prevElClass]">&lt;</div>
				<div ref="navNext" class="swiper-button swiper-button-next" :class="[props.name ? `${props.name}-swiper-button-next` : '', navigation?.nextElClass]">&gt;</div>
			</div>
		</slot>
		<slot name="pagination">
			<div ref="paginationEl" class="swiper-pagination" :class="[props.name ? `${props.name}-swiper-pagination` : '', pagination?.wrapperClass]" v-if="pagination?.enabled" />
		</slot>
	</div>
</template>

<script setup>
	import Swiper from 'swiper';
	import {Navigation, Pagination, EffectFade, Autoplay, Thumbs} from 'swiper/modules';
	import 'swiper/css';
	import 'swiper/css/effect-fade';
	import 'swiper/css/autoplay';

	const attrs = useAttrs();
	const props = defineProps({
		name: String,
		options: Object,
		totalItems: Number,
		thumbsSwiper: Object,
		initTimeout: {
			type: Number,
			default: 100,
		},
	});

	const totalSlides = computed(() => (props.totalItems ? props.totalItems : swiper.value?.slides?.length));
	const swiperContainer = ref(null);
	const swiper = ref(null);
	const emit = defineEmits(['init']);

	// set default navigation options and extend with user options
	const navPrev = ref(null);
	const navNext = ref(null);
	const navigation = ref({
		enabled: true,
		nextEl: navNext,
		prevEl: navPrev,
		...props.options?.navigation,
	});

	// set default pagination options and extend with user options
	const paginationEl = ref(null);
	const pagination = ref({
		enabled: false,
		el: paginationEl,
		...props.options?.pagination,
	});

	let initTimeout;
	onMounted(() => {
		if (initTimeout) clearTimeout(initTimeout);
		initTimeout = setTimeout(() => {
			const modules = [Navigation, Pagination, EffectFade, Autoplay, Thumbs];
			if (props.options?.modules) modules.push(...props.options.modules);
			const swiperOptions = {
				resizeObserver: false,
				...props.options,
				modules,
				navigation: navigation.value,
				pagination: pagination.value,
			};
			swiper.value = new Swiper(swiperContainer.value, swiperOptions);
			emit('init', swiper.value);
		}, props.initTimeout);
	});

	onUpdated(() => {
		if (!swiper.value) return;

		// init thumbs connection once thumbs swiper is ready
		if (props.thumbsSwiper && !props.thumbsSwiper.destroyed && swiper.value.thumbs) {
			swiper.value.thumbs.swiper = props.thumbsSwiper;
			swiper.value.thumbs.init();
			swiper.value.thumbs.update(true);
		}
	});

	onBeforeUnmount(() => {
		if (swiper.value) {
			swiper.value.destroy(true, true);
		}
	});
</script>
