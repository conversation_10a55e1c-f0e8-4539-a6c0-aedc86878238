<template>
	<template v-if="name">
		<div :id="'layout-block-' + name" />
	</template>
	<template v-if="to && mode == 'client'">
		<ClientOnly>
			<Teleport :to="'#layout-block-' + to"><slot /></Teleport>
		</ClientOnly>
	</template>
	<template v-if="to && mode == 'server'">
		<Teleport v-if="mode == 'server'" to="body">
			<div ref="element">
				<slot />
			</div>
		</Teleport>
	</template>
	<template v-if="to && mode == 'custom'">
		<ClientOnly>
			<Teleport :to="to"><slot /></Teleport>
		</ClientOnly>
	</template>
</template>

<script setup>
	const props = defineProps({
		name: String,
		to: String,
		mode: {
			type: String,
			default: 'client',
		},
	});

	const element = ref(null);

	onMounted(() => {
		watchEffect(() => {
			if (props.to && props.mode == 'server') {
				const target = document.querySelector('#layout-block-' + props.to);
				if (target) {
					target.appendChild(element.value);
				}
			}
		});
	});
</script>
