<template>
	<div class="pagination" v-show="visible" v-if="pagination.page?.total > 1">
		<NuxtLink class="pagination-prev-page" v-if="pagination.page?.previous" :to="pageLink(pagination.page.previous)" @click="navigateToPage(pagination.page.previous)">{{ prev ? prev : '<' }}</NuxtLink>
		<template v-for="(page, index) in pages" :key="index">
			<NuxtLink v-if="page.type == 'page'" class="pagination-page" :class="{'current-page': page.title == pagination.page.current}" :to="page.url" @click="navigateToPage(page.title)">{{ page.title }}</NuxtLink>
			<span class="pagination-page pagination-mid-page" v-else>{{ page.title }}</span>
		</template>
		<NuxtLink class="pagination-next-page" v-if="pagination.page?.next" :to="pageLink(pagination.page.next)" @click="navigateToPage(pagination.page.next)">{{ next ? next : '>' }}</NuxtLink>
	</div>
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {emit} = useEventBus();
	const emitData = defineEmits(['load']);
	const {scrollTo} = useDom();
	const props = defineProps({
		visible: Boolean,
		prev: String,
		next: String,
		scrollToElement: String,
		scrollToOffset: {
			type: Number,
			default: 0,
		},
		mode: String,
	});

	// get data provided by parent component
	const {pagination, mode} = inject('pagination');

	// separator between pages
	const separator = {
		title: '...',
		url: '',
		type: 'separator',
	};

	const pages = computed(() => {
		let p = [];
		if (!pagination.value?.page) return p;

		// add all pages
		for (let i = 1; i <= pagination.value.page.total; i++) {
			p.push({
				title: i,
				url: pageLink(i),
				type: 'page',
			});
		}

		// show only three first and three last pages
		if (p.length > 6) {
			// get first and last page
			const firstPage = p.slice(0, 1);
			const lastPage = p.slice(-1);

			// slice few pages before current page
			const prev = pagination.value.page.current < 4 ? 0 : pagination.value.page.current - 4;
			const prevPages = p.slice(prev, pagination.value.page.current);

			// slice few pages after current page
			const next = pagination.value.page.current > pagination.value.page.total - 3 ? pagination.value.page.total : pagination.value.page.current + 3;
			const nextPages = p.slice(pagination.value.page.current, next);

			// merge pages
			p = [...prevPages, ...nextPages];

			// add first page and separator if there are more than 3 pages before current page
			if (pagination.value.page.current > 5) {
				p.unshift(...firstPage, separator);
			}

			// add last page and separator if there are more than 3 pages after current page
			if (pagination.value.page.current < pagination.value.page.total - 4) {
				p.push(separator, ...lastPage);
			}
		}

		return p;
	});

	// navigate and emit event to update module data
	const emitType = mode == 'publish' ? 'publishPostsUpdate' : 'catalogProductsUpdate';
	async function navigateToPage(page) {
		if (props.mode == 'loadmore') {
			const searchParams = {...route.query};
			if (page > 1) {
				searchParams.to_page = page;
			} else {
				delete searchParams.to_page;
			}
			return (window.location.href = `${window.location.pathname}?${new URLSearchParams(searchParams)}`);
		}

		const p = page > 1 ? page : undefined;
		await navigateTo({query: {...route.query, to_page: undefined, page: p}});
		if (props.scrollToElement) {
			scrollTo('#' + props.scrollToElement, {offset: props.scrollToOffset});
		}
		emitData('load', {page});
		return emit(emitType, {mode: 'pagination', page});
	}

	// generate url for page links
	function pageLink(page) {
		const routeQuery = route.fullPath;
		let newQuery = routeQuery;

		// remove page param from query
		newQuery = props.mode == 'loadmore' ? newQuery.replace(/(\?|&)(to_page=\d+)/, '') : newQuery.replace(/(\?|&)(page=\d+)/, '');

		// add page param to query if it's not the first page
		if (page > 1) {
			const separator = newQuery.indexOf('?') == -1 ? '?' : '&';
			const param = props.mode == 'loadmore' ? 'to_page=' : 'page=';
			newQuery = newQuery + separator + param + page;
		}

		return newQuery;
	}
</script>
