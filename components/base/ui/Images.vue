<template>
	<slot :items="files" />
</template>

<script setup>
	const props = defineProps({
		images: Array,
		exclude: [Number, String],
		thumbPreset: String,
	});

	// images
	const files = ref(props.images);

	// remove first x images
	if (props.exclude) {
		files.value = files.value.slice(props.exclude);
	}

	// generate thumbs
	if (props.thumbPreset && files.value.length) {
		const {generateThumbs} = useImages();
		await generateThumbs({
			data: files.value,
			preset: props.thumbPreset,
		});
	}
</script>
