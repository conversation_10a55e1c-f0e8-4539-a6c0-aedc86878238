<template>
	<iframe v-if="type == 'vimeo'" :src="src" :width="width" :height="height" frameborder="0" allowfullscreen v-bind="$attrs"></iframe>
	<iframe v-else :width="width" :height="height" :src="src" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen v-bind="$attrs"></iframe>
</template>

<script setup>
	const props = defineProps({
		src: {
			type: String,
			required: true,
		},
		width: {
			type: [Number, String],
			default: 560,
		},
		height: {
			type: [Number, String],
			default: 315,
		},
	});

	const type = computed(() => {
		if (props.src.includes('vimeo')) {
			return 'vimeo';
		}
		return 'youtube';
	});
</script>
