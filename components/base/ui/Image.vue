<template>
	<slot :loading="isLoading" :isNew="isNew" :item="image" :pictures="pictures">
		<picture v-if="pictures?.length">
			<template v-if="isNew">
				<BaseThemeUiLoading mode="image-loader" />
			</template>
			<template v-else>
				<template v-for="(img, index) in pictures" :key="index">
					<source v-if="img.src" :media="'(max-width: ' + img.maxWidth + ')'" :srcset="img.src" />
				</template>
				<img v-if="image.src" :src="image.src" :width="image.width" :height="image.height" :alt="image.alt ? image.alt : ''" :title="image.title" :loading="loading" v-bind="$attrs" @load="onImageLoad" />
			</template>
		</picture>
		<template v-else>
			<template v-if="isNew">
				<BaseThemeUiLoading mode="image-loader" />
			</template>
			<template v-else>
				<img v-if="image?.src" :src="image.src" :width="image.width" :height="image.height" :alt="image.alt ? image.alt : ''" :title="image.title" :loading="loading" v-bind="$attrs" @load="onImageLoad" />
			</template>
		</template>
	</slot>
</template>

<script setup>
	const {absoluteImagePath} = useText();
	const config = useAppConfig();
	const props = defineProps({
		src: String,
		alt: {
			type: String,
			default: '',
		},
		title: {
			type: String,
			default: '',
		},
		width: [Number, String],
		height: [Number, String],
		data: Object,
		picture: Array,
		default: String,
		loading: {
			type: String,
			default: 'eager',
		},
		delay: {
			type: Number,
			default: 3000,
		},
	});

	const isLoading = ref(true);

	const pictures = computed(() => {
		if (props.picture?.length) {
			return props.picture.map(img => {
				const i = img;
				i.src = img.src && typeof img.src === 'string' ? absoluteImagePath(img.src, 'string') : img.default;
				return i;
			});
		}

		return [];
	});

	function onImageLoad(e) {
		isLoading.value = false;
	}

	const isNew = ref(false);
	const image = computed(() => {
		let imageSrc = props?.data?.thumb ? props.data.thumb : props.src;

		if (imageSrc) imageSrc = absoluteImagePath(imageSrc, 'string');
		if (!imageSrc && props.default) imageSrc = props.default; // Missing image src. Show default image

		const imageType = imageSrc ? imageSrc.split('.').pop() : '';
		if (!['svg', 'gif'].includes(imageType) && props.data?.thumb && !props.data?.width) imageSrc = props.default; // Missing image width - image thumb is not generated server side. Show default image

		const result = {
			src: imageSrc,
			width: props?.data?.width ? props.data.width : props.width,
			height: props?.data?.height ? props.data.height : props.height,
			title: props?.data?.title ? props.data.title : props.title,
			alt: props?.data?.alt ? props.data.alt : props.alt,
		};

		if (isNew.value) {
			setTimeout(() => {
				isNew.value = false;
				isLoading.value = false;
			}, props.delay);
		} else {
			isLoading.value = false;
		}

		return result;
	});

	const stopWatcher = watch(
		() => props.data,
		newImage => {
			if (newImage?.is_new) {
				isNew.value = true;
			}
			nextTick(() => {
				if (stopWatcher) stopWatcher();
			});
		},
		{immediate: true}
	);
</script>
