<template>
	<slot :days="formattedDays" :hours="formattedHours" :minutes="formattedMinutes" :seconds="formattedSeconds" :ended="hasEnded" />
</template>

<script setup>
	const props = defineProps({
		end: {
			type: [Number, String],
			required: false,
		},
		countdown: {
			type: Number,
			required: false,
		},
		onEnd: {
			type: Function,
			default: null,
		},
	});

	const days = ref(0);
	const hours = ref(0);
	const minutes = ref(0);
	const seconds = ref(0);
	const hasEnded = ref(false);
	const startTime = ref(Math.floor(Date.now() / 1000));
	let interval = null;

	function updateCountdown() {
		const currentTime = Math.floor(Date.now() / 1000);
		let remainingTime = props.countdown ? props.countdown - (currentTime - startTime.value) : props.end - currentTime;

		if (remainingTime <= 0) {
			hasEnded.value = true;
			clearInterval(interval);
			if (props.onEnd) props.onEnd();
			return;
		}

		days.value = Math.floor(remainingTime / 86400);
		remainingTime %= 86400;
		hours.value = Math.floor(remainingTime / 3600);
		remainingTime %= 3600;
		minutes.value = Math.floor(remainingTime / 60);
		seconds.value = remainingTime % 60;
	}

	onMounted(() => {
		updateCountdown();
		interval = setInterval(updateCountdown, 1000);
	});

	onBeforeUnmount(() => {
		if (interval) {
			clearInterval(interval);
		}
	});

	const formattedDays = computed(() => String(days.value).padStart(2, '0'));
	const formattedHours = computed(() => String(hours.value).padStart(2, '0'));
	const formattedMinutes = computed(() => String(minutes.value).padStart(2, '0'));
	const formattedSeconds = computed(() => String(seconds.value).padStart(2, '0'));
</script>
