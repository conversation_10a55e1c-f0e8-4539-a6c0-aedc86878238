<template>
	<Body v-if="active" :class="['modal-active', 'modal-' + name + '-active']" />
	<slot :onClose="closeModal" :item="item" :active="active" :loading="loading" />
</template>

<script setup>
	const {debounce} = useUtils();
	const {onPageLeave} = useDom();
	const modal = useModal();
	const page = usePage();
	const emit = defineEmits(['open']);
	const props = defineProps({
		name: {
			type: String,
			required: true,
		},
		active: {
			type: Boolean,
			default: false,
		},
		keyClosable: {
			type: Boolean,
			default: true,
		},
		cookie: {
			type: Boolean,
			default: false,
		},
		cookieExpire: {
			type: String,
			default: 'session',
		},
		pageLeave: {
			type: Boolean,
			default: false,
		},
		scrollTrigger: Number,
		autoOpen: Number,
		page: String,
	});

	// set modal cookie if defined in props. cookieExpire prop can be set to '1y' to set cookie expiration to 1 year. Default is session
	function setCookie() {
		if (!props.cookie) return;
		const cookieOptions = {};
		if (props.cookieExpire == '1y') cookieOptions.expires = new Date(Date.now() + 1000 * 60 * 60 * 24 * 365);
		const cookie = useCookie(`modal_${props.name}`, cookieOptions);
		cookie.value = 1;
	}

	// if page prop is defined, fetch page data and set page data as modal item
	const loading = ref(false);
	async function fetchPage() {
		loading.value = true;
		const pageData = await page.fetch({'slug': props.page});
		if (pageData) modal.open(props.name, pageData);
		loading.value = false;
	}

	// get modal data
	const item = computed(() => {
		return modal.get(props.name);
	});

	// watch if page prop is defined and fetch page data if modal is active
	if (props.page) {
		watchEffect(() => {
			if (item.value) fetchPage();
		});
	}

	// check if modal is active
	const active = computed(() => {
		const isActive = props.name in modal.activeModals() ? true : false;
		emit('open', {active: isActive, data: item.value});
		return isActive;
	});

	// close modal window when Esc key is pressed
	function closeOnEscape(evt) {
		if (evt.keyCode === 27) {
			closeModal();
		}
	}

	function closeModal() {
		setCookie();
		modal.close(props.name);
		window.removeEventListener('scroll', debouncedHandleScroll);
	}

	async function openModal() {
		if (modal.get(props.name)) return;

		if (!props.cookie) {
			return modal.open(props.name);
		}

		if (!useCookie(`modal_${props.name}`).value) {
			modal.open(props.name);
		}
	}

	// trigger handleScroll function on scroll. debounce is used to count how many times user scrolled
	const scrollCount = ref(0);
	function handleScroll() {
		scrollCount.value++;
		if (props.scrollTrigger && scrollCount.value === props.scrollTrigger) {
			openModal();
			window.removeEventListener('scroll', debouncedHandleScroll);
		}
	}
	const debouncedHandleScroll = debounce(handleScroll, 100);

	// trigger openModal function when user leaves the page
	if (props.pageLeave) {
		onPageLeave({
			callback: ({hasLeftPage}) => {
				if (hasLeftPage) openModal();
			},
		});
	}

	let activeTimeout;
	onMounted(() => {
		if (props.active) {
			openModal();
		}

		if (props.autoOpen) {
			activeTimeout = setTimeout(() => {
				openModal();
			}, props.autoOpen);
		}

		if (props.keyClosable) {
			window.addEventListener('keyup', closeOnEscape);
		}

		if (props.scrollTrigger) {
			window.addEventListener('scroll', debouncedHandleScroll);
		}
	});

	// cleanup timeouts and event listeners to avoid memory leaks
	onBeforeUnmount(() => {
		clearTimeout(activeTimeout);
		window.removeEventListener('keyup', closeOnEscape);
		window.removeEventListener('scroll', debouncedHandleScroll);
	});
</script>
