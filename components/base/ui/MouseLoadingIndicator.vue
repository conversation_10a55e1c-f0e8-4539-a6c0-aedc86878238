<template>
	<ClientOnly>
		<div v-if="loading" class="spinner" :style="{left: x + 'px', top: y + 'px'}"></div>
	</ClientOnly>
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const loading = ref(false);
	const x = ref(0);
	const y = ref(0);

	nuxtApp.hook('page:start', () => {
		loading.value = true;
	});

	nuxtApp.hook('page:finish', () => {
		loading.value = false;
	});

	const handleMouseMove = event => {
		x.value = event.clientX - 20; // Half of spinner's width
		y.value = event.clientY - 20; // Half of spinner's height
	};

	onMounted(() => {
		window.addEventListener('mousemove', handleMouseMove);
	});

	onBeforeUnmount(() => {
		window.removeEventListener('mousemove', handleMouseMove);
	});
</script>

<style scoped>
	.spinner {
		position: absolute;
		width: 25px;
		height: 25px;
		background-size: contain;
		z-index: 9999;
	}
</style>
