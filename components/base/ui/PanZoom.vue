<template>
	<div ref="element">
		<slot />
	</div>
</template>

<script setup>
	import Panzoom from '@panzoom/panzoom'; // https://github.com/timmywil/panzoom

	const props = defineProps({
		selector: {
			type: String,
			default: ':scope > :first-child',
		},
		resetTrigger: [Number, String],
		zoomInSelector: String,
		zoomOutSelector: String,
		options: Object,
	});

	const element = ref(null);
	let zoomOutButton = null;
	let zoomInButton = null;
	let panzoom = null;
	let selectorElement = null;

	// toggle zoom in/out buttons disabled class based on current scale
	function handleZoom(event) {
		const currScale = Math.round(event?.detail?.scale * 100) / 100;
		const {minScale, maxScale} = panzoom.getOptions();

		zoomOutButton?.classList?.toggle('disabled', currScale <= minScale);
		zoomInButton?.classList?.toggle('disabled', currScale >= maxScale);
	}

	onMounted(() => {
		selectorElement = element.value.querySelector(props.selector);
		if (!selectorElement) return useLog('PanZoom: selector not found', 'error');

		let options = {
			minScale: 1,
			maxScale: 4,
			contain: 'outside',
			panOnlyWhenZoomed: true,
			overflow: 'visible',
			step: 0.5,
			cursor: 'default',
			...props.options,
		};

		panzoom = Panzoom(selectorElement, options);

		// add mouse events for pan and zoom
		selectorElement.addEventListener('wheel', panzoom?.zoomWithWheel, {passive: false});
		selectorElement.addEventListener('panzoomzoom', handleZoom);

		// add touch events for mobile
		selectorElement.addEventListener('touchstart', panzoom?.setStartTouches, {passive: false});
		selectorElement.addEventListener('touchmove', panzoom?.drag, {passive: false});
		selectorElement.addEventListener(
			'touchmove',
			function (e) {
				e.preventDefault();
			},
			{passive: false}
		);

		// select zoom in/out buttons and if they exist, add click events
		zoomInButton = props.zoomInSelector ? document.querySelector(props.zoomInSelector) : null;
		zoomOutButton = props.zoomOutSelector ? document.querySelector(props.zoomOutSelector) : null;

		if (zoomInButton) zoomInButton.addEventListener('click', panzoom?.zoomIn);
		if (zoomOutButton) zoomOutButton.addEventListener('click', panzoom?.zoomOut);

		// if slide changes, reset panzoom
		watchEffect(() => {
			if (props.resetTrigger) panzoom.reset({animate: false});
		});
	});

	// remove all event listeners
	onBeforeUnmount(() => {
		if (panzoom) panzoom.destroy();
	});

	onUnmounted(() => {
		if (zoomInButton) zoomInButton.removeEventListener('click', panzoom?.zoomIn);
		if (zoomOutButton) zoomOutButton.removeEventListener('click', panzoom?.zoomOut);
		if (selectorElement) {
			selectorElement.removeEventListener('wheel', panzoom?.zoomWithWheel);
			selectorElement.removeEventListener('touchstart', panzoom?.setStartTouches);
			selectorElement.removeEventListener('touchmove', panzoom?.drag);
			selectorElement.removeEventListener('touchmove', function (e) {
				e.preventDefault();
			});
		}
	});
</script>

<style scoped>
	div {
		max-height: 100%;
	}
</style>
