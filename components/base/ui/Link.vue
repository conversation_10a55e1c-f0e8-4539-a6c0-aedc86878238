<template>
	<a v-if="props.native" v-bind="$attrs" @click="onClick"><slot /></a>
	<NuxtLink v-else v-bind="$attrs" @click="onClick">
		<slot />
	</NuxtLink>
</template>

<script setup>
	const router = useRouter();
	const attrs = useAttrs();
	const {emit} = useEventBus();
	const props = defineProps({
		updateProducts: {
			type: Boolean,
			default: false,
		},
		native: {
			type: Boolean,
			default: false,
		},
		preventDefault: {
			type: Boolean,
			default: false,
		},
	});

	// intercept click event and emit event to update products if query contains discount, new or with_qty query param
	function onClick(e) {
		e.preventDefault();

		// navigate to href (only for native)
		if (props.native && !props.preventDefault && attrs.href) router.push(attrs.href);

		// if url query is changed on the same page, emit event to update products
		const linkUrl = new URL(attrs.href, window.location.origin);

		if (linkUrl.pathname == router.currentRoute.value.path) {
			const params = linkUrl.searchParams;
			if (props.updateProducts || params?.size > 0) {
				emit('catalogProductsUpdate');
			}
		}
	}
</script>
