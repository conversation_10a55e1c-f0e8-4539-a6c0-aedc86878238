<template>
	<ClientOnly>
		<Body class="page-loading" v-if="loading" />
		<slot :loading="loading" :percentage="Math.round(percentage)" />
	</ClientOnly>
</template>

<script setup>
	const props = defineProps({
		maxTime: {
			type: Number,
			default: 10000, // how long to wait before stopping the timer
		},
	});
	const nuxtApp = useNuxtApp();
	const percentage = ref(0);
	const loading = ref(false);
	let interval;
	const intervalTime = 10; // timeout interval (how often to increment the timer)
	const totalIntervals = props.maxTime / intervalTime;
	const incrementValue = 100 / totalIntervals;

	const startTimer = () => {
		let timer = 0;
		percentage.value = 0;
		interval = setInterval(() => {
			if (timer >= props.maxTime) {
				clearInterval(interval);

				// hard reload if loading takes too long
				return reloadNuxtApp();
			}
			timer += intervalTime;
			percentage.value += incrementValue;
		}, intervalTime);
	};

	const stopTimer = () => {
		clearInterval(interval);
		percentage.value = 0;
	};

	// Start loading when page starts to load
	nuxtApp.hook('page:start', () => {
		if (nuxtApp?.$appGlobalData) nuxtApp.$appGlobalData.loading = true;
		loading.value = true;
		startTimer();
	});

	// Stop loading when page is loaded or error occurs
	nuxtApp.hook('page:finish', () => {
		if (nuxtApp?.$appGlobalData) nuxtApp.$appGlobalData.loading = false;
		loading.value = false;
		stopTimer();
	});

	onBeforeUnmount(() => {
		if (nuxtApp?.$appGlobalData) nuxtApp.$appGlobalData.loading = false;
		loading.value = false;
		stopTimer();
	});
</script>
