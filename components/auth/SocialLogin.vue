<template>
	<div class="social-login" :class="{'auth-social-checkout': mode == 'checkout', 'auth-social-login': mode == 'login', 'auth-social-register': mode == 'register'}">
		<BaseCmsLabel class="social-login-title" tag="div" code="auth_social_title" />

		<div class="login-social-buttons">
			<BaseAuthSocialLogin v-slot="{socialLogin, loadingFacebook, loadingGoogle}">
				<div class="btn-social btn-social-fb" :class="{'loading': loadingFacebook}" @click="socialLogin('Facebook')">
					<UiLoader v-if="loadingFacebook" /><span><img loading="lazy" src="/assets/images/facebook.svg" /></span>
				</div>
				<div class="btn-social btn-social-google" :class="{'loading': loadingGoogle}" @click="socialLogin('Google')">
					<UiLoader v-if="loadingGoogle" /><span><img loading="lazy" src="/assets/images/google.svg" /></span>
				</div>
			</BaseAuthSocialLogin>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['mode']);
</script>
