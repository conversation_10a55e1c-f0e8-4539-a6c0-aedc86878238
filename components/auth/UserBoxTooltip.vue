<template>
	<div class="aw-tooltip">
		<template v-if="isLoggedIn">
			<NuxtLink :to="urls.auth"><BaseCmsLabel code="my_profile" /></NuxtLink>
			<NuxtLink :to="urls.auth_my_webshoporder"><BaseCmsLabel code="my_orders" /></NuxtLink>
			<NuxtLink :to="urls.auth_wishlist"><BaseCmsLabel code="my_wishlist" /></NuxtLink>
			<NuxtLink :to="urls.auth_my_webshopcoupon" v-if="!b2b"><BaseCmsLabel code="my_coupons" /></NuxtLink>
			<NuxtLink :to="urls.auth_edit"><BaseCmsLabel code="edit_profile" /></NuxtLink>
			<NuxtLink :to="urls.auth_change_password"><BaseCmsLabel code="change_password" /></NuxtLink>
			<NuxtLink :to="`${urls.auth_logout}?redirect=/`"><BaseCmsLabel code="logout" /></NuxtLink>
		</template>
		<template v-else>
			<NuxtLink :to="urls.auth_login"><BaseCmsLabel code="login" /></NuxtLink>
			<NuxtLink :to="urls.auth_signup"><BaseCmsLabel code="signup" /></NuxtLink>
			<NuxtLink :to="urls.auth_forgotten_password"><BaseCmsLabel code="header_forgotten_password" /></NuxtLink>
		</template>
	</div>
</template>

<script setup>
	const props = defineProps(['isLoggedIn', 'urls', 'b2b']);
</script>
