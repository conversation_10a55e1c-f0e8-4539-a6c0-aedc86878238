<template>
	<div class="wp wp-details">
		<div class="wp-image">
			<figure>
				<BaseUiImage loading="lazy" :product="product?.main_image_thumbs?.['width65-height65']" default="/images/no-image-65.jpg" :alt="product.title" />
			</figure>
		</div>

		<div class="wp-details-content">
			<div class="wp-cnt">
				<div class="wp-title">{{ product.title }}</div>
				<div class="wp-code"><BaseCmsLabel tag="span" code="code" />: {{product.code}}</div>
			</div>

			<div class="wp-total">
				<template v-if="product.total_basic < product.total">
					<div v-if="product.total_basic > product.total" class="wp-old-price"><BaseUtilsFormatCurrency :price="product.total_basic" /></div>
					<div class="wp-price-current" :class="[{'wp-price-discount': product.total_basic > product.total}]"><BaseUtilsFormatCurrency :price="product.total" /></div>
					<div class="wp-qty-count" v-if="product.quantity > 1">
						<span>{{ product.quantity }} x </span><BaseUtilsFormatCurrency :price="product.unit_price" />
					</div>
				</template>
				<template v-else>
					<div class="wp-current-price"><BaseUtilsFormatCurrency :price="product.total" /></div>
				</template>
			</div>
			<div class="wp-details-btns">
				<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :data="{fetchId: product.product_id}">
					<div class="btn-order-again" :class="{'loading': loading}" @click="onAddToCart">
						<UiLoader v-if="loading" mode="small" /><span><BaseCmsLabel code="order_again" /></span>
					</div>
				</BaseWebshopAddToCart>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['product']);
</script>
