<template>
	<template v-if="mode != 'checkout'">
		<BaseCmsLabel tag="div" class="a-subtitle a-login-subtitle" :class="[mode]" code="users_signup" />
		<BaseCmsLabel tag="div" class="a-cnt" :class="[mode]" code="email_signup" />
	</template>

	<!-- 	
		FIXME integ dodat tractking gtm na formu ako je mode == 'checkout'
		<form data-tracking_gtm_checkout_advanced="1|Prijava" class="form-label wc-login-form step1 ajax_siteform ajax_siteform_loading" method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" id="webshop_form_login">
	-->
	<BaseAuthLoginForm class="form-label" :class="mode == 'checkout' ? 'wc-login-form' : 'auth-form auth-form-login'" v-slot="{fields, formError}" :submit-url="mode == 'checkout' ? 'webshop_customer' : 'auth'">
		<div class="global-error" v-if="formError">
			<BaseCmsLabel :code="formError" />
		</div>

		<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
			<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}]">
				<template v-if="item.name == 'remember_me'">
					<BaseFormInput :id="mode == 'userBox' ? item.name + 'ub' : item.name" />
					<BaseCmsLabel tag="label" :for="mode == 'userBox' ? item.name + 'ub' : item.name" :code="item.name" />
				</template>
				<template v-else>
					<BaseCmsLabel tag="label" :for="mode == 'userBox' ? item.name + 'ub' : item.name" :code="item.name" />
					<BaseFormInput :id="mode == 'userBox' ? item.name + 'ub' : item.name" />
				</template>
				<span class="error" v-show="errorMessage" v-html="errorMessage" />
			</p>
		</BaseFormField>

		<BaseAuthUser v-slot="{urls}">
			<div class="a-btns">
				<button type="submit" class="btn btn-auth-submit" :class="{'btn-red': mode == 'login', 'btn-green': mode !== 'login'}"><BaseCmsLabel code="login" tag="span" /></button>
				<div class="a-links">
					<NuxtLink :to="urls.auth_forgotten_password" class="a-links-forgotten" data-auth_login="forgotten_password" data-siteform_response="show_hide" data-siteform_response_hide="-1">
						<BaseCmsLabel code="forgotten_password" tag="span" />
					</NuxtLink>
				</div>
			</div>
			<div class="form-login-forgotten-cnt"></div>
		</BaseAuthUser>
	</BaseAuthLoginForm>

	<AuthSocialLogin :mode="mode" />
</template>

<script setup>
	const props = defineProps(['mode']);
</script>
