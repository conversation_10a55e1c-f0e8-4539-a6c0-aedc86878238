<template>
	<ClientOnly>
		<BaseAuthUser v-slot="{isLoggedIn, urls}">
			<div class="aw" :class="{'active': userBoxDropdown}" ref="aw">
				<div class="aw-btn aw-login" @click="userBoxDropdown = !userBoxDropdown"><BaseCmsLabel code="my_profile" tag="span" /></div>
				<LazyAuthUserBoxTooltip :isLoggedIn="isLoggedIn" :urls="urls" :b2b="b2b" />
			</div>
		</BaseAuthUser>
	</ClientOnly>
</template>

<script setup>
	const {onClickOutside} = useDom();
	const {b2b} = useProfile();
	const userBoxDropdown = ref(false);
	const aw = ref();
	onClickOutside(aw, () => {
		userBoxDropdown.value = false;
	});

	// close dropdown on route change
	const route = useRoute();
	watch(
		() => route.fullPath,
		() => {
			userBoxDropdown.value = false;
		}
	);
</script>

<style lang="less" scoped></style>
