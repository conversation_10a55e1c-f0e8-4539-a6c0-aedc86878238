<template>
	<ClientOnly>
		<BaseAuthUser v-slot="{urls}">
			<CmsTwoColumns>
				<template #main>
					<div class="df a-row">
						<div class="a-col a-col1">
							<slot name="col1" />
						</div>
						<div class="a-col a-col2 lists">
							<div class="a-content" v-if="page?.content" v-html="page.content" v-interpolation />
							<NuxtLink :to="urls.auth_signup" class="btn btn-login" v-if="mode !== 'signup'"><BaseCmsLabel code="signup" tag="span" /></NuxtLink>

							<template v-if="mode == 'signup'">
								<div class="reg-have-acc">
									<NuxtLink :to="urls.auth_login"><BaseCmsLabel tag="div" code="already_register_login" /></NuxtLink>
								</div>

								<div class="a-btns">
									<div class="submit">
										<NuxtLink :to="urls.auth_login" class="btn btn-auth-submit">
											<BaseCmsLabel code="login" tag="span" />
										</NuxtLink>
									</div>

									<div class="a-links a-signup-links">
										<NuxtLink :to="urls.auth_forgotten_password" class="a-links-forgotten" data-auth_login="forgotten_password" data-siteform_response="show_hide" data-siteform_response_hide="-1">
											<BaseCmsLabel tag="span" code="forgotten_password" />
										</NuxtLink>
									</div>
								</div>
								<AuthSocialLogin mode="signup" />
							</template>
						</div>
					</div>
				</template>
			</CmsTwoColumns>
		</BaseAuthUser>
	</ClientOnly>
</template>

<script setup>
	const props = defineProps(['page', 'mode']);
</script>
