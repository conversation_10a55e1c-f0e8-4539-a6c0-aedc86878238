<template>
	<div class="qty-container">
		<BaseThemeWebshopQty :quantity="props.quantity" :limit="props.limit" :item="props.item" :steps="props.steps" v-model="item.qty" :mode="props.mode" />
	</div>
</template>

<script setup>
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
		mode: {
			type: String,
		},
		limit: [Number, String],
		quantity: [Number, String],
		steps: [Number, String],
	})
</script>
