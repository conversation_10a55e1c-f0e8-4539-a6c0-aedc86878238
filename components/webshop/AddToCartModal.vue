<template>
	<BaseWebshopAddToCartModal v-slot="{items, status, onClose}" :auto-close="0">
		<div v-if="items?.length">
			<div class="add-to-cart-modal">
				<BaseWebshopCart v-slot="{counter}">
					<div class="add-to-cart-header">
						<div class="add-to-cart-header-title">
							<BaseCmsLabel code="in_cart" /> <span class="modal-counter">({{counter}})</span>
						</div>
						<BaseCmsLabel @click="onClose" tag="div" class="add-to-cart-modal-close" code="continue_shopping" />
					</div>

					<div class="add-to-cart-modal-body">
						<div v-if="status?.data?.label_name" class="add-to-cart-modal-status" :class="{'red': status?.data?.label_name == 'error_limitqty'}">
							<span><BaseCmsLabel :code="status.data.label_name" /></span>
						</div>
						<WebshopCartPreview mode="modal" :onClose="onClose" />
					</div>
				</BaseWebshopCart>
			</div>
			<div class="modal-bg-close" @click="onClose"></div>
		</div>
	</BaseWebshopAddToCartModal>
</template>