<template>
	<div class="wwp ww-item">
		<div class="wwp-image ww-image">
			<NuxtLink :to="data.item.url_without_domain">
				<BaseUiImage loading="lazy" :data="data.item?.image_thumbs?.['width70-height70-crop1']" default="/images/no-image-50.jpg" :alt="data.item.title" />
			</NuxtLink>
		</div>

		<div class="wwp-cnt ww-cnt">
			<div class="wwp-title ww-title">
				<NuxtLink :to="data.item.url_without_domain">{{ data.item.title }}</NuxtLink>
			</div>
			<div class="df aic ww-bottom">
				<div class="ww-price">
					<template v-if="data.total_basic < data.total">
						<div v-if="data.total_basic > data.total" class="wwp-old-price ww-old-price"><BaseUtilsFormatCurrency :price="data.total_basic" /></div>
						<div class="wpw-price-current ww-current-price" :class="[{'wwp-price-discount ww-discount-price': data.total_basic > data.total}]"><BaseUtilsFormatCurrency :price="data.total" /></div>
						<div class="wwp-qty-count" v-if="data.quantity > 1">
							<span>{{ data.quantity }} x </span><BaseUtilsFormatCurrency :price="data.unit_price" />
						</div>
						<div class="wwp-lowest-price ww-lowest-price" v-if="data.extra_price_lowest > 0 && data.total_basic > data.total">
							<BaseCmsLabel code="lowest_price" tag="span" />: <strong><BaseUtilsFormatCurrency :price="data.extra_price_lowest" /></strong>
						</div>
					</template>
					<template v-else>
						<template v-if="b2b">
							<div class="ww-current-price"><BaseUtilsFormatCurrency :price="data.total_basic" /></div>
						</template>
						<template v-else>
							<WebshopLoyalty v-slot="{loyalty}">
								<template v-if="data.type != 'coupon'">
									<template v-if="loyalty?.active">
										<div class="ww-old-price">
											<span><BaseUtilsFormatCurrency :price="data.total_basic" /></span>
										</div>
										<div class="ww-current-price red" v-if="data.discount_percentage > loyalty.discount_percent">
											<span><BaseUtilsFormatCurrency :price="data.total" /></span>
										</div>

										<div class="ww-current-price red" v-else><BaseUtilsFormatCurrency :price="(data.total_basic * (1 - (loyalty.discount_percent  / 100)))" /></div>

										<div class="lowest-price ww-lowest-price">
											<template v-if="data.extra_price_lowest > 0">
												<BaseCmsLabel code="lowest_price" />: <strong><BaseUtilsFormatCurrency :price="data.extra_price_lowest" /></strong>
											</template>
										</div>
									</template>
									<template v-else>
										<div class="ww-old-price" v-if="data.total_basic > data.total">
											<span><BaseUtilsFormatCurrency :price="data.total_basic" /></span>
										</div>
										<div class="ww-current-price" :class="{'red': data.total_basic > data.total}">
											<span><BaseUtilsFormatCurrency :price="data.total" /></span>
										</div>
									</template>
								</template>
								<template v-else>
									<div class="ww-current-price"><BaseUtilsFormatCurrency :price="data.total" /></div>
								</template>
							</WebshopLoyalty>
						</template>
					</template>
				</div>
				<BaseWebshopRemoveProduct :item="data" v-slot="{onRemove, loading}" v-if="mode != 'checkout'">
					<span class="wp-btn ww-btn-delete" :class="{'loading': loading}" @click="onRemove"></span>
				</BaseWebshopRemoveProduct>

				<div class="ww-qty-container">
					<BaseThemeWebshopQty :quantity="data.quantity" :steps="data?.package_qty ? data.package_qty : 1" :item="data" mode="cart" />
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['data', 'mode']);
	const {b2b} = useProfile();
</script>
