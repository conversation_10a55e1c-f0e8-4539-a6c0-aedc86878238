<template>
	<BaseWebshopCart v-slot="{counter, cartUrl}">
		<div class="ww" :class="[{'active': counter}, {'hover': cartPreview}]">
			<NuxtLink :to="cartUrl" class="ww-items" v-if="mobileBreakpoint">
				<ClientOnly>
					<span class="ww-counter btn-header-counter"
						><span>{{counter}}</span></span
					>
				</ClientOnly>
			</NuxtLink>
			<div class="ww-items" v-if="!mobileBreakpoint" @click="cartPreview = !cartPreview">
				<ClientOnly>
					<span class="ww-counter btn-header-counter"
						><span>{{counter}}</span></span
					>
				</ClientOnly>
			</div>
			<ClientOnly>
				<WebshopCartPreview v-if="counter" :active="cartPreview" :counter="counter">
					<template #closePreview>
						<div class="ww-preview-close" @click="cartPreview = !cartPreview" />
					</template>
				</WebshopCartPreview>
			</ClientOnly>
		</div>
	</BaseWebshopCart>
</template>

<script setup>
	const cartPreview = ref(false);
	const route = useRoute();
	const {mobileBreakpoint} = inject('rwd');

	const {subscribe} = useEventBus();
	let timeout;
	subscribe('webshopAddedToCart', data => {
		cartPreview.value = true;
	});

	watch(
		() => route.fullPath,
		() => {
			cartPreview.value = false
		})
</script>

<style lang="less" scoped></style>
