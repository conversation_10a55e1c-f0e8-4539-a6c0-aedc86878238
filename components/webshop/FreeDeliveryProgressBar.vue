<template>
	<div class="free-delivery-container cart_info_total_extra_shipping_to_free_box" v-show="toFree?.amount != 0">
		<div class="free-delivery-title cart_info_total_extra_shipping_to_free_box">
			<div class="free-delivery">
				<BaseCmsLabel code="min_total_missing" tag="span" /> <strong class="w-missing-shipping-value cart_info_total_extra_shipping_to_free"><BaseUtilsFormatCurrency :price="toFree.amount" /></strong>
			</div>
		</div>

		<div class="free-delivery-scale" v-show="toFree.amount != 0">
			<div class="free-delivery-scale-amount">
				<span class="cart_info_total_items"><BaseUtilsFormatCurrency :price="parcels[0].total.total" /></span> /
				<span class="cart_info_total_extra_shipping_free_above"><BaseUtilsFormatCurrency :price="toFree.above" /></span>
			</div>
			<div class="free-delivery-scale-bar cart_info_total_extra_shipping_to_free_percent" :style="'width: '+toFree.percent"></div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['toFree', 'parcels']);
</script>
