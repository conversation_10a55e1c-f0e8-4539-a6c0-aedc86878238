<template>
	<BaseWebshopRemoveProduct :item="data" v-slot="{onRemove, loading}">
		<div class="wp" :class="{'wp-checkout': mode == 'checkout', 'wp-pickup-only': data.type == 'pickup', 'pickup-hidden': pickupErrors?.length && mode == 'cartAll' && data.type == 'pickup'}" :id="'product_details_'+data.shopping_cart_code">
			<BaseCatalogWishlist v-slot="{items: wishlistItems}">
				<div class="wp-image">
					<figure>
						<NuxtLink :to="data.item.url_without_domain">
							<BaseUiImage loading="lazy" :data="data.item?.image_thumbs?.['width70-height70-crop1']" default="/images/no-image-70.jpg" :alt="data.item.title" />
						</NuxtLink>
					</figure>
				</div>

				<div class="wp-content">
					<div class="wp-cnt">
						<div class="wp-title">
							<NuxtLink :to="data.item.url_without_domain">{{ data.item.title }}</NuxtLink>
						</div>
						<!-- FIXME INTEG - tu postoji provjera vezana za 'product_code' koji ne postoji kao parametar <?php if(!empty($product_data['product_code'])): ?><div class="wp-code"><?php echo Arr::get($cmslabel, 'code'); ?>: <?php echo $product_data['product_code']; ?></div><?php endif; ?> -->
						<div class="wp-code"><BaseCmsLabel code="code" />: {{data.item.code}}</div>
						<template v-if="data.type == 'pickup'">
							<span v-if="availableLocationsFormatted.length" class="cp-badge wp-badge"> <BaseCmsLabel code="pickup_only" /> ({{ totalAvailableLocations }}): {{ availableLocationsFormatted }} </span>
						</template>

						<template v-if="data.item.coupon_price"> <BaseCmsLabel code="coupon_value" />: <BaseUtilsFormatCurrency :price="data.item.coupon_price" /> </template>
						<template v-else-if="data.item.bonus_total"> <BaseCmsLabel code="bonus_total_value" />: <BaseUtilsFormatCurrency :price="data.item.bonus_total" /> </template>
						<template v-else-if="data.item.type && data.item.type == 'download' && mode != 'checkout'">
							<BaseCmsLabel code="download_files" />
						</template>

						<template v-if="mode != 'checkout'">
							<div class="wp-btns">
								<div class="wp-btn wp-btn-delete" :class="{'loading': loading}" @click="onRemove"><UiLoader class="small" v-if="loading" /><BaseCmsLabel code="remove_product" tag="span" /></div>
								<template v-if="!wishlistItems.some(wishlistItem => wishlistItem.product_id === data.item.id)">
									<ClientOnly>
										<CatalogSetWishlist :item="data" mode="cart" />
									</ClientOnly>
								</template>
							</div>
						</template>
					</div>

					<template v-if="mode != 'checkout'">
						<BaseWebshopQty :quantity="data.quantity" :item="data" :steps="data.package_qty > 1 ? data.package_qty : 1" mode="cart" v-slot="{loading, status, onDecrement, onIncrement, onUpdate, onReset, quantity}">
							<div class="wp-qty-container" :class="{'loading': loading}">
								<div class="wp-qty">
									<div class="wp-btn-qty wp-btn-dec" @click="onDecrement">-</div>
									<input class="wp-input-qty product_qty_input" type="text" :value="quantity" @keyup="onUpdate" @blur="onReset" />
									<div class="wp-btn-qty wp-btn-inc" @click="onIncrement">+</div>
									<div class="wp-unit" v-if="data?.unit">{{data.unit}}</div>
									<div class="wp-unit" v-else><BaseCmsLabel code="unit" /></div>
									<span v-show="status" class="wp-message product_message" :class="[{'product_message_response_error': status?.data?.label_name == 'error_limitqty' || status == 'error_product_maximum_qty'}, 'product_message_' + data.shopping_cart_code]"
										><BaseCmsLabel :code="status" />{{status == 'error_product_maximum_qty' ? ` ${quantity} kom` : ''}}</span
									>
								</div>
							</div>
						</BaseWebshopQty>
					</template>

					<div class="wp-total">
						<template v-if="data.total_basic < data.total">
							<div v-if="data.total_basic > data.total" class="wwp-old-price ww-old-price"><BaseUtilsFormatCurrency :price="data.total_basic" /></div>
							<div class="wpw-price-current ww-current-price" :class="[{'wwp-price-discount ww-discount-price': data.total_basic > data.total}]"><BaseUtilsFormatCurrency :price="data.total" /></div>
							<div class="wwp-qty-count" v-if="data.quantity > 1">
								<span>{{ data.quantity }} x </span><BaseUtilsFormatCurrency :price="data.unit_price" />
							</div>
							<div class="wwp-lowest-price ww-lowest-price" v-if="data.extra_price_lowest > 0 && data.total_basic > data.total">
								<BaseCmsLabel code="lowest_price" tag="span" />: <strong><BaseUtilsFormatCurrency :price="data.extra_price_lowest" /></strong>
							</div>
						</template>
						<template v-else>
							<template v-if="b2b">
								<div class="ww-current-price"><BaseUtilsFormatCurrency :price="data.total_basic" /></div>
							</template>
							<template v-else>
								<WebshopLoyalty v-slot="{onSubmit, newIsActive, loading, loyalty}">
									<template v-if="data.type != 'coupon'">
										<template v-if="loyalty?.active">
											<div class="wp-old-price">
												<span><BaseUtilsFormatCurrency :price="data.total_basic" /></span>
											</div>
											<div class="wp-current-price wp-discount-price" v-if="data.discount_percentage > loyalty.discount_percent">
												<span><BaseUtilsFormatCurrency :price="data.total" /></span>
											</div>

											<div class="wp-current-price wp-discount-price" v-else><BaseUtilsFormatCurrency :price="(data.total_basic * (1 - (loyalty.discount_percent  / 100)))" /></div>
										</template>
										<template v-else>
											<div class="wp-old-price" v-if="data.total_basic > data.total">
												<span><BaseUtilsFormatCurrency :price="data.total_basic" /></span>
											</div>
											<div class="wp-current-price" :class="{'wp-discount-price': data.total_basic > data.total}">
												<span><BaseUtilsFormatCurrency :price="data.total" /></span>
											</div>
										</template>
									</template>
									<template v-else>
										<div class="wp-current-price"><BaseUtilsFormatCurrency :price="data.total" /></div>
									</template>
								</WebshopLoyalty>
							</template>
						</template>

						<div class="wp-qty-count" v-if="data.quantity > 1">
							<!-- FIXME INTEG - ne dobivam podatak za 'related_item_price_qty'

							<?php if (!empty($product_status['related_item_price_qty'])): ?>
								<?php if ($product_status['related_item_price_qty'] == $product_status['qty']): ?>
									<span class="product_qty"><?php echo $product_status['qty']; ?></span> x 
									<?php echo Utils::currency_format($product_status['related_item_price'] * $currency['exchange'], $currency['display']); ?>
									<?php echo Utils::get_second_pricetag_string($product_status['related_item_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
								<?php else: ?>
								<?php $qty = ($product_status['qty'] - $product_status['related_item_price_qty']); ?>
									<?php $price = ((!empty($shopping_cart['total_extra_loyalty']) AND !empty($product_status['loyalty_price'])) ? $product_status['loyalty_price'] : $product_status['price']); ?>
									<span class="product_qty_1"> <?php echo $product_status['related_item_price_qty']; ?></span> x <?php echo Utils::currency_format($product_status['related_item_price'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($product_status['related_item_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?> + <span class="product_qty_2"><?php echo $qty; ?></span> x <?php echo Utils::currency_format($price * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
								<?php endif; ?>
							<?php else: ?>
								<span class="product_qty"><?php echo $product_status['qty']; ?></span> x 
								<?php echo Utils::currency_format($price * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							<?php endif; ?> -->
							<span>{{ data.quantity }} x </span><BaseUtilsFormatCurrency :price="data.unit_price" />
						</div>
						<span class="wp-lowest-price" v-if="data.extra_price_lowest && data.discount_percentage > 0 && data.extra_price_lowest > 0">
							<BaseCmsLabel code="lowest_price" tag="span" />: <strong><BaseUtilsFormatCurrency :price="data.extra_price_lowest" /></strong>
						</span>
					</div>
				</div>
			</BaseCatalogWishlist>
		</div>
	</BaseWebshopRemoveProduct>
</template>

<script setup>
	const {b2b} = useProfile();
	const props = defineProps(['data', 'item', 'mode', 'pickupErrors']);

	const filteredLocations = computed(() => {
		return props.data?.meta_data?.available_pickup_locations?.filter(loc => loc.available_qty > 0) || []
	})

	const totalAvailableLocations = computed(() => filteredLocations.value.length)
	const availableLocationsFormatted = computed(() => {
	return filteredLocations.value
		.map(loc => `${loc.title}`)
		.join(', ')
	})
</script>
