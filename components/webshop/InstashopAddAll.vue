<template>
	<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :data="itemsData" :redirect="false" :add-to-cart-modal="false">
		<div class="instashop-bottom">
			<div class="instashop-total-products">
				<BaseCmsLabel code="instashop_total" :replace="[{'%TOTAL_PRODUCTS%': totalProducts.length}]" />
				<strong><BaseUtilsFormatCurrency :price="totalPrice" /></strong>
			</div>

			<div class="btn btn-orange cd-btn-add btn-instashop-add" :class="{'loading': loading}" @click="onAddToCart"><UiLoader v-if="loading" /><BaseCmsLabel code="add_instashop_to_shopping_cart" tag="span" /></div>
		</div>
	</BaseWebshopAddToCart>
</template>

<script setup>
	const {b2b, isLoggedIn, user, hasLoyalty} = useProfile();
	const props = defineProps(['items', 'model']);
	const totalProducts = [];
	const itemsData = computed(() => {
		return props.items.map(item => {
			return {
				modalData: item,
				shopping_cart_code: item.shopping_cart_code,
				quantity: item.qty,
			};
		});
	});

	const totalPrice = computed(() => {
		return props.items.reduce((sum, item) => {
			if (item?.available_qty > 0) {
				if(b2b.value){
					return sum + (item.price_b2b_custom || 0);
				}
				else if(hasLoyalty.value){
					return sum + (item.loyalty_price || 0);
				}else{
					return sum + (item.price_custom || 0);
				}
			}
			return sum;
		}, 0);
	});

	if(props?.items){
		props.items.forEach(item => {
			if(item?.available_qty > 0){
				totalProducts.push({modalData: item});
			}
		});
	}
</script>

<style lang="less" scoped>
	.instashop-bottom{
		position: relative; display: flex; justify-content: flex-end; gap: 32px; align-items: center; margin-top: 15px;
		@media (max-width: @tp){padding: 0 16px; flex-flow: column; align-items: inherit; margin-top: 18px; gap: 12px;}
	}
	.btn-instashop-add{
		width: 280px; flex-grow: 0; flex-shrink: 0;
		@media (max-width: @tp){width: 100%;}
	}
	.instashop-total-products{
		display: flex; flex-flow: column; align-items: flex-end; font-size: 16px; line-height: 24px; gap: 3px;
		@media (max-width: @tp){flex-flow: row; align-items: center; justify-content: space-between; font-size: 14px; line-height: 20px;}
	}
</style>
