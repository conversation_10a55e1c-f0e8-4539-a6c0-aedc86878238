<template>
	<Body class="page-checkout main-offset-sm" />
	<BaseWebshopCheckout>
		<ClientOnly>
			<BaseWebshopShippingCountry init :ip-detect="true" default="hr" v-slot="{submitCountry, selected}">
				<BaseWebshopCart v-slot="{parcels, counter}">
					<Body :class="{'body-country-select': hasPickupProducts && selected?.code !== 'hr'}" />
					<WebshopShippingCountrySelect mode="checkout">
						<template #btnChangeCountryBottom>
							<BaseCmsLabel code="autochange_croatia" tag="div" class="btn-autochange" @click="() => { submitCountry({code: 'hr'}); countrySelect = !countrySelect; reloadNuxtApp(); }" />
						</template>
					</WebshopShippingCountrySelect>
					<header class="header">
						<div class="header-placeholder"></div>
						<div class="header-body">
							<div class="checkout-return">
								<NuxtLink :to="logoLink" class="btn-return-home"><BaseCmsLabel code="return_to_homepage" /></NuxtLink>
							</div>
							<div class="wrapper pos-r wrapper-header">
								<BaseCmsLogo class="logo" />
								<BaseCmsLabel code="user_support" tag="div" class="header-contact" />
								<BaseCmsLabel code="checkout_title" tag="h1" class="checkout-title" />
							</div>
						</div>
					</header>

					<div class="main">
						<!-- FIXME INTEG dodati tracking
							<?php foreach($products as $product_code => $product_data):?>
								<div>
									<span data-tracking_gtm_products="Checkout|<?php echo $product_data['code']; ?>">
										<span style="display: none;" data-product_code="<?php echo $product_code; ?>"><?php echo $product_data['code']; ?></span>
										<span style="displa: none;" data-product_title="<?php echo $product_code ?>"><?php echo $product_data['title'] ?></span>
										<span style="display: none;" data-product_price="<?php echo $product_code;; ?>"><?php echo Utils::currency_format($product_data['price']); ?></span>
										<span style="display: none;" data-product_manufacturer_title="<?php echo $product_code; ?>"><?php echo trim(Arr::get($product_data, 'manufacturer_title')); ?></span>
										<span style="display: none;" data-product_category_title="<?php echo $product_code; ?>"><?php echo trim(Arr::get($product_data, 'category_title')); ?></span>
									</span>
								</div>
							<?php endforeach;?>
						-->
						<div class="df wc-row">
							<slot name="wcCol1" />
							<div class="wc-col wc-col2 wc-step1-col2">
								<slot name="wcCol2" />
								<div class="wc-coupons" v-if="mode != 'login'">
									<div class="wc-col-cnt">
										<WebshopCouponForm mode="checkout" />
									</div>
								</div>

								<WebshopLoyaltyNew v-if="mode != 'login'" />

								<div class="wc-priority-order" v-if="mode != 'login'">
									<div class="wc-col-cnt priority-order-col-cnt">
										<WebshopPriorityOrder mode="checkout" />
									</div>
								</div>

								<div class="ww-cart" v-if="parcels[0]?.items?.length && mode != 'login'">
									<div class="wc-col-cnt wc-col-cart">
										<div class="ww-cart-header">
											<h2 class="wc-title w-cart-title">
												<BaseCmsLabel code="products_in_shopping_cart" /> <span class="counter">({{counter}})</span>
											</h2>
											<BaseUtilsAppUrls v-slot="{items: appUrls}">
												<NuxtLink :to="appUrls.webshop_shopping_cart" class="w-btn-change"><BaseCmsLabel code="change_cart" tag="span" /></NuxtLink>
											</BaseUtilsAppUrls>
										</div>

										<!-- Cart items -->
										<div class="ww-cart-btns" @click="cartItemsSmall = !cartItemsSmall">
											<BaseCmsLabel code="toggle_cart_items" class="btn-toggle-cart" :class="{'active': !cartItemsSmall}" />
										</div>

										<div class="ww-cart-items" v-if="cartItemsSmall">
											<WebshopCartItem v-for="item in parcels[0].items" mode="checkout" :data="item" :key="item.shopping_cart_code" />
										</div>
									</div>
									<div class="step3-footer" v-if="mode != 'login'">
										<BaseWebshopFreeShipping v-slot="{item}">
											<WebshopFreeDeliveryProgressBar :parcels="parcels" :toFree="item" />
										</BaseWebshopFreeShipping>
										<div class="wc-col-cnt wc-col-totals">
											<WebshopTotal :simple="true" mode="checkout" />
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</BaseWebshopCart>
			</BaseWebshopShippingCountry>
		</ClientOnly>
	</BaseWebshopCheckout>
</template>

<script setup>
	const config = useAppConfig();
	const {getLang} = useLang();
	const logoLink = computed(() => {
		const currLanguage = getLang();
		return config.lang == currLanguage.code ? '/' : currLanguage.base_url;
	});
	const props = defineProps(['mode']);

	const cartItemsSmall = ref(true);

	const {getCartData, updateShipping} = useWebshop();
	const parcel = computed(() => getCartData()?.parcels[0]);
	const hasPickupProducts = computed(() => {
		const cartProducts = parcel.value?.items;
		if (!cartProducts?.length) return false;
		return cartProducts.some(item => item.type == 'pickup');
	});

	const {insertAfter, insertBefore, onMediaQuery} = useDom();
	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 900px)',
		timeout: 600,
		enter: () => {
			insertAfter('.header-contact', '.footer-col3');
			insertAfter('.wc-col-totals', '.ww-cart-header');
			insertBefore('.free-delivery-container', '.ww-cart-btns');
			insertAfter('.w-btn-change', '.btn-toggle-cart');
		},
		leave: () => {
			window.location.reload();
		},
	});
</script>
