<template>
	<BaseWebshopPriorityOrder v-slot="{onSubmit, loading, isActive}">
		<div class="priority-order" :class="[{'loading': loading}, mode]">
			<input type="checkbox" id="field-priority" :checked="isActive" @click.prevent="onSubmit" />
			<BaseCmsLabel for="field-priority" code="priority_order" tag="label" />
			<div v-if="loading" />
		</div>
	</BaseWebshopPriorityOrder>
</template>

<script setup>
	const props = defineProps(['mode']);
</script>
