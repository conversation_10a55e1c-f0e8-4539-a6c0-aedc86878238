<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<h1>{{ page?.seo_h1 }}</h1>
			<BaseAuthLoginForm v-slot="{fields, submitting, onSubmit, formError}">
				<form :class="{'submitting': submitting}" @submit="onSubmit">
					<div class="global-error" v-show="formError">{{ formError }}</div>
					<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
						<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}]">
							<BaseFormInput />
							<BaseCmsLabel tag="label" :for="item.name" :code="item.name" />
							<span class="error" v-show="errorMessage" v-html="errorMessage" />
						</p>
					</BaseFormField>
					<button type="submit">
						<BaseCmsLabel code="login" />
					</button>
				</form>
			</BaseAuthLoginForm>
		</ClientOnly>
	</BaseCmsPage>
</template>
