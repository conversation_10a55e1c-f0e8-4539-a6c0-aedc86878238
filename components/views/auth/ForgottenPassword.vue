<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<h1>{{ page?.seo_h1 }}</h1>
			<BaseAuthForgottenPasswordForm v-slot="{fields, submitting, onSubmit, status, urls}">
				<div v-if="status && !status?.success" class="global-error">{{ status.data?.label_name }}</div>
				<div v-if="status && status?.success" class="global-success">{{ status.data?.label_name }}</div>

				<form v-if="!status || (status && !status.success)" :class="{'submitting': submitting}" @submit="onSubmit">
					<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
						<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}]">
							<BaseFormInput />
							<BaseCmsLabel tag="label" :for="item.name" :code="item.name" />
							<span class="error" v-show="errorMessage" v-html="errorMessage" />
						</p>
					</BaseFormField>
					<button type="submit">
						<BaseCmsLabel code="send" />
					</button>

					<p>
						<NuxtLink :to="urls.login"><BaseCmsLabel code="back_to_login" /></NuxtLink><br />
						<NuxtLink :to="urls.signup"><BaseCmsLabel code="add_new_account" /></NuxtLink>
					</p>
				</form>
			</BaseAuthForgottenPasswordForm>
		</ClientOnly>
	</BaseCmsPage>
</template>
