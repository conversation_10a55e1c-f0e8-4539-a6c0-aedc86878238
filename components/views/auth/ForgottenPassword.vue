<template>
	<Body class="page-auth-forgotten_password page-auth-forgotten_password page-forgotten-password main-offset-sm" />
	<BaseCmsPage v-slot="{page}">
		<CmsTwoColumns>
			<template #main>
				<div class="fg1 lists main-content">
					<CmsBreadcrumbs :items="page?.breadcrumbs" />
					<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
					<ClientOnly>
						<BaseAuthForgottenPasswordForm class="form-label auth-form auth-forgotten-password-form" v-slot="{fields, status, urls}">
							<div v-if="status && status?.success" class="global-success"><BaseCmsLabel :code="status.data?.label_name" /></div>

							<template v-if="!status || (status && !status.success)">
								<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
									<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}]">
										<BaseCmsLabel tag="label" :for="item.name" :code="item.name" />
										<BaseFormInput />
										<span class="error" v-show="errorMessage" v-html="errorMessage" />
									</p>
								</BaseFormField>
								<div class="a-btns">
									<div class="submit">
										<button type="submit" class="btn btn-auth-submit"><BaseCmsLabel code="send" /></button>
									</div>
									<div class="a-links">
										<NuxtLink :to="urls.auth_login" class="button2 back"><BaseCmsLabel code="back_to_login" tag="span" /></NuxtLink>
										<NuxtLink :to="urls.auth_signup" class="button2 signup"><BaseCmsLabel code="add_new_account" tag="span" /></NuxtLink>
									</div>
								</div>
							</template>
						</BaseAuthForgottenPasswordForm>
					</ClientOnly>
				</div>
			</template>
			<template #sidebar>
				<CmsSidebar />
			</template>
		</CmsTwoColumns>
	</BaseCmsPage>
</template>
