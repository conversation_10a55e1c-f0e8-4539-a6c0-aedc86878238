<template>
	<!-- Template: set new password if forgotten -->

	<BaseCmsPage v-slot="{page}" :fetch-slug-segments="2">
		<ClientOnly>
			<h1>{{ page?.seo_h1 }}</h1>
			<BaseAuthNewPasswordForm v-slot="{fields, loading, status, values}" v-if="!status || status?.success == false" :class="{'loading': loading}">
				<div v-if="status?.success == false" class="global-error">{{ status.data.label_name }}</div>
				<div v-if="status?.success" class="global-success">{{ status.data.label_name }}</div>

				<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
					<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}]">
						<BaseFormInput />
						<BaseCmsLabel tag="label" :for="item.name" :code="item.name" />
						<span class="error" v-show="errorMessage" v-html="errorMessage" />
					</p>
				</BaseFormField>
				<button type="submit">
					<BaseCmsLabel code="send" />
				</button>
			</BaseAuthNewPasswordForm>
		</ClientOnly>
	</BaseCmsPage>
</template>
