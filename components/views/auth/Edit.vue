<template>
	<BaseCmsPage v-slot="{page}">
		<h1>{{ page?.seo_h1 }}</h1>
		<ClientOnly>
			<BaseAuthEditForm v-slot="{fields, submitting, onSubmit, meta, values, status}">
				<template v-if="status?.data?.errors?.length">
					<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.field }}: {{ error.error }}</div>
				</template>
				<div v-if="status?.success" class="global-success"><BaseCmsLabel :code="status.data.label_name" /></div>

				<form :class="{'submitting': submitting}" @submit="onSubmit">
					<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
						<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
							<BaseFormInput :id="item.name" />
							<BaseCmsLabel tag="label" :for="item.name" :code="item.name" />
							<span class="error" v-show="errorMessage" v-html="errorMessage" />
						</p>
					</BaseFormField>
					<button type="submit">
						<BaseCmsLabel code="form_login" />
					</button>
				</form>
			</BaseAuthEditForm>
		</ClientOnly>
	</BaseCmsPage>
</template>
