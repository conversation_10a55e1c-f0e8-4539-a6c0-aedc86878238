<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<h1>{{ page?.seo_h1 }}</h1>
			<BaseAuthComments :separate-by-module="true" v-slot="{items, counter}">
				<p>Ukupno komentara: {{ counter }}</p>

				<div v-if="items?.catalogproduct?.length">
					<h2>Komentari proizvoda ({{ items.catalogproduct.length }})</h2>
					<BaseUiAccordion v-for="item in items.catalogproduct" :key="item">
						<BaseUiAccordionPanel :id="item.id" v-slot="{onToggle, active}">
							<div class="a-comment">
								<div class="a-comment-image"><img src="https://via.placeholder.com/100x100" width="100" height="100" alt="" /></div>
								<div class="a-comment-title">{{ item.title }}</div>
								<div class="a-comment-rate">
									<span v-for="i in 5" :key="i"><span v-if="i <= item.rate">*</span></span>
								</div>
								<div class="a-comment-reviews">
									<span>+ ({{ item.review_positive }})</span>
									<span>- ({{ item.review_negative }})</span>
								</div>
								<div v-if="item.message" class="a-comment-toggle">
									<span @click="onToggle" v-if="!active">Prikaži komentar</span>
									<span @click="onToggle" v-if="active">Sakrij komentar</span>
								</div>
							</div>
							<div class="a-comment-message" v-show="active">
								<p><BaseUtilsFormatDate :date="item.datetime_created" /></p>
								{{ item.message }}
							</div>
						</BaseUiAccordionPanel>
						<hr />
					</BaseUiAccordion>
				</div>

				<div v-if="items?.publish?.length">
					<h2>Komentari članaka ({{ items.publish.length }})</h2>
					<ul>
						<li v-for="item in items.publish" :key="item">{{ item.title }}</li>
					</ul>
				</div>
			</BaseAuthComments>
		</ClientOnly>
	</BaseCmsPage>
</template>
