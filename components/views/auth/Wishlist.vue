<template>
	<Body class="page-catalog-view_wishlist page-catalog-view_wishlist main-offset-sm page-auth" />
	<BaseCmsPage>
		<BaseAuthUser>
			<AuthMainLayout>
				<template #authContent>
					<ClientOnly>
						<BaseCatalogWishlist v-slot="{items, onRemove}">
							<h1 class="a-title">
								<BaseCmsLabel code="my_wishlist" />
								<span v-if="items?.length > 0" class="a-title-counter a-title-wishlist-counter active">
									(<span class="wishlist_count">{{items?.length}}</span
									>)
								</span>
							</h1>

							<!-- <?php if ($item['total_items'] > 0): ?>
								<div id="view_wishlist" class="auth-wishlist-items">
									<?php if (!empty($wishlist['url_delete'])): ?>
										<div class="wishlist-auth-btns wishlist-auth-btns-top">
											<a class="btn btn-white btn-wishlist-delete btn-auth-wishlist-delete" href="javascript:cmswishlist.remove('catalog_catalogproduct')"><span><?php echo Arr::get($cmslabel, 'wishlist_delete'); ?></span></a>
										</div>
									<?php endif; ?>

									<div id="items_wishlist" class="cart-wishlist-items">
										<?php echo View::factory('catalog/index_entry_wishlist', ['items' => $item['items'], 'class' => 'wp-auth-wishlist', 'mode' => 'auth-wishlist', 'list' => 'Lista želja']); ?>
									</div>

									<?php if ($item['total_items'] > 6 AND !empty($wishlist['url_delete'])): ?>
										<div class="wishlist-auth-btns wishlist-auth-btns-bottom">
											<a class="btn btn-white btn-wishlist-delete btn-auth-wishlist-delete" href="javascript:cmswishlist.remove('catalog_catalogproduct')"><span><?php echo Arr::get($cmslabel, 'wishlist_delete'); ?></span></a>
										</div>
									<?php endif; ?>
								</div>

								<div class="c-empty wishlist-empty" id="empty_wishlist" style="display: none;">
									<div class="no-wishlist-title"><?php echo Arr::get($cmslabel, 'wishlist_no_products'); ?></div>
								</div>
							<?php else: ?>
								<div class="c-empty wishlist-empty">
									<div class="no-wishlist-title"><?php echo Arr::get($cmslabel, 'wishlist_no_products'); ?></div>
								</div>
							<?php endif; ?> -->

							<div v-if="items?.length > 0" id="view_wishlist" class="auth-wishlist-items">
								<div v-if="items?.length > 0" @click="onRemove" class="wishlist-auth-btns wishlist-auth-btns-top">
									<span class="btn btn-white btn-wishlist-delete btn-auth-wishlist-delete"><BaseCmsLabel code="wishlist_delete" /></span>
								</div>
								<BaseWebshopCart v-slot="{parcels}">
									<template v-for="item in items" :key="item.id">
										<CatalogIndexEntryWishlist :item="item" :cartItems="parcels[0].items" mode="auth-wishlist" class="wp-auth-wishlist" list="Lista želja" />
									</template>
								</BaseWebshopCart>
								<div v-if="items?.length > 6" @click="onRemove" class="wishlist-auth-btns wishlist-auth-btns-bottom">
									<span class="btn btn-white btn-wishlist-delete btn-auth-wishlist-delete"><BaseCmsLabel code="wishlist_delete" /></span>
								</div>
							</div>
							<div v-else class="auth-no-content" id="empty_wishlist">
								<BaseCmsLabel code="wishlist_no_products" />
							</div>
						</BaseCatalogWishlist>
					</ClientOnly>
				</template>
			</AuthMainLayout>
		</BaseAuthUser>
	</BaseCmsPage>
</template>

<script setup></script>

<style lang="less" scoped>
	.btn-auth-wishlist-delete{cursor: pointer;}
</style>
