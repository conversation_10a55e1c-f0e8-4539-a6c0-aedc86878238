<template>
	<Body class="page-authmy-my_handle page-authmy-my_handle main-offset-sm page-auth" />
	<BaseCmsPage v-slot="{page}">
		<AuthMainLayout>
			<template #authContent>
				<ClientOnly>
					<BaseAuthCoupons v-slot="{items}">
						<h1 class="a-title">
							{{page.title}}<span class="a-title-counter" v-if="items.length > 0"> ({{items.length}})</span>
						</h1>

						<WebshopCouponForm mode="auth" />
						<div v-if="items?.length" class="auth-coupons-list">
							<div class="w-table w-table-head">
								<BaseCmsLabel tag="tag" class="w-table-col col-coupon-num" code="coupon_code" />
								<BaseCmsLabel tag="tag" class="w-table-col col-coupon-desc" code="coupon_description" />
								<BaseCmsLabel tag="tag" class="w-table-col col-coupon-total" code="coupon_value" />
								<BaseCmsLabel tag="tag" class="w-table-col col-coupon-valid" code="coupon_valid_until" />
							</div>
							<div v-for="item in items" :key="item.id" class="order-row w-table coupon-row">
								<div class="w-table-col col-coupon-num">
									<BaseCmsLabel tag="span" class="label" code="coupon_code" />
									<div class="col-code-value">{{ item.code }}</div>
								</div>

								<div class="w-table-col col-coupon-desc">
									<template v-if="item.description">
										<div class="col-description">{{ item.description }}</div>
									</template>
									<template v-else>-</template>
								</div>

								<div class="w-table-col col-coupon-total">
									<BaseCmsLabel class="label" code="coupon_value" tag="span" />
									<strong v-if="item.type == 'f'"><BaseUtilsFormatCurrency :price="item.coupon_price" /></strong>
									<strong v-else>-{{ item.coupon_percent * 100 }}%</strong>
								</div>

								<div class="w-table-col col-coupon-valid">
									<template v-if="+item.max_used > 0 && item.current_used >= item.max_used">
										<BaseCmsLabel code="coupon_used" />
									</template>
									<template v-else-if="item.datetime_expire">
										<div class="col-valid-value" :class="{ 'col-valid-value-expired' : item.datetime_expire < Math.floor(Date.now() / 1000) }">
											<BaseCmsLabel v-if="item.datetime_expire > Math.floor(Date.now() / 1000)" tag="span" code="coupon_valid_until" class="label" />
											<template v-else><BaseCmsLabel code="expired" /></template>&nbsp;
											<BaseUtilsFormatDate :date="item.datetime_expire" />
										</div>
									</template>
									<template v-else>-</template>
								</div>
							</div>
						</div>
						<BaseCmsLabel v-else code="auth_no_coupons" tag="div" class="auth-no-content" />
					</BaseAuthCoupons>
					<template #fallback>
						<BaseThemeUiLoading />
					</template>
				</ClientOnly>
			</template>
		</AuthMainLayout>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
	const fieldCoupon = ref({'id': 'coupon', 'type': 'text'})
</script>

<style scoped lang="less"></style>
