<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<h1>{{ page?.seo_h1 }}</h1>
			<ClientOnly>
				<BaseWebshopCouponForm mode="auth" />
				<BaseAuthCoupons v-slot="{items, onActivate, onDeactivate, onRemove, status, activatedCoupons}">
					<BaseCmsLabel v-if="status?.label_name" :code="status.label_name" />

					<ul class="base-auth-coupons">
						<li v-for="item in items" :key="item.id">
							{{ activatedCoupons.includes(item.code) ? 'AKTIVIRAN' : 'NEAKTIVIRAN' }}
							<p>Šifra: {{ item.code }}</p>
							<p>
								Vrijednost:
								<template v-if="item.type == 'f'">
									<BaseUtilsFormatCurrency :price="item.coupon_price" />
								</template>
								<template v-else> {{ item.coupon_value }}% </template>
							</p>
							<p v-if="item.datetime_created">Kreirano: <BaseUtilsFormatDate :date="item.datetime_created" /></p>
							<p v-if="item.datetime_expire">Ističe: <BaseUtilsFormatDate :date="item.datetime_expire" /></p>
							<p v-if="item.description">Opis: {{ item.description }}</p>
							<p>
								<span @click="onActivate(item.code)">Aktiviraj</span>
								<span @click="onDeactivate(item.code)">Deaktiviraj</span>
								<span @click="onRemove(item.code)">Ukloni</span>
							</p>
						</li>
					</ul>
				</BaseAuthCoupons>
			</ClientOnly>
		</ClientOnly>
	</BaseCmsPage>
</template>
