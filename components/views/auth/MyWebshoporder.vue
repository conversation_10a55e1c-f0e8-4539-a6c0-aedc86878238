<template>
	<Body class="page-authmy-my_handle page-authmy-my_handle main-offset-sm page-auth" />
	<BaseCmsPage v-slot="{page}">
		<AuthMainLayout>
			<template #authContent>
				<BaseAuthOrders v-slot="{items: orders}">
					<h1 class="a-title">
						{{page.title}}<span class="a-title-counter" v-if="orders.length > 0"> ({{orders.length}})</span>
					</h1>
					<div id="items_widgetlist_webshop_layout" v-if="orders?.length">
						<AuthWebshopOrders :orders="orders" mode="orders" />
					</div>
					<BaseCmsLabel code="no_orders" class="auth-no-orders" tag="div" v-interpolation v-else />
				</BaseAuthOrders>
			</template>
		</AuthMainLayout>
	</BaseCmsPage>
</template>
