<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<h1>{{ page?.seo_h1 }}</h1>
			<BaseAuthOrders v-slot="{items, counter, onLoadMore, loading, pagination}" thumb-preset="cart">
				<template v-if="items?.length">
					Total: {{ counter }}<br /><br />
					<BaseUiAccordion>
						<BaseUiAccordionPanel v-for="item in items" :key="item.id" :id="item.id" v-slot="{onToggle, active}">
							<div>
								Broj narudžbe: {{ item.number }}<br />
								Status: {{ item.status.title }}<br />
								Kreirano: <BaseUtilsFormatDate :date="item.datetime_created" /><br />
								<div v-if="item.extra_data?.products_data?.length" @click="onToggle">
									<span v-show="!active"> Prikaži proizvode </span>
									<span v-show="active"> Sakrij proizvode </span>
								</div>
							</div>
							<ul v-if="item.extra_data?.products_data?.length" v-show="active">
								<li class="order" v-for="item in item.extra_data?.products_data" :key="item.id">
									<BaseUiImage :data="item.main_image_thumb" default="/no-image-50.jpg" />
									<div class="order-title">{{ item.title }}</div>
								</li>
							</ul>
							<hr />
						</BaseUiAccordionPanel>
					</BaseUiAccordion>
					<div v-if="pagination.next_page" @click="onLoadMore">Učitaj još</div>
				</template>
				<div v-else>Trenutno nema narudžbi</div>
			</BaseAuthOrders>
		</ClientOnly>
	</BaseCmsPage>
</template>
