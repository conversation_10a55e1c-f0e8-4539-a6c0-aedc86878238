<template>
	<BaseCmsPage v-slot="{page}" :fetch-slug-segments="2">
		<h1>{{ page?.seo_h1 }}</h1>
		<ClientOnly>
			<BaseAuthSignupForm :fields-config="fieldsConfig" v-slot="{fields, submitting, onSubmit, apiErrors, meta, values, status, contentType}">
				<BaseAuthConfirmSignup v-if="contentType == 'confirmSignup'" v-slot="{status, loading}">
					{{ status.label_name }}
					<BaseCmsLabel :code="status.label_name" />
				</BaseAuthConfirmSignup>

				<template v-if="contentType != 'confirmSignup'">
					<template v-if="!status?.success">
						<template v-if="status?.data?.errors?.length">
							<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.field }}: {{ error.error }}</div>
						</template>
						<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
							<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
								<BaseFormInput :id="item.name" />
								<BaseCmsLabel tag="label" :for="item.name" :code="item.name" />
								<span class="error" v-show="errorMessage" v-html="errorMessage" />
							</p>
						</BaseFormField>
						<button type="submit">
							<BaseCmsLabel code="form_login" />
						</button>
					</template>
					<div v-else>
						<BaseCmsLabel tag="div" class="global-success" :code="status.data.label_name" />
					</div>
				</template>
			</BaseAuthSignupForm>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const fieldsConfig = {
		//order: ['email', 'password', 'loyalty_request'],
		gender: {
			type: 'radio',
		},
		loyalty_request: {
			type: 'radio',
		},
	};
</script>
