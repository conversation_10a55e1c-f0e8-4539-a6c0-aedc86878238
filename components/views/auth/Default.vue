<template>
	<Body class="page-auth-index main-offset-sm page-auth" />
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<BaseAuthUser v-slot="{user, urls, currentUrl}">
				<AuthMainLayout>
					<template #authContent>
						<h1 class="a-title" v-if="page?.title">{{page.title}}</h1>
						<BaseAuthEditForm class="form-label form-default-birthday ajax_siteform_loading" v-slot="{fields, loading, status}">
							<template v-if="status?.data?.errors?.length">
								<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.field }}: {{ error.error }}</div>
							</template>
							<div v-if="status?.success" class="global-success"><BaseCmsLabel :code="status.data.label_name" /></div>
							<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
								<template v-if="item.name == 'birthday'">
									<div class="auth-default-birthday" v-if="!item.value">
										<BaseCmsLabel class="birthday-note" tag="span" code="auth_birthday_note" />
										<div class="field-birthday-cnt">
											<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
												<UiDatePicker :minDate="'01.01.1900'" :maxDate="new Date().toLocaleDateString('hr-HR').replace(/\s+/g, '')" :value="item.value" :uid="item.name" v-slot="{ selectedDate }">
													<BaseFormInput hidden readonly :value="formatDate(selectedDate)" />
													<label :for="`dp-input-${item.name}`" class="label-datepicker">
														<BaseCmsLabel :code="item.name" />
													</label>
												</UiDatePicker>
												<span class="error" v-show="errorMessage" v-html="errorMessage" />
											</p>
										</div>
										<button class="btn btn-birthday-save" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="save2" tag="span" /></button>
									</div>
								</template>
								<template v-else>
									<div style="display: none">
										<BaseFormInput hidden readonly :id="item.name" />
									</div>
								</template>
							</BaseFormField>
						</BaseAuthEditForm>
						<div class="df a-intro">
							<div class="fg1 a-intro-left">
								<BaseCmsLabel code="you_can" class="a-intro-title" tag="p" />
								<ul class="a-menu" v-interpolation>
									<!-- <NuxtLink :to="urls.auth_my_webshoporder" :class="{'active': urls.auth_my_webshoporder == currentUrl}"><BaseCmsLabel code="auth_view_orders" tag="span" /></NuxtLink> -->
									<li>
										<BaseCmsLabel tag="span" code="auth_view_orders" :replace="[{'%LINK%': urls.auth_my_webshoporder}]" />
									</li>
									<li>
										<!-- <NuxtLink :to="urls.auth_wishlist" :class="{'active': urls.auth_wishlist == currentUrl}"><BaseCmsLabel code="auth_view_wishlist" tag="span" /></NuxtLink> -->
										<BaseCmsLabel tag="span" code="auth_view_wishlist" :replace="[{'%LINK%': urls.auth_wishlist}]" />
									</li>
									<li v-if="user && user.b2b == 0">
										<!-- <NuxtLink :to="urls.auth+'#coupons'" :class="{'active': urls.auth_my_webshopcoupon == currentUrl}"><BaseCmsLabel code="auth_view_coupons" tag="span" /></NuxtLink> -->
										<BaseCmsLabel tag="span" code="auth_view_coupons" :replace="[{'%LINK%': urls.auth_my_webshopcoupon}]" />
									</li>
									<li>
										<!-- <NuxtLink :to="urls.auth_edit" :class="{'active': urls.auth_edit == currentUrl}"><BaseCmsLabel code="auth_edit_profile" tag="span" /></NuxtLink> -->
										<BaseCmsLabel tag="span" code="auth_edit_profile" :replace="[{'%LINK%': urls.auth_edit}]" />
									</li>
									<li>
										<!-- <NuxtLink :to="urls.auth_change_password" :class="{'active': urls.auth_change_password == currentUrl}"><BaseCmsLabel code="auth_change_password" tag="span" /></NuxtLink> -->
										<BaseCmsLabel tag="span" code="auth_change_password" :replace="[{'%LINK%': urls.auth_change_password}]" />
									</li>
									<li>
										<!-- <NuxtLink :to="urls.auth_logout+'?redirect=/'"><BaseCmsLabel code="auth_logout" tag="span" /></NuxtLink> -->
										<BaseCmsLabel tag="span" code="auth_logout" :replace="[{'%LINK%': urls.auth_logout+'?redirect=/'}]" />
									</li>
								</ul>
							</div>
							<div class="a-intro-user">
								<div class="a-intro-title" v-if="user?.first_name && user?.last_name">{{user.first_name}} {{user.last_name}}</div>
								<p>
									<span class="a-intro-email" v-if="user?.email">{{user.email}}</span>
									<br />
									<span class="a-intro-tel" v-if="user?.phone">{{user.phone}}</span>
								</p>
								<template v-if="user?.address">
									<p>
										{{user.address}}<br />
										{{user.zipcode}}
										{{user.city}}
									</p>
								</template>
								<NuxtLink class="btn btn-gray btn-auth-edit" :to="urls.auth_edit" :class="{'active': urls.auth_edit == currentUrl}"><BaseCmsLabel code="edit_profile" /></NuxtLink>
							</div>
						</div>
						<BaseAuthOrders v-slot="{items: orders}">
							<template v-if="orders?.length">
								<div class="auth-box auth-box-orders orders-container" id="orders">
									<div class="a-section-title a-section-orders-title">
										<NuxtLink :to="urls.auth_my_webshoporder"><BaseCmsLabel code="latest_orders" tag="span" /></NuxtLink>
									</div>
									<AuthWebshopOrders :orders="orders" mode="dashboard" />
									<div class="auth-box-btns">
										<NuxtLink class="btn" :to="urls.auth_my_webshoporder"><BaseCmsLabel code="webshop_orders_show_all" tag="span" /></NuxtLink>
									</div>
								</div>
							</template>
						</BaseAuthOrders>
					</template>
				</AuthMainLayout>
			</BaseAuthUser>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	function formatDate(date) {
		if (!(date instanceof Date)) return '';
		const day = date.getDate().toString().padStart(2, '0');
		const month = (date.getMonth() + 1).toString().padStart(2, '0');
		const year = date.getFullYear();
		return `${day}.${month}.${year}.`;
	}
</script>

<style lang="less" scoped>
	:deep(.field){
		[hidden]{display: none;}
	}
	:deep(.dp__clear_icon){
		right: 50px;
	}
	:deep(.dp__input){
		border: unset; background: white; border-radius: @borderRadius; padding: 0 25px 0 52px; border-radius: 3px 0 0 3px; background: url(assets/images/icons/calendar-black.svg) #FFF no-repeat center left +16px;
		@media(max-width: @t){
			height: 47px; padding: 0 20px 0 47px; background: url(assets/images/icons/calendar-black.svg) #FFF no-repeat center left +15px; background-size: 20px;
		}
	}
	:deep(.dp__clear_icon){
		right: 5px;
	}
	.auth-default-birthday{
		width: 100%; background: linear-gradient(180deg, #ABC075 0%, #809941 100%); border-radius: @borderRadius; padding: 16px 16px 16px 136px; color: white; display: flex; align-content: center; margin-bottom: 32px; position: relative; align-items: center;
		&:before{.pseudo(auto,auto); color: white; .icon-surprise; font: 88px/88px @fonti; left: 24px; top: 8px;}
		@media(max-width: @t){
			padding: 16px 16px 16px 100px;
			&:before{font: 70px/70px @fonti; left: 15px; top: 8px;}
		}
		@media(max-width: @m){
			padding: 19px 16px 16px 16px; flex-flow: row; flex-wrap: wrap; align-items: unset; border-radius: unset; margin: -15px -15px 15px -15px; width: calc(~"100% - -30px");
			&:before{font: 48px/48px @fonti; left: 16px; top: 8px;}
		}
	}
	.form-label{
		label{color: @textColor; font-size: 16px; left: 52px; pointer-events: none;}
		@media(max-width: @t){
			label{left: 47px; top: 13px; font-size: 14px;}
		}
	}
	.form-label .focus label, .form-label .ffl-floated label{
		font-size: 12px;
		@media(max-width: @t){top: 8px; font-size: 11px;}

	}
	.birthday-note{
		max-width: 338px; font-size: 20px; line-height: 1.3; margin-right: 30px; flex-grow: 1; flex-shrink: 0;
		@media(max-width: @l){max-width: 230px;}
		@media(max-width: @t){max-width: 170px; font-size: 15px; margin-right: 20px;}
		@media(max-width: @m){margin-right: 0; max-width: 285px; padding-left: 58px; padding-bottom: 12px;}
	}
	.field-birthday-cnt{display: flex; align-items: center; width: calc(~"100% - 115px");}
	.field-birthday{padding-bottom: 0; flex-grow: 1;}
	:deep(.btn-birthday-save){
		background: #244538; color: white; font-weight: bold; padding: 0 15px; flex-shrink: 0; border-radius: 0 3px 3px 0; .transition(background); width: 115px;
		&:hover{background: #1f3b2f; color: white;}
		&:after{display: none !important;}
		@media(max-width: @t){padding: 0 15px;}
	}
</style>
