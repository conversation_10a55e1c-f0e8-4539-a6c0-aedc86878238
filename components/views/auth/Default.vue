<template>
	<BaseCmsPage v-slot="{page}">
		<h1>{{ page?.seo_h1 }}</h1>
		<ClientOnly>
			<BaseAuthUser v-slot="{urls, user, currentUrl}">
				<p v-if="user">{{ user.first_name }} {{ user.last_name }}</p>
				<ul>
					<li :class="{'active': urls.auth_edit.url == currentUrl}"><NuxtLink :to="urls.auth_edit.url">Moji podaci</NuxtLink></li>
					<li :class="{'active': urls.auth_change_password.url == currentUrl}"><NuxtLink :to="urls.auth_change_password.url">Nova lozinka</NuxtLink></li>
					<li :class="{'active': urls.auth_my_webshoporder.url == currentUrl}"><NuxtLink :to="urls.auth_my_webshoporder.url">Narudžbe</NuxtLink></li>
					<li><NuxtLink :to="urls.auth_logout.url">Odjava</NuxtLink></li>
				</ul>
			</BaseAuthUser>
			<BaseAuthCoupons />
		</ClientOnly>
	</BaseCmsPage>
</template>
