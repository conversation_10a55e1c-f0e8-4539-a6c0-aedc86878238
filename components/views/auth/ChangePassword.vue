<template>
	<BaseCmsPage v-slot="{page}">
		<h1>{{ page?.seo_h1 }}</h1>
		<ClientOnly>
			<BaseAuthChangePasswordForm v-slot="{fields, status}">
				<template v-if="status?.data?.errors?.length">
					<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
				</template>
				<div v-if="status && status?.success" class="global-success">{{ status.data?.label_name }}</div>

				<template v-if="!status?.success">
					<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
						<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}]">
							<BaseFormInput />
							<BaseCmsLabel tag="label" :for="item.name" :code="item.name" />
							<span class="error" v-show="errorMessage" v-html="errorMessage" />
						</p>
					</BaseFormField>
					<button type="submit">
						<BaseCmsLabel code="send" />
					</button>
				</template>
			</BaseAuthChangePasswordForm>
		</ClientOnly>
	</BaseCmsPage>
</template>
