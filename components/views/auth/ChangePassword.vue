<template>
	<Body class="main-offset-sm page-auth page-change-passwrord" />
	<BaseCmsPage v-slot="{page}">
		<AuthMainLayout>
			<template #authContent>
				<ClientOnly>
					<h1 class="a-title" v-if="page?.title">{{page.title}}</h1>
					<BaseAuthChangePasswordForm v-slot="{fields, status, loading}" class="ajax_siteform form-label auth-form auth-form-edit auth-fomr-change-password">
						<template v-if="status?.data?.errors?.length">
							<div class="global_error global-error" v-for="error in status.data.errors" :key="error">{{ error.field }}: {{ error.error }}</div>
						</template>
						<div v-if="status && status?.success" class="global-success"><BaseCmsLabel :code="status.data?.label_name" /></div>
						<template v-if="!status?.success">
							<fieldset>
								<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
									<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}]">
										<BaseCmsLabel v-if="item.name == 'password'" tag="label" for="password" code="reset_your_password" />
										<BaseCmsLabel v-else-if="item.name == 'password_confirm'" tag="label" for="password_confirm" code="password_confirm" />
										<BaseCmsLabel v-else tag="label" :for="item.name" :code="item.name" />
										<BaseFormInput />
										<span class="error" v-show="errorMessage" v-html="errorMessage" />
									</p>
								</BaseFormField>
							</fieldset>
							<button type="submit" class="btn btn-auth-submit" :class="{'loading': loading}"><UiLoader v-if="loading" /><BaseCmsLabel code="save" tag="span" /></button>
						</template>
					</BaseAuthChangePasswordForm>
				</ClientOnly>
			</template>
		</AuthMainLayout>
	</BaseCmsPage>
</template>
