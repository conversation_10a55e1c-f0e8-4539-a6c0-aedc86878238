<template>
	<section class="ln-section ln-section-intro" :class="{'spacing': item.elements[0].large_space > 0}" :id="'position'+item.position" :style="{backgroundImage: `url(${lnBackground})`, backgroundPosition: 'center', backgroundSize: 'cover'}">
		<div class="wrapper">
			<div class="section-intro-cnt">
				<div class="image" v-if="item.elements[0]?.image2">
					<BaseUiImage loading="lazy" :data="item.elements[0]?.image2_thumbs?.['width150-height50']" default="/images/no-image-50.jpg" />
				</div>
				<div class="section-intro-cnt">
					<h2 class="title" v-if="item.elements[0]?.title">{{item.elements[0].title}}</h2>
					<div class="subtitle" v-if="item.elements[0]?.subtitle">{{item.elements[0].subtitle}}</div>
					<div class="lists cnt" v-if="item.elements[0]?.content" v-html="item.elements[0].content" v-interpolation />
					<NuxtLink class="btn btn-green btn-ln-intro" :to="item.elements[0].url" v-if="item.elements[0]?.url"
						><span>{{item.elements[0].url_text}}</span></NuxtLink
					>
				</div>
			</div>
		</div>
	</section>
</template>

<script setup>
	const props = defineProps(['item']);
	const {absolute} = useUrl();

	const lnBackground = computed(() => {
		if (props.item.elements[0].image_upload_path) return absolute(props.item.elements[0].image_upload_path);
	});
</script>
