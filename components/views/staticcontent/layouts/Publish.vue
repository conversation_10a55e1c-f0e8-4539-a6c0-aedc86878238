<template>
	<section class="ln-wrapper ln-section ln-section-publish" :class="{'spacing': item.elements[0]?.elements.some(element => element.large_space?.lengt > 0)}" :id="'position'+item.position">
		<h2 class="section-title" v-if="item.elements[0].elements[0]?.title">{{item.elements[0].elements[0].title}}</h2>
		<div class="p-items">
			<template v-for="itemElement in item.elements[0].elements" :key="itemElement.id">
				<PublishIndexEntry :item="itemElement" class="gray-shadow pp-ln" />
			</template>
		</div>
	</section>
</template>

<script setup>
	const props = defineProps(['item']);
</script>
