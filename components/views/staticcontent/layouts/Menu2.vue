<template>
	<nav class="ln-nav-container ln-nav-container2" :class="{'spacing': item.elements[0].large_space > 0, 'active': menuActiveSecond}">
		<div class="wrapper ln-nav-wrapper">
			<div class="btn-ln-toggle-nav" @click="menuActiveSecond = !menuActiveSecond"><BaseCmsLabel code="menu" /> <span class="toggle-icon"></span></div>
			<ul class="ln-nav ln-nav2">
				<template v-for="menuItem in items" :key="menuItem.id">
					<li v-if="menuItem.layout != 'menu' && menuItem.elements[0]?.menu_title">
						<span @click="scrollTo('#position' + menuItem.position)">{{menuItem.elements[0].menu_title}}</span>
					</li>
				</template>
			</ul>
		</div>
	</nav>
</template>

<script setup>
	const {scrollTo} = useDom();
	const props = defineProps(['item', 'items']);
	const menuActiveSecond = ref(false);
</script>
