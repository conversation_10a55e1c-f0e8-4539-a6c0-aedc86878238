<template>
	<section class="ln-wrapper ln-section ln-section-instashop" :class="{'spacing': item.elements[0]?.elements.some(element => element.large_space?.lengt > 0)}" :id="'position'+item.position">
		<h2 class="section-title" v-if="item.elements[0].elements[0]?.title">{{item.elements[0].elements[0].title}}</h2>
		<div class="instashop-items">
			<PublishIndexEntryInstashop :items="item.elements[0].elements" :mode="'staticcontent'" />
		</div>
	</section>
</template>

<script setup>
	const props = defineProps(['item']);
</script>
