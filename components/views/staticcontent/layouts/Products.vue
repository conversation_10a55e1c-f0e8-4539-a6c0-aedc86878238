<template>
	<section class="ln-section ln-section-products" :class="{'spacing': item.elements[0]?.elements.some(element => element.large_space > 0)}" :id="'position'+item.position">
		<div class="tab-content wrapper">
			<div class="">
				<div class="c-items blazy-container" v-if="item.elements[0].elements">
					<template v-for="itemElement in item.elements[0].elements" :key="itemElement.id">
						<CatalogIndexEntry :item="itemElement" itemListName="Landing stranica" />
					</template>
				</div>
			</div>
		</div>
	</section>
</template>

<script setup>
	const props = defineProps(['item']);
</script>
