<template>
	<nav class="ln-nav-container ln-nav-container1" :class="{'spacing': item.elements[0].large_space > 0, 'active': menuActive}">
		<div class="wrapper ln-nav-wrapper">
			<BaseCmsLogo class="logo" />
			<div class="btn-ln-toggle-nav" @click="menuActive = !menuActive"><BaseCmsLabel code="menu" /> <span class="toggle-icon"></span></div>
			<ul class="ln-nav">
				<template v-for="menuItem in items" :key="menuItem.id">
					<li v-if="menuItem.layout != 'menu' && menuItem.elements[0]?.menu_title">
						<span @click="scrollTo('#position' + menuItem.position)">{{menuItem.elements[0].menu_title}}</span>
					</li>
				</template>
			</ul>
		</div>
	</nav>
</template>

<script setup>
	const {scrollTo} = useDom();
	const props = defineProps(['item', 'items']);
	const menuActive = ref(false);
</script>
