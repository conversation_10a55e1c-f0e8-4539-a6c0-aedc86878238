<template>
	<div class="ln-wrapper ln-section ln-section-promo ln-section-promo2" :class="{'spacing': item.elements[0]?.elements.some(element => element.large_space > 0)}" :id="'position'+item.position" v-if="item.elements[0].elements?.length">
		<div class="ln-promo" v-for="itemElement in item.elements[0].elements" :key="itemElement.id">
			<NuxtLink :to="itemElement.url_without_domain">
				<figure class="image">
					<span>
						<BaseUiImage loading="lazy" :data="itemElement.image_thumbs?.['width730-height380-crop1']" default="/images/no-image-715.jpg" />
						<!--
						<BaseUiImage loading="lazy" :data="itemElement.image?.['width50-he
						ight50']" default="/images/no-image-50.jpg" :alt="data.item.title" />
						<img <?php echo Thumb::generate($item['image'], array('width' => 730, 'height' => 380, 'crop' => true, 'default_image' => '/media/images/no-image-715.jpg', 'placeholder' => '/media/images/no-image-715.jpg')); ?><?php if($item['title']): ?> title="<?php echo $item['title']; ?>"<?php endif; ?> alt="" />
						-->
					</span>
				</figure>
				<span class="title" v-if="itemElement.title">{{itemElement.title}}</span>
			</NuxtLink>
		</div>
	</div>
</template>
<script setup>
	const props = defineProps(['item']);
</script>
