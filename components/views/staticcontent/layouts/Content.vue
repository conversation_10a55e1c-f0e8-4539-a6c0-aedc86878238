<template>
	<section class="ln-section ln-section-title" :class="{'spacing': item.elements[0].large_space > 0}" :id="'position'+item.position">
		<div class="section-content-body">
			<h2 class="section-content-title" v-if="item.elements[0]?.title">{{item.elements[0].title}}</h2>
			<div v-html="item.elements[0].content" v-if="item.elements[0]?.content" v-interpolation />
		</div>
	</section>
</template>

<script setup>
	const props = defineProps(['item']);
</script>
