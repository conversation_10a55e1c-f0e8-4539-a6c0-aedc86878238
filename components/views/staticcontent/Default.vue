<template>
	<BaseStaticcontentPage v-slot="{item}">
		<Body class="page-landing" :class="{'has-menu': item?.items.some(element => element.layout == 'menu')}" />
		<template v-if="item?.items">
			<div v-for="section in item.items" :key="section.layout">
				<template v-if="['menu', 'menu2'].includes(section.layout)">
					<BaseStaticcontentLayout :item="section" :items="item.items" />
				</template>
				<template v-else>
					<BaseStaticcontentLayout :item="section" />
				</template>
			</div>
		</template>
	</BaseStaticcontentPage>
</template>

<script setup></script>
