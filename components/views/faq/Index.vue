<template>
	<Body class="page-faq header-spacing0 white-bg" />
	<BaseCmsPage v-slot="{page}">
		<div class="faq-main-page">
			<div class="wrapper">
				<div class="faq-main-page-intro">
					<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
					<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
					<div v-if="page?.content" class="cms-content" v-html="page.content" ref="content" v-interpolation />
				</div>

				<span class="faq-main">
					<BaseFaqCategories :fetch="{limit: 5}" v-slot="{items}">
						<BaseUiTabs v-if="items" class="faq-c-items">
							<BaseUiTab class="faqc" :class="['facq-' + item.code]" v-for="item in items" :key="item.id" :title="item.title">
								<BaseFaqQuestions :fetch="{category_position: item.position_h}" v-slot="{items}">
									<div v-if="items" class="faq-section lists">
										<CmsFaqItem v-for="faq_item in items" :key="faq_item.id" mode="mainFaqItem" :item="faq_item" />
									</div>
								</BaseFaqQuestions>
							</BaseUiTab>
						</BaseUiTabs>
					</BaseFaqCategories>

					<ClientOnly>
						<CmsFaqInfo />
					</ClientOnly>
					<CmsShare mode="faq" />
				</span>
			</div>
		</div>
	</BaseCmsPage>
</template>

<style scoped lang="less">
	.faq-main-page {
		padding: 40px 0 80px;
		.wrapper {max-width: 740px;}
		@media (max-width: @m) {
			padding: 15px 0 40px;
			.wrapper {max-width: unset; margin: 0;}
			.tab-content {padding-left: 15px; padding-right: 15px; padding-top: 20px;}
		}
	}
	.faq-main-page-intro {
		text-align: center;padding-bottom: 32px;
		h1 {padding-bottom: 0;}
		@media (max-width: @m) {padding-bottom: 16px; margin-left: 15px; margin-right: 15px;}
	}
	.faq-c-items{
		@media(max-width: @m){
			position: relative;
			&:before{.pseudo(40px,86px); background: linear-gradient(270deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%); top: 0; left: 0; visibility: hidden; opacity: 0; transition: visibility .3s, opacity .3s; z-index: 10; pointer-events: none;}
			&.scroll,&.scroll-right{
				&:before{visibility: visible; opacity: 1;}
			}
		}
	}
	:deep(.tabs-nav) {
		display: flex !important; list-style: none; column-gap: 10px; justify-content: center;
		.faqc {
			width: 140px; border: 1px solid @borderColor; position: relative; background: var(--colorWhite); min-height: 104px; 	display: flex; 	align-items: center; border-radius: @borderRadius; justify-content: center; cursor: pointer; .transition(background);
			&:last-child {margin-right: 0;}
			.gradient-green {.gradient(#ABC075,@green);}
			&:before {.pseudo(auto, auto); font: 40px/40px @fonti; .icon-cart(); color: @green; left: 50%; margin-left: -22px; top: 22px; .transition(color);}
			&:after {.pseudo(auto,auto); top: -1px; right: -1px; bottom: -1px; left: -1px; .gradient-green; opacity: 0; z-index: -1; .transition(opacity); border-radius: @borderRadius;}
			span {
				position: relative; padding-left: top; font-size: 12px; line-height: 16px; font-weight: bold; padding: 76px 0 12px 0; width: 100%; text-align: center; .transition(color);
				&:after {.pseudo(10px,10px); left: 50%; margin-left: -5px; transform: rotate(45deg); background: #819a42; bottom: -5px; transition: visibility 0.3s, opacity 0.3s; visibility: hidden; opacity: 0;}
			}
			@media (min-width: @h) {
				&:hover {background: var(--colorLightBlueBackground);}
			}
			&.active {
				&:after {.pseudo(auto,auto); top: -1px; right: -1px; bottom: -1px; left: -1px; .gradient-green; .transition(opacity); z-index: 1; opacity: 1; border-radius: @borderRadius;}
				&:before {color: white;z-index: 2;}
				span {
					z-index: 2; color: white;
					&:after {opacity: 1; visibility: visible;}
				}
			}
			&:hover {
				&:after {opacity: 1; z-index: 1;}
				&:before {color: white; z-index: 2;}
				span {z-index: 2; color: white;}
			}
		}
		.facq-dostava_isporuka:before { font: 32px/32px @fonti; .icon-shipping(); margin-left: -25px; top: 28px;}
		.facq-prijava:before {.icon-user(); top: 24px; margin-left: -20px;}
		.facq-moja_narudzba:before { font: 44px/44px @fonti; .icon-package-box(); margin-left: -22px; top: 20px;}
		.facq-loyalty:before {font: 48px/48px @fonti; .icon-loyalty(); margin-left: -20px; top: 20px;}

		@media (max-width: @m) {
			column-gap: 8px; justify-content: flex-start; overflow: auto; padding-right: 15px;
			&::-webkit-scrollbar {-webkit-appearance: none; height: 0px; display: none;}
			&::-webkit-scrollbar-thumb {height: 0px; display: none;}
			&::-webkit-scrollbar-track-piece {height: 0px; display: none;}
			&::-webkit-scrollbar-track { height: 0px; display: none;}
			.faqc {
				width: 90px; min-height: 72px; flex-shrink: 0; margin-bottom: 6px;
				&:first-child {margin-left: 15px;}
				&:before {font: 28px/28px @fonti; top: 12px; margin-left: -17px;}
				span {
					padding: 50px 5px 10px; font-size: 10px; line-height: 14px; height: 100%; display: flex; align-items: center; justify-content: center;
					&:after {width: 8px; height: 8px; margin-left: -4px;}
				}
			}
			.facq-dostava_isporuka {
				span {padding-top: 44px; padding-bottom: 6px;}
				&:before {font: 24px/24px @fonti; margin-left: -20px; top: 14px;}
			}
			.facq-prijava {
				span {padding-top: 44px; padding-bottom: 6px;}
				&:before {font: 28px/28px @fonti; margin-left: -14px;}
			}
			.facq-moja_narudzba {
				&:before { font: 30px/30px @fonti; margin-left: -15px; top: 12px;}
			}
			.facq-loyalty {
				&:before { font: 32px/32px @fonti; margin-left: -13px; top: 12px;}
			}
		}
	}

	:deep(.faq-share) {
		@media (max-width: @m) {margin-left: 15px; margin-right: 15px;}
	}
</style>
