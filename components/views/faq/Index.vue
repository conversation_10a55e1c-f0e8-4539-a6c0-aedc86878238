<template>
	<BaseFaqCategory v-slot="{item: category}">
		<h1>{{ category?.seo_h1 }}</h1>

		<h2>Categories</h2>
		<BaseFaqCategories v-slot="{items}" :hierarchy="true">
			<ul>
				<li v-for="item in items" :key="item.id">
					<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
					<ul v-if="item.children.length">
						<li v-for="child in item.children" :key="child.id">
							<NuxtLink :to="child.url_without_domain">{{ child.title }}</NuxtLink>
						</li>
					</ul>
				</li>
			</ul>
		</BaseFaqCategories>

		<h2>Questions</h2>
		<BaseFaqQuestions v-slot="{items}">
			<ul>
				<li v-for="item in items" :key="item.id">
					{{ item.title }}
				</li>
			</ul>
		</BaseFaqQuestions>
	</BaseFaqCategory>
</template>
