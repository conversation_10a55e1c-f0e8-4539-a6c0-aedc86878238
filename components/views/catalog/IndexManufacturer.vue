<template>
	<BaseCmsPage v-slot="{page}">
		<Body class="header-spacing0 white-bg page-brands" />

		<div class="wrapper m-header">
			<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" class="bc-brands" />
			<h1 class="m-title" v-if="page?.title">{{ page.title }}</h1>
			<div class="page-content" v-if="page?.content" v-html="page.content" ref="content"></div>
		</div>

		<BaseCatalogManufacturers :fetch="{special: 1, limit: 4, sort: 'position_h'}" v-slot="{items}">
			<ClientOnly>
				<CatalogManufacturers extraClass="m-brands" :items="items" v-if="items?.length" />
			</ClientOnly>
		</BaseCatalogManufacturers>

		<BaseCatalogManufacturers :fetch="{hierarchy_by: 'alphabet', limit: 0, sort: 'title'}" v-slot="{items}">
			<div class="ma">
				<div class="df aic wrapper wrapper-ma">
					<div class="ma-col ma-col1">
						<BaseCmsLabel tag="div" class="ma-title" code="brands_search" />
					</div>
					<div class="ma-col ma-col2">
						<ClientOnly>
							<div class="ma-items">
								<div v-for="manufacturer in items" :key="manufacturer.alphabet" @click="scrollTo(manufacturer.alphabet)" class="ma-item">
									<span>{{ manufacturer.alphabet }}</span>
								</div>
							</div>
						</ClientOnly>
					</div>
				</div>
			</div>

			<div class="wrapper wrapper-m-items">
				<div class="m-items">
					<template v-if="items?.length">
						<div class="m-column" v-for="manufacturer in items" :key="manufacturer.alphabet" :class="{single: manufacturer.items?.length === 1}">
							<div class="m-letter" :id="manufacturer.alphabet">
								<span>{{ manufacturer.alphabet }}</span>
							</div>
							<div class="m-list-section">
								<ul class="m-list">
									<li v-for="item in manufacturer.items" :key="item.id">
										<NuxtLink :to="item.url_without_domain">
											<span class="m-item-title">{{ item.title }}</span>
										</NuxtLink>
									</li>
								</ul>
							</div>
						</div>
					</template>
					<template v-else>
						<BaseCmsLabel code="no_manufacturers" />
					</template>
				</div>
			</div>
		</BaseCatalogManufacturers>
	</BaseCmsPage>
</template>

<script setup>
	function scrollTo(index) {
		let el = '#' + index;
		document.querySelector(el).scrollIntoView({behavior: 'smooth'});
	}
</script>
