<template>
	<BaseCmsPage v-slot="{page}">
		<Body class="page-manufacturers" />
		<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>

		<BaseCatalogManufacturers :fetch="{special: 1, limit: 10}" thumbPreset="manufacturers" v-slot="{items}">
			<div class="special-brands" v-if="items">
				<NuxtLink v-for="manufacturer in items" :key="manufacturer.id" :to="manufacturer.url_without_domain" class="sp-item">
					<BaseUiImage v-if="manufacturer.main_image_upload_path_thumb" :data="manufacturer.main_image_upload_path_thumb" loading="lazy" :alt="manufacturer.title" />
					<span v-else>{{ manufacturer.title }}</span>
				</NuxtLink>
			</div>
		</BaseCatalogManufacturers>

		<BaseCatalogManufacturers :fetch="{hierarchy_by: 'alphabet', limit: 0, sort: 'title'}" v-slot="{items}">
			<div class="m-alphabet">
				<div class="wrapper">
					<div class="ma-title">
						<span>{{ labels.get('alphabet_title') }}</span>
					</div>
					<div class="ma-items">
						<a v-for="manufacturer in items" :key="manufacturer.alphabet" :href="'#' + manufacturer.alphabet" class="ma-item"
							><span>{{ manufacturer.alphabet }}</span></a
						>
					</div>
				</div>
			</div>

			<div class="wrapper">
				<div class="m-items">
					<template v-if="items">
						<div class="m-column" v-for="manufacturer in items" :key="manufacturer.alphabet">
							<div class="m-letter" :id="manufacturer.alphabet">
								<span>{{ manufacturer.alphabet }}</span>
							</div>
							<div class="m-list-section">
								<ul class="m-list">
									<li v-for="item in manufacturer.items" :key="item.id">
										<NuxtLink :to="item.url_without_domain">
											{{ item.title }}
										</NuxtLink>
									</li>
								</ul>
							</div>
						</div>
						<div class="clear"></div>
					</template>
					<template v-else>
						{{ labels.get('no_manufacturers') }}
					</template>
				</div>
			</div>
		</BaseCatalogManufacturers>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
</script>
