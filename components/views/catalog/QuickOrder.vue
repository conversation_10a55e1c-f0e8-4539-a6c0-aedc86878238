<template>
	<BaseCmsPage v-slot="{page}">
		<h1 v-if="page?.title">{{ page.title }}</h1>
		<div v-if="page?.content" v-html="page.content" v-interpolation />

		<ClientOnly>
			<BaseCatalogQuickOrder v-slot="{items, addProduct, removeProduct, addToCartData, valid}">
				<BaseSearchForm :fetch="searchFormConfig" :submit="false" v-slot="{searchResults, updateValue, searchTerm, loading, handleInput, onBlur, selectedIndex}">
					<div :class="{'loading': loading}">
						<input class="sw-input" placeholder="Naziv ili šifra proizvoda" type="text" :value="searchTerm" @blur="onBlur" @input="updateValue" autocomplete="off" @keyup="handleInput" />
					</div>
					<div class="ac" :class="{'loading': loading}" v-if="searchResults || loading">
						<div class="ac-loading" v-if="loading">Loading</div>
						<div class="ac-wrapper" v-if="searchResults?.catalogproduct?.length">
							<template v-for="item in searchResults.catalogproduct" :key="item.id">
								<div class="ac-item" :class="{'active': item.index == selectedIndex}" @click="addProduct(item)">
									<div class="ac-item-title">
										<div class="ac-item-cnt">
											<div class="ac-item-title">{{ item.title }}</div>
											<div class="ac-item-price">
												<template v-if="item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom">
													<div class="ac-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
													<div class="ac-current-price ac-discount-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
												</template>
												<template v-else>
													<div class="ac-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
												</template>
											</div>
										</div>
									</div>
								</div>
							</template>
						</div>
					</div>
				</BaseSearchForm>

				<template v-if="items?.length">
					<div class="quick-order-items">
						<template v-for="item in items" :key="item.id">
							<BaseCatalogVariations :item="item" v-slot="{attributes, selectedVariation}" v-model:selectedCode="item.code" v-model:isSelected="item.selectedVariation" v-model:selectedShoppingCartCode="item.shopping_cart_code">
								<div class="quick-order-item">
									<div class="quick-order-item-col quick-order-item-col1">
										<BaseUiImage :src="item.main_image_upload_path" />
									</div>
									<div class="quick-order-item-col quick-order-item-col2">
										<span class="quick-order-item-remove" @click="removeProduct(item)">X</span>
										<div class="quick-order-item-title">
											<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
										</div>
										<div class="quick-order-item-code"><BaseCmsLabel code="code" />: {{ item.code }}</div>
										<template v-if="item.variation_quickdata">
											<BaseCatalogVariationsAttributeItem v-for="attribute in attributes" :key="attribute.id" :item="attribute" v-slot="{item: attr, items: attrItems, onSelect}">
												<div class="variation-attribute" v-if="!attribute.empty">
													<div class="variation-attribute-title">
														{{ attr.attribute_title }}
													</div>
													<div class="variation-attribute-items">
														<template v-for="attrItem in attrItems" :key="attrItem.id">
															<div v-show="attrItem.available" class="variation-attribute-item" :class="{'not-available': !attrItem.available, 'selected': attrItem.selected}" @click="onSelect(attrItem)">
																{{ attrItem.title }}
															</div>
														</template>
													</div>
												</div>
											</BaseCatalogVariationsAttributeItem>
										</template>
										<div class="quick-order-item-quantity">
											<BaseThemeWebshopQty v-if="(item.variation_quickdata && selectedVariation) || !item.variation_quickdata" :quantity="1" :limit="selectedVariation ? selectedVariation.available_qty : item.available_qty" v-model="item.quantity" />
										</div>
									</div>
									<div class="quick-order-item-col quick-order-item-col3">
										<template v-if="(selectedVariation && +selectedVariation.discount_percent_custom) || (!selectedVariation && +item.discount_percent_custom)">
											<del><BaseUtilsFormatCurrency :price="selectedVariation ? selectedVariation.basic_price_custom : item.basic_price_custom" /></del>
											<div><BaseUtilsFormatCurrency :price="selectedVariation ? selectedVariation.price_custom : item.price_custom" /></div>
										</template>
										<template v-else>
											<BaseUtilsFormatCurrency :price="selectedVariation ? selectedVariation.price_custom : item.price_custom" />
										</template>
									</div>
								</div>
							</BaseCatalogVariations>
						</template>
					</div>

					<!-- Add everything to cart -->
					<BaseWebshopAddToCart v-if="valid && addToCartData" v-slot="{onAddToCart, loading}" :data="addToCartData">
						<button :class="{'loading': loading}" @click="onAddToCart">Dodaj sve u košaricu</button>
					</BaseWebshopAddToCart>
				</template>
			</BaseCatalogQuickOrder>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const searchFormConfig = {
		'allow_models': ['catalogproduct'],
		'result_per_page': 10,
		'result_image': '80x80_r',
	};
</script>

<style lang="less">
	.quick-order-item {
		display: flex;
	}
	.quick-order-item-col1 {
		width: 100px;
		flex-grow: 0;
		flex-shrink: 0;
		margin-right: 40px;
	}
</style>
