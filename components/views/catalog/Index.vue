<template>
	<BaseCatalogCategory v-slot="{item: category, contentType}" :seo="true">
		<BaseCatalogProducts v-slot="{items: products, loading, pagination, nextPage, loadMore}" :infinite-scroll="0">
			<div v-if="contentType == 'search'" class="s-header">
				<div class="s-header-title"><BaseCmsLabel code="search_headline" /></div>
				<BaseSearchResults :search-modules="[{module: 'catalog'}, {module: 'publish'}, {module: 'cms'}]">
					<BaseSearchNavigation />
				</BaseSearchResults>
			</div>
			<div v-else>
				<h1 class="c-title" v-if="category?.seo_h1">{{ category.seo_h1 }}</h1>
			</div>

			<div v-if="category || contentType == 'manufacturer' || contentType == 'list' || contentType == 'search'">
				<div class="c-toolbar">
					<BaseCatalogSpecialFilter filter="new" /> Novo <BaseCatalogSpecialFilter filter="discount" /> Akcija
					<BaseCatalogSort :sort-options="['priority', 'new', 'old', 'expensive', 'cheaper', 'az', 'za']" v-slot="{items: sortOptions, selected, onSort}">
						<select class="sort-select" @change="onSort">
							<option :value="sort" v-for="sort in sortOptions" :selected="sort == selected" :key="sort"><BaseCmsLabel :code="'sort_' + sort" /></option>
						</select>
					</BaseCatalogSort>
				</div>

				<template v-if="products.length">
					<div class="c-items">
						<ul v-for="item in products" :key="item.id">
							<NuxtLink :to="item.url_without_domain">{{ item.title }} </NuxtLink>
						</ul>
					</div>

					<div class="c-load-more-container" v-if="nextPage" data-products-scroll-trigger>
						<a href="javascript:void(0);" class="btn btn-lightBlue btn-medium load-more btn-load-more" v-if="!loading" @click="loadMore()"><BaseCmsLabel code="load_more_catalog" /></a>
						<div class="c-pagination-status-label">
							Prikazanih <span class="total_items_display fw-b">{{ pagination.items.current }}</span> od <span class="fw-b">{{ pagination.items.total }}</span>
						</div>
					</div>
					<BaseUiPagination class="pagination c-pagination-items" v-if="nextPage" />
				</template>
				<div v-else class="c-empty"><BaseCmsLabel code="no_products" /></div>
			</div>
		</BaseCatalogProducts>
	</BaseCatalogCategory>
</template>
