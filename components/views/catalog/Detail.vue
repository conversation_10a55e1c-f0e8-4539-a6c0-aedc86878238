<template>
	<Body class="header-spacing0" />

	<!-- 
		<?php
			$check_products = (Arr::get($info, 'controller', '') == 'webshop' AND Arr::get($info, 'action', '') == 'shipping');
			$unavailable_items = [];
			$country_code = '';
			$shopping_cart_data = [];
			if (!empty($customer_data['country']) AND $check_products) {
				$country = Webshop::countries([
					'lang' => $info['lang'],
					'single' => true,
					'filters' => [
						'id' => $customer_data['country'],
					],
				]);

				$country_code = Arr::get($country, 'code', '');
				$shopping_cart_data = Webshop::shopping_cart_data($info);
			}
			if ($check_products AND !empty($country_code)) {
				$unavailable_items = Webshop::get_unavailable_pickup_items($shopping_cart_data, $country_code);
			}
			?>

			<?php
			$filters = [];
			if (!empty($customer_data['country'])) {
				$filters['id'] = $customer_data['country'];
			} else {
				$filters['code'] = 'hr';
			}

			$selected_country = Webshop::countries([
				'lang' => $info['lang'],
				'filters' => $filters,
				'single' => true,
			]);
			?>
			<?php

			$webshop_only = false;
			$webshop_only_warehouse_id = Kohana::config('app.catalog.webshop_only_warehouse_id');
			if (!empty($webshop_only_warehouse_id)) {
				$warehouses_data = Arr::get($item, 'warehouses_ids', '');
				$warehouses_data = Text::db_to_array($warehouses_data);
				$item_warehouse_qtys = [];

				if (!empty($warehouses_data)) {
					foreach ($warehouses_data as $warehouse_data) {
						list($warehouse_id, $warehouse_qty) = explode('=', $warehouse_data);
						$item_warehouse_qtys[$warehouse_id] = $warehouse_qty;
					}

					$vp_warehoues_qty = (float)Arr::get($item_warehouse_qtys, $webshop_only_warehouse_id, 0);
					$webshop_only = (!empty($vp_warehoues_qty) AND $vp_warehoues_qty > 0);
					if (!empty($item_warehouse_qtys)) {
						foreach ($item_warehouse_qtys as $warehouse_id => $qty) {
							if ($warehouse_id != $webshop_only_warehouse_id AND (float)$qty > 0) {
								$webshop_only = false;
								break;
							}
						}
					}

				}
			}
		?>
		<?php $pickup_only = (!empty($item['type']) AND in_array($item['type'], Kohana::config('app.catalog.product_type.pickup_only'))); ?>
	-->
	<BaseCatalogDetail v-slot="{item}">
		<BasePublishPostsWidget
			:fetch="{category_code: 'blog', related_code: 'related', related_product_id: item.id, limit: 3, response_fields: ['id', 'url_without_domain', 'title', 'main_image_thumbs', 'category_code', 'category_title', 'category_url_without_domain'], extra_fields: ['short_description']}"
			v-slot="{items: publishes}">
			<BasePublishPostsWidget
				:fetch="{category_code: 'recipes', related_code: 'related', related_product_id: item.id, limit: 3, response_fields: ['id', 'url_without_domain', 'title', 'main_image_thumbs', 'category_code', 'category_title', 'category_url_without_domain', 'attributes_summary']}"
				v-slot="{items: recipes}">
				<BaseCatalogProductsWidget :fetch="{related_code: 'bought_together', related_item_id: item.id, only_with_image: true, only_available: true}" v-slot="{items, meta}">
					<BaseFaqQuestions :fetch="{catalogproduct_id: item.id, catalogcategory_id: item.category_id, category_code: 'proizvodi'}" mode="product" v-slot="{items: faqItems}">
						<BasePublishPostsWidget :fetch="{category_code: 'instashop', extra_data: ['images']}" v-slot="{items: instashop}">
							<BaseCatalogProductsWidget :fetch="{related_code: 'related', related_item_id: item.id, limit: 10, related_widget_data: item.related_widget_data}" v-slot="{items: relatedProducts}">
								<div class="cd-row">
									<div class="df cd-wrapper">
										<div class="cd-m-header-placeholder"></div>
										<div class="cd-col cd-col1" :class="{'multiple-images': item?.total_images > 1}">
											<div class="cd-container cd-container-images">
												<div class="cd-badges">
													<div class="cp-badge cp-badge-special cd-badge" v-if="item?.attributeitems_special?.[3]?.length && item.attributeitems_special?.[3].code == 'best_buy'"><BaseCmsLabel code="best_buy" tag="span" /></div>

													<BaseCmsLabel code="badge_new" tag="div" class="cd-badge cd-badge-new" :class="{'special': item?.attributeitems_special?.[3]?.length && item.attributeitems_special?.[3].code == 'best_buy'}" v-if="item?.priority_details?.code == 'new'" />
												</div>

												<div class="cd-brand" v-if="item?.manufacturer_code?.length">
													<NuxtLink :to="item.manufacturer_url_without_domain">
														<BaseUiImage :data="item.manufacturer_main_image_thumbs?.['width250-height30']" :alt="item.manufacturer_title" default="/images/no-image-50.jpg" v-if="item.manufacturer_main_image_thumbs?.['width250-height30']" />
													</NuxtLink>
												</div>

												<div class="cd-attr-container" v-if="item?.attributes_special?.some(attr => attr.attribute_code !== 'posebne_oznake') && !mobileBreakpoint">
													<div class="cd-attr" v-for="specialAttr in item.attributes_special.filter(attr => attr.attribute_code !== 'posebne_oznake')" :key="specialAttr.id">
														<span class="cd-attr-title">{{specialAttr.title}}</span>
														<figure class="cd-attr-img">
															<BaseUiImage :src="specialAttr.image_upload_path" :alt="specialAttr.title" default="/images/no-image-50.jpg" />
														</figure>
													</div>
												</div>

												<div class="cd-images" v-interpolation>
													<template v-if="item.images?.length">
														<div class="cd-hero-image">
															<BaseUiSwiper
																class="cd-hero-slider"
																:options="{
																effect: 'fade', 
																fadeEffect: {crossFade: true}, 
																navigation: {enabled: true},
																on: {
																	init: swiper => {
																		swiperRef = swiper;
																	}
																}
															}">
																<BaseUiSwiperSlide v-for="(file, index) in item.images" :key="index" class="cd-hero-slide">
																	<a :href="file.url" class="fancybox" rel="catalog" :data-index="index" :data-thumb="file.file_thumbs?.['width740-height740']?.thumb">
																		<BaseUiImage :title="file.title" :alt="file.description || item.title" loading="lazy" :data="file.file_thumbs?.['width740-height740']" default="/images/no-image-500.jpg" />
																	</a>
																</BaseUiSwiperSlide>
															</BaseUiSwiper>
														</div>
														<template v-if="item.images?.length && item.images?.length > 1">
															<BaseUiSwiper
																v-if="item.images?.length > 1"
																class="cd-thumbs cd-thumbs-slider"
																:options="{
																	slidesPerView: 5, 
																	slidesPerGroup: 5, 
																	watchSlidesProgress: true, 
																	spaceBetween: 0, 
																	enabled: true,
																	breakpoints: {
																		980: {
																			enabled: true,
																			spaceBetween: 0,
																			slidesPerView: 5,
																			slidesPerGroup: 5,
																			direction: 'vertical'
																		}
																	}
																}">
																<template v-for="(thumb, index) in item.images" :key="index">
																	<template v-if="item.element_video && index == 1">
																		<BaseUiSwiperSlide class="cd-thumb video" :data-index="index" @click="scrollTo('#video', {offset: 100})">
																			<span><BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width120-height120']" default="/images/no-image-50.jpg" /></span>
																		</BaseUiSwiperSlide>
																	</template>

																	<BaseUiSwiperSlide
																		v-else
																		class="cd-thumb"
																		:data-index="index"
																		@click="swiperRef.slideTo(item.element_video && index > 1 ? index - 1 : index); activeThumb = item.element_video && index > 1 ? index - 1 : index"
																		:class="{'active': (item.element_video && index > 1 ? index - 1 : index) == activeThumb}">
																		<span><BaseUiImage loading="lazy" :data="thumb.file_thumbs?.['width120-height120']" default="/images/no-image-50.jpg" /></span>
																	</BaseUiSwiperSlide>
																</template>
															</BaseUiSwiper>
														</template>
													</template>
													<div class="cd-hero-image cd-no-image" v-else>
														<img src="/images/no-image-500.jpg" :title="item.seo_h1" :alt="item.seo_h1" />
													</div>
												</div>
											</div>

											<ul class="cd-nav" v-if="!mobileBreakpoint">
												<li class="cd-nav-m" v-if="item?.content?.length"><BaseCmsLabel code="product_info" tag="span" /></li>
												<li @click="scrollTo('#komplet', {offset: 80})"><BaseCmsLabel code="buy_bundle" tag="span" /></li>
												<li v-if="publishes?.length" @click="scrollTo('#savjeti', {offset: 80})"><BaseCmsLabel code="publish_related" tag="span" /></li>
												<li v-if="recipes?.length" @click="scrollTo('#recepti', {offset: 80})"><BaseCmsLabel code="publish_related_recipes" tag="span" /></li>
												<li v-if="instashop?.length && instashop.some(instashopItem => instashopItem?.images?.some(image => image?.imagemaps?.some(imagemap => imagemap?.id == item.id)))" @click="scrollTo('#inspiration', {offset: 80})">
													<BaseCmsLabel code="instashop_related" tag="span" />
												</li>
												<li v-if="faqItems?.length" @click="scrollTo('#faq', {offset: 80})"><BaseCmsLabel code="product_faq" tag="span" /></li>
											</ul>

											<!-- Bought together -->
											<!-- 
											<?php
												$recommendation_total = $list_recommendation['_basic'];
												$recommendation_has_discount = (!empty($list_recommendation['_basic']['related_discount_percent']));
												unset($list_recommendation['_basic']);
												$ga4_recommended = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $list_recommendation, 'item_list_name' => Arr::get($cmslabel, 'label_bought_together'), 'item_list_id' => 'bought_together']);
												?>
											<div class="cd-related cd-bought-together" id="komplet" data-ga4_events_info='<?php echo $ga4_recommended; ?>'>
											-->
											<BaseWebshopShippingCountry v-slot="{selected}">
												<template v-if="item.type == 'pickup' && selected?.code && selected.code == 'hr' || item.type != 'pickup'">
													<div class="cd-bought-together" id="komplet" v-if="items?.length" :class="{'active': boughtTogetherTab, 'cd-tab': mobileBreakpoint, 'cd-related': !mobileBreakpoint}">
														<BaseCmsLabel code="bought_together_and_save_short" tag="h2" class="cd-tab-title" v-if="mobileBreakpoint" :class="{'active': boughtTogetherTab}" @click="boughtTogetherTab = !boughtTogetherTab" />
														<div class="cd-container" :class="{'cd-tab-cnt': mobileBreakpoint}">
															<BaseCmsLabel code="bought_together_and_save" tag="div" class="subtitle bought-together-title" v-if="!mobileBreakpoint" />

															<CatalogBoughtTogether :items="items" :meta="meta" v-if="items?.length" :current="item.id" />
															<!--<CatalogIndexEntry v-for="item in items" :key="item.id" :item="item" class="cp-list cp-bought-together no-shadow" mode="bought_together" :list="boughtTogetherText" />-->
														</div>
													</div>
												</template>
											</BaseWebshopShippingCountry>

											<div class="cd-faq" id="faq" v-if="faqItems?.length" :class="{'active': faqTab, 'cd-tab': mobileBreakpoint, 'cd-related': !mobileBreakpoint}">
												<BaseCmsLabel code="product_faq" tag="h2" class="cd-tab-title" v-if="mobileBreakpoint" :class="{'active': faqTab}" @click="faqTab = !faqTab" />
												<div class="cd-container" :class="{'cd-tab-cnt': mobileBreakpoint}">
													<BaseCmsLabel code="product_faq" tag="div" class="subtitle faq-title" v-if="!mobileBreakpoint" />

													<div class="cd-faq-items">
														<CmsFaqItem v-for="faqItem in faqItems" :key="faqItem.id" :item="faqItem" />
													</div>

													<div class="faq-contact">
														<BaseCmsLabel code="faq_note" tag="div" class="faq-contact-note" />
													</div>
												</div>
											</div>

											<template v-if="!mobileBreakpoint">
												<div class="cd-related cd-related-publishes" v-if="publishes?.length || recipes?.length">
													<div class="cd-container">
														<div class="cd-related-posts" id="savjeti" v-if="publishes?.length">
															<BaseCmsLabel code="publish_related" tag="div" class="subtitle related-title" />
															<PublishIndexEntry v-for="publish in publishes" :key="publish.id" :item="publish" :mode="'small'" class="pp-related gray-shadow cd-pp-related" />
														</div>

														<div class="cd-related-posts" id="recepti" v-if="recipes?.length">
															<BaseCmsLabel code="publish_related_recipes" tag="div" class="subtitle related-title" />
															<PublishIndexEntryRecipes v-for="recipe in recipes" :key="recipe.id" :item="recipe" :mode="'list'" class="gray-shadow cd-pp-recipe" />
														</div>
													</div>
												</div>
											</template>

											<BaseWebshopShippingCountry v-slot="{selected}">
												<CatalogDetailInstashop :instashop="instashop" :itemId="item.id" v-if="item.type != 'pickup' || item.type == 'pickup' && selected?.code && selected.code == 'hr' " />
											</BaseWebshopShippingCountry>
										</div>
										<!--
											FIXME INTEG dodati trancking kod cd-col2
											div class="cd-col cd-col2" data-tracking_gtm_impression="1|<?php echo $item['code']; ?>"
										-->
										<div class="cd-col cd-col2">
											<div class="cd-header" :class="{'not-available': !item.is_available}">
												<div class="cd-container cd-container-header">
													<div class="cd-m-header cd-m-header-top">
														<CmsBreadcrumbs :items="item.breadcrumbs" mode="catalogDetail" class="bc-short cd-bc" />
														<h1 class="cd-title" data-product_title="1">{{item.title}}</h1>
														<div class="cd-category">
															<NuxtLink :to="item.category_url_without_domain">{{item.category_title}}</NuxtLink>
														</div>
														<div class="cd-info">
															<div class="cd-info-item cd-code" :class="{'no-feedback': !item?.feedback_rate_widget || item?.feedback_comment_widget?.comments_status <= 1}">
																<BaseCmsLabel code="code" />: <span :data-product_code="item.shopping_cart_code">{{item.code}}</span>
															</div>
															<ClientOnly>
																<div class="cd-info-item cd-rates" @click="scrollTo('#comments', {offset: 80})" v-if="item?.feedback_rate_widget">
																	<FeedbackRates :rates="item.feedback_rate_widget.rates" mode="cd-rates" v-if="item.feedback_rate_widget.rates_votes > 0" />
																</div>

																<div class="cd-info-item cd-info-link-comments" @click="scrollTo('#comments', {offset: 80})" v-if="item?.feedback_comment_widget?.comments_status > 1">
																	<BaseCmsLabel code="view_comments" tag="span" class="cd-info-link-comments-title" /> <span class="green counter">({{item.feedback_comment_widget.comments_by_lang}})</span>
																</div>
															</ClientOnly>
														</div>
													</div>

													<CatalogDetailPrice :item="item" :b2b="b2b" />

													<template v-if="item?.type != 'coupon'">
														<template v-if="!b2b">
															<WebshopLoyalty v-slot="{onSubmit, newIsActive, loading, loyalty}">
																<template v-if="!loyalty?.active">
																	<template v-if="loyalty.discount_percent > item.discount_percent">
																		<div class="cd-loyalty">
																			<!--
																			<div class="loyalty-cnt">
																				<div v-html="labels.get('join_loyalty').replace('%AMOUNT%', formatCurrency(item.price_custom - ((item.price_custom * (1 - (loyalty?.discount_percent / 100)))), {showCurrency: true}))"></div>
																			</div>
																			-->
																			<div class="loyalty-checkbox">
																				<input type="checkbox" name="loyalty_request_new" @click.prevent="onSubmit()" id="field-loyalty_request_new" :checked="newIsActive" />
																				<label
																					for="field-loyalty_request_new"
																					v-interpolation
																					v-html="labels.get('join_loyalty').replace('%AMOUNT%', formatCurrency(item.price_custom - ((item.price_custom * (1 - (loyalty?.discount_percent / 100)))), {showCurrency: true}))"></label>
																			</div>
																		</div>
																	</template>
																	<template v-else>
																		<div class="cd-loyalty">
																			<!--
																			<div class="loyalty-cnt">
																				<div v-html="labels.get('join_loyalty').replace('%AMOUNT%', formatCurrency(item.price_custom - ((item.price_custom * (1 - (loyalty?.discount_percent / 100)))), {showCurrency: true}))"></div>
																			</div>
																			-->
																			<div class="loyalty-checkbox">
																				<input type="checkbox" name="loyalty_request_new" @click.prevent="onSubmit()" id="field-loyalty_request_new" :checked="newIsActive" />
																				<label
																					for="field-loyalty_request_new"
																					v-interpolation
																					v-html="labels.get('loyalty_no_discount_confirm').replace('%AMOUNT%', formatCurrency(item.price_custom - ((item.price_custom * (1 - (loyalty?.discount_percent / 100)))), {showCurrency: true}))"></label>
																			</div>
																		</div>
																	</template>
																</template>
															</WebshopLoyalty>
														</template>
													</template>
													<template v-else>
														<WebshopLoyalty v-slot="{onSubmit, newIsActive, loyalty}">
															<div class="cd-loyalty">
																<!--
																<div class="loyalty-cnt">
																	<div v-html="labels.get('join_loyalty').replace('%AMOUNT%', formatCurrency(item.price_custom - ((item.price_custom * (1 - (loyalty?.discount_percent / 100)))), {showCurrency: true}))"></div>
																</div>
																-->
																<div class="loyalty-checkbox">
																	<input type="checkbox" name="loyalty_request_new" @click.prevent="onSubmit()" id="field-loyalty_request_new" :checked="newIsActive" />
																	<label
																		for="field-loyalty_request_new"
																		v-interpolation
																		v-html="labels.get('loyalty_no_discount_confirm').replace('%AMOUNT%', formatCurrency(item.price_custom - ((item.price_custom * (1 - (loyalty?.discount_percent / 100)))), {showCurrency: true}))"></label>
																</div>
															</div>
														</WebshopLoyalty>
													</template>

													<CatalogPickupOnlyNote :item="item" />

													<BaseWebshopShippingCountry v-slot="{selected, submitCountry}">
														<template v-if="selected?.code && selected.code != 'hr' && item.type == 'pickup'">
															<div class="cd-change-delivery">
																<div class="cd-change-delivery-info">
																	<BaseCmsLabel code="cd_change_delivery" /> <span class="bold2">{{selected.title}}</span>
																</div>
																<div class="btn-autochange" @click="submitCountry({code: 'hr'})"><BaseCmsLabel code="cd_autochange_croatia" tag="span" /></div>
															</div>
														</template>
														<template v-else>
															<div class="cd-add-container" :class="{'not-available': !item.is_available}" ref="cdHeader">
																<WebshopAddToCartContainer :item="item" mode="catalogDetail" />
																<CatalogSetWishlist :item="item" mode="detail" />
															</div>
														</template>
													</BaseWebshopShippingCountry>

													<WebshopLoyalty v-slot="{loyalty}">
														<div class="cd-order-info" :class="{'not-available': !item.is_available}">
															<div class="cd-delivery">
																<template v-if="b2b">
																	<div v-html="labels.get('free_delivery_b2b').replace('%AMOUNT%', formatCurrency(item.price_custom - ((item.price_custom * (1 - (loyalty?.discount_percent / 100)))), {showCurrency: true}))" />
																</template>
																<template v-else>
																	<div v-html="labels.get('free_delivery').replace('%AMOUNT%', formatCurrency(item.price_custom - ((item.price_custom * (1 - (loyalty?.discount_percent / 100)))), {showCurrency: true}))" />
																</template>
															</div>

															<div class="cd-phone-order" :class="{'tooltip-active': phoneTooltip}" @mouseenter="phoneTooltip = true" @mouseleave="phoneTooltip = false">
																<BaseCmsLabel code="phone_order" tag="div" />
																<BaseCmsLabel code="phone_order_tooltip" tag="div" class="cd-tooltip cd-phone-order-tooltip" />
															</div>
														</div>
													</WebshopLoyalty>

													<BaseCmsLanguages v-slot="{currentLanguage}">
														<template v-if="currentLanguage?.code == 'hr'">
															<template v-if="item.is_available && item.type != 'pickup'">
																<template v-if="item?.warehouses.some(warehouse => warehouse.available_qty > 0 && warehouse.id == 3)">
																	<BaseCmsLabel code="webshop_only_product" class="cd-webshop-only" tag="div" />
																</template>
																<template v-else>
																	<CatalogWarehouse :warehouses="item.warehouses" :warehousesIDs="item.warehouses_ids" :item="item" />
																</template>
															</template>
														</template>
													</BaseCmsLanguages>

													<BaseCatalogProductsWidget
														:fetch="{related_code: 'related_package', related_item_id: item.id, related_widget_data: item.related_widget_data, always_to_limit: true, limit: 100, id_exclude: item.id, only_with_image: false}"
														v-slot="{items: realtedPackage}">
														<CatalogRelatedPackage :items="realtedPackage" list="relatedPackage" :b2b="b2b" v-if="realtedPackage?.length" />
													</BaseCatalogProductsWidget>

													<BaseCatalogProductsWidget
														:fetch="{related_code: 'related_flavor', related_item_id: item.id, related_widget_data: item.related_widget_data, always_to_limit: true, limit: 100, id_exclude: item.id, only_with_image: true}"
														v-slot="{items: realtedFlavor}">
														<CatalogRelatedPackage :items="realtedFlavor" list="relatedFlavor" :b2b="b2b" v-if="realtedFlavor?.length" />
													</BaseCatalogProductsWidget>
												</div>
											</div>
											<div class="cd-header-placeholder"></div>

											<div class="cd-cnt">
												<div class="cd-container" id="opis">
													<div class="cd-tab cd-tab-content" :class="{'active': contentTab}">
														<BaseCmsLabel code="product_about" tag="h2" class="cd-tab-title" :class="{'active': contentTab}" v-if="mobileBreakpoint" @click="contentTab = !contentTab" />
														<div class="cd-tab-cnt cd-tab-description lists">
															<div class="cd-attr-container" v-if="item?.attributes_special?.some(attr => attr.attribute_code !== 'posebne_oznake') && mobileBreakpoint">
																<div class="cd-attr" v-for="specialAttr in item.attributes_special.filter(attr => attr.attribute_code !== 'posebne_oznake')" :key="specialAttr.id">
																	<span class="cd-attr-title">{{specialAttr.title}}</span>
																	<figure class="cd-attr-img">
																		<BaseUiImage :src="specialAttr.image_upload_path" :alt="specialAttr.title" default="/images/no-image-50.jpg" />
																	</figure>
																</div>
															</div>

															<div class="cd-tab-video" id="video" v-if="item.element_video?.length" v-html="item.element_video" />

															<div class="cd-tab-desc" v-if="item.element_tab_desc?.length" v-html="item.element_tab_desc" />

															<div class="cd-tab-content cd-item-content" v-if="item.content?.length && !mobileBreakpoint" v-html="item.content" />
															<BaseUiToggleContent
																class="cd-tab-content cd-item-content"
																v-if="item.content?.length && mobileBreakpoint"
																:content="item.content"
																:limit="305"
																:append="{
																	show: {
																		title: labels.get('show_more')
																	}
																}"
																v-slot="{content}">
																<div v-html="content" />
															</BaseUiToggleContent>

															<ul class="cd-documents" v-if="item.documents?.some(doc => doc.kind === 'document')">
																<template v-for="document in item.documents" :key="document.id">
																	<li v-if="document.kind != 'video'">
																		<a :href="document.url" target="_blank" class="btn-download btn-download-link btn-download-pdf"
																			><span>{{document.title}}</span></a
																		>
																	</li>
																</template>
															</ul>

															<div class="cd-tab-extra" v-if="item.element_tab_extra?.length" v-html="item.element_tab_extra" />
														</div>
													</div>

													<div class="cd-tab cd-tab-nutrition" v-if="item.element_tab_nutritional?.length" :class="{'active': nutritionTab}">
														<BaseCmsLabel code="nutritional" tag="h2" class="cd-tab-title" :class="{'active': nutritionTab}" @click="nutritionTab = !nutritionTab" />
														<div class="cd-tab-cnt" v-html="item.element_tab_nutritional" />
													</div>

													<div class="tags cd-tags" v-if="item.seo_keywords_tags?.length">
														<template v-for="tag in item.seo_keywords_tags" :key="tag.id">
															<NuxtLink :to="tag.url" target="_blank">{{tag.title}}</NuxtLink>
															<span class="comma">, </span>
														</template>
													</div>

													<CmsShare mode="catalogDetail" />
												</div>

												<div class="publishes-mobile">
													<template v-if="mobileBreakpoint">
														<div class="cd-tab" v-if="publishes?.length" :class="{'active': postsTab}">
															<BaseCmsLabel code="publish_advice" tag="h2" class="cd-tab-title" :class="{'active': postsTab}" @click="postsTab = !postsTab" />
															<div class="cd-tab-cnt">
																<PublishIndexEntry v-for="publish in publishes" :key="publish.id" :item="publish" :mode="'small'" class="pp-related gray-shadow cd-pp-related" />
															</div>
														</div>

														<div class="cd-tab" v-if="recipes?.length" :class="{'active': recipesTab}">
															<BaseCmsLabel code="publish_recipes" tag="h2" class="cd-tab-title" :class="{'active': recipesTab}" @click="recipesTab = !recipesTab" />
															<div class="cd-tab-cnt">
																<PublishIndexEntryRecipes v-for="recipe in recipes" :key="recipe.id" :item="recipe" :mode="'list'" class="gray-shadow cd-pp-recipe" />
															</div>
														</div>
													</template>
												</div>
											</div>

											<div
												class="cd-comments-container"
												:class="{'cd-tab': mobileBreakpoint, 'active': commentsTab}"
												id="comments"
												v-if="item.feedback_comment_widget?.comments_status == 2 && item.feedback_comment_widget?.comments > 0 || item.feedback_comment_widget?.comments_status >= 3">
												<BaseCmsLabel code="comments_and_reviews" tag="h2" class="cd-tab-title" :class="{'active': commentsTab}" @click="commentsTab = !commentsTab" v-if="mobileBreakpoint" />
												<div :class="{'cd-tab-cnt': mobileBreakpoint, 'cd-container': !mobileBreakpoint}">
													<ClientOnly>
														<FeedbackCommentsForm :item="item" mode="catalogDetail" />
													</ClientOnly>
												</div>
											</div>
										</div>
									</div>
								</div>
								<!-- 
									FIXME INTEG DK dodata ga4
									<?php $ga4_related = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $related_products, 'item_list_name' => Arr::get($cmslabel, 'product_related_products'), 'item_list_id' => 'product_related_products']); ?>
								-->
								<div class="cd-rp" id="related" v-if="relatedProducts?.length">
									<!-- 
										FIXME INTEG DK zamijeniti liniju ispod s linijom iz komentara
										<div class="wrapper" data-ga4_events_info='<?php echo $ga4_related; ?>'>
									-->
									<div class="wrapper">
										<BaseCmsLabel code="product_related_products" class="cd-rp-title" />
										<BaseUiSwiper
											class="cd-rp-slider slick-carousel slick-arrow3 blazy-container"
											:options="{
												slidesPerView: 1,
												slidesPerGroup: 1,
												spaceBetween: 0,
												watchSlidesProgress: true,
												enabled: false,
												breakpoints: {
													900: {
														enabled: true,
														spaceBetween: -1,
														slidesPerView: 5,
														slidesPerGroup: 5,
														watchSlidesProgress: true,
													},
													1250: {
														enabled: true,
														spaceBetween: -1,
														slidesPerView: 5,
														slidesPerGroup: 5,
														watchSlidesProgress: true,
													}
												}
											}">
											<BaseUiSwiperSlide v-for="relatedItem in relatedProducts" :key="relatedItem.id">
												<CatalogIndexEntry :item="relatedItem" />
											</BaseUiSwiperSlide>
										</BaseUiSwiper>
									</div>
								</div>
							</BaseCatalogProductsWidget>
						</BasePublishPostsWidget>
					</BaseFaqQuestions>
				</BaseCatalogProductsWidget>
			</BasePublishPostsWidget>
		</BasePublishPostsWidget>

		<BaseWebshopShippingCountry v-slot="{selected}">
			<template v-if="selected?.code && selected.code != 'hr' && item.type == 'pickup'"> </template>
			<template v-else>
				<CatalogBottomBar :item="item" :b2b="b2b" v-show="!addToCartVisible" />
			</template>
		</BaseWebshopShippingCountry>
	</BaseCatalogDetail>
</template>

<script setup>
	const {b2b} = useProfile();
	const {scrollTo, onElementVisibility, prependTo, appendTo, insertBefore, insertAfter, onMediaQuery} = useDom();
	const labels = useLabels();

	const activeThumb = ref(0);

	const cdHeader = ref();
	const { isVisible: addToCartVisible } = onElementVisibility({
		target: cdHeader,
	});

	const {getCartData} = useWebshop();
	const {formatCurrency} = useCurrency();
	const freeShippingAmount = computed(() => {
		const amount = getCartData()?.cart?.total_extra_shipping_to_free?.above;
		return amount ? formatCurrency(amount, {showCurrency: true}) : ' ';
	});

	const phoneTooltip = ref(false);
	const swiperRef = ref(null);

	const relatedProductsLink = (labels.labels.product_related_products_link?.length) ? labels.labels.product_related_products_link : labels.labels.product_related_products;

	const boughtTogetherRaw = labels?.labels?.bought_together ?? ''
	const boughtTogetherText = boughtTogetherRaw.replace(/<[^>]*>/g, '')

	const contentTab = ref(true);
	const nutritionTab = ref(false);
	const faqTab = ref(false);
	const boughtTogetherTab = ref(false);
	const postsTab = ref(false);
	const recipesTab = ref(false);
	const commentsTab = ref(false);

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 900px)',
		timeout: 600,
		enter: () => {
			prependTo('.cd-m-header-top', '.cd-m-header-placeholder');
			insertBefore('.cd-col2', '.cd-col1')
			prependTo('.cd-container-images', '.cd-col2');
			insertAfter('.cd-share', '.cd-cnt');
			insertBefore('.cd-tags', '.cd-share');
			insertBefore('.cd-faq', '.publishes-mobile');
			insertBefore('.cd-bought-together', '.publishes-mobile');
			appendTo('.cd-comments-container', '.cd-cnt');
		},
		leave: () => {
			window.location.reload();
		},
	});
</script>

<style lang="less" scoped>
	.cd-container-images{
		margin-top: 16px;
		@media (max-width: @m){margin-top: 0;}
	}
	.cd-images{
		display: flex; align-items: flex-start; width: 100%; overflow: hidden;
		@media (max-width: @m){
			:deep(.swiper-wrapper){align-items: center; justify-content: center;}
		}
	}
	.cd-hero-image{
		order: 2;
		@media (max-width: @m){
			:deep(img){max-height: 100%;}
		}
	}
	.cd-thumbs{
		order: 1; margin-right: 4px; max-height: 616px;
		:deep(.swiper){width: 100%;}
		:deep(.swiper-wrapper){gap: 4px;}
		:deep(.swiper-navigation){display: none;}
		@media (max-width: @m){
			:deep(.swiper-wrapper){justify-content: flex-start;}
		}
	}
	.cd-thumb{
		display: flex; align-items: center; justify-content: center; aspect-ratio: 1; width: 100%; height: auto!important; border: 1px solid transparent; line-height: 0; border-radius: @borderRadius; cursor: pointer; position: relative;
		&.active{border-color: @green;}
		&.video{
			display: flex; align-items: center; justify-content: center;
			&:after{
				.pseudo(100%,100%); background: url('assets/images/icons/youtube.png') center no-repeat;
				@media (max-width: @tp){background-size: 90%; background-position: center top 80%;}
			}
		}
		:deep(img){border-radius: @borderRadius;}
	}
	:deep(.pp-short-desc){overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; line-clamp: 2; -webkit-box-orient: vertical;}
	.cd-faq .cd-container{
		padding-right: 80px;
		@media (max-width: @l){padding-right: 45px;}
		@media (max-width: @t){padding-top: 0;}
		@media (max-width: @m){padding: 0 16px 22px;}
	}
	.cd-faq-items{display: flex; flex-flow: column; gap: 10px;}
	.faq-contact{position: relative; display: flex; align-items: center; justify-content: space-between; background: url(assets/images/faq-bg.png) center no-repeat; background-size: cover; margin-top: 16px;}
	.faq-contact-note{
		display: flex; z-index: 1; color: @lightGreen; align-items: center; width: 100%; justify-content: space-between; font-size: 22px; line-height: 1.5; padding: 24px 32px;
		:deep(p){padding: 0; display: flex; flex-flow: column;}
		:deep(a){color: #fff; text-align: right; font-size: 28px; font-weight: 600;}
		:deep(a[href^="mailto:"]){
			font-size: 16px; font-weight: normal;
			@media (min-width: @h){
				&:hover{text-decoration-color: transparent;}
			}
		}
		@media (max-width: 1400px){
			font-size: 16px; padding: 18px 20px;
			:deep(a){font-size: 20px;}
			:deep(a[href^="mailto:"]){font-size: 14px;}
		}
		@media (max-width: 990px){
			flex-flow: column; align-items: flex-start; justify-content: flex-start; gap: 8px;
			:deep(a){text-align: left;}
		}
	}
	.cd-rates{
		line-height: 0; display: inline-flex;
		:deep(.icon-star), :deep(.icon-star-empty){
			position: relative; margin-right: 2px;
			&:before{.icon-star(); font: 13px/1 @fonti; color: #d5d9d3;}
		}
		:deep(.icon-star:before){color: @yellow;}
	}
	.cd-info-link-comments-title{
		color: @darkGreen; text-decoration: underline; cursor: pointer; .transition(color);
		@media (min-width: @h){
			&:hover{color: @green;}
		}
	}

	.cd-tab-video{
		:deep(iframe){
			width: 100%;
			@media (max-width: @m){aspect-ratio: 16 / 9; height: auto;}
		}
	}
	.cd-tags{
		margin: 0 0 32px; padding-top: 0; padding-right: 0; padding-bottom: 0; background: none; border: none;
		&:before{top: 0;}
		@media (max-width: @m){
			margin: 22px 16px 0; padding-left: 30px;
			&:before{left: 2px;}
		}
	}
	.cd-rp-slider{
		:deep(.swiper){overflow: inherit;}
		:deep(.swiper-wrapper){box-sizing: border-box;}
		:deep(.swiper-slide){
			flex-grow: 1; flex-shrink: 0; display: flex; opacity: 0; visibility: hidden; transition: opacity 0.3s, visibility 0.3s; height: auto;
			&.swiper-slide-visible{opacity: 1; visibility: visible;}
			&:hover{z-index: 1; position: relative;}
		}
		.cp{width: 100%;}
		:deep(.cp-addtocart){border: transparent; box-shadow: 0 10px 30px 0 rgba(0,0,0,0.25)}
		@media (max-width: 1670px){
			:deep(.swiper-button-prev){left: -25px;}
			:deep(.swiper-button-next){right: -25px;}
		}
		@media (max-width: @t){
			:deep(.cp-addtocart){box-shadow: none;}
		}
		@media (max-width: @m){
			width: 100%;
			:deep(.swiper-navigation){display: none;}
			:deep(.swiper-wrapper){padding: 0 16px; overflow-x: auto;}
			:deep(.swiper-slide){opacity: 1; visibility: visible; flex-grow: 0; flex-shrink: 0; width: 152px!important; margin-left: -1px;}
			:deep(.cp){
				.cp-btn-addtocart span:before{display: block;}
			}
		}
	}
	:deep(.btn-toggle-content){
		color: @lightGreen; font-weight: bold; text-decoration: underline; background: #fff; position: relative;
		&:before{.pseudo(46px,18px); background: linear-gradient(109deg, rgba(248, 249, 248, 0) 0%, rgba(249, 249, 249, 1) 100%); left: -46px; top: 0;}
	}

	// comments
	:deep(.cd-comments-header){
		@media (max-width: @m){
			.comments-title{display: none;}
		}
	}
	.cd-comments-container{
		position: relative; display: flex; flex-flow: column; padding-top: 40px; padding-bottom: 40px;
		@media (max-width: @m){padding: 0;}
	}
	.cd-comment-rates{
		:deep(.rate-item){
			margin-right: 5px;
			&:before{font-size: 28px; line-height: 1;}
		}
	}

	// ontop
	.page-catalog-detail{
		:deep(.ontop){bottom: 90px;}
	}

	// inspiration
	:deep(.cd-related-inspiration){
		.cd-container{padding-right: 80px;}
		@media (max-width: @l){
			.cd-container{padding-right: 45px;}
		}
		@media (max-width: @m){
			display: block; margin-top: 22px;
			.cd-container{padding-right: 0;}
		}
	}
</style>
