<template>
	<BaseCmsPage>
		<ClientOnly>
			<BaseCatalogCompare v-slot="{items, onRemove, attributes, loading, counter, differentAttributes, onToggleMode, mode}">
				<div class="base-compare-container">
					<div class="base-compare-col1">
						<div class="base-cp-compare-header">
							<div>Loading: {{ loading }}</div>
							<div>Mode: {{ mode }}</div>
							<div>Counter: {{ counter }}</div>
							<div @click="onToggleMode('all')" class="base-compare-toggle-mode-btn" :class="{'active': mode == 'all'}">Prikaži sve</div>
							<div @click="onToggleMode('difference')" class="base-compare-toggle-mode-btn" :class="{'active': mode == 'difference'}">Prikaži samo razlike</div>
						</div>
						<template v-if="items?.length">
							<div v-for="attribute in attributes" :key="attribute.id" class="attribute-row" :class="{'difference': differentAttributes.includes(attribute.attribute_code) && mode == 'difference'}" :data-compare-attribute="attribute.attribute_code">
								{{ attribute.attribute_title }}
							</div>
						</template>
					</div>
					<div class="base-compare-col2">
						<template v-if="items?.length">
							<div class="base-cp-compare" v-for="item in items" :key="item.id">
								<div class="base-cp-compare-header">
									<div @click="onRemove(item.shopping_cart_code)">Obriši iz usporedilice</div>
									<BaseCatalogCompareSearch placeholder="Upiši naziv proizvoda" :item="item" />
									<div>
										<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
									</div>
									<div><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
								</div>
								<div class="base-cp-compare-attributes">
									<div v-for="attr in item.attributes" :key="attr.id" class="attribute-row" :class="{'difference': differentAttributes.includes(attr.attribute_code) && mode == 'difference'}" :data-compare-attribute="attr.attribute_code">
										<template v-if="attr.title">{{ attr.title }}</template>
										<template v-else> - </template>
									</div>
								</div>
							</div>
						</template>
						<div class="base-cp-compare">
							<BaseCatalogCompareSearch placeholder="Upiši naziv proizvoda" />
						</div>
					</div>
				</div>
			</BaseCatalogCompare>
		</ClientOnly>
	</BaseCmsPage>
</template>

<style lang="less">
	.base-compare-container {
		display: flex;
		max-width: 80%;
		margin: auto;
	}
	.base-compare-col1 {
		flex: 0 0 300px;
		font-weight: bold;
	}
	.base-compare-col2 {
		overflow: auto;
		display: flex;
	}
	.base-cp-compare {
		flex: 0 0 300px;
	}
	.base-cp-compare-header {
		height: 330px;
		font-weight: normal;
		img {
			max-height: 100px;
			margin: 20px auto;
			width: auto;
			display: block;
		}
	}
	.base-cp-compare {
		border-left: 1px solid #eaeaea;
	}
	.attribute-row {
		border-bottom: 1px solid #eaeaea;
		padding: 10px;
		&.difference {
			background: #f6f6f6;
		}
	}
	.base-compare-toggle-mode-btn.active {
		font-weight: bold;
	}
</style>
