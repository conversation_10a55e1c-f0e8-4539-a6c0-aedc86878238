<template>
	<BaseLocationPoint v-slot="{item, locationsUrl}">
		<CmsTwoColumns>
			<template #main>
				<div class="df ld-row">
					<div class="ld-col ld-col1">
						<CmsBreadcrumbs :items="item?.breadcrumbs" class="bc-ld" />
						<NuxtLink class="btn-all-stores" :to="locationsUrl"><BaseCmsLabel code="all_locations" /></NuxtLink>
						<h1 class="ld-title">{{ item.title }}</h1>
						<div class="ld-icon ld-address" v-if="item?.address" ref="address" v-html="item.address" v-interpolation></div>
						<div class="ld-icon ld-hours" v-if="item?.business_hour" ref="businesshours" v-html="item.business_hour" v-interpolation></div>
						<div class="ld-icon ld-contact" v-if="item?.contact" ref="contact" v-html="item.contact" v-interpolation></div>
						<div class="ld-cnt" v-if="item?.content" ref="content" v-html="item.content" v-interpolation></div>
					</div>
					<div class="ld-col ld-col2">
						<template v-if="item.total_images > 0">
							<template v-if="item.total_images > 1">
								<BaseUiSwiper
									name="ld"
									class="ld-images slick-arrow2"
									:options="{
										slidesPerView: 1,
										slidesPerGroup: 1,
										loop: true,
										effect: 'fade',
										fadeEffect: {
											crossFade: true,
										},
										pagination: {
											enabled: true,
											clickable: true,
										},
									}">
									<BaseUiSwiperSlide>
										<BaseUiImage :data="item.main_image_thumbs?.['width700-height520-crop1']" :alt="item.title" default="/images/no-image-700.jpg" />
									</BaseUiSwiperSlide>
									<template v-if="item?.other_images">
										<BaseUiSwiperSlide v-for="otherImage in item.other_images" :key="otherImage.id">
											<BaseUiImage :data="otherImage.image_thumbs?.['width700-height520-crop1']" :alt="item.title" default="/images/no-image-700.jpg" />
										</BaseUiSwiperSlide>
									</template>
								</BaseUiSwiper>
							</template>
							<span v-else>
								<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width700-height520-crop1']" :alt="item.title" default="/images/no-image-700.jpg" />
							</span>
						</template>
					</div>
				</div>
				<CmsMap :items="[item]" class="l-map" mode="locationDetail" />
			</template>
		</CmsTwoColumns>
	</BaseLocationPoint>
</template>

<style lang="less" scoped>
	.slick-arrow2 {
		:deep(.swiper-button-prev) {
			left: 30px;
			cursor: pointer;
			@media (max-width: @m) {
				left: 10px;
			}
		}
		:deep(.swiper-button-next) {
			right: 30px;
			cursor: pointer;
			@media (max-width: @m) {
				right: 10px;
			}
		}
	}
</style>
