<template>
	<Body class="page-location-index page-locations nw-main" />
	<BaseCmsPage v-slot="{page}">
		<CmsTwoColumns>
			<template #main>
				<BaseLocationPoints v-slot="{places, items}">
					<CmsMap :items="items" extraclass="locations-map" />
					<div class="locations-header">
						<div class="pos-r wrapper2">
							<CmsBreadcrumbs :items="page?.breadcrumbs" />
							<h1 class="locations-title" v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
							<template v-if="items?.length">
								<ul class="nav-locations">
									<li v-for="place in places" :key="place.id">
										<a href="javascript:void(0)" @click="scrollTo('#' + place.location_code)"
											><span>{{ place.title }}</span></a
										>
									</li>
								</ul>
							</template>
							<BaseCmsLabel code="no_locations" class="no-locations" tag="div" v-else />
						</div>
					</div>

					<div class="l-items" id="mappoints" v-if="items?.length">
						<div class="l-section" v-for="(place, index) in places" :key="place.id">
							<div class="wrapper2">
								<h2 class="l-title" :class="'l-title' + index" :id="place.location_code">
									{{ place.title }}
									<span class="l-counter">({{ place?.items?.length }})</span>
								</h2>
								<template v-for="item in items" :key="item.id">
									<div class="lp" v-if="item.location_code === place.location_code">
										<div class="lp-left">
											<template v-if="item.total_images > 0">
												<template v-if="item.total_images > 1">
													<BaseUiSwiper
														name="lp"
														class="lp-images slick-arrow2"
														:options="{
															slidesPerView: 1,
															slidesPerGroup: 1,
															loop: true,
															effect: 'fade',
															fadeEffect: {
																crossFade: true,
															},
															pagination: {
																enabled: true,
																clickable: true,
															},
														}">
														<BaseUiSwiperSlide>
															<BaseUiImage :data="item.main_image_thumbs?.['width480-height360-crop1']" :alt="item.title" default="/images/no-image-480.jpg" />
														</BaseUiSwiperSlide>
														<template v-if="item?.other_images">
															<BaseUiSwiperSlide v-for="otherImage in item.other_images" :key="otherImage.id">
																<BaseUiImage :data="otherImage.image_thumbs?.['width480-height360-crop1']" :alt="item.title" default="/images/no-image-560.jpg" />
															</BaseUiSwiperSlide>
														</template>
													</BaseUiSwiper>
												</template>
												<span v-else>
													<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width480-height360-crop1']" :alt="item.title" default="/images/no-image-480.jpg" />
												</span>
											</template>
										</div>
										<div class="lp-right">
											<div class="lp-cnt">
												<h3 class="lp-title">
													<NuxtLink :to="item?.url_without_domain">{{ item.title }}</NuxtLink>
												</h3>
												<NuxtLink href="#map" class="lp-address">
													<div v-html="item.address"></div>
												</NuxtLink>
												<div class="lp-hours" v-html="item.business_hour"></div>
												<div class="lp-contact" v-html="item.contact"></div>
												<NuxtLink :to="item?.url_without_domain" class="btn btn-location-detail">
													<BaseCmsLabel code="more_info" tag="span"></BaseCmsLabel>
												</NuxtLink>
											</div>
										</div>
									</div>
								</template>
							</div>
						</div>
					</div>
				</BaseLocationPoints>
			</template>
		</CmsTwoColumns>
	</BaseCmsPage>
</template>

<script setup>
	const {scrollTo} = useDom();
</script>

<style lang="less" scoped>
	.no-locations {
		width: 100%;
		text-align: center;
		padding: 15px 15px 45px;
	}
	.lp-address {
		:deep(p) {
			padding-bottom: 0;
		}
	}
	.lp-images {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		:deep(img) {
			display: block;
			max-width: 100%;
			max-height: 100%;
		}
	}
	:deep(.lp-swiper-navigation) {
		position: absolute;
		width: 100%;
		display: flex;
		justify-content: space-between;
	}
	:deep(.swiper-button) {
		position: relative;
		width: 46px;
		height: 46px;
		top: calc(~'50% - 23px');
		cursor: pointer;
		&:after {
			.icon-arrow-right;
			font: 20px/10px @fonti;
			.transition(color);
		}
	}
	.slick-arrow2 {
		:deep(.swiper-button-prev) {
			left: -23px;
			@media (max-width: @m) {
				left: 10px;
			}
		}
		:deep(.swiper-button-next) {
			right: -23px;
			&:after {
				.rotate(180deg);
			}
			@media (max-width: @m) {
				right: 10px;
			}
		}
	}
</style>
