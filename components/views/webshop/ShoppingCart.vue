<template>
	<BaseCmsPage v-slot="{page}" :remarketing="false">
		<ClientOnly>
			<h1>{{ page.seo_h1 }}</h1>

			<BaseWebshopCart v-slot="{parcels, total}" :gtm-tracking="true" :remarketing="true">
				<BaseWebshopCartErrors v-slot="{errors, warnings}">
					<div v-if="errors">
						<p class="global-error" v-for="error in errors" :key="error.code">
							<BaseCmsLabel :code="error.code" />
						</p>
					</div>
					<div v-if="warnings">
						<p class="global-warning" v-for="warning in warnings" :key="warning.code">
							<BaseCmsLabel :code="warning.code" />
						</p>
					</div>
				</BaseWebshopCartErrors>

				<div v-for="parcel in parcels" :key="parcel.id">
					<ul v-if="parcel.items">
						<li v-for="item in parcel.items" :key="item.id">
							<NuxtLink :to="item.item.url_without_domain">{{ item.item.title }}</NuxtLink>
							<br />
							{{ item.item.code }}
							<div>{{ item.quantity }} x <BaseUtilsFormatCurrency :price="item.unit_price" /></div>
							<BaseThemeWebshopQty :quantity="item.quantity" :limit="item.available_quantity" :item="item" mode="cart" />
							<BaseWebshopRemoveProduct :item="item" />
						</li>
					</ul>
				</div>

				<template v-for="(value, key) in total" :key="key">
					<div v-if="value">{{ key }}: <BaseUtilsFormatCurrency :price="value" /></div>
				</template>
			</BaseWebshopCart>
		</ClientOnly>
	</BaseCmsPage>
</template>
