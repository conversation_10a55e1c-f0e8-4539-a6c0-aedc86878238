<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<BaseWebshopCheckout v-slot="{urls}">
				<h1>{{ page?.seo_h1 }}</h1>
				<div><NuxtLink class="step" :to="urls.webshop_customer">Podaci o kupcu</NuxtLink></div>
				<div><NuxtLink class="step" :to="urls.webshop_shipping"><PERSON><PERSON><PERSON> dostave</NuxtLink></div>
				<div><NuxtLink class="step" :to="urls.webshop_payment">Na<PERSON><PERSON> pla<PERSON>ja</NuxtLink></div>

				<BaseWebshopPaymentForm v-slot="{fields, loading, status, onPaymentUpdate}">
					<div v-if="status?.success && status?.data?.label_name">
						<div class="global-success"><BaseCmsLabel :code="status.data.label_name" /></div>
					</div>
					<div v-if="status?.data?.errors">
						<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
					</div>

					<p v-for="field in fields" :key="field.value">
						<BaseFormField :item="field" v-slot="{errorMessage}">
							<BaseFormInput :id="field.code" @click="onPaymentUpdate(field)" />
							<label :for="field.code">{{ field.title }}</label>
							<span class="error" v-show="errorMessage" v-html="errorMessage" />
						</BaseFormField>
					</p>

					<button type="submit" :disabled="loading">Sljedeći korak</button>
				</BaseWebshopPaymentForm>
				<div><NuxtLink class="step" :to="urls.webshop_review_order">Potvrda narudžbe</NuxtLink></div>
			</BaseWebshopCheckout>
		</ClientOnly>
	</BaseCmsPage>
</template>
