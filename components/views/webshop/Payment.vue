<template>
	<Body class="page-webshop page-webshop-payment page-checkout" />
	<BaseCmsPage v-slot="{page}">
		<WebshopCheckoutLayout>
			<template #wcCol1>
				<div class="wc-col wc-col1 wc-step3-col1">
					<WebshopStep :step="1" :completed="true" />
					<div class="wc-col-cnt">
						<BaseWebshopCheckout v-slot="{customer, urls}">
							<div v-html="page.content" v-if="page?.content" v-interpolation />
							<BaseCmsLabel code="step2" tag="h1" class="wc-title" />
							<BaseWebshopShippingForm class="step3 step-form3 form form-animated-label ajax_siteform ajax_siteform_loading" v-slot="{fields, onShippingUpdate, loading, status, activeShipping}" submitUrl="webshop_review_order">
								<div v-if="status?.data?.errors">
									<div class="error global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
								</div>

								<div class="shipping-options">
									<BaseCmsLabel code="checkout_shipping" tag="h2" class="wc-subtitle" v-interpolation />
									<template v-for="field in fields" :key="field.value">
										<BaseFormField :item="field" v-slot="{errorMessage}">
											<BaseFormInput :id="field.code">
												<template #default="{option, updateFormValue}">
													<input type="radio" :name="field.name" :id="'shipping-'+option.id" :value="option.id" :checked="option.selected" @change="updateFormValue" @click="onShippingUpdate(option)" />
													<label :for="'shipping-'+option.id">
														<span class="shipping-row" :class="option.code">
															<span class="shipping-payment-row-title" @click="onShippingUpdate(option)" v-html="option.title"></span>
															<div class="shipping-note">
																<div v-if="option.description" class="shipping_info" v-html="option.description"></div>
																<template v-if="option.code == 'dostavna_sluzba'">
																	<div class="shipping-address">
																		<span>{{customer.first_name}} {{customer.last_name}}</span>
																		<span>{{customer.address}}</span>
																		<!-- FIXME - kad ispišem {{customer.country}} vraća mi id a ne odabranu državu, potrebno proslijediti country_name -->
																		<span>{{customer.zipcode}} {{customer.city}} {{customer.country}}</span>
																		<span>{{customer.phone}}</span>
																		<span>{{customer.email}}</span>
																		<NuxtLink :to="urls.webshop_customer" class="btn-change-address"><BaseCmsLabel code="change_shipping_address" /></NuxtLink>
																	</div>
																</template>
															</div>
															<div class="shipping-data shipping_info shipping-info-parcels">
																<LazyBaseThemeWebshopGlsParcelLockers v-if="['gls_locker', 'gls_locker2'].includes(option.widget) && activeShipping.id == option.id" @select="onShippingUpdate(option, $event)" />
																<LazyBaseThemeWebshopBoxNowParcelLockers v-if="['boxnow_locker'].includes(option.widget) && activeShipping.id == option.id" @select="onShippingUpdate(option, $event)" />
																<LazyBaseThemeWebshopWoltShipping v-if="option.code.startsWith('wolt_') && activeShipping.id == option.id" />
															</div>
															<BaseUiImage v-if="option?.image_upload_path" loading="lazy" :src="option.image_upload_path" default="/images/no-image-100.jpg" :alt="option.title ? option.title : ''" />
															<BaseWebshopPickupLocations v-slot="{fields: locationFields, selectedLocation, onSelectLocation}" v-if="fields?.length && option.widget == 'location'">
																<div class="personal-pickup">
																	<BaseFormField :item="locationFields[0]" v-slot="{errorMessage}">
																		<BaseFormInput type="select" id="field-shipping_pickup_location" @change="onSelectLocation($event), onShippingUpdate(option)" :value="selectedLocation?.id ? selectedLocation.id : ''">
																			<option v-for="option in locationFields" :key="option.name" :value="option.value">{{ option.title }}</option>
																		</BaseFormInput>
																		<span class="error" v-show="errorMessage" v-html="errorMessage" />
																	</BaseFormField>
																	<div class="shipping-location" v-if="selectedLocation">
																		<div class="shipping-location-address"><BaseCmsLabel code="address" tag="strong" />: <span class="address" v-if="selectedLocation.address" v-html="selectedLocation.address"></span></div>
																	</div>
																</div>
															</BaseWebshopPickupLocations>
														</span>
													</label>
												</template>
											</BaseFormInput>
											<span class="error" v-show="errorMessage" v-html="errorMessage" />
										</BaseFormField>
									</template>
								</div>
							</BaseWebshopShippingForm>

							<BaseWebshopPaymentForm v-slot="{fields, status, loading, onPaymentUpdate}" submitUrl="webshop_review_order">
								<div v-if="status?.data?.errors">
									<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
								</div>

								<div class="payment-options">
									<BaseCmsLabel code="checkout_payment" tag="h2" class="wc-subtitle" v-interpolation />
									<div class="field-payment">
										<template v-for="field in fields" :key="field.value">
											<BaseFormField :item="field" v-slot="{errorMessage}">
												<BaseFormInput :id="field.code" option-class="payment-row">
													<template #default="{option, updateFormValue}">
														<input type="radio" :name="field.name" :id="'payment-'+option.id" :value="option.id" :checked="option.selected" @change="updateFormValue" @click="onPaymentUpdate(option)" />
														<label :for="'payment-'+option.id">
															<span class="shipping-payment-row-title" @click="onPaymentUpdate(option)">{{option.title}}</span>
															<div v-if="option.description" class="payment_info" :data-payment_code="option.code">
																<BaseUiImage v-if="option?.image_upload_path" loading="lazy" :src="option.image_upload_path" default="/images/no-image-100.jpg" :alt="option.title ? option.title : ''" />
																<span v-html="option.description"></span>
															</div>
														</label>
													</template>
												</BaseFormInput>

												<span class="error" v-show="errorMessage" v-html="errorMessage" />
											</BaseFormField>
										</template>
									</div>
								</div>
								<!-- FIXME - provjeriti s Miletom da li je ovo dobro napravljeno
								             te  šta ćemo s ovim dijelom oko minprice, GITHUB: https://github.com/markerhr/tvornicazdravehrane.com/blob/e6b9b6cc8544ececb1a3bead8cfd705817ae118d/application/views/webshop/payment.php#L98 -->
								<div class="section payment-section">
									<button class="btn btn-checkout btn-orange" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="next_step" tag="span" /></button>
								</div>
							</BaseWebshopPaymentForm>
						</BaseWebshopCheckout>
					</div>
					<WebshopStep :step="3" />
				</div>
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<style scoped lang="less">
	:deep(input[type=radio]+label){
		width: 100%;
	}
</style>
