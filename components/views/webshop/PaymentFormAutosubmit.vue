<template>
	<div v-if="pg?.form?.hidden_form">
		<form :action="pg.form.redirect_url" method="post" name="payment_confirm">
			<span v-html="pg.form.hidden_form"></span>
			<button type="submit" id="paymentHiddenButton" style="display: none">
				<BaseCmsLabel code="pay_by_credit_card" default="Plati" />
			</button>
		</form>
		<BaseThemeUiLoading />
	</div>
</template>

<script setup>
	const {pg} = useWebshop();
	onMounted(() => {
		setTimeout(function () {
			if (pg.value.form) {
				const paymentButton = document.getElementById('paymentHiddenButton');
				if (paymentButton) {
					paymentButton.click();
				}
			}
		}, 1000);
	});
</script>
