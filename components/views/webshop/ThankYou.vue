<template>
	<Body class="page-thankyou" />
	<BaseCmsPage v-slot="{page}">
		<BaseWebshopCheckout v-slot="{user}">
			<CmsTwoColumns>
				<template #main>
					<div class="fg1 lists main-content">
						<h1 class="thank-you-title" v-if="page?.seo_h1">
							{{ page.seo_h1 }} <template v-if="user?.first_name"> {{user.first_name}}</template>
						</h1>

						<ClientOnly>
							<BaseWebshopOrder v-slot="{order, shipping, termsPdf, invoicePdf, paymentTransferUrl}">
								<template v-if="order">
									<div v-if="page?.content" v-html="page.content" v-interpolation />
									<p v-if="termsPdf">
										<a :href="termsPdf" target="_blank" class="btn btn-download btn-download-pdf">
											<BaseCmsLabel code="thank_you_download_terms_pdf" tag="span" />
										</a>
									</p>

									<div class="invoice-container">
										<p>
											<BaseCmsLabel code="choosen_shipping" /> "{{ shipping?.title }}". <br />
											<BaseCmsLabel code="order_email" /> {{order?.customer?.email}}.
										</p>

										<p v-if="invoicePdf">
											<a :href="invoicePdf" target="_blank" class="btn btn-primary btn-download btn-download-pdf btn-download-terms"
												><span class="icon icon-pdf"><BaseCmsLabel :code="order?.cart?.payment_transfer_data ? 'order_download_invoice_payment' : 'order_download_invoice'" /></span
											></a>
										</p>
									</div>

									<div class="payment-transfer" v-if="order?.cart?.payment_transfer_data">
										<BaseWebshopPaymentTransfer :data="order.cart.payment_transfer_data" />
										<p>
											<a :href="paymentTransferUrl+'&mode=pdf'" target="_blank" class="btn btn-print"><BaseCmsLabel code="print_payment" tag="span" /></a>
										</p>
									</div>

									<!-- FIXME - provjeriti nakon što proradi registracija na cijelom webu da li i ovdje radi -->
									<div class="thank-you-wrapper" v-if="!user">
										<BaseAuthQuickSignupForm class="form-label ajax_siteform ajax_siteform_loading" :show-password="true" v-slot="{fields, status, loading}">
											<BaseCmsLabel code="thank_you_signup" tag="div" class="thank-you-content" />
											<div class="thank-you-login">
												<template v-if="!status?.success">
													<div class="quick-signup-fields" v-interpolation>
														<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
															<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
																<BaseFormInput :id="'quicksignup-' + item.name" />
																<BaseCmsLabel tag="label" :for="'quicksignup-' + item.name" :code="item.name" />
																<span class="error" v-show="errorMessage" v-html="errorMessage" />
															</p>
														</BaseFormField>
													</div>
													<button type="submit" class="btn" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel tag="span" code="save" /></button>
												</template>

												<!-- Uspješna registracija -->
												<div v-if="status?.success && status?.data.label_name" class="global-success thankyou_signup_success"><BaseCmsLabel :code="status.data.label_name" /></div>
											</div>
											<BaseCmsLabel code="safe_purchase_thankyou" tag="div" class="thank-you-safe" />
										</BaseAuthQuickSignupForm>
									</div>
								</template>
							</BaseWebshopOrder>
							<template #fallback>
								<BaseThemeUiLoading />
							</template>
						</ClientOnly>
					</div>
				</template>

				<template #sidebar>
					<CmsSidebar />
				</template>
			</CmsTwoColumns>
		</BaseWebshopCheckout>
	</BaseCmsPage>
</template>

<style scoped lang="less">
	.field-show_password{padding-bottom: 5px;}
	.field-accept_terms{padding-bottom: 15px;}
</style>

<script setup>
	const {togglePassword} = useDom();
	onMounted(() => {
		togglePassword({
			toggler: '#quicksignup-show_password',
			input: '#quicksignup-password',
		})
	});
</script>
