<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<BaseWebshopCheckout v-slot="{user}">
				<BaseWebshopOrder v-slot="{order, payment, shipping, termsPdf, invoicePdf, paymentTransferUrl}">
					<template v-if="order">
						<h1>{{ order.customer.first_name }}, hvala vam na narudžbi</h1>
						<div v-if="page?.content" v-html="page.content" />

						<!-- PDF predugovorne obavijesti -->
						<BaseUiLink :to="termsPdf" v-if="termsPdf">
							<BaseCmsLabel code="thank_you_download_terms_pdf" />
						</BaseUiLink>

						<!-- PDF račun -->
						<div class="thank-you-invoice" v-if="invoicePdf">
							<a :href="invoicePdf" target="_blank" class="btn btn-green"><BaseCmsLabel code="order_download_invoice" /></a>
						</div>

						<!-- Oda<PERSON>na dostava -->
						<h3><PERSON>da<PERSON><PERSON> ste sljedeće:</h3>
						<p><strong>Dostava</strong></p>
						<p>{{ shipping.title }}</p>

						<!-- Odabrano plaćanje -->
						<p><strong>Plaćanje</strong></p>
						<p>{{ payment.title }}</p>

						<!-- Prikaz popunjene opće uplatnice i link za print -->
						<BaseWebshopPaymentTransfer :data="order.cart.payment_transfer_data" />
						<NuxtLink :to="paymentTransferUrl + '&mode=pdf'" target="_blank" class="btn btn-print"><BaseCmsLabel code="print_payment" /></NuxtLink>

						<!-- Obrazac za brzu registraciju. Treba se prikazivati samo ako korisnik nije prijavljen -->
						<BaseAuthQuickSignupForm v-slot="{fields, status, loading}" v-if="!user">
							<h3>Brza registracija</h3>
							<!-- Greške -->
							<template v-if="status?.data?.errors?.length">
								<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.field }}: {{ error.error }}</div>
							</template>

							<!-- Forma za registraciju -->
							<template v-if="!status?.success">
								<div v-interpolation>
									<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
										<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
											<BaseFormInput :id="'quicksignup-' + item.name" />
											<BaseCmsLabel tag="label" :for="'quicksignup-' + item.name" :code="item.name" />
											<span class="error" v-show="errorMessage" v-html="errorMessage" />
										</p>
									</BaseFormField>
								</div>
								<button type="submit" :disabled="loading">
									<BaseCmsLabel code="form_login" />
								</button>
							</template>

							<!-- Uspješna registracija -->
							<div v-if="status?.success && status?.data.label_name" class="global-success"><BaseCmsLabel :code="status.data.label_name" /></div>
						</BaseAuthQuickSignupForm>
					</template>
				</BaseWebshopOrder>
			</BaseWebshopCheckout>
		</ClientOnly>
	</BaseCmsPage>
</template>
