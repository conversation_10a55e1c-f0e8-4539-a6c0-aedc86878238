<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<BaseWebshopCheckout v-slot="{urls}">
				<h1>{{ page?.seo_h1 }}</h1>
				<div><NuxtLink class="step" :to="urls.webshop_customer">Podaci o kupcu</NuxtLink></div>
				<div><NuxtLink class="step" :to="urls.webshop_shipping"><PERSON><PERSON><PERSON> dostave</NuxtLink></div>

				<BaseWebshopShippingForm v-slot="{loading, fields, onShippingUpdate, status}">
					<div v-if="status?.success && status?.data?.label">
						<div class="global-success"><BaseCmsLabel :code="status.data.label" /></div>
					</div>
					<div v-if="status?.data?.errors">
						<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
					</div>

					<p v-for="field in fields" :key="field.value">
						<BaseFormField :item="field" v-slot="{errorMessage}">
							<BaseFormInput :id="field.code" @click="onShippingUpdate(field)" />
							<label :for="field.code">
								{{ field.title }}
								<span v-show="field.description">{{ field.description }}</span>
							</label>
							<span class="error" v-show="errorMessage" v-html="errorMessage" />

							<!-- Pickup locations select menu -->
							<BaseWebshopPickupLocations v-slot="{fields, selectedLocation, onSelectLocation}" v-if="fields?.length && field.widget == 'location' && field.isSelected">
								<BaseFormField :item="fields[0]" v-slot="{errorMessage}">
									<BaseFormInput type="select" @change="onSelectLocation($event), onShippingUpdate(field)">
										<option value="">-</option>
										<option v-for="field in fields" :key="field.name" :value="field.value">{{ field.title }}</option>
									</BaseFormInput>
									<span class="error" v-show="errorMessage" v-html="errorMessage" />
								</BaseFormField>
								<div v-if="selectedLocation">{{ selectedLocation.title }}</div>
							</BaseWebshopPickupLocations>
						</BaseFormField>
					</p>
					<button :disabled="loading" type="submit">Sljedeći korak</button>
				</BaseWebshopShippingForm>

				<div><NuxtLink class="step" :to="urls.webshop_payment">Način plaćanja</NuxtLink></div>
				<div><NuxtLink class="step" :to="urls.webshop_review_order">Potvrda narudžbe</NuxtLink></div>
			</BaseWebshopCheckout>
		</ClientOnly>
	</BaseCmsPage>
</template>
