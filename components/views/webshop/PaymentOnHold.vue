<template>
	<BaseCmsPage>
		<BaseThemeWebshopPageContent />
		<BaseThemeUiLoading />
	</BaseCmsPage>
</template>

<script setup>
	const route = useRoute();
	const webshop = useWebshop();
	const {getAppUrl} = useApiRoutes();
	let intervalCallOrderStatus;

	/* 
		Try to fetch order status every 2 seconds.
		If order status is "thank_you", redirect to thank you page (webshop/ThankYou).
		If order status is "failed_payment", redirect to failed payment page (webshop/FailedPayment).
		If alt_payments is enabled, redirect to new page (webshop/PaymentNew) where user can select another payment method.
	*/
	onMounted(async () => {
		const urlOrderIdentificator = route.query?.order_identificator?.split('|') || [];

		async function callOrderStatus() {
			if (intervalCallOrderStatus) clearInterval(intervalCallOrderStatus);

			// Redirect to homepage if order_identificator is empty
			if (!urlOrderIdentificator?.length) {
				await navigateTo('/');
				return;
			}

			const orderIdentificator = urlOrderIdentificator?.length > 1 ? `${urlOrderIdentificator[0]}-${urlOrderIdentificator[1]}` : urlOrderIdentificator[0];
			const status = await webshop.fetchOrderStatus({code: orderIdentificator});
			if (status?.data?.next_view) {
				if (status.data.next_view == 'thank_you') {
					return await navigateTo({path: getAppUrl('webshop_thank_you'), query: {order_identificator: orderIdentificator}});
				}
				if (status.data.next_view == 'failed_payment') {
					return await navigateTo({path: getAppUrl('webshop_failed_payment'), query: {order_identificator: orderIdentificator}});
				}
			}
		}

		intervalCallOrderStatus = setInterval(callOrderStatus, 2000);
	});

	onBeforeUnmount(() => {
		clearInterval(intervalCallOrderStatus);
	});
</script>
