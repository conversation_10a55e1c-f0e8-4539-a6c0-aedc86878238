<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<BaseWebshopCheckout>
				<h1>{{ page?.seo_h1 }}</h1>
				<div v-html="page?.content" />

				<BaseWebshopGuestForm v-slot="{loading, fields}" class="login-form">
					<p v-for="field in fields" :key="field.name">
						<BaseFormField :item="field" v-slot="{errorMessage}">
							<BaseFormInput id="guest-email" />
							<label for="guest-email"><BaseCmsLabel code="email" /></label>
							<span class="error" v-show="errorMessage" v-html="errorMessage" />
						</BaseFormField>
					</p>
					<button type="submit" :disabled="loading">Nastavi kao gost</button>
				</BaseWebshopGuestForm>
			</BaseWebshopCheckout>

			<BaseAuthLoginForm submit-url="webshop_customer" v-slot="{loading, fields}">
				<p v-for="field in fields" :key="field.name">
					<BaseFormField :item="field" v-slot="{errorMessage}">
						<BaseFormInput :id="`checkout_${field.name}`" />
						<label :for="`checkout_${field.name}`"><BaseCmsLabel :code="field.name" /></label>
						<span class="error" v-show="errorMessage" v-html="errorMessage" />
					</BaseFormField>
				</p>
				<button type="submit" :disabled="loading">Prijava</button>
			</BaseAuthLoginForm>
		</ClientOnly>
	</BaseCmsPage>
</template>
