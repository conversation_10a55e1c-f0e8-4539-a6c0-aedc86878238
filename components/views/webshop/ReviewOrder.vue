<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<BaseWebshopCheckout v-slot="{urls}">
				<h1>{{ page?.seo_h1 }}</h1>
				<div><NuxtLink class="step" :to="urls.webshop_customer">Podaci o kupcu</NuxtLink></div>
				<div><NuxtLink class="step" :to="urls.webshop_shipping"><PERSON><PERSON><PERSON> dostave</NuxtLink></div>
				<div><NuxtLink class="step" :to="urls.webshop_payment">Na<PERSON><PERSON> plaćanja</NuxtLink></div>
				<div><NuxtLink class="step" :to="urls.webshop_review_order">Potvrda narudžbe</NuxtLink></div>
				<BaseWebshopReviewOrderForm v-slot="{fields, customer, total, shipping, payment, loading, orderErrors}">
					<h2>Podaci o kupcu <NuxtLink :to="urls.webshop_customer">Uredi</NuxtLink></h2>
					<div>
						<p>{{ customer.first_name }} {{ customer.last_name }}</p>
						<p>
							{{ customer.address.street }}, {{ customer.address.zipcode }} {{ customer.address.city }}
							<span v-if="customer?.address?.country">, {{ customer.address.country }}</span>
						</p>
						<p v-if="customer?.phone">{{ customer.phone }}</p>

						<!-- FIXME PROG ne prenosi se email adresa s prvog koraka -->
						<p v-if="customer?.email">{{ customer.email }}</p>

						<p class="global-error" v-if="orderErrors?.customer"><BaseCmsLabel :code="orderErrors.customer" /></p>
					</div>

					<h2>Podaci za dostavu <NuxtLink :to="urls.webshop_customer">Uredi</NuxtLink></h2>
					<div v-if="customer?.b_first_name">
						<p>{{ customer.b_first_name }} {{ customer.b_last_name }}</p>
						<p>
							{{ customer.b_address.b_street }}, {{ customer.b_address.b_zipcode }} {{ customer.b_address.b_city }}
							<span v-if="customer?.b_address?.b_country">, {{ customer.b_address.b_country }}</span>
						</p>
						<p v-if="customer?.b_phone">{{ customer.b_phone }}</p>
					</div>
					<div v-else>
						<p>Jednaki su podacima o kupcu</p>
					</div>

					<!-- FIXME PROG polja o tvrtki (OIB, ime, adresa) https://markerdoo.eu.teamwork.com/app/tasks/26063314 -->
					<h2 v-if="customer?.b_company_oib">Podaci o tvrtki <NuxtLink :to="urls.webshop_customer">Uredi</NuxtLink></h2>
					<div v-if="customer?.b_company_oib">
						<p>{{ customer.b_company_name }}</p>
						<p>{{ customer.b_company_address }}</p>
						<p>{{ customer.b_company_oib }}</p>
					</div>

					<h2>Dostava <NuxtLink :to="urls.webshop_shipping">Uredi</NuxtLink></h2>
					<p>{{ shipping?.title }}</p>
					<p class="global-error" v-if="orderErrors?.shipping"><BaseCmsLabel :code="orderErrors.shipping" /></p>

					<h2>Plaćanje <NuxtLink :to="urls.webshop_payment">Uredi</NuxtLink></h2>
					<p>{{ payment?.title }}</p>
					<p class="global-error" v-if="orderErrors?.payment"><BaseCmsLabel :code="orderErrors.payment" /></p>

					<h2>Sveukupno za platiti</h2>
					<p><BaseUtilsFormatCurrency :price="total.total" /></p>

					<!-- FIXME PROG dodatno polje za prihvaćanje obveze plaćanja https://markerdoo.eu.teamwork.com/app/tasks/26063044 -->
					<template v-if="fields">
						<div class="alert" v-show="acceptTermsAlert">Obavezni ste prihvatiti uslove kupovine i obavezu plaćanja</div>
						<p v-for="field in fields" :key="field.name" v-interpolation>
							<BaseFormField :item="field" v-slot="{errorMessage}">
								<BaseFormInput @click="checkAlert" />
								<BaseCmsLabel tag="label" :for="field.name" :code="field.name" />
								<span class="error" v-show="errorMessage" v-html="errorMessage" />
							</BaseFormField>
						</p>
					</template>

					<button type="submit" :disabled="loading" v-if="!orderErrors">Završi narudžbu</button>
				</BaseWebshopReviewOrderForm>
			</BaseWebshopCheckout>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const acceptTerms = ref(false);
	const acceptTerms2 = ref(false);
	function checkAlert(e) {
		if (e.target.name == 'accept_terms') acceptTerms.value = e.target.checked;
		if (e.target.name == 'accept_terms_2') acceptTerms2.value = e.target.checked;
	}

	// show/hide alert
	const acceptTermsAlert = computed(() => {
		return acceptTerms.value && acceptTerms2.value ? false : true;
	});

	// show/hide alert on page load
	onMounted(() => {
		const acceptTermsCheckbox = document.querySelector('[name="accept_terms"]');
		if (acceptTermsCheckbox) acceptTerms.value = acceptTermsCheckbox?.checked;

		const acceptTerms2Checkbox = document.querySelector('[name="accept_terms_2"]');
		if (acceptTerms2Checkbox) acceptTerms2.value = acceptTerms2Checkbox?.checked;
	});
</script>
