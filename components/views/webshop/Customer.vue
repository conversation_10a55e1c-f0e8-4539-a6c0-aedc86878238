<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<BaseWebshopCheckout v-slot="{urls}">
				<h1>{{ page?.seo_h1 }}</h1>
				<div><NuxtLink class="step" :to="urls.webshop_customer">Podaci o kupcu</NuxtLink></div>

				<BaseWebshopCustomerForm v-slot="{loading, fields, meta, values, status, errors}" data-autofocus>
					<p class="global-error" v-if="status?.data?.errors?.length">
						Greška kod popunjavanja obrasca
						<!--<div v-for="(error, index) in status.data.errors" :key="index">{{ error.field }}: {{ error.error }}</div>-->
					</p>

					<p v-for="field in fields" :key="field.name">
						<BaseFormField :item="field" v-slot="{errorMessage}">
							<BaseFormInput />
							<label :for="field.name"><BaseCmsLabel :code="field.name" /></label>
							<span class="error" v-show="errorMessage" v-html="errorMessage" />
						</BaseFormField>
					</p>

					<button type="submit" :disabled="loading">Sljedeći korak</button>
				</BaseWebshopCustomerForm>

				<div><NuxtLink class="step" :to="urls.webshop_shipping">Način dostave</NuxtLink></div>
				<div><NuxtLink class="step" :to="urls.webshop_payment">Način plaćanja</NuxtLink></div>
				<div><NuxtLink class="step" :to="urls.webshop_review_order">Potvrda narudžbe</NuxtLink></div>
			</BaseWebshopCheckout>
		</ClientOnly>
	</BaseCmsPage>
</template>
