<template>
	<div>
		<Title>{{ pageTitle }}</Title>
		<BaseThemeUiLoading />
	</div>
</template>

<script setup>
	const {handleOrder, fetchOrder} = useWebshop();
	const {getLang} = useLang();
	const route = useRoute();
	const {getUrlSegments} = useUrl();
	const {getAppUrl} = useApiRoutes();
	const pageTitle = computed(() => {
		return getLang('hr') ? 'Plaćanje' : 'Payment';
	});

	onMounted(async () => {
		const orderIdentificator = getUrlSegments(route.path, {ignoreLang: true, offset: 3, stringify: true});

		if (!orderIdentificator) {
			return console.error('Missing order identificator');
		}

		try {
			const orderRes = await fetchOrder({code: orderIdentificator});
			await handleOrder({order: orderRes, orderId: orderIdentificator});
		} catch (error) {
			console.error(error);
			return navigateTo(getAppUrl('webshop_failed_order'));
		}
	});
</script>
