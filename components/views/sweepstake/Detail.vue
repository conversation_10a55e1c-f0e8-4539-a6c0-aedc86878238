<template>
	<Body class="header-spacing0 page-sweepstake" />
	<ClientOnly>
		<!-- /upitnik/upitnik-zadovoljstva/?webshoporder_code=  &webshoporder_id=   -->
		<!-- FIXME INTEG postaviti trackinge kako je na produkciji -->
		<div class="sweepstake-main">
			<div class="wrapper sweepstake-wrapper">
				<SweepstakePageCustom v-slot="{page}" @load="onPageLoad">
					<div class="sweepstake-header">
						<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
						<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
						<div v-if="page?.datetime_active_from" class="sw-date sweepstake-date">
							<BaseCmsLabel code="vote_active" tag="strong" />&nbsp;
							<template v-if="page?.datetime_active_from"> <BaseCmsLabel code="vote_active_from" /> <BaseUtilsFormatDate :date="page.datetime_active_from" format="DD.MM.YYYY." />&nbsp; </template>
							<template v-if="page?.datetime_active_to"><BaseCmsLabel code="vote_active_to" /> <BaseUtilsFormatDate :date="page.datetime_active_to" format="DD.MM.YYYY." /></template>
						</div>
					</div>
					<div class="cms-content">
						<div class="sweepstake-cnt" v-if="page?.content" v-html="page.content" v-interpolation />

						<div v-if="successValue" class="global-success" data-scroll-to-success><BaseCmsLabel :code="successValue" tag="span" /></div>
						<BaseForm v-else-if="page?.can_vote" v-slot="{errors}" @submit="onSubmit">
							<template v-if="Object.keys(errors)?.length">
								<BaseCmsLabel tag="div" class="global-error" code="form_validation_error" data-scroll-to-error />
							</template>
							<template v-if="errorValue">
								<BaseCmsLabel tag="div" class="global-error" :code="errorValue" data-scroll-to-error />
							</template>

							<div v-if="productsForm.length" class="sw-products">
								<BaseCmsLabel code="sweepstake_products_review_title" class="sw-section-title" />

								<div class="sw-product form-animated-label" v-for="(product, index) in productsForm" :key="index">
									<div class="sw-product-info">
										<div class="sw-product-image" v-if="product?.main_image_upload_path"><BaseUiImage :src="product.main_image_upload_path" /></div>
										<div class="sw-product-cnt">
											<div class="sw-product-category">{{product.category_title}}</div>
											<div class="sw-product-title">{{product.title}}</div>
										</div>
									</div>

									<template v-for="field in product.formFields" :key="field.name">
										<BaseFormField v-if="field.type != 'hidden'" :item="field" v-slot="{errorMessage, value, isTouched, floatingLabel}">
											<div class="field field-images" v-if="field.name.includes('image_1')">
												<input type="file" :name="field.name" :id="field.name" multiple ref="imageInput" @change="(event) => handleImageChange(event, product.product_id)" />
												<label :for="field.name"><BaseCmsLabel code="field_add_photos" /><BaseCmsLabel code="field_add_photos_max" tag="span" /></label>

												<span class="uploaded-images" v-if="images[product.product_id] && images[product.product_id].length">
													<span class="uploaded-images-items">
														<div v-for="(image, index) in images[product.product_id]" :key="index">{{image.name}}</div>
													</span>
													<span class="uploaded-delete" @click="clearImages(product.product_id, index)"><BaseCmsLabel code="delete_images" /></span>
												</span>
											</div>
											<div v-else-if="field.name.includes('video')" class="sw-product-field field field-video" :class="['field-' + field.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
												<input type="file" :name="field.name" :id="field.name" ref="videoInput" @change="(event) => {videos[product.product_id] = event.target.files?.[0]}" />
												<BaseCmsLabel tag="label" :for="field.name" code="video" />

												<div class="uploaded-images" v-if="videos[product.product_id]?.name">
													<span class="uploaded-images-items">
														{{ videos[product.product_id].name }}
													</span>
													<span class="uploaded-delete" @click="clearVideo(product.product_id, index)"><BaseCmsLabel code="delete_video" /></span>
												</div>
											</div>

											<div
												v-else
												class="sw-product-field field"
												:class="['field-' + field.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}, {'field-type-rate': field.type == 'rate'}, {'field-video': field.name.includes('video')}, {'field-textarea': field.type == 'textarea'}]">
												<span v-if="field.type == 'rate'" class="label"><BaseCmsLabel code="your_rate" /></span>
												<BaseFormInput :id="field.name" :name="field.name" :placeholder="labels.get('inquiry_message')" v-if="field.type == 'textarea'" />
												<BaseFormInput :id="field.name" :name="field.name" v-else />
												<span class="field_error error" v-show="errorMessage" v-html="errorMessage" />
											</div>
										</BaseFormField>
									</template>
								</div>
							</div>

							<div class="sweepstake-form form-animated-label form-label">
								<div class="sweepstake-questions">
									<BaseCmsLabel code="sweepstake_other_title" class="sw-section-title" />

									<template v-for="(field, index) in fields" :key="field.name">
										<div class="sweepstake-field clear">
											<BaseFormField :item="field" v-slot="{errorMessage, value, isTouched, floatingLabel}">
												<h2 v-if="field.title" class="sweepstake-question-title">{{ field.title }}<span v-if="field.validation.length"> *</span></h2>
												<div
													class="sweepstake-question-fields field"
													:class="['field-' + field.id, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value},{'last': index === fields.length - 1}, {'field-textarea': field.type == 'textarea'}]">
													<div v-if="field.tips" class="sweepstake-note" v-html="field.tips"></div>
													<BaseFormInput />
													<!--BaseCmsLabel v-if="field.type != 'textarea'" tag="label" :for="field.id" code="inquiry_message" /-->
													<span class="error" v-show="errorMessage" v-html="errorMessage" />
												</div>
											</BaseFormField>
										</div>
									</template>

									<template v-for="field in userFields" :key="field.name">
										<BaseFormField :item="field" v-slot="{errorMessage, value, isTouched, floatingLabel}">
											<div class="field" :class="['field-' + field.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
												<BaseFormInput />
												<BaseCmsLabel tag="label" :for="field.name" :code="'sweepstake_'+field.name" />
												<span class="error" v-show="errorMessage" v-html="errorMessage" />
											</div>
										</BaseFormField>
									</template>

									<div class="sweepstake-btn-section">
										<button type="submit" class="btn btn-sweepstake" :class="{'loading': loadingValue}"><UiLoader v-if="loadingValue" /><BaseCmsLabel code="vote" tag="span" /></button>
									</div>
								</div>
							</div>
						</BaseForm>
						<template v-else>
							<BaseCmsLabel v-if="!page?.vote_url" class="votes-finish global-warning" code="votes_closed" />
							<BaseCmsLabel v-else-if="!page?.can_vote" class="votes-finish global-success" code="already_vote" />
						</template>
					</div>
				</SweepstakePageCustom>
			</div>
		</div>
	</ClientOnly>
</template>

<script setup>
	const labels = useLabels();
	const endpoints = useEndpoints();
	let fields = ref(null);
	let userFields = ref([]);
	let loadingValue = ref(false);
	let successValue = ref(null);
	let errorValue = ref(null);
	let pageCode = ref('');
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {fetchOrder} = useWebshop();
	const order = ref(null);

	const productsForm = ref([]);

	const images = ref({})
	const imageInput = ref({})
	const videos = ref({})
	const videoInput = ref({})

	function handleImageChange(event, productId) {
		const selectedImages = Array.from(event.target.files)
		images.value[productId] = selectedImages.slice(0, 5)
	}

	function clearImages(productId, index) {
		images.value[productId] = []
		imageInput.value[index].value = ''
	}

	function clearVideo(productId, index) {
		videos.value[productId] = ''
		videoInput.value[index].value = ''
	}

	async function onPageLoad(item) {
		if(!item) return;

		if(item) {
			pageCode.value = item?.code;
			if(route?.query?.webshoporder_code && route?.query?.webshoporder_id){
				order.value = await fetchOrder({code: `${route.query.webshoporder_id}-${route.query.webshoporder_code}`});
				console.log(order.value);
			}

			if(item.items_form?.length && item.items.length){
				const fieldsByProductId = {};
				let pid = null;

				item.items_form.forEach(field => {
					if (field.name === 'id') {
						pid = field.value.match(/catalogproduct-(\d+)-hr/)?.[1];
						if (pid) fieldsByProductId[pid] = [];
						return;
					}

					if (pid) {
						if (field.name === 'message') {
							field.name = 'comment';
						}

						if (field.name === 'rate') {
							field.type = 'rate';
						}

						if (['comment', 'rate'].includes(field.name)) {
							field.validation.push({type: 'not_empty', value: null, error: 'error_not_empty'});
						}

						field.name = `${field.name}-${pid}`;

						if (field.name.startsWith('image_1') || !field.name.includes('image')) {
							fieldsByProductId[pid].push(field);
						}
					}
				});

				productsForm.value = item.items.map(product => ({
					...product,
					formFields: fieldsByProductId[product.product_id] || []
				}));
			}


			//fields
			const questionsArray = Object.values(item?.questions);

			fields.value = questionsArray
			.sort((a, b) => (a.position || 0) - (b.position || 0))
			.map(obj => {
				let fieldType = obj.type;

				if (obj.type === "selectm") {
					fieldType = "checkbox";
				} else if (obj.type === "text") {
					fieldType = "textarea";
				} else if (obj.type === "") {
					fieldType = "radio";
				}

				let optionsValue = null;
				if(obj?.answers_option){
					optionsValue = Object.entries(obj.answers_option).map(([key, title]) => ({
						title,
						key: parseInt(key),
						selected: false,
					}));
				}

				return {
					id: obj.id,
					name: 'question_' + obj.id,
					value: '',
					type: fieldType,
					title: obj.title || null,
					description: obj.tips || null,
					related_field: null,
					opens_fields: null,
					extra_info: null,
					options: optionsValue,
					error_text: obj.error_text || null,
					validation: obj.requested == 1 ? [{"type": "not_empty", "value": null, "error": "error_not_empty"}] : []
				};
			});

			//user fields
			if(item?.user_form?.length){
				item.user_form.forEach(field => {
					if(field.name == 'gdpr_accept') {
						field.validation = [{type: 'not_empty', value: null, error: 'error_not_empty'}];
					}
					userFields.value.push(field);
				})
			}


		}
	}

	//submit
	async function onSubmit({values, actions}) {
		values.email = order.value?.data?.customer?.email ? order.value.data.customer.email : '';
		values.first_name = order.value?.data?.customer?.first_name ? order.value.data.customer.first_name : '';
		values.last_name = order.value?.data?.customer?.last_name ? order.value.data.customer.last_name : '';
		values.phone = order.value?.data?.customer?.phone ? order.value.data.customer.phone : '';
		values.webshoporder_code = order.value?.data?.cart?.order_code ? order.value.data.cart.order_code : '';

		const formData = new FormData()

		formData.append('page_code', pageCode.value)

		Object.entries(values).forEach(([key, value]) => {
			if (Array.isArray(value)) {
				value.forEach((v, index) => {
					formData.append(`${key}[${index}]`, v)
				})
			}
			else {
				formData.append(key, value)
			}
		})

		Object.entries(images.value).forEach(([productId, images]) => {
			images.forEach((file, index) => {
				const fieldName = `image_${index + 1}-${productId}`
				formData.append(fieldName, file)
			})
		})

		Object.entries(videos.value).forEach(([productId, video]) => {
			if(video){
				const fieldName = `video-${productId}`
				formData.append(fieldName, video)
			}
		})

		/*
		for (let [key, value] of formData.entries()) {
			console.log(key, value)
		}*/

		loadingValue.value = true;

		await useApi(`${endpoints.get('_post_hapi_sweepstake_participant')}`, {
			method: 'POST',
			body: formData
		}).then(async (res) => {
			errorValue.value = null;
			if(res.success) {
				successValue.value = res?.data?.label_name;
				if(res?.data?.redirect_url_without_domain) {
					navigateTo({path: res.data.redirect_url_without_domain});
				} else {
					await nextTick();

					const successEl = document.querySelector('.global-success');
					if (successEl) {
						successEl.scrollIntoView({behavior: 'smooth'});
					}
				}
			} else {
				errorValue.value = res?.data?.label_name;

				await nextTick();

				const errorEl = document.querySelector('.global-error');
				if (errorEl) {
					errorEl.scrollIntoView({behavior: 'smooth'});
				}

				// set field errors if response contains api errors
				if (res?.data?.errors?.length) {
					res.data.errors.forEach(error => {
						actions.setFieldError(error.field, labels.get(error.error, error.error));
					});

					await nextTick();
					const errorEl = document.querySelector('.global-error');
					if (errorEl) {
						errorEl.scrollIntoView({behavior: 'smooth'});
					}

					return res;
				}
			}
		});

		loadingValue.value = false;
	}
</script>

<style lang="less" scoped>
	.sweepstake-main{
		padding: 40px 0 88px;
		@media (max-width: @m){padding: 15px 0 40px;}
	}
	.sweepstake-header{
		position: relative; display: flex; flex-flow: column; align-items: center; margin-bottom: 24px;
		@media (max-width: @m){align-items: flex-start; margin-bottom: 10px;}
	}
	h1{
		font-size: 40px; color: #244538;
		@media (max-width: @m){font-size: 24px; line-height: 1.4;}
	}
	.sweepstake-date{
		position: relative; padding-left: 32px; font-size: 18px; line-height: 24px;
		&:before{.pseudo(22px,22px); .icon-clock(); font: 22px/1 @fonti; color: @lightGreen; left: 0;}
		@media (max-width: @m){font-size: 14px; line-height: 22px;}
	}
	.sweepstake-cnt{
		font-size: 16px; line-height: 24px; padding-bottom: 24px;
		:deep(p){padding-bottom: 8px;}
		@media (max-width: @m){font-size: 12px; line-height: 1.3333; padding-bottom: 16px;}
	}
	.sw-products, .sweepstake-questions{
		display: flex; flex-flow: column; gap: 24px; margin-bottom: 32px;
		@media (max-width: @m){gap: 12px; margin-bottom: 24px;}
	}
	.sweepstake-questions{
		margin-bottom: 0;
		@media (max-width: @m){margin-bottom: 0;}
	}
	.sw-section-title{
		display: block; font-size: 22px; line-height: 1.2; font-weight: bold;
		@media (max-width: @m){font-size: 16px; line-height: 1.38;}
	}
	.sw-product{
		position: relative; display: flex; flex-wrap: wrap; align-items: center; gap: 8px; padding: 24px; border: 1px solid #DEDEDE; border-radius: 2px; background: #F9FAF9;
		@media (max-width: @m){padding: 16px; gap: 8px;}
	}
	.sw-product-info{
		position: relative; flex-grow: 1; flex-shrink: 1; display: flex; align-items: center; gap: 16px;
		@media (max-width: @m){gap: 12px;}
	}
	.sw-product-image{
		position: relative; display: flex; align-items: center; justify-content: center; background: #fff; border: 1px solid #DEDEDE; border-radius: 2px; width: 80px; height: 80px;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%;}
		@media (max-width: @m){width: 72px; height: 72px;}
	}
	.sw-product-cnt{
		position: relative; display: flex; flex-flow: column; gap: 5px;
		@media (max-width: @m){gap: 3px;}
	}
	.sw-product-category{
		font-size: 12px; line-height: 1.2; text-transform: uppercase; font-weight: bold; color: @lightGreen;
		@media (max-width: @m){font-size: 11px;}
	}
	.sw-product-title{
		display: block; font-size: 14px; line-height: 1.43; max-width: 245px;
		@media (max-width: @m){font-size: 12px; max-width: inherit;}
	}

	.field-type-rate{
		display: flex; flex-flow: column; align-items: center; width: 300px; flex-grow: 0; flex-shrink: 0;
		.label{display: block; font-size: 14px; line-height: 1.2; margin-bottom: 5px;}
		@media (max-width: @m){
			width: 100%; flex-flow: row; margin: 4px 0;
			.label{margin-bottom: 0; margin-right: 8px;}
		}
	}
	:deep(.form-rate-item){
		display: inline-flex; margin-right: 5px; width: 24px; height: 24px;
		input[type=radio]+label{
			padding: 0; font-size: 0; width: 24px; height: 24px;
			&:before{.icon-star(); width: 100%; height: 100%; color: #D5D9D3; font: 24px/24px @fonti; border: 0; box-shadow: none!important; background: none!important;}
			&:after{display: none;}
		}
		&.active, &.active-hover{
			input[type=radio]+label:before{color: @yellow;}
		}
		@media (max-width: @m){
			input[type=radio]+label{
				width: 22px; height: 22px;
				&:before{font-size: 22px; line-height: 1;}
			}
		}
	}
	:deep(.form-field-rate-items){display: flex;}
	.field-images, .field-video{
		width: calc(~"50% - 4px"); position: relative; align-self: flex-start;
		label{
			position: absolute; display: flex; align-items: center; background: #fff; top: 1px; left: 1px; right: 1px; height: 52px; border-radius: @borderRadius; padding: 0 20px;
			&:before{.icon-image-gallery(); font: 24px/1 @fonti; color: @lightGreen; margin-right: 8px;}
			span{font-size: 12px; line-height: 1.2; color: @gray2; flex-grow: 1; flex-shrink: 1; text-align: right;}
			@media (max-width: @m){height: 45px;}
		}
		input{display: block; width: 100%; height: 54px;}
		@media (max-width: @m){
			width: 100%;
			input{height: 47px;}
		}
	}
	.field-video label:before{.icon-video();}
	.uploaded-images{position: relative; display: flex; flex-flow: column; padding: 5px 20px 0;}
	.uploaded-images-items{display: flex; flex-flow: column; gap: 5px; font-size: 12px; line-height: 1.2; color: @lightGreen;}
	.uploaded-delete{
		display: block; font-size: 12px; line-height: 1.2; color: @red; margin-top: 5px; font-weight: bold; cursor: pointer; .transition(color);
		@media (min-width: @h){
			&:hover{color: @red*1.2;}
		}
	}
	.error{padding-left: 20px; font-size: 12px; line-height: 1.2;}
	.field-textarea{
		position: relative; display: block; width: 100%;
		:deep(textarea){width: 100%;}
	}

	.sweepstake-question-title{
		display: block; font-size: 18px; line-height: 1.333; padding: 0 0 15px;
		@media(max-width: @m){font-size: 14px; padding: 0 0 10px;}
	}
	.sweepstake-question-fields{
		display: flex; flex-wrap: wrap; gap: 24px; row-gap: 18px;
		@media(max-width: @m){
			flex-flow: column; gap: 10px;
			:deep(input[type=radio]+label:before){width: 20px;}
		}
	}

	.btn-sweepstake{
		min-width: 220px;
		@media(max-width: @m){min-width: 107px;}
	}
</style>
