<template>
	<BaseCmsPage v-slot="{page}">
		<CmsTwoColumns>
			<template #main>
				<div class="fg1 lists main-content">
					<CmsBreadcrumbs :items="page?.breadcrumbs" />
					<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
					<div v-if="page?.content" v-html="page.content" v-interpolation />

					<BaseTagTags v-slot="{items}">
						<ul v-if="items?.length">
							<li v-for="item in items" :key="item.id">
								<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
							</li>
						</ul>
					</BaseTagTags>

					<CmsShare />
				</div>
			</template>

			<template #sidebar>
				<CmsSidebar />
			</template>
		</CmsTwoColumns>
	</BaseCmsPage>
</template>
 meet.google.com/gak-cprm-ioc