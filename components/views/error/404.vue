<template>
	<Body class="page-error page-error-404" />
	<Title>{{ labels.get('404_title', '404') }}</Title>
	<h1 class="error-404-title"><BaseCmsLabel code="404_h1" default="404" /></h1>
	<div v-interpolation>
		<BaseCmsLabel code="404_description" tag="div" class="error-404-desc" />
	</div>
	<div class="error-404-links">
		<NuxtLink :to="backLink" class="btn" type="button"><BaseCmsLabel code="return_back" /></NuxtLink>
	</div>
</template>

<script setup>
	const {getAppUrl} = useApiRoutes();
	const labels = useLabels();
	const router = useRouter();
	const backLink = computed(() => {
		if (router?.options?.history?.state?.back) {
			return router.options.history.state.back;
		}
		return getAppUrl('home');
	});
</script>
