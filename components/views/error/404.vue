<template>
	<BaseCmsPage :fetch="{slug: '/404/'}" v-slot="{page}">
		<Body class="page-error page-error-404" />

		<CmsTwoColumns>
			<template #main>
				<div class="fg1 lists main-content">
					<CmsBreadcrumbs :items="page?.breadcrumbs" />
					<BaseCmsLabel code="404_h1" default="404" tag="h1" class="error-404-title" />
					<div v-interpolation>
						<BaseCmsLabel code="404_description" tag="div" class="error-404-desc" />
					</div>
					<div class="error-404-links">
						<NuxtLink :to="backLink" class="btn" type="button"><BaseCmsLabel code="return_back" tag="span" /></NuxtLink>
					</div>
				</div>
			</template>

			<template #sidebar>
				<CmsSidebar />
			</template>
		</CmsTwoColumns>
	</BaseCmsPage>
</template>

<script setup>
	const {getAppUrl} = useApiRoutes();
	const router = useRouter();
	const backLink = computed(() => {
		if (router?.options?.history?.state?.back) {
			return router.options.history.state.back;
		}
		return getAppUrl('home');
	});
</script>

<style lang="less" scoped>
	.error-404-links{display: block; margin-top: 15px;}
</style>
