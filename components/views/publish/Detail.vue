<template>
	<Body class="page-publish-detail page-article-detail" />
	<BasePublishDetail :root-category="true" v-slot="{item}">
		<BaseCatalogProductsWidget :fetch="{related_publish_id: item.id, limit: 20}" v-slot="{items}" v-model="relatedProducts">
			<div class="pd-body">
				<div class="pd-content-wrapper">
					<div class="pd-short-description" v-if="item.short_description" v-html="item.short_description" v-interpolation></div>
					<div class="pd-info">
						<NuxtLink class="pd-all-articles" :class="{hasProducts: items?.length}" :to="item.root_category.url_without_domain" v-if="item.root_category"><BaseCmsLabel code="all_articles" /></NuxtLink>
						<div class="pd-date"><BaseUtilsFormatDate :date="item.datetime_published" format="DD. MMMM YYYY." /></div>
						<BaseFeedbackCommentsWidget :data="item.feedback_comment_widget" v-slot="{counter}">
							<NuxtLink href="#comments" class="pd-info-link pd-info-link-comments">
								<BaseCmsLabel code="view_comments" /> <span class="green counter">({{ counter }})</span>
							</NuxtLink>
						</BaseFeedbackCommentsWidget>
						<NuxtLink href="#related" class="pd-info-link pd-info-link-products" v-if="items?.length">
							<BaseCmsLabel code="related_products" /> <span class="green counter">({{ items.length }})</span>
						</NuxtLink>
					</div>
					<div class="pd-info-feedback" v-if="mobileBreakpoint"></div>
					<div class="lists pd-desc">
						<BaseCmsShortcodeContent v-if="item.content" :content="item.content">
							<template #catalogProduct="{params}">
								<CatalogContentProducts :params="params" :item="item" />
							</template>
						</BaseCmsShortcodeContent>
						<ClientOnly>
							<BaseUiImages v-if="item?.images?.length > 1" :images="item.images" v-slot="{items: images}">
								<BaseUiSwiper class="pd-thumbs" v-if="images" :options="sliderOptions">
									<BaseUiSwiperSlide v-for="file in images.slice(1)" :key="file.id">
										<a class="fancybox" rel="gallery" :title="file.title + (file.description ? ' - ' + file.description : '')">
											<BaseUiImage loading="lazy" :data="file.file_thumbs?.['width740-height500']" default="/images/no-image-500.jpg" :title="item.title" :alt="item.description ? item.description : item.title" />
										</a>
									</BaseUiSwiperSlide>
								</BaseUiSwiper>
							</BaseUiImages>
						</ClientOnly>
						<ul class="pd-documents" v-if="item.documents?.length">
							<li v-for="document in item.documents" :key="document.id">
								<a :href="document.url" :title="document.title + (document.description ? ' - ' + document.description : '')" target="_blank">{{ document.title || document.url }}</a>
							</li>
						</ul>
						<PublishTags v-if="item?.seo_keywords_tags" :itemSeoKeywordTags="item.seo_keywords_tags" />
						<div class="pd-footer">
							<CmsShare />
							<div class="pd-author" v-if="item.author">
								<div class="pd-author-name">
									<BaseCmsLabel tag="strong" code="author" />:
									{{ item.author }}
								</div>
								<NuxtLink class="pd-all-author-posts" :to="item.author_url" v-if="item.author_url"><BaseCmsLabel code="all_author_posts" /></NuxtLink>
							</div>
						</div>
					</div>

					<div class="comments pd-comments" id="comments" v-if="item.feedback_comment_widget">
						<ClientOnly>
							<FeedbackCommentsForm :item="item" mode="pd" />
						</ClientOnly>
					</div>

					<div class="pd-related-products" id="related" v-if="items?.length">
						<div class="subtitle related-title"><BaseCmsLabel code="related_products" /></div>
						<ClientOnly>
							<div class="related-items">
								<!-- FIXME pogledati koji sve parametri se trebaju proslijediti na indexEntry
									 GITHUB: https://github.com/markerhr/tvornicazdravehrane.com/blob/master/application/views/publish/detail.php#L132
								 -->
								<CatalogIndexEntry v-for="item in items" :key="item.id" :item="item" mode="list" list="Blog" />
							</div>
							<div class="pd-related-btns">
								<div class="related-products-add-message product_message_list"></div>
								<BaseWebshopAddToCart :data="addToCartRelatedProducts" v-slot="{onAddToCart, loading}">
									<button :class="['btn btn-orange btn-add-all', {'loading': loading}]" @click="onAddToCart">
										<UiLoader v-if="loading" />
										<BaseCmsLabel code="all_to_cart" tag="span" />
									</button>
								</BaseWebshopAddToCart>
							</div>
						</ClientOnly>
					</div>
				</div>
			</div>
		</BaseCatalogProductsWidget>

		<BasePublishPostsWidget :fetch="{related_item_id: item.id, category_code: item.category_code, related_code: 'related', limit: 5, extra_fields: ['short_description']}" v-slot="{items: relatedArticles}">
			<BasePublishPostsWidget :fetch="{related_item_id: item.id, category_code: 'recipe', related_code: 'related', limit: 5, extra_fields: ['short_description']}" v-slot="{items: relatedRecipes}">
				<div v-if="relatedArticles?.length || relatedRecipes?.length" class="pd-related">
					<div class="pd-content-wrapper">
						<div class="pd-related-posts">
							<template v-if="relatedArticles?.length">
								<BaseCmsLabel class="subtitle related-title" code="publish_related" tag="div" />
								<PublishIndexEntry v-for="relatedArticle in relatedArticles" :key="relatedArticle.id" :item="relatedArticle" mode="smallPublish" />
							</template>
							<template v-if="relatedRecipes?.length">
								<BaseCmsLabel class="subtitle related-title" code="publish_related_recipes" tag="div" />
								<PublishIndexEntryRecipes v-for="relatedRecipe in relatedRecipes" :key="relatedRecipe.id" :item="relatedRecipe" mode="list" />
							</template>
						</div>
					</div>
				</div>
			</BasePublishPostsWidget>
		</BasePublishPostsWidget>
	</BasePublishDetail>
</template>

<script setup>
	const {formatDate} = useText();
	const {onMediaQuery, appendTo, prependTo, insertAfter, insertBefore} = useDom();
	const {mobileBreakpoint} = inject('rwd');
	const props = defineProps(['mode']);

	const relatedProducts = ref([]);
	const addToCartRelatedProducts = computed (() => {
		if(!relatedProducts.value.length) return [];
		return relatedProducts.value.map(item => {
			return{
				modalData: item,
				shopping_cart_code: item.shopping_cart_code,
				quantity: item.qty,
			};
		});
	});

	onMediaQuery({
		query: '(max-width: 900px)',
		enter: () => {
			insertAfter('.pd-header', '.header');
			insertBefore('.pd-info', '.pd-title');
			//before('.pd-desc', '<div class="pd-info-feedback"/>');
			//$('.pd-desc').before('<div class="pd-info-feedback"/>');
			appendTo('.pd-info-link-comments', '.pd-info-feedback');
			appendTo('.pd-info-link-products', '.pd-info-feedback');
			//$('.pd-info-link').appendTo('.pd-info-feedback');
		},
		leave() {
			/* appendTo('.pd-recipe-header-wrapper', '.header');
			prependTo('.pd-recipe-info', '.pd-recipe-cnt');
			insertAfter('.pd-recipe-sidebar', '.pd-recipe-main'); */
		},
	});

	const sliderOptions = {
		slidesPerView: 1,
		slidesPerGroup: 1,
		speed: 500,
		effect: 'fadeOut',
		fadeEffect: {
			crossFade: true,
		},
		loop: true,
		autoplay: {
			delay: 5000,
			disableOnInteraction: true,
		},
		navigation: {
			enabled: true,
		},
	};
</script>
