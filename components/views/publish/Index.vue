<template>
	<BasePublishCategory v-slot="{item: category}" :seo="true">
		<h1>{{ category.seo_h1 }}</h1>

		<BasePublishPosts v-slot="{items: posts, nextPage, loadMore, loading}">
			<div class="p-items" v-if="posts">
				<ul>
					<li v-for="post in posts" :key="post.id">
						<NuxtLink :to="post.url_without_domain">
							{{ post.title }}
						</NuxtLink>
					</li>
				</ul>
			</div>
			<div v-else class="p-empty"><BaseCmsLabel code="no_publish" default="Nema članaka" /></div>

			<div class="load-more-container" v-if="nextPage" data-posts-scroll-trigger>
				<button class="btn btn-white btn-medium load-more btn-load-more" :class="{'loading': loading}" @click="loadMore"><BaseCmsLabel code="load_more_publish" /></button>
			</div>
			<BaseUiPagination class="pagination" />
		</BasePublishPosts>
	</BasePublishCategory>
</template>
