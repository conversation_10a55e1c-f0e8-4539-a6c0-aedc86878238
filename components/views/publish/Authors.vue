<template>
	<BaseCmsPage v-slot="{page}">
		<CmsTwoColumns>
			<template #main>
				<div class="fg1 lists main-content">
					<CmsBreadcrumbs :items="page?.breadcrumbs" />
					<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
					<BasePublishAuthors v-slot="{items}">
						<ul v-if="items?.length">
							<li v-for="item in items" :key="item.id">
								<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
							</li>
						</ul>
					</BasePublishAuthors>
				</div>
			</template>
		</CmsTwoColumns>
	</BaseCmsPage>
</template>
