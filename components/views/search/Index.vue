<template>
	<Body class="page-search white-bg" />
	<BaseCmsPage>
		<div class="search-body">
			<BaseSearchResults :search-modules="[{module: 'catalog'}, {module: 'publish|01'}, {module: 'publish|02'}, {module: 'cms'}]" v-slot="{items, searchContent, searchTerm}">
				<div class="search-container">
					<template v-if="searchContent == 'cms' && items?.cms">
						<div class="s-cms-wrapper s-wrapper">
							<div class="search-cnt">
								<BaseCmsPage :fetch="{mode: 'search', 'search_q': searchTerm}" v-slot="{page}" :seo="false">
									<article class="s-item" v-for="item in page.items" :key="item.id">
										<h2 class="s-item-title">
											<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
										</h2>
										<div class="s-item-content" v-if="item?.content" v-html="limitWords(stripHtml(item.content), 50)" />
									</article>
								</BaseCmsPage>
							</div>
						</div>
					</template>
				</div>
			</BaseSearchResults>
		</div>
	</BaseCmsPage>
</template>

<script setup>
	const {stripHtml, limitWords} = useText();
</script>
