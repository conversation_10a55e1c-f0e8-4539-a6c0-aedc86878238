<template>
	<BaseCmsPage>
		<BaseSearchResults :search-modules="[{module: 'catalog'}, {module: 'publish'}, {module: 'cms'}]" v-slot="{items, searchContent, searchTerm}">
			<BaseSearchNavigation />

			<template v-if="searchContent == 'publish.01'">
				<div class="search-publish" v-if="items">
					<BasePublishPosts :fetch="{category_position: '01'}" v-slot="{items, nextPage, loading, loadMore}">
						{{ items }}
						<div class="load-more-container" v-if="nextPage" data-posts-scroll-trigger>
							<a href="javascript:void(0);" class="btn btn-white btn-medium load-more btn-load-more" :class="{'loading': loading}" @click="loadMore">{{ labels.get('load_more_publish') }}</a>
						</div>
						<BaseUiPagination class="pagination" />
					</BasePublishPosts>
				</div>
			</template>

			<template v-if="searchContent == 'cms' && items?.cms">
				<BaseCmsPage :fetch="{mode: 'search', 'search_q': searchTerm}" v-slot="{page}" :seo="false">
					<div class="search-cms" v-if="page?.items">
						<article class="s-item" v-for="item in page.items" :key="item.id">
							<h2 class="s-item-title">
								<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
							</h2>
							<div v-if="item?.content" v-html="limitWords(stripHtml(item.content), 50)" />
						</article>
					</div>
				</BaseCmsPage>
			</template>
		</BaseSearchResults>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
	const {stripHtml, limitWords} = useText();
</script>
