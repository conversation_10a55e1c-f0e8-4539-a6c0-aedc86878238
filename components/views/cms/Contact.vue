<template>
	<Body class="page-contact page-cms-kontakt" />
	<BaseCmsPage v-slot="{page}">
		<CmsTwoColumns>
			<template #main>
				<div class="fg1 lists main-content">
					<CmsBreadcrumbs :items="page?.breadcrumbs" />
					<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
					<div class="df contact-row">
						<div class="contact-col contact-col1">
							<div v-if="page?.content" v-html="page.content" v-interpolation />
						</div>
						<div class="contact-col contact-col2">
							<div class="support support-contact">
								<BaseCmsLabel code="footer_user_support" tag="p" class="support-title support-title-contact" />
								<BaseCmsLabel code="support" tag="div" />
							</div>
							<BaseCmsLabel code="contact_social" tag="div" class="social social-contact" />
							<BaseCmsLabel code="job_applications" tag="div" class="contact-links contact-links-job" />
							<BaseCmsLabel code="wholesale" tag="div" class="contact-links contact-links-wholesale" />
							<BaseUtilsAppUrls v-slot="{items}">
								<NuxtLink :to="items.location" class="btn btn-locations">
									<BaseCmsLabel code="locations_info" tag="span" />
								</NuxtLink>
							</BaseUtilsAppUrls>
						</div>
					</div>
				</div>
			</template>
			<template #sidebar>
				<CmsSidebar :support="false" />
			</template>
			<template #afterWrapper>
				<BaseLocationPoints v-slot="{items}">
					<CmsMap :items="items" />
				</BaseLocationPoints>
			</template>
		</CmsTwoColumns>
	</BaseCmsPage>
</template>

<script setup>
	const {onMediaQuery, insertAfter} = useDom();
	onMediaQuery({
		query: '(max-width: 1300px)',
		enter() {
			insertAfter('.page-contact .map', '.contact-row');
		},
		leave() {
			insertAfter('.page-contact .map', '.main-wrapper');
		},
	});
</script>
