<template>
	<Body class="page-homepage" />
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<BaseCatalogLists :fetch="{code: ['most_buy', 'best_buy', 'new'], limit: 1, response_fields: ['id','code','title','url_without_domain']}" v-slot="{items: listItems}">
				<BaseCatalogProductsWidget :fetch="{list_code: ['most_buy', 'best_buy', 'new'], limit: 10}" v-slot="{items}">
					<div class="cw" v-if="items?.most_buy?.length || items?.best_buy?.length || items?.new?.length">
						<div class="wrapper">
							<div class="m-tabs cw-tabs">
								<ul class="tabs">
									<template v-for="(listItem,index) in listItems" :key="listItem">
										<li v-if="items?.[listItem.code]?.length" :class="{'active': activeTabIndex == (index+1)}">
											<span @click="activeTab((index+1))">{{listItem.title}}</span>
										</li>
									</template>
								</ul>
								<div class="tabs-content">
									<template v-for="(listItem,index) in listItems" :key="listItem">
										<template v-if="items?.[listItem.code]?.length">
											<!--
											<?php $ga4_data = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $most_buy_items, 'item_list_name' => $most_buy['title'], 'item_list_id' => 'most_buy']); ?>
											<div class="tab" id="tab1" data-ga4_events_info='<?php echo $ga4_data; ?>'>
											-->
											<div class="tab" :id="'tab'+(index+1)" :class="{'active': (index+1) == activeTabIndex}">
												<div class="tab-toggle">{{listItem.title}}</div>
												<!--<div class="tab-content" data-tracking_gtm_tabs="1">-->
												<div class="tab-content">
													<div class="c-items cw-items blazy-container">
														<template v-for="item in items?.[listItem.code]" :key="item.id">
															<CatalogIndexEntry :item="item" :list="listItem.title" :idtemListId="listItem.code" />
														</template>
													</div>

													<div class="tab-btns">
														<NuxtLink :to="listItem.url_without_domain" class="btn"><BaseCmsLabel code="show_all" tag="span" /></NuxtLink>
													</div>
												</div>
											</div>
										</template>
									</template>
								</div>
							</div>
						</div>
					</div>
				</BaseCatalogProductsWidget>
			</BaseCatalogLists>
		</ClientOnly>

		<CatalogManufacturers mode="homepage" extraClass="homepage" />

		<LazyPublishFeatured />

		<LazyCmsTestimonials />

		<LazyPublishRecipesFeatured />

		<PublishInstashopWidget />
	</BaseCmsPage>
</template>

<script setup>
	const {appendTo,onMediaQuery} = useDom();
	function listTitle(listItems, listCode) {
		return listItems?.find(item => item.code == listCode)?.title || '';
	}

	const activeTabIndex = ref(1);
	function activeTab(index){
		activeTabIndex.value = index;
	}

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 900px)',
		timeout: 600,
		enter: () => {
			appendTo('.sw', '.sw-placeholder');
		},
		leave: () => {
			window.location.reload();
		},
	});
</script>
