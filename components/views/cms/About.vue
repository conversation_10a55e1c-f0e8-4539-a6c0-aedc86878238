<template>
	<Body class="page-about" />
	<BaseCmsPage v-slot="{page}">
		<CmsTwoColumns>
			<template #main>
				<div class="fg1 main-content about-content">
					<div class="about-header">
						<h1 v-if="page?.seo_h1" class="about-title">{{ page.seo_h1 }}</h1>
						<BaseCmsLabel code="slogan" tag="p" class="about-slogan" /> 
					</div>
					<div v-if="page?.content" v-html="page.content" v-interpolation />
				</div>
			</template>

			<template #sidebar>
				<BaseCmsRotator :fetch="{code: 'about', limit: 10, response_fields: ['id','image_upload_path','image_thumbs','url_without_domain']}" v-slot="{items}">
					<template v-if="items?.length">
						<div class="sidebar">
							<BaseUiSwiper class="about-images slick-arrow2" :options="{
								slidesPerView: 1,
								slidesPerGroup: 1,
								loop: true,
								effect: 'fade',
								fadeEffect: {
									crossFade: true
								},
								pagination: {
									enabled: true,
									clickable: true,
								},
							}">
								<BaseUiSwiperSlide v-for="item in items" :key="item.id">
									<component :is="item.link ? NuxtLink: 'div'" :to="item.link ? item.url_without_domain : undefined" class="about-image">
										<BaseUiImage :data="item.image_thumbs?.['width700-height770-crop1']" default="/images/no-image-250.jpg" loading="lazy" />
									</component>
								</BaseUiSwiperSlide>
							</BaseUiSwiper>
						</div>
					</template>
				</BaseCmsRotator>
			</template>
		</CmsTwoColumns>

		<div class="about-fact">
			<BaseCmsLabel code="about_fact"  tag="span" />
		</div>
	</BaseCmsPage>
</template>