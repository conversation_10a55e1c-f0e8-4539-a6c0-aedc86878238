<template>
	<Teleport to="body">
		<ClientOnly>
			<div class="base-under-development" v-if="!isStaff">
				<Body class="under-development" />
				<Link href="//fonts.googleapis.com/css?family=Rubik:300,400,500,700&amp;subset=latin-ext" rel="stylesheet" />
				<div class="base-under-development-content">
					<template v-if="labels.get('under_development_content')">
						<BaseCmsLabel code="under_development_content" tag="div" />
					</template>
					<template v-else>
						<h1>Web stranice su u fazi izrade</h1>
						<p class="base-under-development-extra"><strong>Marker d.o.o.</strong></p>
						<p>
							Varaždinska ulica br. 8, II. odvojak - Jalkovec<br />
							Tel: +385 42 421 700<br />
							E-mail: <a href="mailto:<EMAIL>"><EMAIL></a><br />
							Web: <a href="https://www.marker.hr" target="_blank">www.marker.hr</a>
						</p>

						<a href="https://www.marker.hr" target="_blank">
							<svg width="200" height="49" viewBox="0 0 200 49">
								<g class="logo-letters">
									<path
										d="M83.7 20L81.9 24.7 78.9 32.5C78.4 33.7 78.1 34 77 34L75.4 34C74.3 34 74 33.7 73.5 32.5L70.6 25.1 68.7 20.4 68.5 20.4 68.5 36.1C68.5 36.8 68.3 36.9 67.8 36.9L65.3 36.9C64.8 36.9 64.5 36.8 64.5 36.1L64.5 14.5C64.5 13 65 12.3 66.5 12.3L67.9 12.3C69.1 12.3 69.5 12.6 70 13.9L74 24.2 76.1 29.6 76.3 29.6 78.4 24.1 82.4 13.9C82.9 12.6 83.4 12.3 84.6 12.3L85.8 12.3C87.3 12.3 87.8 13 87.8 14.4L87.8 36.1C87.8 36.8 87.6 36.9 87 36.9L84.6 36.9C84.1 36.9 83.9 36.8 83.9 36.1L83.9 20 83.7 20Z"></path>
									<path
										d="M105.1 27.9L103.3 22 101.9 17 101.7 17 100.3 21.9 98.6 27.9 105.1 27.9ZM97.6 31.5L96.1 36.3C95.9 36.7 95.7 36.9 95.2 36.9L92.1 36.9C91.6 36.9 91.5 36.5 91.6 36.1L98.9 14C99.4 12.6 99.8 12.3 101.1 12.3L102.5 12.3C103.9 12.3 104.4 12.6 104.8 14L112.1 36.1C112.3 36.5 112.2 36.9 111.7 36.9L108.6 36.9C108.1 36.9 107.8 36.8 107.6 36.3L106.2 31.5 97.6 31.5Z"></path>
									<path
										d="M125.4 16.1L120.1 16.1 120.1 23.1 125.2 23.1C127.9 23.1 129.1 21.5 129.1 19.6 129.1 18.2 128.4 16.1 125.4 16.1M133.9 36.9L130.9 36.9C130.5 36.9 130.1 36.8 129.9 36.3L128.3 29.8C127.6 27.4 126.5 26.7 124.1 26.7L120.1 26.7 120.1 36.1C120.1 36.8 120 36.9 119.3 36.9L116.7 36.9C116.2 36.9 115.9 36.7 115.9 36.1L115.9 27.4 115.9 14.3C115.9 12.9 116.4 12.3 117.8 12.3L125.8 12.3C130.3 12.3 133.3 15.2 133.3 19.4 133.3 22.1 131.9 24.3 129.8 25.1L129.8 25.3C131.4 26 132 27.2 132.5 28.9L134.3 36.1C134.4 36.5 134.3 36.9 133.9 36.9"></path>
									<path
										d="M157.8 36.1C158.1 36.6 157.9 36.9 157.4 36.9L154.4 36.9C153.6 36.9 153.3 36.9 152.9 36.1L146.9 24.7 142.6 30.2 142.6 36.1C142.6 36.8 142.4 36.9 141.8 36.9L139.3 36.9C138.7 36.9 138.5 36.7 138.5 36.1L138.5 13.1C138.5 12.5 138.7 12.4 139.3 12.4L141.8 12.4C142.4 12.4 142.6 12.4 142.6 13.1L142.6 23.7 142.7 23.8 146.1 19.4 151.2 12.8C151.5 12.5 151.9 12.4 152.5 12.4L156 12.4C156.4 12.4 156.6 12.6 156.3 13L149.7 21.2 157.8 36.1Z"></path>
									<path
										d="M176.3 36.9L166.7 36.9C161.9 36.9 160.7 33.8 160.7 30.6L160.7 18.6C160.7 15.4 162.3 12.3 166.7 12.3L176.1 12.3C176.7 12.3 176.8 12.6 176.8 13.1L176.8 15.3C176.8 15.9 176.7 16.1 176.1 16.1L167.1 16.1C165.4 16.1 164.8 16.9 164.8 18.4L164.8 30.8C164.8 32.3 165.5 33.1 167.1 33.1L176.3 33.1C177 33.1 177 33.2 177 33.9L177 36.1C177 36.6 176.9 36.9 176.3 36.9"></path>
									<path
										d="M190.9 16.1L185.7 16.1 185.7 23.1 190.7 23.1C193.4 23.1 194.6 21.5 194.6 19.6 194.6 18.2 193.9 16.1 190.9 16.1M199.4 36.9L196.4 36.9C196 36.9 195.6 36.8 195.4 36.3L193.8 29.8C193.1 27.4 192 26.7 189.6 26.7L185.6 26.7 185.6 36.1C185.6 36.8 185.5 36.9 184.9 36.9L182.2 36.9C181.7 36.9 181.4 36.7 181.4 36.1L181.4 14.3C181.4 12.9 181.9 12.3 183.3 12.3L191.3 12.3C195.8 12.3 198.8 15.2 198.8 19.4 198.8 22.1 197.4 24.3 195.3 25.1L195.3 25.3C196.9 26 197.5 27.2 198 28.9L199.8 36.1C199.9 36.5 199.8 36.9 199.4 36.9"></path>
									<path
										class="logo-letter-dot"
										fill="#C52329"
										d="M172 22.7L170.3 22.7 170 22.7 169.2 22.7C168.7 22.7 168.6 22.8 168.6 23.4L168.6 25.8C168.6 26.3 168.7 26.5 169.2 26.5L170 26.5 170.3 26.5 172 26.5C172.5 26.5 172.6 26.3 172.6 25.8L172.6 23.4C172.6 22.8 172.5 22.7 172 22.7"></path>
								</g>
								<g class="logo-circle" fill="#C52329">
									<path d="M29.2 18.2L25.6 34C25.5 34.4 25.2 35.6 23.9 35.6L23.1 35.6C21.8 35.6 21.6 34.4 21.5 34L17.8 21.4 15.8 27.5 0.2 27.5C1.6 39.6 11.5 49 23.5 49 35.5 49 45.4 39.6 46.8 27.5L31.6 27.5 29.2 18.2Z"></path>
									<path
										d="M15.7 18.8C15.9 17.9 16.2 17.1 17.4 17.1L18.3 17.1C19.4 17.1 19.7 17.9 20 18.8L23.5 30.8 27.1 15.4C27.3 14.5 27.6 13.6 28.7 13.6L29.6 13.6C30.8 13.6 31.1 14.5 31.3 15.4L33.7 24.5 47 24.5C46.9 11.1 36.4 0.3 23.5 0.3 10.6 0.3 0.1 11.1 0 24.5L13.8 24.5 15.7 18.8Z"></path>
								</g>
							</svg>
						</a>
					</template>
				</div>
			</div>
			<template #fallback>
				<BaseThemeUiLoading mode="fullpage" />
			</template>
		</ClientOnly>
	</Teleport>
</template>

<script setup>
	const labels = useLabels();
	const {getUser} = useAuth();
	const isStaff = computed(() => {
		return getUser()?.staff || getUser()?.developer || getUser()?.superuser;
	});
</script>

<style>
	body.under-development {
		overflow: hidden;
	}
</style>

<style scoped lang="less">
	* {
		font-family: 'Rubik', sans-serif !important;
		line-height: 1.5;
	}
	.base-under-development {
		background: #c32228 url('https://marker.hr/shared/zastitna/zastitna.jpg') no-repeat left top;
		background-position: -140px -70px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #353537;
		font-size: 15px;
		font-weight: normal;
		text-align: center;
		position: fixed;
		inset: 0;
		z-index: 9999999999;
	}
	.base-under-development-content {
		width: 90%;
		max-width: 800px;
		padding: clamp(35px, 10vw, 55px) clamp(40px, 10vw, 60px);
		margin: auto;
		background: #fff;
		box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.5);
		max-height: 90dvh;
		overflow: auto;
		h1 {
			font-size: clamp(30px, 5vw, 40px);
			text-wrap: balance;
			line-height: 1.3;
			font-weight: bold;
			padding: 0 0 20px 0;
			margin: 0;
			color: #353537;
		}
		a {
			color: #c52329;
		}
		p {
			padding-bottom: 10px;
		}
		svg {
			width: 100px;
			height: auto;
			margin-top: 10px;
		}
	}
	.base-under-development-extra {
		font-size: 18px;
	}
</style>
