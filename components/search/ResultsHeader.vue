<template>
	<div class="wrapper s-header-wrapper">
		<h1 class="s-header-title s-h1">
			<span class="s-headline"><BaseCmsLabel code="search_headline" /></span>
			<span class="s-keyword">{{searchTerm}}</span>
		</h1>
		<BaseSearchNavigation v-slot="{items}">
			<ul class="s-nav">
				<template v-for="(item, key) in items" :key="key">
					<li :class="{'selected': item.url == currentUrl, 'disabled': !item.total}">
						<BaseUiLink :to="item.url">
							<span>
								<template v-if="item.id == 'publish.02'">
									<BaseCmsLabel code="search_recipes" />
								</template>
								<template v-else>
									{{ item.title }}
								</template>
								<span class="s-counter">({{ item.total }})</span>
							</span>
						</BaseUiLink>
					</li>
				</template>
			</ul>
		</BaseSearchNavigation>

		<!--
		<BaseThemeSearchNavigation>
			<template #title="{item}">
				<template v-if="item.id == 'publish.02'">
					<BaseCmsLabel code="search_recipes" />
				</template>
				<template v-else>
					{{item.title}}
				</template>
				<span class="s-counter">({{item.total}})</span>
			</template>
		</BaseThemeSearchNavigation>
		-->
	</div>
</template>

<script setup>
	const route = useRoute();
	const props = defineProps(['searchTerm']);

	const currentUrl = computed(() => {
		const controller = route.meta.controller
		const query = route.query.search_q
		if(controller == 'cms'){
			return route.fullPath
		}else{
	  		return `${route.path}?search_q=${encodeURIComponent(query || '')}`
		}
	})
</script>
