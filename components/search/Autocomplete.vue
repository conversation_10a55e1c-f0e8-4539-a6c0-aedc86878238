<template>
	<div class="ac autocomplete-container" v-if="totalSearchResults || loading">
		<div class="ac-loading" v-if="loading"><UiLoader /></div>
		<div class="ac-wrapper autocomplete-wrapper" v-if="searchResults?.catalogproduct?.length">
			<div class="ac-menu ac-products autocomplete-col autocomplete-col1 catalogproduct" v-if="searchResults?.catalogproduct?.length">
				<ul class="ui-autocomplete ui-autocomplete1" data-autocomplete_contenttype="catalogproduct">
					<li v-for="item in searchResults.catalogproduct" :key="item.id">
						<NuxtLink :to="item.url_without_domain" class="ac-item" :class="{'active': item.index == selectedIndex}">
							<div class="ac-item-img search-image">
								<BaseUiImage loading="lazy" :src="item.image_upload_path" width="70" height="70" />
							</div>
							<div class="ac-item-cnt search-col">
								<div class="ac-item-category search-category">{{ item.category_title }}</div>
								<div class="ac-item-title search-title" v-html="highlightText(stripHtml(item.title), searchTerm)"></div>
								<!--div class="ac-item-title search-title">{{ item.title }}</div-->
								<div class="ac-item-brand">{{ item.manufacturer_title }}</div>
								<div class="ac-item-price search-price">
									<template v-if="(item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom)">
										<div class="ac-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
										<div class="ac-current-price ac-discount-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
									</template>
									<template v-else>
										<div class="ac-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
									</template>
								</div>
							</div>
						</NuxtLink>
					</li>

					<li class="ac-showall ui-menu-item autocomplete-showall" v-if="searchResults.catalogproduct_show_all.total > 5">
						<a class="ac-showall-btn" @click="onSubmit">
							<span>{{ labels.get('show_all') }}</span>
						</a>
					</li>
				</ul>
			</div>
			<div class="autocomplete-col autocomplete-col2 catalogcategory">
				<div class="ac-menu ac-categories autocomplete-col-item" v-if="searchResults?.catalogcategory?.length">
					<BaseCmsLabel code="autocomplete_categories" tag="div" class="autocomplete-title" />
					<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all">
						<li v-for="item in searchResults.catalogcategory" :key="item.id">
							<NuxtLink :to="item.url_without_domain" class="ac-menu-item ac-menu-cube" :class="{'active': item.index == selectedIndex}">
								<span v-html="highlightText(stripHtml(item.title), searchTerm)"></span>
							</NuxtLink>
						</li>
					</ul>
				</div>
				<div class="autocomplete-col-item publish.02" v-if="searchResults?.['publish.02']?.length">
					<BaseCmsLabel code="autocomplete_recipes" tag="div" class="autocomplete-title" />
					<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all">
						<NuxtLink v-for="item in searchResults['publish.02']" :key="item.id" :to="item.url_without_domain" class="ac-menu-item" :class="{'active': item.index == selectedIndex}">
							<span v-html="highlightText(stripHtml(item.title), searchTerm)"></span>
						</NuxtLink>
					</ul>
				</div>
				<div class="autocomplete-col-item publish.01" v-if="searchResults?.['publish.01']?.length">
					<BaseCmsLabel code="autocomplete_publishes" tag="div" class="autocomplete-title" />
					<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all">
						<NuxtLink v-for="item in searchResults?.['publish.01']" :key="item.id" :to="item.url_without_domain" class="ac-menu-item" :class="{'active': item.index == selectedIndex}">
							<span v-html="highlightText(stripHtml(item.title), searchTerm)"></span>
						</NuxtLink>
					</ul>
				</div>
				<div class="autocomplete-col-item catalogmanufacturer" v-if="searchResults?.catalogmanufacturer?.length">
					<BaseCmsLabel code="autocomplete_brands" tag="div" class="autocomplete-title" />
					<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all">
						<li v-for="item in searchResults.catalogmanufacturer" :key="item.id">
							<NuxtLink :to="item.url_without_domain" class="ac-menu-item ac-menu-cube" :class="{'active': item.index == selectedIndex}">
								<span v-html="highlightText(stripHtml(item.title), searchTerm)"></span>
							</NuxtLink>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const {stripHtml, limitWords, highlightText} = useText();
	const labels = useLabels();
		const props = defineProps(['searchResults', 'totalSearchResults', 'loading', 'selectedIndex', 'onReset', 'searchTerm']);
</script>

<style lang="less" scoped>
	.ac-loading{display: flex; justify-content: center;}
	.ac-item{
		display: flex; padding: 6px 20px 7px; text-decoration: none; .transition(all);
		&:hover{text-decoration: none; background: @borderColor*1.04; color: @textColor;}
	}
</style>
