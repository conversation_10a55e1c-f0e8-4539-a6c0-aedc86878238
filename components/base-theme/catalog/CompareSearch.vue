<template>
	<BaseCatalogCompareSearch v-slot="{items, loading, onAddToCompare, handleInput, onBlur, selectedIndex}" :thumb-preset="props.thumbPreset" :item="props.item" v-model="searchTerm">
		<input type="search" autocomplete="off" :class="[props.class, {'loading': loading}]" :id="id" :placeholder="placeholder" @keyup="handleInput" @blur="onBlur" v-model="searchTerm" />
		<span class="field-compare-autocomplete" :class="{'loading': loading}" v-if="items?.length || loading">
			<span class="field-autocomplete-container field-compare-autocomplete-container">
				<ul class="ui-autocomplete compare-ui-autocomplete">
					<li :class="['ui-menu-item', {'active': index == selectedIndex}]" v-for="(item, index) in items" :data-compare-search-index="index" :key="item.id" @click="onAddToCompare(item)">
						<span class="image" v-if="props.imageField"><BaseUiImage loading="lazy" :data="getImageData(item)" :default="props.noImage" /></span>
						<span class="content">
							<span class="title">{{ item.title }}</span>
							<span class="price"><BaseUtilsFormatCurrency :price="item.price" /></span>
						</span>
					</li>
				</ul>
			</span>
		</span>
	</BaseCatalogCompareSearch>
</template>

<script setup>
	const props = defineProps({
		item: Object,
		placeholder: String,
		thumbPreset: String,
		imageField: String,
		noImage: {
			type: String,
			default: '/images/no-image-50.jpg',
		},
		class: String,
		id: String,
	});
	const searchTerm = ref('');

	function getImageData(item) {
		if (!item.main_image_upload_path) return null;
		const imageData = getNestedValue(item, props.imageField);
		return imageData ? imageData : null;
	}

	function getNestedValue(obj, path) {
		if (!obj && !path) return;
		return (
			path
				.split(/[\.\[\]\'\"]/)
				.filter(Boolean)
				.reduce((acc, key) => acc && acc[key], obj) || null
		);
	}
</script>

<style lang="less" scoped>
	* {
		--acBorderColor: #ccc;
		--acFontSize: 14px;
		--acLineHeight: 1.5;
		--acMaxHeight: 280px;
		--acBackgroundColor: #fff;
		--acItemPadding: 5px 20px;
		--acHoverBackgroundColor: #eaeaea;
	}

	.field-compare-autocomplete {
		position: relative;
		display: block;
		font-size: var(--acFontSize);
		line-height: var(--acLineHeight);
	}
	.field-autocomplete-container {
		position: absolute;
		left: 0;
		right: 0;
		top: 100%;
		background: var(--acBackgroundColor);
		z-index: 100;
		max-height: var(--acMaxHeight);
		overflow: auto;
		border: 1px solid var(--acBorderColor);
		display: block;
	}
	ul {
		list-style: none;
		padding: 0 !important;
		margin: 0 !important;
	}
	li {
		padding: var(--acItemPadding) !important;
		cursor: pointer;
		&:before {
			display: none !important;
		}
		&:hover,
		&.active {
			background: var(--acHoverBackgroundColor);
		}
	}
</style>
