<template>
	<BaseSearchForm :fetch="searchFormConfig" v-slot="{searchResults, updateValue, totalSearchResults, searchTerm, loading, handleInput, onReset, onBlur, selectedIndex, onSubmit}">
		<div class="sw-form" :class="{'loading': loading}">
			<input class="sw-input" name="search_q" id="search_q" type="text" :value="searchTerm" @input="updateValue" autocomplete="off" @blur="onBlur" @keyup="handleInput" />
			<div class="sw-clear" v-show="searchTerm" @click="onReset" />
			<button @click="onSubmit" class="sw-btn" type="submit" />
		</div>
		<ClientOnly>
			<div class="ac" :class="{'loading': loading}" v-if="searchResults || loading">
				<div class="ac-loading" v-if="loading" />
				<div class="ac-wrapper" v-if="searchResults?.catalogproduct?.length">
					<div class="ac-item" :class="{'active': item.index == selectedIndex}">
						<NuxtLink v-for="item in searchResults.catalogproduct" :key="item.id" :to="item.url_without_domain" class="ac-item-title">
							<div class="ac-item-cnt">
								<div class="ac-item-title">{{ item.title }}</div>
								<div class="ac-item-price">
									<template v-if="item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom">
										<div class="ac-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
										<div class="ac-current-price ac-discount-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
									</template>
									<template v-else>
										<div class="ac-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
									</template>
								</div>
							</div>
						</NuxtLink>
						<div class="ac-item-showall" v-if="searchResults?.catalogproduct_show_all?.show">
							<NuxtLink class="ac-showall-btn" :to="searchResults?.catalogproduct_show_all.url_without_domain">
								<BaseCmsLabel code="autocomplete_show_all" /> <span>({{ searchResults.catalogproduct_show_all.total }})</span>
							</NuxtLink>
						</div>
					</div>
				</div>
			</div>
		</ClientOnly>
	</BaseSearchForm>
</template>

<script setup>
	const searchFormConfig = {
		'allow_models': ['catalogcategory', 'catalogmanufacturer', 'catalogproduct', 'publish', 'cms'],
		'result_per_page': {
			'catalogcategory': 9,
			'catalogmanufacturer': 12,
			'_default': 4,
		},
		'result_image': '80x80_r',
	};
</script>
