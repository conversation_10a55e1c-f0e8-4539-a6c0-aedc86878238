<template>
	<BaseSearchNavigation v-slot="{items, selected, loading}">
		<ul class="s-nav">
			<li :class="{'selected': item.id == selected, 'disabled': !item.total}" v-for="(item, key) in items" :key="key">
				<BaseUiLink :to="item.url">
					<slot name="title" :item="item" :loading="loading">
						<span>
							{{ item.title }} <span class="s-counter">({{ item.total }})</span>
						</span>
					</slot>
				</BaseUiLink>
			</li>
		</ul>
	</BaseSearchNavigation>
</template>

<style scoped>
	:deep(.disabled) {
		pointer-events: none;
	}
</style>
