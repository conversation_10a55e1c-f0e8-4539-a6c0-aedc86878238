<template>
	<div :id="props.id">
		<slot />
	</div>
</template>

<style lang="less">
	/*
	.pswp {
		--pswp-bg: #fff;
	}
	.pswp__bullets-indicator {
		position: absolute;
		width: 210px;
		padding: 20px 20px 20px 50px;
		background: #fff;
		top: 0;
		bottom: 0;
		left: 0;
		overflow: auto;
		display: flex;
		align-items: center;
		@media (max-width: 1450px) {
			width: 180px;
		}
		@media (max-width: 1200px) {
			width: 100%;
			top: auto;
			padding: 10px;
			display: block;
			background: none;
			bottom: 30px;
		}
	}
	.pswp__bullets-indicator-items {
		max-height: 100%;
		width: 100%;
		@media (max-width: 1200px) {
			display: flex;
			gap: 5px;
			justify-content: center;
			align-items: center;
		}
	}
	.pswp__bullet {
		width: 100%;
		border: 1px solid transparent;
		background: #fff;
		aspect-ratio: 1;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		.transition(border);
		@media (max-width: 1200px) {
			width: 80px;
		}
		img {
			display: block;
			width: auto;
			height: auto;
			max-width: 90%;
			max-height: 90%;
		}
	}
	.pswp__bullet--active {
		border: 1px solid @blue;
	}
	.pswp__button--arrow {
		svg {
			display: none;
		}
		&:before {
			.icon-arrow();
			display: block;
			font: 40px/1 @fonti;
			color: #000;
			@media (max-width: 1200px) {
				font-size: 30px;
			}
		}
		&:hover:before {
			color: @blue;
		}
	}
	.pswp__button--arrow--next {
		right: 15%;
		@media (max-width: 1200px) {
			right: 3%;
		}
		&:before {
			.scaleX(-1);
		}
	}
	.pswp__button--arrow--prev {
		left: 17%;
		@media (max-width: 1200px) {
			left: 3%;
		}
	}
	.pswp__top-bar {
		position: absolute;
		top: 5%;
		right: 3%;
		left: auto;
		width: 50px;
		height: auto;
		display: block;
		@media (max-width: 1200px) {
			top: 3%;
		}
		.pswp__button {
			width: 100%;
			margin: 0;
			height: 50px;
			width: 50px;
			border: 1px solid #e0e8ee;
			margin-top: -1px;
			background: #fff;
			opacity: 1;
			&:after {
				.pseudo(100%, 100%);
				top: 0;
				left: 0;
				display: flex;
				align-items: center;
				justify-content: center;
				.icon-x();
				font: 22px/1 @fonti;
				color: #000;
			}
			&.disabled {
				opacity: 0.5;
			}
		}
		.pswp__button--zoomin:after {
			.icon-zoom-in();
		}
		.pswp__button--zoomout:after {
			.icon-zoom-out();
		}
		.pswp__button--close {
			background: @red;
			border-color: @red;
			&:after {
				color: #fff;
			}
		}
		svg {
			display: none;
		}
	}
	.pswp__preloader {
		display: none;
	}
	.pswp__img {
		cursor: default !important;
	}
	*/
</style>

<script setup>
	// https://photoswipe.com/options/
	import PhotoSwipeLightbox from 'photoswipe/lightbox';
	import 'photoswipe/style.css';

	const config = useAppConfig();
	const props = defineProps({
		id: {
			type: String,
			required: true,
		},
		options: Object,
	});

	onMounted(() => {
		const lightbox = new PhotoSwipeLightbox({
			gallery: '#' + props.id,
			children: 'a',
			bgOpacity: 1,
			wheelToZoom: true,
			maxZoomLevel: 3,
			counter: false,
			arrowKeys: true,
			showHideAnimationType: 'fade',
			clickToCloseNonZoomable: false,
			imageClickAction: false,
			tapAction: false,
			pinchToClose: false,
			closeOnVerticalDrag: false,
			bgClickAction: false,
			zoom: false,
			padding: {top: 20, bottom: 20, left: 100, right: 100},
			...props.options,
			pswpModule: () => import('photoswipe'),
		});

		lightbox.on('uiRegister', function () {
			lightbox.pswp.ui.registerElement({
				name: 'bulletsIndicator',
				className: 'pswp__bullets-indicator',
				appendTo: 'wrapper',
				onInit: (el, pswp) => {
					const bullets = [];
					let bullet;
					let prevIndex = -1;
					const elItems = document.createElement('div');
					elItems.className = 'pswp__bullets-indicator-items';
					el.appendChild(elItems);
					const galleryThumbs = lightbox.options.dataSource.items;
					for (let i = 0; i < pswp.getNumItems(); i++) {
						bullet = document.createElement('div');
						bullet.innerHTML = '<img src="' + config.host + galleryThumbs[i].attributes['data-thumb'].value + '" alt="" />';
						bullet.className = 'pswp__bullet';
						bullet.onclick = e => {
							pswp.goTo(i);
						};
						elItems.appendChild(bullet);
						bullets.push(bullet);
					}

					// update selected bullet
					pswp.on('change', a => {
						if (prevIndex >= 0) {
							bullets[prevIndex].classList.remove('pswp__bullet--active');
						}
						bullets[pswp.currIndex].classList.add('pswp__bullet--active');
						prevIndex = pswp.currIndex;
					});

					// update zoom buttons based on current zoom level
					pswp.on('zoomPanUpdate', e => {
						const maxZoomLevel = pswp.currSlide.zoomLevels.max;
						const minZoomLevel = pswp.currSlide.zoomLevels.min;
						const currentZoomLevel = pswp.currSlide.currZoomLevel;
						const zoomInButton = pswp.element.querySelector('.pswp__button--zoomin');
						const zoomOutButton = pswp.element.querySelector('.pswp__button--zoomout');
						currentZoomLevel >= maxZoomLevel ? zoomInButton.classList.add('disabled') : zoomInButton.classList.remove('disabled');
						currentZoomLevel <= minZoomLevel ? zoomOutButton.classList.add('disabled') : zoomOutButton.classList.remove('disabled');
					});
				},
			});
			lightbox.pswp.ui.registerElement({
				name: 'zoomin',
				order: 20,
				isButton: true,
				html: '+',
				onClick: (event, el, pswp) => {
					const currentZoomLevel = pswp.currSlide.currZoomLevel;
					pswp.currSlide.zoomTo(currentZoomLevel * 1.5, {x: 400, y: 400}, 300, false);
				},
			});
			lightbox.pswp.ui.registerElement({
				name: 'zoomout',
				order: 20,
				isButton: true,
				html: '-',
				onClick: (event, el, pswp) => {
					const currentZoomLevel = pswp.currSlide.currZoomLevel;
					pswp.currSlide.zoomTo(currentZoomLevel / 1.5, {x: 400, y: 400}, 300, false);
				},
			});
		});

		lightbox.on('afterInit', () => {
			document.body.classList.add('modal-active', 'modal-photoswipe-open');
		});

		function stopWheelEvent(event) {
			event.stopPropagation();
		}

		let bulletsIndicator;
		lightbox.on('bindEvents', () => {
			bulletsIndicator = document.querySelector('.pswp__bullets-indicator');
			if (bulletsIndicator) {
				bulletsIndicator.addEventListener('wheel', stopWheelEvent, {passive: false});
			}
		});

		lightbox.on('close', () => {
			document.body.classList.remove('modal-active', 'modal-photoswipe-open');
			if (bulletsIndicator) {
				bulletsIndicator.removeEventListener('wheel', stopWheelEvent, {passive: false});
			}
		});

		lightbox.init();
	});
</script>
