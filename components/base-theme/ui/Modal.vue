<template>
	<div>
		<BaseUiModal v-bind="$attrs" v-slot="{onClose, active, item, loading}" @open="onOpen">
			<Transition :name="transition">
				<div class="base-modal" :class="[$attrs?.name, props.class, {'gallery': item?.items?.length}]" :style="style" v-if="active">
					<div class="base-modal-body">
						<div class="base-modal-toolbar" v-if="item?.items?.length">
							<div v-if="closable" class="base-modal-toolbar-item base-modal-toolbar-close" @click="onClose">
								<svg width="24px" height="24px" fill="#000000" viewBox="0 0 24 24" v-if="svgicons">
									<g transform="translate(-406.000000, -2828.000000)" fill-rule="nonzero">
										<g transform="translate(68.000000, 2805.000000)">
											<g transform="translate(350.000000, 35.000000) rotate(-270.000000) translate(-350.000000, -35.000000) translate(338.000000, 23.000000)">
												<path
													d="M-2.82746785,10.8569051 L26.8274678,10.8569051 C27.3797526,10.8569051 27.8274678,11.3046204 27.8274678,11.8569051 L27.8274678,12.1430949 C27.8274678,12.6953796 27.3797526,13.1430949 26.8274678,13.1430949 L-2.82746785,13.1430949 C-3.3797526,13.1430949 -3.82746785,12.6953796 -3.82746785,12.1430949 L-3.82746785,11.8569051 C-3.82746785,11.3046204 -3.3797526,10.8569051 -2.82746785,10.8569051 Z"
													transform="translate(12.000000, 12.000000) rotate(45.000000) translate(-12.000000, -12.000000) "></path>
												<rect transform="translate(12.000000, 12.000000) rotate(-45.000000) translate(-12.000000, -12.000000) " x="-3.82746785" y="10.8569051" width="31.6549357" height="2.2861898" rx="1"></rect>
											</g>
										</g>
									</g>
								</svg>
							</div>
							<div v-show="zoomBtns" class="base-modal-toolbar-item base-modal-toolbar-zoom-in" :class="{'disabled': disabledZoomIn}" @click="zoom('in')">
								<svg fill="#000000" width="21px" height="21px" viewBox="0 0 21 21" v-if="svgicons">
									<g transform="translate(-1860.000000, -99.000000)" fill-rule="nonzero">
										<g transform="translate(1840.000000, 20.000000)">
											<g transform="translate(0.000000, 59.000000)">
												<g transform="translate(20.000000, 20.000000)">
													<path
														d="M20.7596895,19.5993574 L14.8349004,13.6745684 C17.5343027,10.3902422 17.3506348,5.51475586 14.2831992,2.44736133 C11.0200371,-0.815800781 5.71052344,-0.815800781 2.44736133,2.44736133 C-0.815800781,5.71052344 -0.815800781,11.0200371 2.44736133,14.2831992 C5.51393555,17.3497734 10.3888066,17.5354512 13.6745684,14.8349004 L19.5993574,20.7596895 C19.9197715,21.0801035 20.4392344,21.0801035 20.7596484,20.7596895 C21.0801035,20.4392344 21.0801035,19.9197715 20.7596895,19.5993574 Z M13.1229082,13.1228672 C10.4995488,15.7462266 6.23105273,15.7461855 3.60769336,13.1228672 C0.984333984,10.4995078 0.984333984,6.23101172 3.60769336,3.60765234 C6.2309707,0.984416016 10.4994668,0.984210937 13.1229082,3.60765234 C15.7462676,6.23101172 15.7462676,10.4995078 13.1229082,13.1228672 Z"></path>
													<path
														d="M12.3824941,7.5447832 L9.18573633,7.5447832 L9.18573633,4.34802539 C9.18573633,3.89488477 8.81840039,3.52754883 8.36525977,3.52754883 C7.91211914,3.52754883 7.5447832,3.89488477 7.5447832,4.34802539 L7.5447832,7.5447832 L4.34802539,7.5447832 C3.89488477,7.5447832 3.52754883,7.91211914 3.52754883,8.36525977 C3.52754883,8.81840039 3.89488477,9.18573633 4.34802539,9.18573633 L7.5447832,9.18573633 L7.5447832,12.3824941 C7.5447832,12.8356348 7.91211914,13.2029707 8.36525977,13.2029707 C8.81840039,13.2029707 9.18573633,12.8356348 9.18573633,12.3824941 L9.18573633,9.18573633 L12.3824941,9.18573633 C12.8356348,9.18573633 13.2029707,8.81840039 13.2029707,8.36525977 C13.2029707,7.91211914 12.8356348,7.5447832 12.3824941,7.5447832 Z"></path>
												</g>
											</g>
										</g>
									</g>
								</svg>
							</div>
							<div v-show="zoomBtns" class="base-modal-toolbar-item base-modal-toolbar-zoom-out" :class="{'disabled': disabledZoomOut}" @click="zoom('out')">
								<svg fill="#000000" width="21px" height="21px" viewBox="0 0 21 21" v-if="svgicons">
									<g transform="translate(-1860.000000, -158.000000)" fill-rule="nonzero">
										<g transform="translate(1840.000000, 20.000000)">
											<g transform="translate(0.000000, 118.000000)">
												<g transform="translate(20.000000, 20.000000)">
													<path
														d="M20.7596895,19.5993574 L14.8349004,13.6745684 C17.5343027,10.3902422 17.3506348,5.51475586 14.2831992,2.44736133 C11.0200371,-0.815800781 5.71052344,-0.815800781 2.44736133,2.44736133 C-0.815800781,5.71052344 -0.815800781,11.0200371 2.44736133,14.2831992 C5.51393555,17.3497734 10.3888066,17.5354512 13.6745684,14.8349004 L19.5993574,20.7596895 C19.9197715,21.0801035 20.4392344,21.0801035 20.7596484,20.7596895 C21.0801035,20.4392344 21.0801035,19.9197715 20.7596895,19.5993574 Z M13.1229082,13.1228672 C10.4995488,15.7462266 6.23105273,15.7461855 3.60769336,13.1228672 C0.984333984,10.4995078 0.984333984,6.23101172 3.60769336,3.60765234 C6.2309707,0.984416016 10.4994668,0.984210937 13.1229082,3.60765234 C15.7462676,6.23101172 15.7462676,10.4995078 13.1229082,13.1228672 Z"></path>
													<path
														d="M12.3824941,7.5447832 L9.18573633,7.5447832 L7.5447832,7.5447832 L4.34802539,7.5447832 C3.89488477,7.5447832 3.52754883,7.91211914 3.52754883,8.36525977 C3.52754883,8.81840039 3.89488477,9.18573633 4.34802539,9.18573633 L7.5447832,9.18573633 L9.18573633,9.18573633 L12.3824941,9.18573633 C12.8356348,9.18573633 13.2029707,8.81840039 13.2029707,8.36525977 C13.2029707,7.91211914 12.8356348,7.5447832 12.3824941,7.5447832 Z"></path>
												</g>
											</g>
										</g>
									</g>
								</svg>
							</div>
						</div>

						<div class="base-modal-gallery" :class="{'zoom-toggle': zoomToggle}" v-if="item?.items?.length">
							<div class="base-modal-gallery-main" @wheel="onWheel">
								<BaseUiSwiper
									:options="{
										modules: [Zoom, Keyboard],
										lazyPreloaderClass: 'base-modal-gallery-loader',
										initialSlide: item?.startIndex || 0,
										zoom: {maxRatio: 3, toggle: false},
										effect: 'fade',
										fadeEffect: {crossFade: true},
										keyboard: true,
										on: {
											zoomChange: onZoom,
											slideChange: onSlideChange,
										},
									}"
									@init="setMainSwiper">
									<BaseUiSwiperSlide v-for="modalItem in item.items" :key="modalItem.id" @click="onClick">
										<template v-if="modalItem.kind == 'video'">
											<video controls :src="absolute(modalItem.url)" />
										</template>
										<template v-else>
											<div class="swiper-zoom-container">
												<BaseUiImage loading="lazy" :src="modalItem.url" />
											</div>
											<div class="base-modal-gallery-loader">
												<svg version="1.1" id="loader-1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="50px" height="50px" viewBox="0 0 50 50" style="enable-background: new 0 0 50 50" xml:space="preserve">
													<path fill="#cccccc" d="M43.935,25.145c0-10.318-8.364-18.683-18.683-18.683c-10.318,0-18.683,8.365-18.683,18.683h4.068c0-8.071,6.543-14.615,14.615-14.615c8.072,0,14.615,6.543,14.615,14.615H43.935z">
														<animateTransform attributeType="xml" attributeName="transform" type="rotate" from="0 25 25" to="360 25 25" dur="0.6s" repeatCount="indefinite"></animateTransform>
													</path>
												</svg>
											</div>
										</template>
										<div class="base-modal-gallery-title" v-if="modalItem?.title">
											<span>{{ modalItem.title }}</span>
										</div>
									</BaseUiSwiperSlide>
									<template #navigation>
										<div class="base-modal-gallery-nav" v-if="item.items?.length > 1">
											<div class="base-modal-gallery-nav-btn base-modal-gallery-nav-prev" :class="{'swiper-button-disabled': mainSwiper?.activeIndex == 0}" @click="mainSwiper.slidePrev()">
												<span>
													<svg width="22px" height="33px" viewBox="0 0 22 33" fill="#FFFFFF" fill-rule="nonzero" v-if="svgicons">
														<g stroke="none" stroke-width="1">
															<g transform="translate(-62.000000, -494.000000)">
																<g transform="translate(0.000000, 94.000000)">
																	<g transform="translate(73.000000, 416.500000) rotate(90.000000) translate(-73.000000, -416.500000) translate(56.500000, 405.500000)">
																		<path
																			d="M15.9999476,20.2113404 C15.4340178,20.2113404 14.8681578,19.9952556 14.436687,19.5639944 L0.859123213,5.98629087 C-0.00458718268,5.12258047 -0.00458718268,3.72222211 0.859123213,2.85886114 C1.72248418,1.99550017 3.122563,1.99550017 3.98634328,2.85886114 L15.9999476,14.8731643 L28.0136218,2.85928045 C28.8773322,1.99591948 30.2772712,1.99591948 31.1405623,2.85928045 C32.004692,3.72264143 32.004692,5.12299978 31.1405623,5.98671018 L17.5632082,19.5644137 C17.1315277,19.9957448 16.5656678,20.2113404 15.9999476,20.2113404 Z"></path>
																	</g>
																</g>
															</g>
														</g>
													</svg>
												</span>
											</div>
											<div class="base-modal-gallery-nav-btn base-modal-gallery-nav-next" :class="{'swiper-button-disabled': mainSwiper?.activeIndex == mainSwiper?.slides?.length - 1}" @click="mainSwiper.slideNext()">
												<span>
													<svg width="22px" height="33px" viewBox="0 0 22 33" fill="#FFFFFF" fill-rule="nonzero" v-if="svgicons">
														<g stroke="none" stroke-width="1">
															<g transform="translate(-62.000000, -494.000000)">
																<g transform="translate(0.000000, 94.000000)">
																	<g transform="translate(73.000000, 416.500000) rotate(90.000000) translate(-73.000000, -416.500000) translate(56.500000, 405.500000)">
																		<path
																			d="M15.9999476,20.2113404 C15.4340178,20.2113404 14.8681578,19.9952556 14.436687,19.5639944 L0.859123213,5.98629087 C-0.00458718268,5.12258047 -0.00458718268,3.72222211 0.859123213,2.85886114 C1.72248418,1.99550017 3.122563,1.99550017 3.98634328,2.85886114 L15.9999476,14.8731643 L28.0136218,2.85928045 C28.8773322,1.99591948 30.2772712,1.99591948 31.1405623,2.85928045 C32.004692,3.72264143 32.004692,5.12299978 31.1405623,5.98671018 L17.5632082,19.5644137 C17.1315277,19.9957448 16.5656678,20.2113404 15.9999476,20.2113404 Z"></path>
																	</g>
																</g>
															</g>
														</g>
													</svg>
												</span>
											</div>
										</div>
									</template>
								</BaseUiSwiper>
								<slot name="afterSlider" />
							</div>

							<div class="base-modal-gallery-thumbs" v-if="item.items?.length > 1">
								<slot name="thumbs" :items="item.items" :swiper="mainSwiper" :activeIndex="activeSlideIndex">
									<div class="base-modal-gallery-thumbs-items">
										<div v-for="(modalItem, index) in item.items" :key="modalItem.id" class="base-modal-gallery-thumbs-item" :class="{'active': index == activeSlideIndex}" @click="mainSwiper.slideTo(index)">
											<BaseUiImage loading="lazy" :src="modalItem.thumb ? modalItem.thumb : modalItem.url" />
										</div>
									</div>
								</slot>
							</div>
						</div>
						<div v-else class="base-modal-cnt">
							<div class="base-modal-cnt-close" @click="onClose">
								<svg width="24px" height="24px" fill="#000000" viewBox="0 0 24 24" v-if="svgicons">
									<g transform="translate(-406.000000, -2828.000000)" fill-rule="nonzero">
										<g transform="translate(68.000000, 2805.000000)">
											<g transform="translate(350.000000, 35.000000) rotate(-270.000000) translate(-350.000000, -35.000000) translate(338.000000, 23.000000)">
												<path
													d="M-2.82746785,10.8569051 L26.8274678,10.8569051 C27.3797526,10.8569051 27.8274678,11.3046204 27.8274678,11.8569051 L27.8274678,12.1430949 C27.8274678,12.6953796 27.3797526,13.1430949 26.8274678,13.1430949 L-2.82746785,13.1430949 C-3.3797526,13.1430949 -3.82746785,12.6953796 -3.82746785,12.1430949 L-3.82746785,11.8569051 C-3.82746785,11.3046204 -3.3797526,10.8569051 -2.82746785,10.8569051 Z"
													transform="translate(12.000000, 12.000000) rotate(45.000000) translate(-12.000000, -12.000000) "></path>
												<rect transform="translate(12.000000, 12.000000) rotate(-45.000000) translate(-12.000000, -12.000000) " x="-3.82746785" y="10.8569051" width="31.6549357" height="2.2861898" rx="1"></rect>
											</g>
										</g>
									</g>
								</svg>
							</div>

							<slot :item="item" :onClose="onClose" :active="active" :loading="loading">
								<div v-if="loading" class="base-modal-loading">Loading</div>
								<template v-else>
									<div class="base-modal-content-wrapper">
										<h1 v-if="item?.seo_h1">{{ item.seo_h1 }}</h1>
										<div class="base-modal-content" v-if="item?.content" v-html="item?.content" v-interpolation />
									</div>
								</template>
							</slot>
						</div>
					</div>
					<div class="base-modal-mask" @click="maskClosable && onClose()" />
				</div>
			</Transition>
		</BaseUiModal>
	</div>
</template>

<script setup>
	import {Zoom, Keyboard} from 'swiper/modules';
	const {absolute} = useUrl();
	const props = defineProps({
		style: {String, Object},
		class: {
			type: String,
			default: '',
		},
		closable: {
			type: Boolean,
			default: true,
		},
		maskClosable: {
			type: Boolean,
			default: false,
		},
		transition: {
			type: String,
			default: 'fade',
		},
		zoom: {
			type: [Object, Boolean],
			default: false,
		},
		wheelZoom: {
			type: Boolean,
			default: true,
		},
		zoomToggle: {
			type: Boolean,
			default: false,
		},
		svgicons: {
			type: Boolean,
			default: true,
		},
	});

	const emit = defineEmits(['slideChange']);

	// show zoom buttons if zoom is enabled and there are multiple modal items
	const zoomBtns = computed(() => props.zoom && modalData.value?.items?.length);

	// main swiper instance
	const mainSwiper = ref(null);
	const setMainSwiper = swiper => {
		mainSwiper.value = swiper;
	};

	// zoom buttons
	const zoomLevel = ref(1);
	const zoomIncrement = props.zoom?.increment || 1;
	const maxZoom = ref(props.zoom?.max || 3);
	function zoom(mode) {
		if (!mainSwiper.value) return;
		if (!props.zoom || props.zoom == false) return;
		if (mode == 'in' && zoomLevel.value < maxZoom.value) {
			mainSwiper.value.zoom.in(zoomLevel.value + zoomIncrement);
		}
		if (mode == 'out' && zoomLevel.value > 1) {
			mainSwiper.value.zoom.in(zoomLevel.value - zoomIncrement);
		}
	}

	function isMobile() {
		return /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent);
	}

	// update current zoom level on zoom change
	function onZoom(swiper, scale, imageEl, slideEl) {
		if (!props.zoom || props.zoom == false) return;
		zoomLevel.value = scale;
		if (isMobile()) {
			mainSwiper.value.allowTouchMove = scale <= 1 ? true : false;
		}
	}

	// zoom on wheel scroll
	function onWheel(e) {
		if (!props.zoom || props.zoom == false || !props.wheelZoom) return;
		e.deltaY > 0 ? zoom('out') : zoom('in');
	}

	const activeSlideIndex = ref(0);
	function onSlideChange(e) {
		activeSlideIndex.value = e.activeIndex;
		emit('slideChange', {activeIndex: e.activeIndex});
	}

	// Toogle zoom when user clicks on image
	function onClick(e) {
		if (!props.zoomToggle) return;
		if (!e.defaultPrevented) mainSwiper.value.zoom.toggle();
	}

	// set initial modal data
	const modalData = ref();
	function onOpen({active, data}) {
		modalData.value = data;
	}

	const disabledZoomIn = computed(() => zoomLevel.value >= maxZoom.value);
	const disabledZoomOut = computed(() => zoomLevel.value <= 1);
</script>

<style>
	body.modal-active {
		touch-action: none;
		-ms-scroll-chaining: none;
		overscroll-behavior: none;
		-webkit-overflow-scrolling: auto;
		overflow: hidden;
	}
</style>

<style scoped lang="less">
	* {
		--modal-icon-color: #000;
		--modal-thumb-border-color: #b4b4b4;
		--modal-thumb-active-border-color: #000;
		--modal-close-color: #e5454e;

		// prevents text and element selection
		/*
		user-select: none;
		-webkit-user-select: none;
		-ms-user-select: none;
		*/
	}

	:deep(img) {
		width: auto;
		height: auto;
		max-height: 100vh;
		max-width: 100%;
		transition: transform 0.2s ease;
		display: block;
		margin: auto;
	}
	video {
		width: 80%;
		height: 95%;
	}
	:deep(iframe) {
		max-height: 100%;
		max-width: 100%;
		height: 100%;
		width: calc(100vh * 1.4);
	}
	.base-modal {
		position: fixed;
		display: flex;
		align-items: center;
		justify-content: center;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 9999;
		pointer-events: none;
	}
	.base-modal-body {
		z-index: 1;
		pointer-events: all;
	}
	.base-modal-cnt {
		background: #fff;
		border-radius: 4px;
		box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.4);
		width: 850px;
		max-width: 90vw;
		position: relative;
	}
	.base-modal-content-wrapper {
		overflow: auto;
		max-height: 90vh;
		padding: 4% 5%;
	}
	.base-modal-cnt-close {
		position: absolute;
		top: 15px;
		right: 15px;
		width: 40px;
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		svg {
			fill: var(--modal-close-color);
		}
		@media (max-width: 980px) {
			top: 10px;
			right: 10px;
		}
	}
	.base-modal-mask {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		background: rgba(0, 0, 0, 0.75);
		pointer-events: all;
		z-index: -1;
	}

	// toolbar
	.base-modal-toolbar {
		position: absolute;
		top: 15px;
		right: 15px;
		z-index: 100;
		@media (max-width: 1200px) {
			top: 1vh;
			right: 2vw;
		}
	}
	.base-modal-toolbar-item {
		font: 15px/1 Arial;
		width: 45px;
		height: 45px;
		display: flex;
		margin: 0 auto 10px;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		font-weight: bold;
		border-radius: 100%;
		@media (max-width: 1200px) {
			width: 40px;
			height: 40px;
			margin: 0 auto 5px;
		}
		svg {
			width: 50%;
			height: auto;
			fill: var(--modal-icon-color);
		}
		&.disabled {
			pointer-events: none;
			opacity: 0.5;
		}
	}
	.base-modal-toolbar-close {
		background: var(--modal-close-color);
		width: 55px;
		height: 55px;
		@media (max-width: 1200px) {
			width: 40px;
			height: 40px;
		}
		svg {
			width: 30%;
			fill: #fff;
		}
	}

	// gallery
	.gallery {
		.base-modal-mask {
			background: #fff;
		}
		.base-modal-body {
			height: 100%;
			width: 100%;
		}
		@media (max-width: 980px) {
			.base-modal {
				display: block;
			}
			.base-modal-cnt {
				box-shadow: none;
				max-height: 100vh;
				border-radius: 0;
				width: 100%;
				padding: 3% 4%;
			}
		}
	}
	.base-modal-gallery,
	.base-modal-gallery-main {
		height: 100%;
	}
	:deep(.swiper) {
		height: 100%;
	}
	:deep(.swiper-slide) {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	:deep(.swiper-container) {
		height: 100%;
	}
	.base-modal-gallery-loader {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		background: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 999999;
	}
	.base-modal-gallery-nav {
		position: fixed;
		top: 48%;
		left: 10%;
		right: 10%;
		z-index: 100;
		@media (max-width: 1200px) {
			left: 0;
			right: 0;
		}
	}
	.base-modal-gallery-nav-btn {
		position: absolute;
		top: 0;
		left: 0;
		width: 60px;
		height: 60px;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		@media (max-width: 1200px) {
			width: 40px;
			height: 40px;
			svg {
				width: 18px;
			}
		}
		svg {
			fill: var(--modal-icon-color);
			display: block;
		}
		&.swiper-button-disabled {
			pointer-events: none;
			opacity: 0.3;
		}
	}
	.base-modal-gallery-nav-next {
		left: auto;
		right: 0;
		transform: scaleX(-1);
	}

	.base-modal-gallery-thumbs {
		position: fixed;
		width: 120px;
		top: 0;
		z-index: 100;
		bottom: 0;
		display: flex;
		flex-wrap: wrap;
		overflow: auto;
		background: #fff;
		padding: 15px;
		scroll-snap-type: y proximity;
		@media (max-width: 1200px) {
			top: auto;
			left: 0;
			right: 0;
			bottom: 0;
			width: 100%;
			padding: 10px;
			scroll-snap-type: x proximity;
		}
	}
	.base-modal-gallery-thumbs-items {
		display: flex;
		flex-direction: column;
		justify-content: center;
		gap: 10px;
		@media (max-width: 1200px) {
			flex-direction: row;
			align-items: center;
			margin: auto;
		}
	}
	.base-modal-gallery-thumbs-item {
		width: 100%;
		display: flex;
		align-items: center;
		cursor: pointer;
		justify-content: center;
		overflow: hidden;
		background: #fff;
		border: 1px solid var(--modal-thumb-border-color);
		border-radius: 5px;
		padding: 5px;
		aspect-ratio: 1;
		scroll-snap-align: center;
		@media (max-width: 1200px) {
			width: auto;
			height: 70px;
		}
		img {
			display: block;
		}
		&.active {
			border-color: var(--modal-thumb-active-border-color);
		}
	}
	.base-modal-gallery-title {
		position: absolute;
		display: flex;
		justify-content: center;
		bottom: 0;
		left: 0;
		right: 0;
		font-size: 11px;
		line-height: 1.5;
		color: #fff;
		span {
			display: inline-block;
			padding: 7px 15px;
			max-width: 70%;
			background: rgba(0, 0, 0, 0.4);
			border-radius: 3px 3px 0 0;
		}
	}

	/* animations */
	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.2s ease;
	}

	.fade-enter-from,
	.fade-leave-to {
		opacity: 0;
	}

	/* Add cursor style for zoom toggle */
	.base-modal-gallery.zoom-toggle .swiper-zoom-container {
		cursor: zoom-in;
	}
	.base-modal-gallery.zoom-toggle .swiper-slide-zoomed .swiper-zoom-container {
		cursor: move; /* Change cursor to indicate draggable when zoomed */
	}
</style>
