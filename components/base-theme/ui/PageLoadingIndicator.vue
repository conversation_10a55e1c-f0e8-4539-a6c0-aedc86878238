<template>
	<BaseUiPageLoadingIndicator v-slot="{loading, percentage}" v-bind="$attrs">
		<Transition name="fade">
			<div class="page-loading" :style="[{backgroundColor: props.trackColor}, {height: props.height}]" v-if="loading && percentage > 1">
				<div class="page-loading-bar" :style="[{width: percentage + '%'}, {backgroundColor: props.barColor}]" />
			</div>
		</Transition>
	</BaseUiPageLoadingIndicator>
</template>

<script setup>
	const props = defineProps({
		height: {
			type: String,
			default: '2px',
		},
		trackColor: {
			type: String,
			default: 'rgba(255, 255, 255, 0.5)',
		},
		barColor: {
			type: String,
			default: '#f00',
		},
	});
</script>

<style scoped>
	.page-loading {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 9999999;
		box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.6);
	}
	.page-loading-bar {
		height: 100%;
		transition: width 0.2s ease;
	}

	/* animations */
	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.2s ease;
	}

	.fade-enter-from,
	.fade-leave-to {
		opacity: 0;
	}
</style>
