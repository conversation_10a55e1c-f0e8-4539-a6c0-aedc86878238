<template>
	<div class="admin-bar" :class="{'active': active}" v-if="user?.staff || user?.superuser || user?.developer">
		<div class="admin-bar-wrapper">
			<div class="admin-bar-header">
				<a href="javascript:void(0);" @click="toggleBar">
					<svg width="30px" height="30px" fill="#ffffff" viewBox="0 0 30 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
						<g transform="translate(-1052.000000, -5408.000000)">
							<g transform="translate(60.000000, 4941.000000)">
								<g transform="translate(992.000000, 467.000000)">
									<path
										d="M18.6500853,11.030597 L16.3308742,20.7599147 C16.2635181,21.0369296 16.0851386,21.7691258 15.261919,21.7691258 L14.7776759,21.7691258 C13.959936,21.7691258 13.7786567,21.0453518 13.7103625,20.7660981 L11.3894883,13.0328998 L10.1297441,16.8008316 L0.123944563,16.8008316 C1.01232409,24.2305117 7.33503198,29.990597 15.0034115,29.990597 C22.6718124,29.990597 28.9945416,24.230533 29.8828998,16.8008316 L20.1874627,16.8008316 L18.6500853,11.030597 Z"
										id="Shape"></path>
									<path
										d="M10.0454584,11.4208955 C10.1832623,10.8755011 10.380597,10.3468443 11.1109595,10.3468443 L11.6827505,10.3468443 C12.4165885,10.3468443 12.6126013,10.881322 12.7508102,11.4285288 L15.0019403,18.781791 L17.288081,9.32046908 C17.4276759,8.77142857 17.6244989,8.23658849 18.3573774,8.23658849 L18.9288273,8.23658849 C19.6622388,8.23658849 19.8585501,8.77142857 19.9965032,9.31901919 L21.5169296,14.9124733 L29.9892111,14.9124733 C29.9404264,6.6775693 23.2501706,0.0162473348 15.0034115,0.0162473348 C6.7566951,0.0162473348 0.0664392324,6.6775693 0.0176332623,14.9124733 L8.85036247,14.9124733 L10.0454584,11.4208955 Z"
										id="Shape"></path>
								</g>
							</g>
						</g>
					</svg>
				</a>
			</div>

			<ul class="admin-bar-menu" v-show="active">
				<li>
					<a :href="config.host + '/admin/'" target="_blank">
						<svg id="fi_3208904" enable-background="new 0 0 512 512" height="512" viewBox="0 0 512 512" width="512" xmlns="http://www.w3.org/2000/svg">
							<g>
								<path d="m375 125.018v39.982h39.983c-5.297-19.391-20.592-34.687-39.983-39.982z"></path>
								<path
									d="m467 29h-422c-24.813 0-45 20.187-45 45v268c0 24.813 20.187 45 45 45h422c24.813 0 45-20.187 45-45v-268c0-24.813-20.187-45-45-45zm-377.49 226.6c-10.145 8.006-24.51.915-24.51-11.6 0-8.603 7.374-15.766 16.48-14.931 13.24 1.401 18.344 17.834 8.03 26.531zm5.41-74.13c-.645 7.901-8.017 14.303-16.4 13.45-7.949-.663-14.313-8.048-13.44-16.391.659-8.148 8.535-14.983 17.85-13.239 7.887 1.637 12.752 8.832 11.99 16.18zm.01-63.991c-1.352 13.047-17.73 18.483-26.53 8.03-5.515-6.977-3.953-16.17 2.09-21.11 10.42-8.491 25.784-.02 24.44 13.08zm73.07 205.521h-24c-8.284 0-15-6.716-15-15s6.716-15 15-15h24c8.284 0 15 6.716 15 15s-6.716 15-15 15zm56-64h-80c-8.284 0-15-6.716-15-15s6.716-15 15-15h80c8.284 0 15 6.716 15 15s-6.716 15-15 15zm0-64h-80c-8.284 0-15-6.716-15-15s6.716-15 15-15h80c8.284 0 15 6.716 15 15s-6.716 15-15 15zm0-64h-80c-8.284 0-15-6.716-15-15s6.716-15 15-15h80c8.284 0 15 6.716 15 15s-6.716 15-15 15zm88 192h-24c-8.284 0-15-6.716-15-15s6.716-15 15-15h24c8.284 0 15 6.716 15 15s-6.716 15-15 15zm48-56c-47.972 0-87-39.028-87-87s39.028-87 87-87 87 39.028 87 87-39.028 87-87 87z"></path>
								<path d="m345 180v-54.982c-24.174 6.603-42 28.744-42 54.982 0 31.43 25.57 57 57 57 26.238 0 48.38-17.826 54.983-42h-54.983c-8.284 0-15-6.716-15-15z"></path>
								<path d="m416 453h-97v-36h-126v36h-97c-8.284 0-15 6.716-15 15s6.716 15 15 15h320c8.284 0 15-6.716 15-15s-6.716-15-15-15z"></path>
							</g>
						</svg>
					</a>
					<div class="admin-bar-tooltip">Admin</div>
				</li>
				<li v-if="editUrl">
					<a :href="editUrl" target="_blank">
						<svg height="492pt" viewBox="0 0 492.49284 492" width="492pt" xmlns="http://www.w3.org/2000/svg" id="fi_1828911">
							<path
								d="m304.140625 82.472656-270.976563 270.996094c-1.363281 1.367188-2.347656 3.09375-2.816406 4.949219l-30.035156 120.554687c-.898438 3.628906.167969 7.488282 2.816406 10.136719 2.003906 2.003906 4.734375 3.113281 7.527344 3.113281.855469 0 1.730469-.105468 2.582031-.320312l120.554688-30.039063c1.878906-.46875 3.585937-1.449219 4.949219-2.8125l271-270.976562zm0 0"></path>
							<path
								d="m476.875 45.523438-30.164062-30.164063c-20.160157-20.160156-55.296876-20.140625-75.433594 0l-36.949219 36.949219 105.597656 105.597656 36.949219-36.949219c10.070312-10.066406 15.617188-23.464843 15.617188-37.714843s-5.546876-27.648438-15.617188-37.71875zm0 0"></path>
						</svg>
					</a>
					<div class="admin-bar-tooltip">{{ labels.edit }}</div>
				</li>
				<li>
					<a href="https://pomoc.marker.hr/" target="_blank">
						<svg id="fi_3524344" enable-background="new 0 0 512 512" height="512" viewBox="0 0 512 512" width="512" xmlns="http://www.w3.org/2000/svg">
							<g>
								<circle cx="256" cy="467" r="45"></circle>
								<path
									d="m405.792 142.082c-4.051-78.079-68.5-140.475-146.721-142.052-1.026-.02-2.043-.03-3.063-.03-74.638 0-138.222 55.019-148.564 129.134-.959 6.871-1.445 13.891-1.445 20.866 0 24.853 20.147 45 45 45s45-20.147 45-45c0-2.827.195-5.661.582-8.426 4.188-30.007 30.222-52.165 60.678-51.562 15.021.303 29.36 6.34 40.379 17.001 11.009 10.651 17.499 24.763 18.276 39.732.865 16.67-4.961 32.489-16.406 44.544-11.457 12.066-26.908 18.711-43.51 18.711-24.853 0-45 20.147-45 45v89c0 24.853 20.147 45 45 45s45-20.147 45-45v-50.968c24.073-7.604 46.159-21.218 63.777-39.774 28.607-30.131 43.174-69.614 41.017-111.176z"></path>
							</g>
						</svg>
					</a>
					<div class="admin-bar-tooltip">{{ labels.help }}</div>
				</li>
				<li>
					<a class="admin-bar-unclicakble" href="#">
						<svg version="1.1" id="fi_151773" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 118.783 118.783" style="enable-background: new 0 0 118.783 118.783" xml:space="preserve">
							<g>
								<path
									d="M115.97,101.597L88.661,74.286c4.64-7.387,7.333-16.118,7.333-25.488c0-26.509-21.49-47.996-47.998-47.996
		S0,22.289,0,48.798c0,26.51,21.487,47.995,47.996,47.995c10.197,0,19.642-3.188,27.414-8.605l26.984,26.986
		c1.875,1.873,4.333,2.806,6.788,2.806c2.458,0,4.913-0.933,6.791-2.806C119.72,111.423,119.72,105.347,115.97,101.597z
		 M47.996,81.243c-17.917,0-32.443-14.525-32.443-32.443s14.526-32.444,32.443-32.444c17.918,0,32.443,14.526,32.443,32.444
		S65.914,81.243,47.996,81.243z"></path>
							</g>
						</svg>
					</a>
					<div class="admin-bar-tooltip">
						<form :action="config.host + '/admin/_search/'" method="GET" target="_blank">
							<input type="text" name="q" :placeholder="labels.search" autocomplete="off" />
							<button type="submit" name="search" value="1">
								<svg version="1.1" id="fi_151773" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 118.783 118.783" style="enable-background: new 0 0 118.783 118.783" xml:space="preserve">
									<g>
										<path
											d="M115.97,101.597L88.661,74.286c4.64-7.387,7.333-16.118,7.333-25.488c0-26.509-21.49-47.996-47.998-47.996
		S0,22.289,0,48.798c0,26.51,21.487,47.995,47.996,47.995c10.197,0,19.642-3.188,27.414-8.605l26.984,26.986
		c1.875,1.873,4.333,2.806,6.788,2.806c2.458,0,4.913-0.933,6.791-2.806C119.72,111.423,119.72,105.347,115.97,101.597z
		 M47.996,81.243c-17.917,0-32.443-14.525-32.443-32.443s14.526-32.444,32.443-32.444c17.918,0,32.443,14.526,32.443,32.444
		S65.914,81.243,47.996,81.243z"></path>
									</g>
								</svg>
							</button>
						</form>
					</div>
				</li>
				<li><hr /></li>
				<li :class="{'loading': cacheLoading}">
					<a @click="clearCache(['all'])" href="javascript:void(0);">
						<BaseThemeUiLoading v-if="cacheLoading" />
						<svg v-else id="fi_5137857" enable-background="new 0 0 512.026 512.026" height="512" viewBox="0 0 512.026 512.026" width="512" xmlns="http://www.w3.org/2000/svg">
							<g>
								<path d="m163.218 205.629c-62.181-.986-120.373-11.691-163.182-46.919l-.015 63.521c61.556 60.232 265.472 60.431 326.394-.688l-.015-62.833c-42.834 35.236-100.977 45.926-163.182 46.919z"></path>
								<path
									d="m162.908 297.222c-63.573-1.05-115.848-9.414-162.897-36.844 0 0-.011 46.737-.011 46.737 10.573 48.083 100.358 64.553 168.979 64.189-18.394-20.494-9.543-53.728 17.069-61.491 14.702-4.346 29.053-10.177 42.985-17.434-20.961 3.182-43.261 4.843-66.125 4.843z"></path>
								<path d="m52.589 155.854c58.74 26.171 162.517 26.172 221.258-.001 69.902-28.576 69.922-88.438-.001-117.017-58.741-26.17-162.516-26.171-221.257.001-69.902 28.577-69.922 88.439 0 117.017z"></path>
								<path d="m495.857 134.565c-17.417-12.539-41.792-8.57-54.332 8.848l-72.409 100.576c4.58 2.144 8.99 4.721 13.174 7.733 9.007 6.95 42.963 29.551 50.022 37.729l72.393-100.553c12.54-17.418 8.571-41.792-8.848-54.333z"></path>
								<path
									d="m403.26 303.786-38.499-27.718c-19.277-13.88-45.286-12.718-63.25 2.821-32.718 28.3-68.703 48.384-106.958 59.693-2.358.697-4.225 2.506-4.996 4.841s-.348 4.899 1.131 6.864c10.195 13.535 19.816 25.359 29.415 36.149 1.684 1.893 4.212 2.805 6.71 2.433l35.164-5.246-17.477 17.348c-1.444 1.433-2.244 3.391-2.216 5.425.027 2.034.88 3.97 2.362 5.363 17.673 16.612 36.209 30.544 56.667 42.59 2.774 1.632 6.28 1.314 8.715-.793l19.785-17.128s-5.435 23.889-5.435 23.889c-.777 3.419.917 6.919 4.079 8.431 17.477 8.353 36.983 16.092 59.636 23.66 4.755 1.74 10.209-2.316 9.872-7.391-1.341-35.817 2.987-64.335 13.208-93.031 3.569-10.872 6.389-19.459 9.769-28.047 8.701-22.102 1.595-46.275-17.682-60.153z"></path>
							</g>
						</svg>
					</a>
					<div class="admin-bar-tooltip">{{ labels.clearCache }}</div>
				</li>
				<template v-if="user.developer">
					<li><hr /></li>
					<li>
						<a :href="config.host + '/admincp/'" target="_blank">
							<svg version="1.1" id="fi_484613" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" style="enable-background: new 0 0 512 512" xml:space="preserve">
								<g>
									<g>
										<path
											d="M500.633,211.454l-58.729-14.443c-3.53-11.133-8.071-21.929-13.55-32.256c8.818-14.678,27.349-45.571,27.349-45.571
			c3.545-5.903,2.607-13.462-2.256-18.325l-42.422-42.422c-4.863-4.878-12.407-5.815-18.325-2.256L347.055,83.53
			c-10.269-5.435-21.006-9.932-32.065-13.433l-14.443-58.729C298.876,4.688,292.885,0,286,0h-60
			c-6.885,0-12.891,4.688-14.546,11.367c0,0-10.005,40.99-14.429,58.715c-11.792,3.735-23.188,8.584-34.043,14.502l-47.329-28.403
			c-5.918-3.516-13.447-2.607-18.325,2.256l-42.422,42.422c-4.863,4.863-5.801,12.422-2.256,18.325l29.268,48.882
			c-4.717,9.302-8.672,18.984-11.821,28.901l-58.729,14.487C4.688,213.124,0,219.115,0,226v60c0,6.885,4.688,12.891,11.367,14.546
			l58.744,14.443c3.56,11.294,8.188,22.266,13.799,32.798l-26.191,43.652c-3.545,5.903-2.607,13.462,2.256,18.325l42.422,42.422
			c4.849,4.849,12.407,5.771,18.325,2.256c0,0,29.37-17.607,43.755-26.221c10.415,5.552,21.313,10.137,32.549,13.696l14.429,58.715
			C213.109,507.313,219.115,512,226,512h60c6.885,0,12.876-4.688,14.546-11.367l14.429-58.715
			c11.558-3.662,22.69-8.394,33.281-14.136c14.78,8.862,44.443,26.66,44.443,26.66c5.903,3.53,13.462,2.622,18.325-2.256
			l42.422-42.422c4.863-4.863,5.801-12.422,2.256-18.325l-26.968-44.927c5.317-10.093,9.727-20.654,13.169-31.523l58.729-14.443
			C507.313,298.876,512,292.885,512,286v-60C512,219.115,507.313,213.124,500.633,211.454z M256,361c-57.891,0-105-47.109-105-105
			s47.109-105,105-105s105,47.109,105,105S313.891,361,256,361z"></path>
									</g>
								</g>
							</svg>
						</a>
						<div class="admin-bar-tooltip">AdminCP</div>
					</li>
					<li>
						<a :href="config.host + '/admincp/ajax_syncdb/'" target="_blank">
							<svg id="fi_3031712" enable-background="new 0 0 512 512" height="512" viewBox="0 0 512 512" width="512" xmlns="http://www.w3.org/2000/svg">
								<path
									d="m512 86.401v91.199c0 17.673-14.327 32-32 32h-91.199c-17.673 0-32-14.327-32-32s14.327-32 32-32h24.45c-35.335-50.645-93.244-81.6-157.251-81.6-79.639 0-149.848 47.913-178.866 122.063-6.442 16.458-25.006 24.577-41.462 18.137-16.458-6.441-24.578-25.004-18.137-41.461 18.559-47.423 50.547-87.906 92.506-117.073 42.977-29.875 93.448-45.666 145.959-45.666s102.982 15.791 145.959 45.665c17.076 11.87 32.501 25.616 46.041 40.94v-.203c0-17.673 14.327-32 32-32s32 14.326 32 31.999zm-35.672 221.4c-16.457-6.442-35.02 1.68-41.462 18.137-29.018 74.15-99.227 122.062-178.866 122.062-62.647 0-119.454-29.654-154.97-78.4h22.169c17.673 0 32-14.327 32-32s-14.327-32-32-32h-91.199c-17.673 0-32 14.327-32 32v91.2c0 17.673 14.327 32 32 32s32-14.327 32-32v-3.404c13.54 15.324 28.965 29.069 46.041 40.939 42.977 29.874 93.448 45.665 145.959 45.665s102.982-15.791 145.959-45.665c41.959-29.167 73.947-69.65 92.506-117.073 6.441-16.457-1.679-35.02-18.137-41.461z"></path>
							</svg>
						</a>
						<div class="admin-bar-tooltip">SyncDB</div>
					</li>
					<li>
						<a class="admin-bar-unclicakble" href="#">
							<svg version="1.1" id="fi_151776" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 111.577 111.577" style="enable-background: new 0 0 111.577 111.577" xml:space="preserve">
								<g>
									<path
										d="M78.962,99.536l-1.559,6.373c-4.677,1.846-8.413,3.251-11.195,4.217c-2.785,0.969-6.021,1.451-9.708,1.451
		c-5.662,0-10.066-1.387-13.207-4.142c-3.141-2.766-4.712-6.271-4.712-10.523c0-1.646,0.114-3.339,0.351-5.064
		c0.239-1.727,0.619-3.672,1.139-5.846l5.845-20.688c0.52-1.981,0.962-3.858,1.316-5.633c0.359-1.764,0.532-3.387,0.532-4.848
		c0-2.642-0.547-4.49-1.636-5.529c-1.089-1.036-3.167-1.562-6.252-1.562c-1.511,0-3.064,0.242-4.647,0.71
		c-1.59,0.47-2.949,0.924-4.09,1.346l1.563-6.378c3.829-1.559,7.489-2.894,10.99-4.002c3.501-1.111,6.809-1.667,9.938-1.667
		c5.623,0,9.962,1.359,13.009,4.077c3.047,2.72,4.57,6.246,4.57,10.591c0,0.899-0.1,2.483-0.315,4.747
		c-0.21,2.269-0.601,4.348-1.171,6.239l-5.82,20.605c-0.477,1.655-0.906,3.547-1.279,5.676c-0.385,2.115-0.569,3.731-0.569,4.815
		c0,2.736,0.61,4.604,1.833,5.597c1.232,0.993,3.354,1.487,6.368,1.487c1.415,0,3.025-0.251,4.814-0.744
		C76.854,100.348,78.155,99.915,78.962,99.536z M80.438,13.03c0,3.59-1.353,6.656-4.072,9.177c-2.712,2.53-5.98,3.796-9.803,3.796
		c-3.835,0-7.111-1.266-9.854-3.796c-2.738-2.522-4.11-5.587-4.11-9.177c0-3.583,1.372-6.654,4.11-9.207
		C59.447,1.274,62.729,0,66.563,0c3.822,0,7.091,1.277,9.803,3.823C79.087,6.376,80.438,9.448,80.438,13.03z"></path>
								</g>
							</svg>
						</a>
						<div class="admin-bar-tooltip"><BaseUtilsVersion /></div>
					</li>
				</template>
			</ul>
		</div>
	</div>
</template>

<script setup>
	const {user} = useAuth();
	const config = useAppConfig();
	const route = useRoute();
	const editUrl = computed(() => {
		let id = route.meta.entityId ? route.meta.entityId : route.meta.pageId;
		if (!id) return '';

		const action = route.meta.action;
		let module = route.meta.controller;
		const contentType = route.meta.contentType;
		let url;

		// publish
		if (module == 'publish') {
			if (action == 'index') url = 'publishcategory';
			if (action == 'detail') url = 'publish';
		}

		// catalog
		if (module == 'catalog') {
			if (action == 'index') url = 'catalogcategory';
			if (action == 'detail') url = 'catalogproduct';
			if (contentType == 'list') url = 'cataloglist';
			if (contentType == 'manufacturer') url = 'catalogmanufacturer';
		}

		if (!route.meta.entityId) {
			module = 'cms';
			url = 'page';
		}

		if (!url) return '';

		const baseUrl = config.host + `/admin/${module}`;
		return `${baseUrl}/${url}/form/${id}/`;
	});

	// Menu labels
	const labels = computed(() => {
		let edit = 'Edit';
		let help = 'Help';
		let search = 'Search admin...';
		let clearCache = 'Clear Cache';

		if (['hr', 'ba', 'rs'].includes(config.lang)) {
			edit = 'Uredi';
			help = 'Pomoć';
			search = 'Pretraga administracije...';
			clearCache = 'Očisti cache';
		}

		return {edit, help, search, clearCache};
	});

	// Clear Cache
	const cacheLoading = ref(false);
	async function clearCache(modules) {
		cacheLoading.value = true;
		await $fetch(`/api/nuxtapi/clearCache/`, {
			method: 'POST',
			body: modules,
		});
		window.location.reload();
	}

	// Toggle admin bar and set cookie
	const adminBarCookie = useCookie('cmsAdminBar');
	if (adminBarCookie.value == undefined) adminBarCookie.value = 1;
	const active = computed(() => adminBarCookie.value);
	function toggleBar() {
		adminBarCookie.value = adminBarCookie.value == 1 ? 0 : 1;
	}
</script>

<style scoped>
	.admin-bar {
		display: flex;
		/*background: linear-gradient(to bottom, #be151b, #a0272b);*/
		position: fixed;
		top: 0;
		bottom: 0;
		left: 10px;
		z-index: 999;
		align-items: center;
		color: #404040;
		font-size: 10px;
		line-height: 1.4;
	}
	.admin-bar a {
		text-decoration: none;
		color: #000;
	}
	.admin-bar hr {
		width: 80%;
		margin: 3px auto;
	}
	.admin-bar svg {
		width: 100%;
		max-width: 100%;
		height: auto;
		display: block;
	}
	.admin-bar-wrapper {
		border-radius: 100px;
		width: 40px;
		background: #fff;
		box-shadow: 0px 0px 10px 5px rgba(0, 0, 0, 0.05);
		border: 1px solid #ececec;
	}
	.admin-bar-header {
		display: flex;
		aspect-ratio: 1;
		align-items: center;
		justify-content: center;
		padding: 5px;
	}
	.admin-bar-header svg {
		fill: red;
	}
	.admin-bar-menu {
		list-style: none;
		padding: 5px;
		margin: 0;
		display: flex;
		flex-direction: column;
		gap: 5px;
	}
	.admin-bar-menu > li {
		padding: 0;
		margin: 0;
		position: relative;
	}
	.admin-bar-menu > li > a {
		display: block;
		padding: 25%;
		color: #fff;
		background: #eaeaea;
		border-radius: 100px;
	}
	.admin-bar-menu > li > a:hover {
		background: #dbdbdb;
	}
	.admin-bar-menu li:hover .admin-bar-tooltip {
		display: block;
	}
	.admin-bar-menu > li.loading > a {
		padding: 15%;
	}
	.admin-bar-menu > li.loading .base-loader {
		padding: 0;
		width: 100%;
		height: auto;
	}
	.admin-bar-menu > li.loading .base-loader:deep(svg) {
		width: 100%;
		height: auto;
	}
	.admin-bar-tooltip {
		position: absolute;
		background: #fff;
		border-radius: 7px;
		border: 1px solid #ebebeb;
		padding: 5px 8px;
		white-space: nowrap;
		top: 2px;
		display: none;
		left: calc(100% + 15px);
	}
	.admin-bar-tooltip .dev-version {
		display: block !important;
	}
	.admin-bar-tooltip a {
		text-decoration: underline;
	}
	.admin-bar-tooltip a:hover {
		text-decoration: none;
	}
	.admin-bar-tooltip:after {
		content: '';
		position: absolute;
		border-left: 1px solid #ebebeb;
		border-bottom: 1px solid #ebebeb;
		width: 8px;
		height: 8px;
		background: #fff;
		transform: rotate(45deg);
		top: 7px;
		left: -5px;
	}
	.admin-bar-tooltip:before {
		content: '';
		display: block;
		position: absolute;
		width: 20px;
		height: 100%;
		top: 0;
		bottom: 0;
		left: -20px;
	}
	.admin-bar-unclicakble {
		cursor: default;
		pointer-events: none;
	}
	.admin-bar input {
		height: 25px;
		border: none;
		font-size: 12px;
		color: #404040;
		padding: 0 35px 0 5px;
		width: 160px;
		font-size: 11px;
	}
	.admin-bar form {
		position: relative;
	}
	.admin-bar button {
		width: 25px;
		height: 100%;
		background: transparent;
		box-shadow: none;
		border: none;
		font-size: 12px;
		position: absolute;
		top: 0;
		padding: 0;
		margin: 0;
		right: 0;
	}
	.admin-bar button:hover {
		background: none;
	}
	.admin-bar button svg {
		fill: #000;
		width: 55%;
	}
	@media (max-width: 1200px) {
		.admin-bar {
			display: none;
		}
	}
</style>
