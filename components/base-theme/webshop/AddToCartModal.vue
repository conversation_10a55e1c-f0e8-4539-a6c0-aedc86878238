<template>
	<BaseWebshopAddToCartModal v-slot="{items, status, onClose, urls}" :auto-close="0">
		<Transition :name="transition">
			<div class="add-to-cart-modal" v-if="items?.length">
				<div class="add-to-cart-modal-body">
					<slot :items="items" :status="status" :onClose="onClose" :urls="urls">
						<div class="add-to-cart-modal-close" @click="onClose">X</div>
						<div v-if="status?.data?.label_name" class="add-to-cart-modal-status">{{ status.data.label_name }}</div>
						<div v-for="item in items" :key="item.id">
							<div class="add-to-cart-modal-image"><BaseUiImage :src="item.main_image_upload_path" width="300" :alt="item.title" /></div>
							<div class="add-to-cart-modal-title">{{ item.title }}</div>
							<div class="add-to-cart-modal-price">
								<template v-if="item.discount_percent_custom">
									<del><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></del>
									<BaseUtilsFormatCurrency :price="item.price_custom" />
								</template>
								<template v-else>
									<BaseUtilsFormatCurrency :price="item.price_custom" v-if="item.price_custom" />
								</template>
							</div>
						</div>
						<div class="add-to-cart-modal-buttons">
							<NuxtLink class="add-to-cart-modal-btncart" :to="urls.webshop_shopping_cart">Pregled košarice</NuxtLink>
						</div>
					</slot>
				</div>
				<div :class="['add-to-cart-modal-mask']" />
			</div>
		</Transition>
	</BaseWebshopAddToCartModal>
</template>

<script setup>
	const props = defineProps({
		transition: {
			type: String,
			default: 'fade',
		},
	});
</script>

<style scoped>
	.add-to-cart-modal {
		position: fixed;
		inset: 0;
		z-index: 1000;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: auto;
	}
	.add-to-cart-modal-body {
		background: #fff;
		padding: 30px 50px;
		z-index: 1;
		width: 400px;
		text-align: center;
		position: relative;
		box-shadow: 0 15px 35px -20px rgba(0, 0, 0.2);
	}
	.add-to-cart-modal-mask {
		position: fixed;
		inset: 0;
		background: rgba(0, 0, 0, 0.4);
	}
	.add-to-cart-modal-btncart {
		font-size: 14px;
	}
	.add-to-cart-modal-buttons {
		padding-top: 20px;
	}
	.add-to-cart-modal-close {
		position: absolute;
		top: -17px;
		right: -17px;
		width: 40px;
		height: 40px;
		border-radius: 100px;
		background: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 15px;
		line-height: 1;
		font-weight: bold;
		cursor: pointer;
	}
	.add-to-cart-modal-image {
		padding: 0 0 10px;
	}
	:deep(.add-to-cart-modal-image img) {
		display: block;
		margin: auto;
		max-width: 200px;
		max-height: 200px;
		width: auto;
		height: auto;
	}
	.add-to-cart-modal-status {
		font-weight: bold;
		margin: 0 0 20px;
	}
	.add-to-cart-modal-price {
		padding-top: 15px;
	}

	/* animations */
	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.2s ease;
	}

	.fade-enter-from,
	.fade-leave-to {
		opacity: 0;
	}
</style>
