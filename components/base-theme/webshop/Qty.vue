<template>
	<BaseWebshopQty v-bind="$attrs" :limit="limit" v-slot="{loading, status, onDecrement, onIncrement, onUpdate, onReset, quantity}" @update="onUpdate">
		<div class="qty-input-container" :class="{'loading': loading}">
			<template v-if="type == 'select'">
				<select class="qty-input" name="qty" @change="onUpdate">
					<option v-for="(i, index) in selectItems" :value="i" :selected="i == quantity" :key="index">{{ i }}</option>
				</select>
			</template>
			<template v-else>
				<span class="qty-btn qty-btn-dec" @click="onDecrement">-</span>
				<input class="qty-input" type="text" :value="quantity" @keyup="onUpdate" @blur="onReset" />
				<span class="qty-btn qty-btn-inc" @click="onIncrement">+</span>
			</template>
		</div>
		<template v-if="$attrs.mode == 'cart'">
			<div v-show="status" class="qty-status"><BaseCmsLabel :code="status" /></div>
		</template>
	</BaseWebshopQty>
</template>

<script setup>
	const props = defineProps({
		type: String,
		limit: [Number, String],
		selectLimit: Number,
	});

	// emit quantity to parent component
	const emit = defineEmits(['update']);
	function onUpdate(value) {
		emit('update', value);
	}

	const selectItems = computed(() => {
		let limit = +props.selectLimit || 10;
		if (props.limit && +props.limit < limit) limit = +props.limit;
		return limit;
	});
</script>
