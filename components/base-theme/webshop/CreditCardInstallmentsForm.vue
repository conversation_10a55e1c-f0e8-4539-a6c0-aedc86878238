<template>
	<BaseWebshopCreditCardForm v-if="props.selectedField?.id == props.field?.id && +props.selectedField?.is_credit_card && props.selectedField?.widget == 'installments'" :item="field" v-slot="{fields}">
		<div class="wc-card-payment">
			<template v-for="(field, index) in fields" :key="field.id">
				<BaseFormField :item="field" v-slot="{errorMessage}" v-if="index == 0" v-model="card">
					<div :class="['field', 'field-' + field.name]">
						<div class="label"><BaseCmsLabel code="cc_types" /></div>
						<BaseFormInput @change="onChange($event), onPaymentUpdate(field)" />
						<span class="error" v-show="errorMessage" v-html="errorMessage" />
					</div>
				</BaseFormField>
				<BaseFormField :item="field" v-slot="{errorMessage}" v-if="index > 0 && card?.value">
					<div :class="['field', 'field-' + field.name]">
						<div class="label"><BaseCmsLabel code="cc_installments" /></div>
						<BaseFormInput @change="onPaymentUpdate(field)" />
						<span class="error" v-show="errorMessage" v-html="errorMessage" />
					</div>
				</BaseFormField>
			</template>
		</div>
	</BaseWebshopCreditCardForm>
</template>

<script setup>
	const props = defineProps({
		field: Object,
		selectedField: Object,
		onPaymentUpdate: Function,
	});

	const {setFieldValue} = inject('baseFormData');
	const card = ref(null);

	// reset installments when card type changes
	function onChange(e) {
		setFieldValue('cc_installments', 1);
	}
</script>
