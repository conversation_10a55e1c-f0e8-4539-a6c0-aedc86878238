<template>
	<BaseWebshopKeksPay v-slot="{data, reload}">
		<div class="kekspay-payment" v-if="data">
			<BaseCmsLabel code="kekspay_title" tag="div" class="kekspay-title" />
			<BaseCmsLabel code="kekspay_qrcode" tag="div" class="kekspay-content" />
			<div v-if="data?.qr_code" id="qr_code" class="kekspay-qr">
				<img :src="data?.qr_code" alt="Keks Pay QR" />
			</div>
			<p>
				<BaseCmsLabel code="kekspay_time_remaining" />
				[<BaseUiCountdown :countdown="120" :onEnd="reload" v-slot="{minutes, seconds}"> {{ minutes }}:{{ seconds }} </BaseUiCountdown>]
			</p>
			<BaseCmsLabel code="kekspay_already_paid" tag="div" :replace="[{'%payment-hold-url%': 'javascript:location.reload();'}]" />
		</div>
	</BaseWebshopKeksPay>
</template>
