<template>
	<BaseWebshopGlsParcelLockers ref="baseGlsParcelLockers" v-slot="{selectedLocker, showMap, errorMessage}">
		<div class="base-parcel-lockers-btn base-gls-parcel-lockers-btn" @click="showMap"><BaseCmsLabel code="select_parcel_locker" default="Odaberi paketomat" tag="span" /></div>
		<div class="base-parcel-lockers-content base-gls-parcel-lockers-content" v-if="selectedLocker">
			<p v-if="selectedLocker.parcel_locker_id">{{ selectedLocker.parcel_locker_id }}</p>
			<p v-if="selectedLocker.parcel_locker_title">{{ selectedLocker.parcel_locker_title }}</p>
			<p v-if="selectedLocker.parcel_locker_address">{{ selectedLocker.parcel_locker_address }}</p>
			<p v-if="selectedLocker.parcel_locker_zipcode || selectedLocker.parcel_locker_city">
				<template v-if="selectedLocker.parcel_locker_zipcode">{{ selectedLocker.parcel_locker_zipcode }}</template> <template v-if="selectedLocker.parcel_locker_city">{{ selectedLocker.parcel_locker_city }}</template>
			</p>
			<p v-if="selectedLocker.parcel_locker_countrycode">{{ selectedLocker.parcel_locker_countrycode }}</p>
		</div>
		<span class="error" v-show="errorMessage" v-html="errorMessage" />
	</BaseWebshopGlsParcelLockers>
</template>

<script setup>
	const baseGlsParcelLockers = ref(null);
	defineExpose({
		showMap: () => baseGlsParcelLockers.value?.showMap(),
	});
</script>
