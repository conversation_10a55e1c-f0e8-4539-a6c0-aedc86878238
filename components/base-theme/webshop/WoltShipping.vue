<template>
	<BaseWebshopWoltShipping v-slot="{shippingData, onSubmit, loading, dates, hours, minutes, valid}">
		<div class="wolt-shipping">
			<div v-if="shippingData?.shipment_promise?.promised_delivery_eta">
				Predviđeno vrijeme dostave: <strong>{{ shippingData?.shipment_promise?.promised_delivery_eta }}</strong>
			</div>

			<div class="dropoff">
				<input type="checkbox" name="dropoff" id="scheduled-dropoff" v-model="scheduledDropoff" @change="onSubmit" />
				<label for="scheduled-dropoff">Zakaži dostavu za kasnije</label>
				<div class="fields" v-if="scheduledDropoff">
					<div class="date">
						Odaberite datum
						<select name="date" v-model="selectedDate" @change="onSubmit">
							<option v-for="option in dates" :key="option[0]" :value="option[0]">{{ option[1].alternative_format }}</option>
						</select>
					</div>
					<div class="time">
						Odaberite vrijeme
						<div class="hours">
							Sat
							<select name="hours" v-model="selectedHours" @change="onSubmit">
								<option v-for="hour in hours" :key="hour" :value="hour">{{ hour }}</option>
							</select>
						</div>
						<div class="minutes">
							Minute
							<select name="minutes" v-model="selectedMinutes" @change="onSubmit">
								<option v-for="minute in minutes" :key="minute" :value="minute">{{ minute }}</option>
							</select>
						</div>
					</div>
				</div>
			</div>

			<div class="cash">
				<input type="checkbox" name="cash" id="cash" v-model="cash" @change="onSubmit" />
				<label for="cash">Nemam pripremljen točan iznos za naplatu</label>
				<div class="description">Unesi pripremljen iznos gotovine za plaćanje narudžbe</div>
				<div v-if="cash">
					<input type="number" v-model="cashAmount" @input="onSubmit" />
				</div>
			</div>
		</div>
	</BaseWebshopWoltShipping>
</template>

<script setup></script>

<style scoped></style>
