<template>
	<BaseWebshopAlternatePayments v-slot="{order, fields, selected, onSelect, loading, onSubmit, invoiceUrl, onCancelOrder}">
		<div class="alternate-payments">
			<div class="fields">
				<div v-for="field in fields" :key="field.id">
					<input type="radio" name="payment" :id="'altpayment-' + field.id" :value="field.id" :checked="selected?.id == field.id" @click="onSelect(field)" />
					<label :for="'altpayment-' + field.id">
						{{ field.title }}
						<div v-show="field.description && field.id == selected?.id" class="payment-info" v-html="field.description"></div>
					</label>
				</div>
			</div>
			<div class="btns">
				<button type="button" class="btn-submit" :disabled="!selected || loading" @click="onSubmit">
					<BaseCmsLabel code="save_new_payment" default="Spremi novi način pla<PERSON>anja" />
				</button>
				<button type="button" class="btn-cancel" :disabled="loading" @click="onCancelOrder">
					<BaseCmsLabel code="canceled_order" default="Potvrdi otkazivanje narudžbe" />
				</button>
			</div>
			<div class="loader" v-if="loading">
				<BaseThemeUiLoading />
			</div>
		</div>
	</BaseWebshopAlternatePayments>
</template>

<style scoped></style>
