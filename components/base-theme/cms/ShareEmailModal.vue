<template>
	<BaseThemeUiModal transition="fade" name="shareemail" :key-closable="true" :mask-closable="true">
		<BaseCmsShareEmailForm class="base-modal-share-email-form" v-slot="{fields, status, loading}">
			<div :class="[{'global-success': status?.success}, {'global-error': !status?.success}]" v-if="status">
				<BaseCmsLabel :code="`tellafriend_` + status?.data?.label_name" />
			</div>
			<template v-if="!status?.success">
				<template v-for="field in fields" :key="field.name">
					<BaseFormField :item="field" v-slot="{errorMessage}">
						<p class="field" :class="'field-' + field.name">
							<BaseFormInput :placeholder="labels.get(field.name)" />
							<span class="error" v-show="errorMessage" v-html="errorMessage" />
						</p>
					</BaseFormField>
				</template>
				<div class="base-modal-share-email-btns">
					<button :disabled="loading" :class="{'loading': loading}" class="base-modal-share-email-submit" type="submit"><BaseCmsLabel code="send" /></button>
				</div>
			</template>
		</BaseCmsShareEmailForm>
	</BaseThemeUiModal>
</template>

<script setup>
	const labels = useLabels();
</script>

<style scoped>
	button {
		width: 100%;
	}
</style>
