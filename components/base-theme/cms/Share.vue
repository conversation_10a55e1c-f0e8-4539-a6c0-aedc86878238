<template>
	<BaseCmsShare :networks="networks" :title="title" :description="description" :image="image" v-slot="{items, onShare}">
		<div class="base-share" v-if="items.length">
			<div v-for="network in items" :key="network" :class="['base-share-item', 'base-share-item-' + network.code]" @click="onShare(network), showTooltip(network)">
				<span>{{ network.title }}</span>
				<span class="base-share-tooltip" v-if="network.code == 'link' && tooltip">
					<BaseCmsLabel :code="copyLabel" v-if="copyLabel" />
					<template v-else>Link stranice je kopiran</template>
				</span>
			</div>
		</div>
	</BaseCmsShare>
	<BaseThemeCmsShareEmailModal />
</template>

<script setup>
	const props = defineProps({
		networks: {
			type: Array,
			default: () => ['facebook', 'whatsapp', 'viber', 'email', 'link'],
		},
		title: String,
		description: String,
		image: String,
		copyLabel: String,
	});

	const tooltip = ref(false);
	let timer;
	function showTooltip(network) {
		clearTimeout(timer);

		if (network.code == 'link') {
			tooltip.value = true;
			timer = setTimeout(() => {
				tooltip.value = false;
			}, 3000);
		}
	}

	onBeforeUnmount(() => {
		clearTimeout(timer);
	});
</script>
