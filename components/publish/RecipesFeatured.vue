<template>
	<BasePublishCategory :fetch="{start_position_with: '02', mode: 'full', hierarhy_by_position: true, response_fields: ['id','code','url_without_domain','children','title','seo_h1']}" v-slot="{item: category}">
		<BasePublishPostsWidget
			:fetch="{category_code: 'recipe', limit: 6, response_fields: ['id', 'title', 'short_description', 'url_without_domain', 'main_image_thumbs', 'category_title', 'category_url_without_domain', 'attributes_summary', 'feedback_comment_widget', 'feedback_rate_widget'], extra_fields: ['short_description']}"
			v-slot="{items}">
			<div class="pw pw-recipes" v-if="items?.length">
				<div class="wrapper wrapper-recipes df">
					<div class="pw-recipes-col pw-recipes-col1">
						<BaseCmsLabel code="blog_wrapper" tag="div" class="pw-recipes-title" />
						<BaseCmsLabel code="recipes_by_category" tag="div" class="recipes-sidebar-title" />
						<ul class="nav-recipes nav-recipes-category" v-if="category?.children?.length">
							<li v-for="categoryItem in category.children" :key="categoryItem.id">
								<NuxtLink :to="categoryItem.url_without_domain">{{categoryItem.title}}</NuxtLink>
							</li>
							<li>
								<NuxtLink :to="category.url_without_domain"><BaseCmsLabel code="show_all" tag="span" /></NuxtLink>
							</li>
						</ul>

						<BaseCmsNav code="recipes" level-range="2" v-slot="{items: menuItems}">
							<div class="recipes-sidebar-filters">
								<template v-for="menuItem in menuItems" :key="menuItem.id">
									<template v-if="menuItem?.items?.length">
										<div class="recipes-sidebar-title">{{menuItem.title}}</div>
										<ul class="nav-recipes">
											<li v-for="menuSubItem in menuItem.items" :key="menuSubItem.id">
												<NuxtLink :to="menuSubItem.url_without_domain">{{menuSubItem.title}}</NuxtLink>
											</li>
										</ul>
									</template>
								</template>
							</div>
						</BaseCmsNav>
					</div>

					<div class="pw-recipes-col pw-recipes-col2">
						<div class="p-items p-items-recipes pw-recipe-items blazy-container">
							<PublishIndexEntryRecipes v-for="item in items" :key="item.id" :item="item" />
						</div>
						<div class="pw-btns">
							<NuxtLink :to="category.url_without_domain" class="btn"><BaseCmsLabel code="show_all_recipes" class="span" /></NuxtLink>
						</div>
					</div>
				</div>
			</div>
		</BasePublishPostsWidget>
	</BasePublishCategory>
</template>

<script setup>
	/* const {onMediaQuery, appendTo, insertAfter} = useDom();
	onMediaQuery({
		query: '(max-width: 950px)',
		enter() {
			appendTo('.pw-btns', '.pw-recipes-col1');
		},
		leave() {
			insertAfter('.pw-btns', '.pw-recipe-items');
		},
	}); */
</script>

<style lang="less" scoped>
	.pw-recipes{background: url('/assets/images/homepage.jpg');}
</style>
