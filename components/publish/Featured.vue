<template>
	<BasePublishCategory :fetch="{start_position_with: '01', mode: 'full', hierarhy_by_position: true, response_fields: ['id','code','url_without_domain','children','title','seo_h1']}" v-slot="{item: category}">
		<BasePublishPostsWidget :fetch="{category_code: 'blog', limit: 5, response_fields: ['id', 'title', 'short_description', 'url_without_domain', 'main_image_thumbs', 'category_title', 'category_url_without_domain'], extra_fields: ['short_description']}" v-slot="{items}">
			<div class="pw homepage" v-if="items?.length">
				<div class="wrapper wrapper-pw">
					<BaseCmsLabel code="homepage_health" tag="div" class="pw-title" />
					<div class="pw-nav-container" v-if="category?.children">
						<ul class="pw-nav">
							<li v-for="categoryChildren in category.children" :key="categoryChildren.id">
								<NuxtLink :to="categoryChildren.url_without_domain">{{categoryChildren.title}}</NuxtLink>
							</li>
							<li><NuxtLink :to="category.url_without_domain"><BaseCmsLabel code="show_all_posts" /></NuxtLink></li>
						</ul>
					</div>
					<div class="pw-col pw-col1">
						<template v-for="(item,index) in items" :key="item.id">
							<PublishIndexEntry :item="item" :mode="'big3'" v-if="(index+1) == 1" />
						</template>
					</div>

					<div class="pw-col pw-col2">
						<template v-for="(item,index) in items" :key="item.id">
							<PublishIndexEntry :item="item" :mode="'small2'" v-if="(index+1) > 1" />
						</template>
					</div>
				</div>
			</div>
		</BasePublishPostsWidget>
	</BasePublishCategory>
</template>


