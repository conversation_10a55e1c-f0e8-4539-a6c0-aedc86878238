<template>
	<ul class="p-nav" v-if="rootCategory?.children">
		<li v-for="subcategory in subcategories" :key="subcategory.id">
			<NuxtLink :to="subcategory.url_without_domain">
				{{ subcategory.title }}
			</NuxtLink>
		</li>
		<li v-if="category && category.level > 1">
			<NuxtLink class="p-btn-all" :to="rootCategory?.url_without_domain">
				<BaseCmsLabel code="show_all_posts" tag="span" />
			</NuxtLink>
		</li>
	</ul>
</template>

<script setup>
	const props = defineProps(['category', 'rootCategory']);

	const subcategories = computed(() => {
		if(!props.rootCategory?.children?.length) return [];
		return props.rootCategory.children.filter(el => {
			return el.url != props.category.url;
		});
	});
</script>
