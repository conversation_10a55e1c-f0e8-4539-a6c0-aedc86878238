<template>
	<NuxtLink
		:to="item.url_without_domain"
		:class="['pp', {'pp-small pp-hover2': mode == 'small'}, {'pp-small': mode == 'small2'}, {'pp-small pp-related': mode == 'smallPublish'}, {'pp-big pp-hover2': mode == 'big'}, {'pp-big pp-big-horizontal pp-hover2': mode == 'big2'}, {'pp-big': mode == 'big3'}]">
		<figure class="pp-image">
			<BaseUiImage loading="lazy" :default="defaultImage" :data="imageData" :alt="item.main_image_description || item.title" :title="item.main_image_title" />
		</figure>
		<span class="pp-cnt">
			<span class="pp-category" v-if="item.category_url_without_domain">{{ item.category_title }}</span>
			<span class="pp-title">{{ item.title }} </span>
			<span class="pp-short-desc" v-if="item.short_description" v-html="stripHtml(item.short_description)" />
		</span>
	</NuxtLink>
</template>

<script setup>
	const {stripHtml} = useText();
	const props = defineProps(['item', 'mode']);

	const defaultImage = computed(() => {
		switch (props.mode) {
			case 'big2':
				return '/images/no-image-715.jpg';
			case 'big':
				return '/images/no-image-800.jpg';
			case 'big3':
				return '/images/no-image-800.jpg';
			case 'small':
				return '/images/no-image-260.jpg';
			case 'small2':
				return '/images/no-image-260.jpg';
			default:
				return '/images/no-image-470.jpg';
		}
	});

	const imageData = computed(() => {
		const thumbs = props.item.main_image_thumbs || {};
		switch (props.mode) {
			case 'big2':
				return thumbs['width715-height370-crop1'];
			case 'big':
				return thumbs['width810-height410-crop1'];
			case 'small':
				return thumbs['width260-height135-crop1'];
			default:
				return thumbs['width470-height240-crop1'];
		}
	});
</script>
