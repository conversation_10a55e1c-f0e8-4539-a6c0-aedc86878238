<template>
	<NuxtLink :to="item.url_without_domain" :class="['pp-recipe', {'pp-recipe-list gray-shadow': mode == 'list'}]">
		<figure class="pp-recipe-image">
			<span><BaseUiImage loading="lazy" :default="defaultImage" :data="imageData" :alt="item.main_image_description || item.title" :title="item.main_image_title" /></span>
		</figure>
		<span class="pp-recipe-cnt">
			<span class="pp-recipe-rate">
				<template v-if="item?.feedback_comment_widget && item.feedback_comment_widget.comments_status != 1">
					<FeedbackRates :rates="item.feedback_rate_widget.rates" v-if="item.feedback_rate_widget.rates_votes > 0" />
					<div class="cp-rate-counter" v-if="item.feedback_comment_widget?.comments > 0">
						<span class="pp-recipe-comments-counter">({{ item.feedback_comment_widget.comments ? item.feedback_comment_widget.comments : 0 }})</span>
					</div>
				</template>
			</span>
			<span class="pp-recipe-category" v-if="item.category_url_without_domain">{{ item.category_title }}</span>
			<span class="pp-recipe-title">{{ item.title }} </span>
			<span class="df pp-recipe-attrs" v-if="item.attributes_summary">
				<span v-for="itemSummary in item.attributes_summary" :key="itemSummary.id" class="pp-recipe-attr" :class="[{'pp-recipe-time': itemSummary.code == 'vrijeme_pripreme'}, {'pp-recipe-ingredients': itemSummary.code == 'broj_sastojaka'}]">
					<BaseCmsLabel v-if="itemSummary.code == 'vrijeme_pripreme'" code="preparation" />
					<BaseCmsLabel v-else-if="itemSummary.code == 'broj_sastojaka'" code="ingredients" />:
					<strong>{{ itemSummary.content }}</strong>
				</span>
			</span>
		</span>
	</NuxtLink>
</template>

<script setup>
	const {stripHtml} = useText();
	const props = defineProps(['item', 'mode']);

	const defaultImage = computed(() => {
		switch (props.mode) {
			case 'list':
				return '/images/no-image-260.jpg';
			default:
				return '/images/no-image-320.jpg';
		}
	});

	const imageData = computed(() => {
		const thumbs = props.item.main_image_thumbs || {};
		switch (props.mode) {
			case 'list':
				return thumbs['width260-height135-crop1'];
			default:
				return thumbs['width320-height165-crop1'];
		}
	});
</script>
