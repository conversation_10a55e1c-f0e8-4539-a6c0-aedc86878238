<template>
	<!-- Instashop , response_fields: ['id', 'title', 'short_description', 'url_without_domain', 'main_image_thumbs'] -->
	<ClientOnly>
		<BasePublishCategory :fetch="{code: 'instashop', response_fields: ['id', 'code', 'title', 'url_without_domain']}" v-slot="{item: category}">
			<BasePublishPostsWidget :fetch="{category_code: 'instashop', limit: 8, extra_data: ['images']}" v-slot="{items}">
				<div class="iw" id="instashop">
					<div class="wrapper wrapper-iw">
						<BaseCmsLabel code="instashop_title" tag="div" class="iw-title" />
						<div class="instashop-items">
							<PublishIndexEntryInstashop :items="items" />
						</div>
						<div class="iw-btns">
							<NuxtLink class="btn" :to="category.url_without_domain"><BaseCmsLabel code="show_all" tag="span" /></NuxtLink>
						</div>
					</div>
				</div>
			</BasePublishPostsWidget>
		</BasePublishCategory>
	</ClientOnly>
</template>
