<template>
	<template v-if="item.category_template == 'articles'">
		<div class="pd-header">
			<div class="pd-wrapper">
				<CmsBreadcrumbs :items="item.breadcrumbs" class="bc-pd" />
				<h1 class="pd-title">{{ item.seo_h1 }}</h1>
				<div class="pd-headline">{{ item.headline }}</div>
				<div class="pd-hero-image">
					<ClientOnly>
						<BasePublishPostsWidget :fetch="{limit: 1, sort: 'next', id_exclude: item.id, datetime_published: item.datetime_published, category_position: item.category_position_h, response_fields: ['id', 'title', 'url_without_domain']}" :watch-fetch="true" v-slot="{items}">
							<NuxtLink v-for="prevItem in items" :key="prevItem.id" class="pd-navigation pd-navigation-next" :to="prevItem.url_without_domain">
								<span
									><span>{{ prevItem.title }}</span></span
								>
							</NuxtLink>
						</BasePublishPostsWidget>
						<BasePublishPostsWidget :fetch="{limit: 1, sort: 'prev', id_exclude: item.id, datetime_published: item.datetime_published, category_position: item.category_position_h, response_fields: ['id', 'title', 'url_without_domain']}" :watch-fetch="true" v-slot="{items}">
							<NuxtLink v-for="nextItem in items" :key="nextItem.id" class="pd-navigation pd-navigation-prev" :to="nextItem.url_without_domain">
								<span
									><span>{{ nextItem.title }}</span></span
								>
							</NuxtLink>
						</BasePublishPostsWidget>
					</ClientOnly>
					<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width1230-height630-crop1']" default="/images/no-image-1230.jpg" :alt="item.main_image_description || item.seo_h1" :title="item.main_image_title" />
				</div>
			</div>
		</div>
	</template>
	<template v-else-if="item.category_template == 'recipes'">
		<div class="wrapper pd-recipe-header-wrapper">
			<div class="pd-recipe-info-container"></div>
			<CmsBreadcrumbs :items="item.breadcrumbs" class="pd-recipe-bc" />

			<div class="df aic pd-recipe-intro" v-if="item.attributes">
				<template v-for="itemAttribute in item.attributes" :key="itemAttribute.id">
					<div class="pd-recipe-category" v-if="itemAttribute.attribute_code == 'vrsta_jela'">{{ itemAttribute.title }}</div>
				</template>
				<span class="pd-recipe-rates">
					<template v-if="item?.feedback_comment_widget && item.feedback_comment_widget.comments_status != 1">
						<FeedbackRates :rates="item.feedback_rate_widget.rates" />
					</template>
				</span>

				<div class="pd-recipe-link-comments">
					<NuxtLink to="#comments"><BaseCmsLabel code="view_comments" /></NuxtLink>
					<span class="green counter"> ({{ item.feedback_comment_widget.comments ? item.feedback_comment_widget.comments : 0 }})</span>
				</div>
			</div>

			<h1 class="pd-recipe-title">{{ item.title }}</h1>
		</div>
	</template>
</template>

<script setup>
	const item = useState('publishPost');
</script>

<style scoped lang="less">
	:deep(.pd-bc) {
		a {
			&:last-of-type {
				padding-right: 0;
				margin-right: 0;
				&:after {
					display: none;
				}
			}
		}
		.bc-last {
			display: none;
		}
	}
</style>
