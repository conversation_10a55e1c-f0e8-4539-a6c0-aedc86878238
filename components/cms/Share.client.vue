<template>
	<BaseCmsShare :networks="['facebook', 'whatsapp', 'viber', 'link']" v-slot="{items, onShare}">
		<div class="share" :class="[{'pd-recipe-share': mode == 'recipesDetail', 'cd-share': mode == 'catalogDetail', 'faq-share': mode == 'faq'}]" v-if="items.length">
			<BaseCmsLabel code="share" tag="div" class="share-title ss-label" />
			<div class="share-items share-icons">
				<div v-for="network in items" :key="network" :class="['ss-item', 'ss-' + network.code, 'ss_' + network.code]" :title="network.title" @click="onShare(network), showTooltip(network)">
					<span class="share-tooltip" v-if="tooltip">{{ network }}</span>
				</div>
			</div>
		</div>
	</BaseCmsShare>
</template>

<script setup>
	const tooltip = ref(false);
	const props = defineProps(['mode']);
	let timer;
	function showTooltip(network) {
		clearTimeout(timer);

		if (network.code == 'link') {
			tooltip.value = true;
			timer = setTimeout(() => {
				tooltip.value = false;
			}, 1000);
		}
	}

	onBeforeUnmount(() => {
		clearTimeout(timer);
	});
</script>
