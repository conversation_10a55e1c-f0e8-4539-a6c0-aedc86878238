<template>
	<div class="bc" v-if="items?.length">
		<template v-if="mode == 'catalogDetail'">
			<div class="cd-bc-items" ref="bcItemsContainer">
				<template v-for="(item, index) in items" :key="item">
					<NuxtLink v-if="index != items.length - 1" :to="item.url_without_domain">{{ item.title }}</NuxtLink>
				</template>
			</div>
		</template>
		<template v-else>
			<template v-for="(item, index) in items" :key="item">
				<NuxtLink v-if="index != items.length - 1" :to="item.url_without_domain">{{ item.title }}</NuxtLink>
				<span v-else class="bc-last">{{ item.title }}</span>
			</template>
		</template>
	</div>
</template>

<script setup>
	const props = defineProps(['items', 'mode']);
	const {mobileBreakpoint} = inject('rwd');

	const bcItemsContainer = ref();
	onMounted(async () => {
		//mobile container scroll
		if (bcItemsContainer.value && mobileBreakpoint) {
			const containerWidth = bcItemsContainer.value.scrollWidth;
			const documentWidth = document.documentElement.clientWidth;

			if (containerWidth > documentWidth) {
				let scrollLeft = bcItemsContainer.value.getBoundingClientRect().right;
				bcItemsContainer.value.scrollTo({
					left: scrollLeft,
					behavior: 'smooth',
				});
			}
		}
	});
</script>