<template>
	<ClientOnly>
		<Body :class="{'body-country-select': countrySelect}" />
		<BaseWebshopShippingCountry init :ip-detect="true" default="hr" v-slot="{items: countries, submitCountry, selected, loading}">
			<div class="w-lang" :class="{'active': langActive}" ref="langShipping">
				<BaseCmsLanguages v-slot="{items, currentLanguage}">
					<div class="w-lang-span" @click="langActive = !langActive">
						<div class="btn-w-toggle">
							<span :class="'flag '+currentLanguage"></span><span>{{currentLanguage.lang}}</span>
						</div>
						<div class="delivery-to">
							<BaseCmsLabel code="shipping_country_select" /> <span class="bold2" v-if="selected">{{selected.title}}</span>
						</div>
					</div>

					<div class="w-list">
						<div class="ww-preview-close w-lang-mobile-close" @click="langActive = !langActive"></div>
						<div class="w-list-lang">
							<a v-for="item in items" :key="item.code" :class="[item.code, currentLanguage.code == item.code && 'active']" :href="item.url"><span :class="'flag '+item.code"></span>{{item.code}}</a>
						</div>

						<div class="w-delivery-change-cnt">
							<div>
								<BaseCmsLabel code="shipping_country_select" /> <span class="bold2" v-if="selected">{{selected.title}}</span>
							</div>

							<BaseCmsLabel code="change_shipping_country_btr" tag="div" class="w-delivery-change" @click="countrySelectOpen()" />
						</div>
					</div>
				</BaseCmsLanguages>
			</div>
			<WebshopShippingCountrySelect @countrySelect="countrySelect != countrySelect">
				<template #closeCountrySelect v-if="!hasPickupProducts || (hasPickupProducts && selected?.code === 'hr')">
					<div class="fancybox-item fancybox-close shipping-country-close" @click="countrySelect = !countrySelect"></div>
				</template>

				<template #submitCountrySelect v-if="!hasPickupProducts || (hasPickupProducts && selected?.code === 'hr')">
					<button class="btn btn-orange shipping-country-submit" @click="countrySelect = !countrySelect"><BaseCmsLabel code="country_select_confirm_btn" tag="span" /></button>
				</template>

				<template #btnChangeCountryBottom>
					<BaseCmsLabel code="autochange_croatia" tag="div" class="btn-autochange" @click="() => { submitCountry({code: 'hr'}); countrySelect = !countrySelect; }" />
				</template>
			</WebshopShippingCountrySelect>
		</BaseWebshopShippingCountry>
	</ClientOnly>
</template>

<script setup>
	const {onClickOutside} = useDom();
	const langActive = ref(false);
	const countrySelect = ref(false);
	const langShipping = ref(null);

	const {getCartData, updateShipping} = useWebshop();
	const parcel = computed(() => getCartData()?.parcels[0]);

	const hasPickupProducts = computed(() => {
		const cartProducts = parcel.value?.items;
		if (!cartProducts?.length) return false;
		return cartProducts.some(item => item.type == 'pickup');
	});

	function countrySelectOpen(){
		langActive.value = false;
		countrySelect.value = true;
	}
	onClickOutside(langShipping, () => {
		langActive.value = false;
	});
</script>

<style lang="less" scoped>
	.shipping-country-close{position: absolute; top: -18px; right: -18px;}
</style>
