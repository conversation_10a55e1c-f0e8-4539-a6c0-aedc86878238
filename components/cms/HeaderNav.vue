<template>
	<BaseCmsNav code="main" level-range="2" v-slot="{items}">
		<ul class="nav" v-if="items?.length" @mouseleave="resetActive()">
			<li @mouseenter="setActive(item.id)" v-for="item in items" :key="item.id" :class="[{'active': activeItem == item.id}, {'has-children': item.items?.length}]">
				<NuxtLink :to="item.url_without_domain" @click="resetActive">{{ item.title }}</NuxtLink>
				<ul class="nav-dropdown" v-if="item?.items?.length">
					<li v-for="subItem in item.items" :key="subItem.id">
						<NuxtLink :to="subItem.url_without_domain" @click="resetActive">{{ subItem.title }}</NuxtLink>
					</li>
				</ul>
			</li>
		</ul>
	</BaseCmsNav>
</template>

<script setup>
	const activeItem = ref(0);
	let delay;

	function setActive(item) {
		activeItem.value = 0;
		clearTimeout(delay);
		delay = setTimeout(() => {
			activeItem.value = item;
		}, 200);
	}

	function resetActive() {
		clearTimeout(delay);
		activeItem.value = 0;
	}
</script>
