<template>
	<div class="faq-item" :class="[{'active': active === true}, mode]">
		<div class="faq-item-title" @click="handleClick"><span class="icon-dropdown"></span> {{item.title}}</div>
		<div class="faq-item-content" v-html="item.content" v-interpolation></div>
	</div>
</template>

<script setup>
	const props = defineProps(['item', 'mode']);
	const active = ref(0);

	function handleClick() {
		if (props.mode !== 'mainFaqItem') {
			active.value = !active.value
		}
	}
</script>

<style lang="less" scoped>
	.faq-item{
		position: relative; display: flex; flex-flow: column;
		&.active{
			.faq-item-title{color: @green;}
			.icon-dropdown:after{display: none;}
			.faq-item-content{display: block;}
		}
	}
	.faq-item-title{
		position: relative; display: block; font-size: 16px; font-weight: 700; padding: 0 0 0 27px; cursor: pointer;
		@media (max-width: @m){font-size: 14px; padding-left: 22px;}
	}
	.icon-dropdown{
		position: absolute; display: flex; align-items: center; justify-content: center; width: 14px; height: 14px; left: 0; top: 6px;
		&:before{.pseudo(14px,2px); background: @green;}
		&:after{.pseudo(2px,14px); background: @green;}
		@media (max-width: @m){
			width: 12px; height: 12px; top: 4px;
			&:before{width: 12px;}
			&:after{height: 12px;}
		}
	}
	.faq-item-content{
		display: none; padding-left: 27px;
		img{display: block; width: auto; height: auto;}
	}

	//main faq page
	.mainFaqItem{
		padding-bottom: 15px;
		.faq-item-title{padding-left: 0; font-size: 18px; line-height: 24px; padding-bottom: 8px; cursor: default;}
		.icon-dropdown{display: none;}
		.faq-item-content{padding-left: 0; display: block; font-size: 16px; line-height: 24px;}
		@media(max-width: @m){
			.faq-item-title{font-size: 14px; line-height: 19px;}
			.faq-item-content{font-size: 12px; line-height: 18px;}
			:deep(p){
				padding-bottom: 12px;
			}
			:deep(ul){
				li{
					padding: 2px 0 2px 16px;
					&:before{top: 8px;}
				}
			}
		}
	}
</style>
