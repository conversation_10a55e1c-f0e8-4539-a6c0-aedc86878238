<template>
	<BaseCmsRotator
		@loadRotatorItems="onLoad"
		:fetch="{code: 'hello_bar', limit: 1, response_fields: ['id','link','url_without_domain','title','content','image_upload_path','image_thumbs','image_2_upload_path','image_2_thumbs','datetime_counter', 'title2', 'color', 'color2', 'coupon_code', 'date_active_from', 'date_active_to', 'element_hellobar_message_button_link', 'element_hellobar_message_content', 'image_3_upload_path']}"
		v-slot="{items}">
		<CmsHelloBar :items="items" v-if="items?.length" />
	</BaseCmsRotator>

	<BaseWebshopLoyalty :fetch-barcode="true" v-slot="{item, loading, onSubmit}">
		<div class="loyalty-quick" v-if="item?.active">
			<div class="btn-loyalty-header" :class="{'active': loyaltyDropdown}" @click="loyaltyDropdown = !loyaltyDropdown">
				<BaseCmsLabel code="show_loyalty_cart" tag="span" class="s" />
				<BaseCmsLabel code="hide_loyalty_cart" tag="span" class="h" />
			</div>
			<div class="loyalty-header" :class="{'active': loyaltyDropdown}">
				<img :src="item.barcode" alt="" />
				<div style="width:100%;text-align: center;letter-spacing: 0.55em;padding-left:0.55em">{{item.code}}</div>
			</div>
		</div>
	</BaseWebshopLoyalty>
</template>

<script setup>
	const loyaltyDropdown = ref(false);

	const isHidden = ref(true);
	let rotatorItemId = 0;


	function onLoad(data) {
		rotatorItemId = data?.items?.[0]?.id;
		const cookie = useCookie('hellobar_new');
		if(!cookie.value) {
			isHidden.value = false;
		}
	}

	function close() {
		const cookie = useCookie('hellobar_new', {
			maxAge: 60 * 60 * 24 * 30 // 1 month
		});
		cookie.value = 1;
		isHidden.value = true;
	}
</script>
