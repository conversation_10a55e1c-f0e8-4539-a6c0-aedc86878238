<template>
	<div class="sidebar">
		<BaseCmsNav code="cms_menu" level-range="2" v-slot="{items, currentUrl}">
			<ul class="nav-sidebar">
				<li v-for="item in items" :key="item.id" :class="{'selected': currentUrl == item.url_without_domain}">
					<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
				</li>
			</ul>
		</BaseCmsNav>
		<div class="sidebar-cnt" v-if="props.support">
			<div class="support support-sidebar">
				<BaseCmsLabel code="footer_user_support" tag="p" class="support-title" />
				<BaseCmsLabel code="support" tag="div" />
			</div>
			<BaseCmsLabel code="contact_social" tag="div" class="social social-sidebar" />
		</div>
	</div>
</template>

<script setup>
	const props = defineProps({
		support: {
			type: Boolean,
			default: true,
		},
	});
</script>
