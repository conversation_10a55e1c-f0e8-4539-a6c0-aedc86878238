<template>
	<div v-if="items?.length" class="map" id="map">
		<BaseLocationGoogleMap pin="/images/icons/pin1.svg" :locations="items" api-key="">
			<template v-slot="{item}">
				<div class="infoBox">
					<span class="image" v-if="mode != 'locationDetail'">
						<BaseUiImage :data="item.main_image_thumbs?.['width300-height140-crop1']" default="/images/no-image-140.jpg" loading="lazy" />
					</span>
					<div class="infoBox-cnt">
						<span class="title">{{ item.title }}</span>
						<span v-if="item.address" class="address" v-html="item.address"></span>
						<span v-if="item.business_hour" class="business-hour" v-html="item.business_hour" />
						<span v-if="item.contact" class="contact" v-html="item.contact" />
					</div>
				</div>
			</template>
		</BaseLocationGoogleMap>
	</div>
</template>

<script setup>
	const props = defineProps(['items', 'mode']);
	let items = props.items;
</script>

<style lang="less" scoped>
	:deep(.infoBox-window) {
		position: relative;
		left: 15px;
		top: -165px;
	}
	:deep(.address) {
		p {
			padding-bottom: 0;
		}
	}
	:deep(.business-hour) {
		img {
			max-width: 100%;
		}
	}
	:deep(.infoBox-close) {
		position: absolute;
		cursor: pointer;
		width: 30px;
		height: 30px;
		font-size: 0;
		z-index: 1;
		background: url(assets/images/icons/close.svg) no-repeat center center;
		top: -12px;
		right: 15px;
	}
	:deep(.gm-ui-hover-effect) {
		span {
			display: none !important;
		}
	}
	.l-map {
		:deep(.infoBox-window) {
			position: relative;
			left: 15px;
			top: -50px;
		}
		.infoBox {
			&:before {
				top: 38px;
			}
		}
	}
</style>
