<template>
	<BaseUtilsAppUrls v-slot="{items}">
		<header class="header">
			<div class="header-placeholder"></div>
			<div class="header-body">
				<div class="wrapper pos-r wrapper-header">
					<BaseCmsLabel code="menu" tag="span" class="m-nav-title" v-if="!categoryTitle?.length" />
					<div class="m-nav-title active" v-if="categoryTitle?.length" @click="resetMenu"><span>{{categoryTitle}}</span></div>

					<BaseCmsLogo class="logo" />
					<BaseCmsLabel code="user_support" tag="div" class="header-contact" />
					
					<CatalogHeaderCategories v-if="!mobileBreakpoint" />

					<CmsHeaderNav v-if="!mobileBreakpoint" />

					<SearchForm />

					<NuxtLink :to="items.catalog_quickorder" class="btn quick-order" v-if="!mobileBreakpoint"><span><BaseCmsLabel code="quick_order" tag="span" class="btn-label" /></span></NuxtLink>
					
					<CmsLangShipping v-if="!mobileBreakpoint" />

					<AuthUserBox />

					<CatalogWishlistWidget mode="header-wishlist" />

					<WebshopShoppingCartWidget />

					<slot name="navBtn" />

				</div>
			</div>
			<div class="sw-placeholder"></div>

			<slot name="afterHeader" />
			<!--
			<?php $this->block('after_header'); ?><?php $this->endblock('after_header'); ?>
			-->
		</header>

		<div class="m-nav">
			<CatalogHeaderCategories v-if="mobileBreakpoint" ref="resetMenuRef">
				<template #lang>	
					<CmsLangShipping v-if="mobileBreakpoint" />
				</template>
				<template #quickOrderBtn>
					<li class="m-cat-item"><NuxtLink :to="items.catalog_quickorder" class="btn quick-order"><span><BaseCmsLabel code="quick_order" tag="span" class="btn-label" /></span></NuxtLink></li>
				</template>
			</CatalogHeaderCategories>
			<CmsHeaderNav v-if="mobileBreakpoint" />
			<div class="m-nav-support">
				<BaseCmsLabel code="support_mobile" tag="div" class="m-nav-support-col" />
				<BaseCmsLabel code="contact_social" tag="div" class="social social-m-nav" />
			</div>
		</div>
	</BaseUtilsAppUrls>
</template>

<script setup>
	const route = useRoute();
	const {mobileBreakpoint} = inject('rwd');
	const categoryTitle = useState('categoryTitle');

	const resetMenuRef = ref(null);

	const resetMenu = () => {
		categoryTitle.value = 0;
		resetMenuRef.value?.resetState();
	}

	watch(
		() => route.fullPath,
		(newPath, oldPath) => {
			categoryTitle.value = 0;
		}
	)
</script>
