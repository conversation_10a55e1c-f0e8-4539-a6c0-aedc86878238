<template>
	<!-- 
	<?php
		$ga4_data = Google4::create_event('select_promotion', ['cart_info' => $shopping_cart, 'items' => [], 'promotion_info' => $hero_item]);
		$ga4_data_view = Google4::create_event('view_promotion', ['cart_info' => $shopping_cart, 'items' => [], 'promotion_info' => $hero_item]);
		?>
		<a class="slider-item" href="<?php echo $hero_item['link']; ?>" data-create_ga4_event='<?php echo $ga4_data; ?>' data-tracking_gtm_promo_view="<?php echo $hero_item['id']; ?>|<?php echo $hero_item['title']; ?>|banner<?php echo $i; ?>|slot<?php echo $i; ?>" data-tracking_gtm_promo_click="<?php echo $hero_item['id']; ?>|<?php echo $hero_item['title']; ?>|banner<?php echo $i; ?>|slot<?php echo $i; ?>">
			<picture data-ga4_events_info='<?php echo $ga4_data_view; ?>'>
				<?php if(!empty($hero_item['image_2'])): ?>
					<source srcset="<?php echo Thumb::generate($hero_item['image_2'], 760, 1500, true, 'thumb', TRUE, '/media/images/no-image-500.jpg'); ?>" media="(max-width: 700px)">
				<?php endif; ?>
				<img<?php if($i != 1): ?> loading="lazy"<?php endif; ?> data-lazy="<?php echo Thumb::generate($hero_item['image'], 1480, 550, true, 'thumb', TRUE, '/media/images/no-image-1480.jpg'); ?>" <?php echo Thumb::generate($hero_item['image'], array('width' => 1480, 'height' => 550, 'crop' => true, 'default_image' => '/media/images/no-image-1480.jpg', 'html_tag' => true)); ?> alt="" />
			</picture>
		</a>
	-->
	<BaseCmsRotator :fetch="{code: 'hero', limit: 4, response_fields: ['id','image_upload_path','image_thumbs','image_2_upload_path','image_2_thumbs', 'image_3_upload_path', 'image_3_thumbs', 'url_without_domain', 'title']}" v-slot="{items}">
		<div class="wrapper slider hi-slider" v-if="items?.length">
			<BaseUiSwiper
				:thumbs-swiper="thumbsSwiper"
				:options="{
					slidesPerView: 1,
					slidesPerGroup: 1,
					loop: true,
					effect: 'fade',
					watchSlidesProgress: true,
					fadeEffect: {
						crossFade: true
					},
					pagination: {
						enabled: true,
						clickable: true,
					},
					speed: 400,
					autoplay: {
						delay: 4000,
						disableOnInteraction: false,
					},
					on: {
						init: swiper => {
							swiperRef = swiper;
							startProgressBar();
						},
						slideChangeTransitionStart: swiper => {
							stopProgressBar();
						},
						slideChangeTransitionEnd: swiper => {
							startProgressBar();
						}
					}
				}">
				<BaseUiSwiperSlide v-for="(item, index) in items" :key="index" class="hi-item" @mouseenter="pauseSlider" @mouseleave="resumeSlider">
					<NuxtLink :to="item.url_without_domain">
						<BaseUiImage :data="item.image_thumbs?.['width1480-height550-crop1']" default="/images/no-image-1480.jpg" loading="lazy" :picture="[{maxWidth: '700px', src: item.image_2_thumbs?.['width760-height1500-crop1'].thumb, default: '/images/no-image-500.jpg'}]" />
					</NuxtLink>
				</BaseUiSwiperSlide>
			</BaseUiSwiper>

			<BaseUiSwiper
				v-if="items?.length > 1"
				class="hi-thumbs hi-thumbs-slider slider-nav"
				:options="{
					slidesPerView: 4, 
					slidesPerGroup: 4, 
					watchSlidesProgress: true, 
				}"
				@init="setThumbsSwiper">
				<BaseUiSwiperSlide v-for="(item, index) in items" :key="index" class="slider-nav-item">
					<div class="slider-nav-item-image"><BaseUiImage :data="item.image_3_thumbs?.['width100-height100-crop1']" default="/images/no-image-60.jpg" loading="lazy" /></div>
					<div class="slider-nav-item-title">{{item.title}}</div>
					<div class="slider-nav-progress"><span :style="{width: progress + '%'}" /></div>
				</BaseUiSwiperSlide>
			</BaseUiSwiper>
		</div>
	</BaseCmsRotator>

	<BaseCmsRotator :fetch="{code: 'homepage_intro_benefits', limit: 3}" v-slot="{items: benefits}">
		<div class="df wrapper benefits" v-if="benefits?.length">
			<div class="benefit" v-for="benefit in benefits" :key="benefit.id" :class="benefit.title2">
				<component :is="benefit.link ? NuxtLink : 'div'" :to="benefit.link ? benefit.url_without_domain : undefined">
					<span v-if="benefit?.image_upload_path?.length">
						<BaseUiImage :src="benefit.image_upload_path" width="84" height="26" loading="lazy" />
						{{benefit.title}}
					</span>
					<span v-html="benefit.title" v-else />
				</component>
			</div>
		</div>
	</BaseCmsRotator>

	<CatalogCategoriesWidget :extraclass="'categories c-categories'" />

	<BaseCmsRotator :fetch="{code: 'homepage_intro_benefits_last', limit: 3}" v-slot="{items: benefits}">
		<div class="df wrapper benefits benefits2" v-if="benefits?.length">
			<div class="benefit" v-for="benefit in benefits" :key="benefit.id" :class="benefit.title2">
				<component :is="benefit.link ? NuxtLink : 'div'" :to="benefit.link ? benefit.url_without_domain : undefined">
					<span v-html="benefit.title" />
				</component>
			</div>
		</div>
	</BaseCmsRotator>
</template>

<script setup>
	import { NuxtLink } from '#components';

	const progress = ref(0)
	let swiperRef = null;
	let progressInterval;
	let isPaused = false;
	let pausedAt = 0;

	const thumbsSwiper = ref(null);
	const setThumbsSwiper = (swiper) => {
		thumbsSwiper.value = swiper;
	};

	function startProgressBar() {
		clearInterval(progressInterval);

		if (!isPaused) progress.value = 0;

		// Calculate interval step to reach 100 in 4 seconds (4000ms)
		// 100 / (4000 / interval) = increment per step
		const intervalMs = 10; // Update every 10ms for smooth animation
		const incrementPerStep = 100 / (4000 / intervalMs);

		progressInterval = setInterval(() => {
			if (!isPaused) {
				progress.value += incrementPerStep;
				if (progress.value >= 100) {
					progress.value = 100; // Cap at 100%
				}
			}
		}, intervalMs);
	}

	function stopProgressBar() {
		progress.value = 0;
		clearInterval(progressInterval);
	}

	function pauseSlider() {
		if (swiperRef && swiperRef.autoplay) {
			swiperRef.autoplay.pause();
			isPaused = true;
			pausedAt = progress.value;
		}
	}

	function resumeSlider() {
		if (swiperRef && swiperRef.autoplay) {
			swiperRef.autoplay.resume();
			isPaused = false;
		}
	}

	onBeforeUnmount(() => {
		clearInterval(progressInterval);
	});
</script>

<style lang="less" scoped>
	.hi-slider{
		@media (max-width: @tp){
			:deep(.swiper-pagination){
				position: absolute; display: flex; align-items: center; justify-content: center; bottom: 30px; left: 16px; right: 16px; z-index: 1; gap: 8px;
				&>span{
					position: relative; display: block; width: 12px; height: 12px; border-radius: 50%; flex-grow: 0; flex-shrink: 0; background: #fff;
					&:after{.pseudo(12px,12px); top: 0; left: 0; border-radius: 50%; background: linear-gradient(180deg, #9baf6a 0%, #748b3b 100%); display: none;}
					&.swiper-pagination-bullet-active:after{display: block;}
				}
			}
		}
	}
	.hi-item{
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; border-radius: 3px;}
	}
	:deep(.swiper-button){top: calc(~"50% - 40px");}
	.slider-nav{
		:deep(.swiper){width: 100%;}
		:deep(.swiper-wrapper){display: flex; justify-content: flex-start;}
	}
	:deep(.slider-nav-item){
		width: 25%; display: flex; align-items: center; padding: 10px; cursor: pointer; position: relative;
		&.swiper-slide-thumb-active, &.active{
			color: #fff; .gradient(#ABC075,@green); //.gradient-green;
			.slider-nav-progress{display: block;}
		}
		img{.transition(opacity);}
		&:hover{
			img{opacity: .7;}
		}
		@media (max-width: @t){padding: 7px;}
	}
	.slider-nav-item-title{
		padding-right: 20px; line-height: 1.4;
		@media (max-width: @tp){padding-right: 0;}
	}
	.slider-nav-progress{
		position: absolute; display: none; bottom: 0; left: 0; right: 0; height: 5px; background: rgba(0,0,0,.2); overflow: hidden;
		span{display: block; height: 100%; width: 0; background: @lightGreen;}
	}
	.slider-nav-item-image{
		width: 100px; flex-grow: 0; flex-shrink: 0; margin-right: 20px;
		img{display: block; border-radius: @borderRadius; max-height: 100%; width: auto;}
		@media (max-width: @l){width: 80px;}
		@media (max-width: @t){width: 60px;}
		@media (max-width: @tp){width: 50px; margin-right: 10px;}
	}

	.benefits2{
		padding-bottom: 144px;
		@media (max-width: @tp){padding-bottom: 20px;}
	}
</style>
