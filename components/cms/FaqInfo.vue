<template>
	<BaseCmsLabel class="faq-info" code="faq_info" tag="div" />
</template>

<style scoped lang="less">
	.faq-info{
		background: url(assets/images/faq-info.jpg) no-repeat center center; position: relative; margin-top: 12px; margin-bottom: 12px; padding: 24px 40px; display: flex; justify-content: space-between; align-items: center; min-height: 112px; color: white;
		:deep(span){
			font-size: 22px; line-height: 1.4; color: @lightGreen; display: block; font-weight: bold;
		}
		:deep(.faq-info-links){
			display: flex; flex-flow: column; align-items: flex-end;
			a{
				font-size: 16px; line-height: 22px; color: white; text-decoration: underline; text-underline-offset: 2px;
				&:hover{color: @lightGreen; text-decoration-color: @lightGreen;}
			}
			a[href^=tel]{ font-weight: bold; font-size: 28px; line-height: 40px; text-decoration: none; cursor: pointer;}
		}
		@media(max-width: @m){
			padding: 13px 15px 16px; display: flex; flex-flow: column; align-items: flex-start; justify-content: unset; min-height: unset; margin: 0 15px; border-radius: 2px; background: url(assets/images/faq-info-mobile.png) no-repeat center center; background-size: cover;
			:deep(span){
				font-size: 14px; line-height: 20px; padding-bottom: 3px;
				br{display: none;}
			}
			:deep(.faq-info-links){
				align-items: flex-start; row-gap: 2px;
				a{font-size: 12px; line-height: 18px;}
				a[href^=tel]{font-size: 20px; line-height: 24px;}
			}
		}
	}
</style>
