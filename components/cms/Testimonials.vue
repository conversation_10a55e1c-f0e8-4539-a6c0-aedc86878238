<template>
	<BaseCmsRotator :fetch="{code: 'testimonials', limit: 3, response_fields: ['id','image_upload_path','image_thumbs','image_2_upload_path','image_2_thumbs','content', 'title']}" v-slot="{items}">
		<div class="testimonials" v-if="items?.length">
			<BaseCmsLanguages v-slot="{currentLanguage}">
				<div class="wrapper wrapper-testimonials">
					<div class="ts-col ts-col1">
						<BaseUiSwiper
							class="ts-items slick-arrow2"
							:options="{
							slidesPerView: 1,
							slidesPerGroup: 1,
							loop: true,
							effect: 'fade',
							fadeEffect: {
								crossFade: true
							},
							pagination: {
								enabled: true,
								clickable: true,
							},
						}">
							<BaseUiSwiperSlide v-for="item in items" :key="item.id" class="ts-item">
								<div class="ts">
									<figure class="ts-image">
										<span>
											<BaseUiImage :data="item.image_thumbs?.['width180-height180-crop1']" alt="" default="/images/no-image-50.jpg" loading="lazy" />
										</span>
									</figure>
									<div class="ts-cnt">
										<BaseUiImage :data="item.image_2_thumbs?.['width105-height20-crop1']" alt="" default="/images/no-image-50.jpg" loading="lazy" v-if="item.image_2_thumbs?.['width105-height20-crop1']" />
										<div class="ts-comment" v-if="item.content" v-html="item.content" />
										<div class="ts-name" v-if="item.title">{{item.title}}</div>
									</div>
								</div>
							</BaseUiSwiperSlide>
						</BaseUiSwiper>
					</div>
					<div class="ts-col ts-col2">
						<div class="ts-col2-cnt" v-interpolation>
							<p><img data-css="lazyload" :src="'/images/loyalty-card2-'+currentLanguage.code+'.png'" default="/images/no-image-50.jpg" width="231" height="158" alt="" /></p>
							<BaseCmsLabel code="loyalty_box" tag="div" />
						</div>
					</div>
				</div>
			</BaseCmsLanguages>
		</div>
	</BaseCmsRotator>
</template>

<style lang="less" scoped>
	.ts-col2{background: url(/assets/images/bg-green.jpg);}
</style>
