<template>
	<div class="nw nw-bottom nw-bottom-custom">
		<div class="wrapper">
			<div class="nw-col nw-col1">
				<div class="support">
					<BaseCmsLabel code="footer_user_support" tag="p" class="support-title" />
					<BaseCmsLabel code="support" tag="div" />
				</div>
				<BaseCmsLabel code="contact_social" tag="div" class="social" />
			</div>	

			<!--	<div class="nw-col nw-col2">-->
			<div class="nw-col nw-col2" :style="{backgroundImage: `url(${labelImage[0]?.url})`, backgroundPosition: 'center', backgroundSize: 'cover'}">
				<div class="nw-body">
					<BaseCmsLabel code="newsletter_widget_title" tag="div" class="nw-title" />
					<BaseCmsLabel code="newsletter_subtitle" tag="div" class="nw-cnt" />

					<BaseNewsletterSignupForm class="nw-form" v-slot="{fields, gdprFields, status}">
						<template v-if="!status?.success">
							<div class="nw-fields">
								<div class="nw-field">
									<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage}">
										<template v-if="item.type != 'hidden'">
											<BaseFormInput :id="`newsletter-${item.name}`" class="nw-input" :placeholder="labels.get('enter_email')" />
											<span class="error" v-show="errorMessage" v-html="errorMessage" />
										</template>
										<BaseFormInput v-else />
									</BaseFormField>
									<button class="btn nw-button" type="submit">
										<BaseCmsLabel code="newsletter_signup" />
									</button>
								</div>
							</div>
							<template v-if="gdprFields">
								<BaseFormField v-for="field in gdprFields" :key="field.name" :item="field">
									<BaseFormInput v-if="field.type == 'hidden'" />
									<div class="nw-gdpr nw-checkbox" v-else v-interpolation>
										<BaseFormInput :id="`nl-${field.name}`" />
										<label :for="`nl-${field.name}`">
											<span v-html="field.description"></span>
										</label>
									</div>
								</BaseFormField>
							</template>
						</template>
						<BaseCmsLabel v-show="status?.success" tag="div" class="nw-success" code="success_subscribe" />
					</BaseNewsletterSignupForm>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const labels = useLabels();
	const modal = useModal();
	const {mobileBreakpoint} = inject('rwd');
	const { extractImageUrls } = useImages();

	const labelImage = computed(() => {
		return mobileBreakpoint.value ? extractImageUrls(labels.get('newsletter_image')) : extractImageUrls(labels.get('newsletter_image'));
	})

</script>

<style lang="less" scoped>
	.wrapper{
		display: flex; width: 100%;
		@media (max-width: @m){flex-flow: column;}
	}
</style>