<template>
	<BaseWebshopCouponForm v-slot="{onSubmit, onRemove, activeCoupon}">
		<div class="hello-bar hello-bar-live" :style="(mobileBreakpoint) ? styleMobile : styleDesktop" :class="{'active': activeCoupon?.code == item?.coupon_code, 'hello-bar-clear': item?.coupon_code == null && counterEnds == null, 'hello-bar-none': item?.coupon_code != null && b2b}">
			<div class="wrapper">
				<div class="heb-image" v-if="item?.image_2_upload_path">
					<BaseUiImage loading="lazy" :data="item.image_2_thumbs?.['width205-height120']" default="/images/no-image-205.jpg" />
				</div>

				<div class="heb-content" :class="{'heb-content-noimage': !item.image_2_upload_path}" :style="customColor">
					<template v-if="item.link?.length">
						<NuxtLink :to="item.link" :style="customColor">
							<div class="heb-title" v-html="item.title" />
							<div class="heb-subtitle" v-html="item.title2" />
						</NuxtLink>
					</template>
					<template v-else>
						<div class="heb-title" v-html="item.title" />
						<div class="heb-subtitle" v-html="item.title2" />
					</template>
				</div>

				<div class="heb-right-container" v-if="item?.coupon_code">
					<div class="activated-coupon-note" v-html="item.element_hellobar_message_content" v-if="item.element_hellobar_message_content?.length" :style="customColor" />
					<button class="btn btn-blue hellobar-coupon-btn" @click="activeCoupon ? onRemove() : onSubmit({code: item.coupon_code})">
						<BaseCmsLabel code="hellobar_coupon_title_inactive" tag="span" class="i" />
						<BaseCmsLabel code="hellobar_coupon_title_active" tag="span" class="a" />
					</button>
				</div>

				<template v-if="counterEnds?.length">
					<BaseUiCountdown :end="counterEnds" v-slot="{days, hours, minutes, seconds, ended}">
						<div class="heb-right-timer" v-if="!ended" :style="customColor">
							<div class="heb-right">
								<div v-if="days && days > 1" class="day">
									<span>{{ days }}</span> d
								</div>
								<div class="hour">
									<span>{{ hours }}</span> h
								</div>
								<div class="min">
									<span>{{ minutes }}</span> m
								</div>
								<div class="sec">
									<span>{{ seconds }}</span> s
								</div>
							</div>
						</div>
					</BaseUiCountdown>
				</template>
			</div>
		</div>
	</BaseWebshopCouponForm>
</template>

<script setup>
	const props = defineProps(['items']);
	const {b2b} = useProfile();
	const {mobileBreakpoint} = inject('rwd');
	const {absolute} = useUrl();
	const item = props.items[0];

	const styleDesktop = computed(() => {
		if (item.image_upload_path) {
			return {
				backgroundImage: `url(${absolute(item.image_upload_path)})`,
				backgroundRepeat: 'no-repeat',
			}
		} else if (item.color2 && item.color2?.length === 7) {
			return { background: item.color2, }
		} else {
			return {}
		}
	});

	const styleMobile = computed(() => {
		if (item.image_3_upload_path) {
			return {
				backgroundImage: `url(${absolute(item.image_3_upload_path)})`,
				backgroundRepeat: 'no-repeat',
			}
		} else if (item.color2 && item.color2?.length === 7) {
			return { background: item.color2, }
		} else {
			return {}
		}
	});

	const customColor = computed(() => {
		if (item.color && item.color?.length === 7) {
			return { color: item.color, }
		} else {
			return {}
		}
	});

	const counterEnds = computed(() => {
		if (item.date_active_to) {
			return item.date_active_to;
		} else {
			return {}
		}
	});

	item.date_active_to

	/*
	const helloBarStyle = computed(() => {
		if (item.image_upload_path) {
			if(!mobileBreakpoint){
				console.log('1')
				return {
					backgroundImage: `url(${absolute(item.image_upload_path)})`,
					backgroundRepeat: 'no-repeat',
				}
			}else{
				console.log('1')
				return {
					backgroundImage: `url(${absolute(item.image_3_upload_path)})`,
					backgroundRepeat: 'no-repeat',
				}
			}
		} else if (item.color2 && item?.length === 7) {
			return { background: item.color2, }
		} else {
			return {}
		}
	})
	*/
</script>
