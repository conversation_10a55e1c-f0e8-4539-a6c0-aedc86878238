<template>
	<BaseThemeUiLoading :class="[props.size, props.color]" />
</template>

<script setup>
	const props = defineProps({
		size: String,
		color: String
	})
</script>

<style scoped lang="less">
	.inline{
		:deep(svg){display: block; margin: auto; width: 30px; height: 30px;}
	}
	.small{
		:deep(svg){width: 18px; height: 18px;}
	}
	.medium{
		:deep(svg){width: 25px; height: 25px;}
	}
	.white{
		:deep(svg) path{fill: #fff;}
	}
</style>
