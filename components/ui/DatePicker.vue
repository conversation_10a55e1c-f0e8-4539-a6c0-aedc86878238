<template>
	<slot :selectedDate="date" />
	<VueDatePicker v-model="date" locale="hr-HR" :format="format" :uid="uid || null" auto-apply hide-input-icon :enable-time-picker="false" :min-date="minDateRange" :max-date="maxDateRange" :clearable="false" :disabled="value != null" prevent-min-max-navigation hide-offset-dates />
</template>

<script setup>
	import { ref, computed } from 'vue';
	import VueDatePicker from '@vuepic/vue-datepicker';
	import '@vuepic/vue-datepicker/dist/main.css';

	const props = defineProps(['minDate', 'maxDate', 'uid', 'value', 'format']);

	// Inicijaliziraj date s Date objektom
	const date = ref(props.value);

	// Funkcija za formatiranje za prikaz u datepickeru
	const format = (date) => {
		const day = date.getDate().toString().padStart(2, '0');
		const month = (date.getMonth() + 1).toString().padStart(2, '0');
		const year = date.getFullYear();
		return `${day}.${month}.${year}.`;
	};

	// Funkcija za parsiranje hrvatskog datuma u Date objekt (koristi se za min/max)
	function parseHrDate(str) {
		const [day, month, year] = str.split('.');
		return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
	}

	const minDateRange = computed(() => {
		if (props.minDate) {
			const date = parseHrDate(props.minDate);
			date.setDate(date.getDate() + 1);
			return date;
		}
		return new Date(1900, 0, 1);
	});

	const maxDateRange = computed(() => {
		if (props.maxDate) {
			return parseHrDate(props.maxDate);
		}
		return new Date();
	});
</script>

<style lang="less">
	:root{
		--dp-font-family: @font;
	}
	#dp-menu-birthday{border: unset; box-shadow: 0 5px 20px 0 rgba(0, 0, 0, 0.15);}
	.dp--menu-wrapper{left: 0 !important;}
	.dp__menu_inner{padding: 12px;}
	.dp__month_year_select{
		border: 1px solid @borderColor; border-radius: 2px 0 0 2px; margin-left: 7px; height: 32px; font-size: 14px; line-height: 20px; padding: 0 16px !important; justify-content: flex-start; position: relative;
		&:before{.pseudo(auto,auto); color: @lightGreen; .icon-arrow-down; font: 7px/7px @fonti; right: 12px; top: 12px;}
		&:last-child{border-radius: 0 2px 2px 0; margin-right: 7px; margin-left: -1px;}
		&:hover{background: white !important;}
	}
	.dp__arrow_top,.dp__arrow_bottom{background: white; z-index: 20; border: unset; left: 25px; width: 8px; height: 8px;}
	.dp--arrow-btn-nav{width: 32px; height: 32px; flex-shrink: 0; background: @lightGreen; border-radius: @borderRadius;}
	.dp__inner_nav{
		width: 32px; height: 32px; border-radius: @borderRadius;
		svg{width: 17px; height: 17px;}
		&:hover{background: @green;}
		.dp__icon{stroke: white; fill: white;}
	}
	.dp--header-wrap{padding-bottom: 10px;}
	.dp__calendar_header_separator{display: none;}
	.dp__calendar_header_item{
		text-transform: uppercase;
		font-size: 0;
		&:first-letter{font-size: 14px;}
	}
	.dp__theme_light {
	    --dp-primary-color: @green;
	}
	.dp__cell_inner{border-radius: unset; width: 32px; height: 32px; font-size: 14px;}
	.dp__range_end, .dp__range_start, .dp__active_date{background: @lightGreen;}
	.dp__icon{stroke: @red; fill: @red;}
	.dp__clear_icon{color: @red; right: 33px;}
	.dp__input {font-size: 14px; padding: 0 52px 0 22px; border-radius: 0; background: url(assets/images/icons/calendar2.svg) no-repeat center right +22px;}
</style>

<style lang="less" scoped>
	:deep(button){
		padding: 0; .transition(background);
		&:after{display: none;}
		&:hover{background: @green;}
	}
</style>
