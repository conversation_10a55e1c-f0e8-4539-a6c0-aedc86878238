<template>
	<div class="cd-tab-info-body cd-tab-body-warehouse flyout cd-flyout" data-flyout="stores">
		<slot name="flyoutClose" />
		<BaseCmsLabel code="cd_availability_title" tag="div" class="cd-flyout-title" />
		<div class="cd-flyout-content" id="cdStores">
			<BaseCmsLabel code="cd_availability_info" tag="div" class="cd-flyout-content-intro" />
			<div class="cd-stores">
				<template v-for="location in items" :key="location.id">
					<CatalogFlayoutStoreItem :item="location" />
				</template>
			</div>

			<BaseCmsLabel code="item_stores_note" tag="div" class="cd-store-note special" v-if="labels.labels?.item_stores_note" />

			<div class="cd-flyout-bottom" v-if="labels.labels?.flyout_close">
				<BaseCmsLabel code="flyout_close" tag="div" class="cd-flyout-close-label" />
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['items']);
	const labels = useLabels();
</script>
