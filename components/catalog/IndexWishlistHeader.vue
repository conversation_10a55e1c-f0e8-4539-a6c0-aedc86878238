<template>
	<ClientOnly>
		<BaseCatalogWishlist v-slot="{items, onRemove}">
			<div class="wrapper wishlist-header">
				<h1 class="wishlist-title">
					<BaseCmsLabel code="wishlist" />
					<span class="wishlist-title-counter">
						<span class="wishlist_count" :class="{'active': items?.length}"> {{items?.length}} </span>
					</span>
				</h1>

				<div v-if="items?.length > 0" @click="onRemove" class="btn btn-white btn-wishlist-delete">
					<BaseCmsLabel tag="span" code="wishlist_delete" />
				</div>
			</div>
		</BaseCatalogWishlist>
	</ClientOnly>
</template>

<script setup>
	/* function onLoadedProducts(products){
		if(products?.items.length){
			sendProductImpressions(products.items, "wishlist");
		}
	} */
</script>
