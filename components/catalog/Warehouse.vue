<template>
	<Body :class="{'flyout-active': flyoutActive}" />
	<template v-if="webshopOnly">
		<BaseCmsLabel code="webshop_only_product" tag="div" class="cd-webshop-only" />
	</template>
	<template v-else>
		<div class="cd-stores-availability cd-flyout-btn" @click="flyout('stores')"><BaseCmsLabel code="warehouses_availability_status" tag="span" /></div>
		<CatalogFlayoutStores :items="warehouses" :class="{'active': flyoutActive}">
			<template #flyoutClose>
				<div class="cd-flyout-close" @click="flyout('stores')"></div>
			</template>
		</CatalogFlayoutStores>
	</template>
</template>

<script setup>
	const props = defineProps(['warehouses', 'warehousesIDs', 'item']);
	const {getInfo} = useInfo();

	const flyoutActive = ref(false);
	function flyout(el){
		console.log(el);
		flyoutActive.value = !flyoutActive.value;
	}

	const webshopOnly = ref(false);
	const webshopOnlyId = '3';
	onMounted(() => {
		const warehousesData = props.warehousesIDs;
		const warehouses = ref(
			Object.fromEntries(
				warehousesData
				.split(',')
				.map(pair => {
					const [key, value] = pair.split('=');
					return [key, Number(value)];
				})
				.filter(([, value]) => !isNaN(value))
			)
		);

		webshopOnly.value = (() => {
			const inStock = Object.entries(warehouses.value).filter(([, qty]) => qty > 0);
			return inStock.length === 1 && inStock[0][0] === "3";
		})();
	})
</script>
