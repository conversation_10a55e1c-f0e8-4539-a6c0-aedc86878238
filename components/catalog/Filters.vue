<template>
	<div class="cf" v-if="searchFields.length">
		<BaseCatalogActiveFilters v-slot="{items, onRemove}">
			<div class="cf-items" :class="[{active: items?.length}]">
				<BaseCatalogFilterItem v-for="(filter,index) in searchFields" :key="filter.id" :item="filter" v-slot="{fields, onFilter, onClear, selectedFilters, active, onToggle, onSearch}">
					<CatalogFilter :filter="filter" :index="index" :fields="fields" :onFilter="onFilter" :onClear="onClear" :selectedFilters="selectedFilters" :active="active" :onToggle="onToggle" :onSearch="onSearch" />
				</BaseCatalogFilterItem>
				<div class="cf-btns">
					<div class="btn btn-gray btn-m-filter btn-m-cf-active-clear cf-btn-clear" v-if="items?.length" @click="onRemove(), $emit('closeFilters');"><BaseCmsLabel code="clear_active_filters" /></div>
					<div class="btn cf-btn-bottom cf-btn-confirm" @click="$emit('closeFilters');">
						<span>{{labels.get('filters_confirm').replace('%VALUE%', totalProducts)}}</span>
					</div>
				</div>
			</div>
		</BaseCatalogActiveFilters>
	</div>
</template>

<script setup>
	const {onClickOutside} = useDom();
	const props = defineProps(['totalProducts', 'searchFields', 'contentType', 'selectedFilters']);
	const emit = defineEmits(['closeFilters']);
	const labels = useLabels();
</script>

<style lang="less" scoped>
	.cf-items{
		display: flex; align-items: center; gap: 8px; justify-content: flex-start;
		@media (max-width: @t){flex-wrap: wrap;}
		@media (max-width: @tp){flex-flow: column; align-items: flex-start; justify-content: flex-start; gap: 0;}
	}
</style>
