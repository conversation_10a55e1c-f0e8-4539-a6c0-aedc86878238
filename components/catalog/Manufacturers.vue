<template>
	<BaseCatalogManufacturers :fetch="{limit: 6, sort: 'position', special: true, response_fields: ['id', 'url_without_domain', 'title', 'main_image_thumbs']}" v-slot="{items}">
		<div class="brands" :class="extraClass" v-if="items?.length">
			<div class="wrapper wrapper-brands">
				<div class="brands-items">
					<template v-for="item in items" :key="item.id">
						<NuxtLink :to="item.url_without_domain" class="brand-item">
							<span v-if="item?.main_image_thumbs">
								<BaseUiImage :data="item.main_image_thumbs?.['width120-height64']" :alt="item.title" default="/images/no-image-50.jpg" />
							</span>
							<span v-else>{{item.title}}</span>
						</NuxtLink>
					</template>

					<template v-if="mode == 'homepage'">
						<BaseUtilsAppUrls v-slot="{items: urls}">
							<NuxtLink :to="urls.manufacturer" class="brand-item all-brands"><BaseCmsLabel code="all_manufacturers" /></NuxtLink>
						</BaseUtilsAppUrls>
					</template>
				</div>
			</div>
		</div>
	</BaseCatalogManufacturers>
</template>

<script setup>
	const props = defineProps({
		mode: String,
		extraClass: String,
	});
</script>
