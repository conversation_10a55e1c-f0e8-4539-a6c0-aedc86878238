<template>
	<div class="cd-related-package" :class="{'cd-related-flavor': list == 'relatedFlavor'}">
		<BaseCmsLabel code="related_package" tag="div" class="cd-related-package-title" v-if="list == 'relatedPackage'" />
		<BaseCmsLabel code="related_flavor" tag="div" class="cd-related-package-title" v-else />

		<div class="cd-related-package-items">
			<template v-for="item in items" :key="item.id">
				<NuxtLink :to="item.url_without_domain" class="cd-related-package-item">
					<div class="cd-related-package-item-inner">
						<template v-if="list == 'relatedFlavor'">
							<div class="cd-related-package-image">
								<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width120-height120']" default="/images/no-image-60.jpg" :title="item.main_image_title" :alt="item.main_image_description || item.title" />
							</div>
							<template v-if="item.attributes?.some(attr => attr.attribute_code === 'okus')">
								<template v-for="attr in item.attributes" :key="attr.id">
									<div class="cd-related-package-unit" v-if="attr.attribute_code == 'okus'">{{attr.title}}</div>
								</template>
							</template>
						</template>
						<template v-else>
							<div class="cd-related-package-unit">{{item.unitd_qty}} {{item.unitd_unit}}</div>
						</template>

						<template v-if="item.attributes_special?.some(attr => attr.code === 'organic') && list != 'relatedFlavor'">
							<template v-for="attr in item.attributes_special" :key="attr.id">
								<div class="cd-related-package-attr cd-related-package-attr-organic" v-if="attr.code == 'organic'">{{attr.title}}</div>
							</template>
						</template>

						<div class="cd-related-package-price" v-if="b2b">
							<div class="cd-related-main-price"><BaseUtilsFormatCurrency :price="item.price_b2b_custom" /></div>
						</div>
						<div class="cd-related-package-price" v-else>
							<WebshopLoyalty v-slot="{loyalty}">
								<template v-if="loyalty && item?.loyalty_price < item?.basic_price && item.type != 'coupon'">
									<div class="cd-related-main-price">
										<span class="cd-related-package-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
										<span class="cd-related-package-discount-price"><BaseUtilsFormatCurrency :price="item.loyalty_price" /></span>
									</div>
								</template>
								<template v-else-if="item.discount_percent_custom > 0 || item.price_custom < item.basic_price">
									<div class="cd-related-main-price">
										<span class="cd-related-package-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
										<span class="cd-related-package-discount-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></span>
									</div>
								</template>
								<div class="cd-related-package-current-price" v-else>
									<div class="cd-related-main-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
								</div>
							</WebshopLoyalty>
						</div>
					</div>
				</NuxtLink>
			</template>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['items', 'list', 'b2b']);
</script>
