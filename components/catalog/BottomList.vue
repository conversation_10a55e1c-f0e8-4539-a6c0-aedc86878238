<template>
	<div class="c-bottom" v-if="items?.length">
		<div class="wrapper">
			<BaseCmsLabel code="catalog_bottom_list_title" tag="div" class="c-bottom-title" />
			<BaseUiSwiper
				class="bottom-list-items slick-arrow3"
				:class="{'center': items.length < 5}"
				:options="{
					slidesPerView: 1,
					slidesPerGroup: 1,
					spaceBetween: 0,
					watchSlidesProgress: true,
					enabled: false,
					breakpoints: {
						900: {
							enabled: true,
							spaceBetween: -1,
							slidesPerView: 5,
							slidesPerGroup: 1,
							watchSlidesProgress: true,
						},
						1250: {
							enabled: true,
							spaceBetween: -1,
							slidesPerView: 5,
							slidesPerGroup: 1,
							watchSlidesProgress: true,
						}
					}
				}">
				<BaseUiSwiperSlide v-for="item in items" :key="item.id">
					<CatalogIndexEntry :item="item" itemListId="catalog_special_title" />
				</BaseUiSwiperSlide>
			</BaseUiSwiper>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['relatedItems', 'categoryPosition', 'limit']);
	let items = props?.relatedItems ?? [];
	const categoryPosition = props?.categoryPosition;

	if(items?.length){
		items = items
		.filter(item => {
			const itemCategoryPosition = String(parseInt(item.category_position_h)).padStart(2, '0');
			return itemCategoryPosition === categoryPosition;
		})
		.slice(0, props.limit);
	}
</script>

<style lang="less" scoped>
	.c-bottom{
		position: relative; display: block; background: url(assets/images/bg.jpg); padding: 56px 0 220px; margin-bottom: -133px; overflow: hidden;
		@media (max-width: @l){padding-top: 60px;}
		@media (max-width: @t){padding-left: 30px; padding-right: 30px; margin-bottom: -80px; padding-bottom: 136px;}
		@media (max-width: @tp){padding: 32px 0 25px; max-width: 100%; overflow: hidden; margin-bottom: 0;}
	}
	.c-bottom-title{
		display: block; text-align: center; font-size: 32px; font-weight: bold; padding: 0 0 30px;
		@media (max-width: @t){font-size: 26px; padding-bottom: 20px;}
		@media (max-width: @tp){font-size: 22px; padding-bottom: 15px;}
		@media (max-width: @m){font-size: 20px; text-align: left; padding-left: 16px; padding-bottom: 12px;}
	}
	.bottom-list-items{
		:deep(.swiper){overflow: inherit;}
		:deep(.swiper-wrapper){padding: 1px; box-sizing: border-box;}
		:deep(.swiper-slide){
			opacity: 0; visibility: hidden; flex-grow: 1; flex-shrink: 0; display: flex; height: auto;
			&.swiper-slide-visible{
				opacity: 1; visibility: visible;
				&.swiper-slide-prev{opacity: 0; visibility: hidden;}
			}
			@media (min-width: @h){
				&:hover{z-index: 1; position: relative;}
			}
		}
		:deep(.cp){width: 100%;}
		&.center{
			:deep(.swiper-wrapper){display: flex; justify-content: center;}
			:deep(.swiper-slide){flex-grow: 0;}
			:deep(.cp-cnt), :deep(.cp-price){text-align: left;}
		}
		@media (max-width: 1670px){
			:deep(.swiper-button-prev){left: -25px;}
			:deep(.swiper-button-next){right: -25px;}
		}
		@media (max-width: @t){
			:deep(.cp-addtocart){box-shadow: none;}
			:deep(.swiper-wrapper){justify-content: flex-start;}
			&.center{
				:deep(.swiper-wrapper){justify-content: flex-start;}
				:deep(.swiper-slide){flex-grow: 0;}
			}
		}
		@media (max-width: @m){
			width: 100%;
			:deep(.swiper-navigation){display: none;}
			:deep(.swiper-wrapper){padding: 1px 16px; overflow-x: auto;}
			:deep(.swiper-slide){opacity: 1; visibility: visible; flex-grow: 0; flex-shrink: 0; width: 152px!important; margin-left: -1px;}
			:deep(.cp){
				.cp-btn-addtocart span:before{display: block;}
				.cp-cnt{text-align: left;}
				.cp-price{text-align: left;}
			}
		}
	}
</style>
