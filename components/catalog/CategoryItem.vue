<template>
	<li :class="{'active': activeItem}" @mouseenter="!mobileBreakpoint && openItem()" @mouseleave="!mobileBreakpoint && closeItem()" >
		<BaseUiLink native :to="item.url_without_domain" :prevent-default="(mobileBreakpoint && item.children?.length) ? true : false" @click="mobileBreakpoint && toggleItem()" class="category-title" >
			<span class="category-image" v-if="item.main_image">
				<BaseUiImage :src="item.main_image" loading="lazy" width="100" height="100" :alt="item.title" default="/images/no-image-100.jpg" />
			</span>
			<span class="category-title-link">{{item.title}}</span>
			<span class="toggle-icon category-toggle-icon"></span>
		</BaseUiLink>
		<ul class="category-subnav" v-if="item?.children?.length">
			<li v-for="subItem in item.children" :key="subItem.id">
				<NuxtLink :to="subItem.url_without_domain">{{subItem.title}}</NuxtLink>
			</li>
			<li class="subcategory-new" v-if="item?.total_new > 0"><NuxtLink :to="item.url_without_domain + '?new=1'"><BaseCmsLabel code="new" /></NuxtLink></li>
			<li class="subcategory-sale" v-if="item?.total_discount > 0"><NuxtLink :to="item.url_without_domain + '?discount=1'"><BaseCmsLabel code="sale" /></NuxtLink></li>
			<li class="m-all-products"><NuxtLink :to="item.url_without_domain"><BaseCmsLabel code="show_all" /></NuxtLink></li>
		</ul>
	</li>
</template>

<script setup>
	const props = defineProps(['item']);
	const {mobileBreakpoint} = inject('rwd');

	const activeItem = ref(false);

	function openItem(){
		activeItem.value = true;
	}
	function closeItem(){
		activeItem.value = false;
	}
	function toggleItem() {
		activeItem.value = !activeItem.value;
	}
</script>
