<template>
	<!--
		<BaseCatalogProductsWidget :fetch="{related_code: 'related', related_item_id: item.id, limit: 12, related_widget_data: item.related_widget_data}" v-slot="{items: relatedItems}">
		</BaseCatalogProductsWidget>
	-->
	<template v-for="list in lists" :key="list.id">
		{{list}}
	</template>
	
</template>

<script setup>
	const props = defineProps(['lists']);

	const lists = props?.lists;
	onMounted(() => {
		lists.forEach((item, index) => {
			console.log(item)
			// Do something with each item here
		})
	})
</script>
