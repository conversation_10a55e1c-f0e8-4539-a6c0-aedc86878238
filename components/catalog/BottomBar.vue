<template>
	<div class="fixed-footer">
		<div class="cd-header">
			<div class="cd-container cd-container-header">
				<div class="cd-mini-image"><BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width120-height120']" default="/images/no-image-70.jpg" /></div>
				<h1 class="cd-title">{{item.title}}</h1>

				<CatalogDetailPrice :item="item" :b2b="b2b" />

				<div class="cd-add-container" :class="{'not-available': !item.is_available}">
					<WebshopAddToCartContainer :item="item" mode="catalogDetail" />
					<CatalogSetWishlist :item="item" mode="detail" />
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['item', 'b2b']);
</script>
