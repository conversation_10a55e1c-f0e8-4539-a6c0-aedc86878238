<template>
	<div class="cd-relate cd-related-inspiration" v-if="existInInstashop">
		<div class="cd-container">
			<div class="cd-related-posts" id="inspiration">
				<BaseCmsLabel code="instashop_related" tag="div" class="subtitle related-title" />
				<div class="cd-related-instashop-items">
					<BaseUiSwiper
						class="instashop-items-slider"
						:options="{
						slidesPerView: 1,
						slidesPerGroup: 1,
						loop: true,
						effect: 'fade',
						fadeEffect: {
							crossFade: true
						},
						pagination: {
							enabled: true,
							clickable: true,
						},
					}">
						<BaseUiSwiperSlide v-for="instashopItem in matchingImagemaps" :key="instashopItem.id" class="instashop-item">
							<div class="instashop-item-top">
								<div class="instashop-item-image">
									<template v-for="(imagemap,index) in [...instashopItem.images[0].imagemaps].sort((a, b) => a.id - b.id)" :key="imagemap.id">
										<span class="instashop-point pd-instashop-point" :style="{ top: `${imagemap.posRelative.y}%`, left: `${imagemap.posRelative.x}%` }">
											<span class="instashop-num pd-instashop-point-num"
												><span>{{index+1}}</span></span
											>
										</span>
									</template>
									<BaseUiImage :data="instashopItem.main_image_thumbs?.['width740-height740']" default="/images/no-image-500.jpg" loading="lazy" />
								</div>
								<div class="instashop-item-bottom">
									<BasePublishPostsWidget :fetch="{related_item_id: instashopItem.id, related_code: 'related', limit: 1}" v-slot="{items: publish}">
										<PublishIndexEntryRecipes :item="publish[0]" v-if="publish[0]?.length" />
									</BasePublishPostsWidget>
									<CatalogDetailInstashopProducts :items="instashopItem.images[0].imagemaps" />
								</div>
							</div>
						</BaseUiSwiperSlide>
					</BaseUiSwiper>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['instashop', 'itemId']);

	const existInInstashop = ref(false);
	const matchingImagemaps = [];
	const productsIds = [];

	if (props?.instashop?.length) {
		props.instashop.forEach(item => {
			item?.images?.forEach(image => {
				image?.imagemaps?.forEach(imagemapItem => {
					if (imagemapItem?.id === props.itemId) {
						existInInstashop.value = true;
						matchingImagemaps.push(item);
					}
				})
			})
		})
	}
</script>

<style lang="less" scoped>
	.instashop-item{display: flex; flex-flow: column;}
	.instashop-item-image{
		position: relative; display: flex; align-items: center; justify-content: center; line-height: 0;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%;}
	}
	.instashop-item-bottom{position: relative; display: flex; flex-flow: column;}
	:deep(.pp-recipe){border: none; border-radius: 0; flex-flow: row; padding: 16px 0; align-items: center;}
	:deep(.pp-recipe-image){padding: 0; width: 260px; flex-grow: 0; flex-shrink: 0; margin-right: 30px;}
	:deep(.pp-recipe-cnt){padding: 0;}
	:deep(.pp-recipe-attrs){padding-top: 13px; justify-content: flex-start; gap: 40px;}

	.instashop-items-slider{
		:deep(.swiper-button){
			width: 54px; height: 54px; background: #fff; border-radius: @borderRadius; box-shadow: 0 15px 20px rgba(0,0,0,0.2); top: 14%; cursor: pointer;
			&:before{color: @textColor; font-size: 20px; line-height: 1;}
		}
		:deep(.swiper-button-prev){left: -27px;}
		:deep(.swiper-button-next){right: -27px;}
		@media (max-width: 1160px){
			:deep(.swiper-button){top: 12%;}
		}
		@media (max-width: @tp){
			:deep(.swiper-button){top: 9%;}
		}
		@media (max-width: @m){
			:deep(.swiper-button){top: calc(~"50vw - 22px"); width: 44px; height: 44px;}
			:deep(.swiper-button-prev){left: 16px;}
			:deep(.swiper-button-next){right: 16px;}
		}
	}
</style>
