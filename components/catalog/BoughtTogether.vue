<template>
	<BaseCatalogBoughtTogether :items="props.items" :meta="props.meta" v-slot="{total, saving, addToCartData}" v-model="checkedItems">
		<!-- FIXME INTEG DK provjeriti kod ispod
			<div id="product_special_list_recommendation" class="items-list" data-tracking_gtm_bl_list="1">
				<div class="catalog-product-related-labels">
					<?php foreach ($list_recommendation as $related_item): ?>
						<span style="display: none;" data-tracking_gtm_bl="<?php echo $related_item['shopping_cart_code'] . '|' . $related_item['id'] . '|' . trim($related_item['title']) . '|' . trim($related_item['category_title']); ?>"></span>
						<?php $recommendation_total_selected += $related_item['price_custom']; ?>
						<?php $recommendation_products_selected += 1; ?>
						<?php $recommendation_products_total += 1; ?>
						<?php $ga4_data = Google4::create_event('select_item', ['cart_info' => $shopping_cart, 'items' => [$related_item]]); ?>
						<?php echo View::factory('catalog/index_entry_single', ['item' => $related_item, 'item_id' => $item['id'], 'product_priorities' => $product_priorities, 'class' => 'cp-bought-together no-shadow', 'mode' => 'bought_together', 'list' => strip_tags(Arr::get($cmslabel, 'bought_together')), 'recommendation_has_discount' => $recommendation_has_discount, 'ga4_data' => $ga4_data]); ?>
					<?php endforeach; ?>
				</div>
			</div>
		-->
		<div class="catalog-product-related-labels">
			<article v-for="(item, index) in items" :key="item.id" class="cp cp-list cp-bought-together no-shadow" :class="{'cp-unavailable': !item.is_available}">
				<div class="cp-col cp-col1">
					<div class="cp-brand" v-if="item?.manufacturer_id?.length">
						<BaseUiImage loading="lazy" :src="item.manufacturer_main_image_upload_path" default="/images/no-image-50.jpg" />
					</div>

					<div class="cp-image">
						<div class="cp-badges" v-if="item.id == current && meta?.extra?.related_discount_percent_general && meta.extra.related_discount_percent_general > 0">
							<div class="cp-badge cp-badge-discount">-{{meta.extra.related_discount_percent_general}}%</div>
						</div>

						<NuxtLink :to="item.url_without_domain" class="cp-main-image">
							<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width300-height295']" default="/images/no-image-300.jpg" :title="item.main_image_title" :alt="item.main_image_description || item.title" />
						</NuxtLink>
					</div>
				</div>

				<div class="cp-col cp-col2">
					<div class="cp-cnt">
						<ClientOnly>
							<!-- Rating -->
							<template v-if="item?.feedback_comment_widget && item.feedback_comment_widget.comments_status != 1">
								<div class="cp-rate cp-rate-cnt">
									<FeedbackRates :rates="item.feedback_rate_widget.rates" mode="cp-rates" v-if="item.feedback_rate_widget.rates_votes > 0" />
									<div class="cp-rate-counter" v-if="item.feedback_comment_widget?.comments > 0">
										<span class="num">({{ item.feedback_comment_widget.comments ? item.feedback_comment_widget.comments : 0 }})</span>
									</div>
								</div>
							</template>
						</ClientOnly>

						<NuxtLink :to="item.category_url_without_domain" class="cp-category">{{ item.category_title }}</NuxtLink>
						<div class="cp-title">
							<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
						</div>
					</div>

					<div class="cp-footer">
						<div class="cp-price">
							<template v-if="b2b">
								<div class="cp-current-price"><BaseUtilsFormatCurrency :price="item.price_b2b_custom" /></div>
							</template>
							<template v-else>
								<WebshopLoyalty v-slot="{onSubmit, newIsActive, loading, loyalty}">
									<template v-if="loyalty?.active && item.type != 'coupon'">
										<div class="old-price cp-old-price">
											<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
										</div>
										<div class="current-price red discount-price cp-discount-price" v-if="item.discount_percent > loyalty.discount_percent"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
										<div class="current-price red discount-price cp-discount-price" v-else><BaseUtilsFormatCurrency :price="item.loyalty_price" /></div>

										<div class="lowest-price cp-lowest-price" v-if="mode != 'instashop'">
											<template v-if="item.extra_price_lowest > 0">
												<BaseCmsLabel code="lowest_price" />: <strong><BaseUtilsFormatCurrency :price="item.extra_price_lowest" /></strong>
											</template>
										</div>
									</template>
									<template v-else>
										<template v-if="item.price_custom > 0">
											<template v-if="item.discount_percent_custom > 0">
												<div class="old-price cp-old-price">
													<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
												</div>
												<div class="current-price red discount-price cp-discount-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
												<div class="lowest-price cp-lowest-price" v-if="mode != 'instashop'">
													<template v-if="item.extra_price_lowest > 0">
														<BaseCmsLabel code="lowest_price" />: <strong><BaseUtilsFormatCurrency :price="item.extra_price_lowest" /></strong>
													</template>
												</div>
											</template>
											<template v-else>
												<div class="current-price cp-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
												<div class="lowest-price cp-lowest-price" v-if="mode != 'instashop'"></div>
											</template>
										</template>
									</template>
								</WebshopLoyalty>
							</template>
						</div>

						<div class="cp-addtocart" :class="{'cp-addtocart-single': item.available_qty <= item.package_qty}">
							<template v-if="item.is_available">
								<div class="cp-qty" :class="{'cp-qty-single': item.available_qty <= item.package_qty}" v-if="item.available_qty > 1">
									<BaseThemeWebshopQty :limit="item.available_qty" :item="item" v-model="item.quantity" />
								</div>
								<div class="cd-bt-item-add cp-checkbox">
									<input type="checkbox" :name="item.shopping_cart_code" :value="item" :id="item.shopping_cart_code" v-model="checkedItems" :disabled="index == 0" />
									<label :for="item.shopping_cart_code" v-html="labels.get('bought_together_label')"></label>
								</div>
							</template>
						</div>
					</div>
				</div>
			</article>

			<ClientOnly>
				<div class="cd-bought-together-footer" v-if="checkedItems?.length">
					<div class="choosen-info">
						<div class="choosen-info-total" :class="{'saving': saving}">
							<BaseCmsLabel code="choosen" tag="span" />
							<span class="counter"> {{checkedItems.length}}/{{items.length}}</span>
						</div>
						<div class="total-choosen-cnt">
							<BaseCmsLabel code="total" tag="span" class="label-total>" />: <strong><BaseUtilsFormatCurrency :price="total" /></strong>
						</div>
						<div class="chosen-total-saving" v-if="saving">
							<BaseCmsLabel code="saving" /> <span><BaseUtilsFormatCurrency :price="saving" /> (-{{meta.extra.related_discount_percent_general}}%)</span>
						</div>
					</div>
					<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :data="addToCartData" :redirect="true" :add-to-cart-modal="false">
						<div class="btn btn-orange btn-bought-together-add" :class="{'loading': loading, 'disabled': checkedItems?.length == 1}" @click="onAddToCart"><UiLoader v-if="loading" /><BaseCmsLabel code="add_choosen_to_shopping_cart" tag="span" /></div>
					</BaseWebshopAddToCart>
				</div>
			</ClientOnly>
		</div>
	</BaseCatalogBoughtTogether>
</template>

<script setup>
	const {b2b, isLoggedIn, user, hasLoyalty} = useProfile();
	const props = defineProps(['items', 'meta', 'current', 'mode']);
	const items = ref(props.items);
	const labels = useLabels();

	const checkedItems = ref([]);

	// extract attribute name from attributes
	function attributeName(itemAttributes,varAttributeIds){
		if(!itemAttributes || !varAttributeIds) return;

		const attArr = (varAttributeIds.slice(1, -1)).split(',');
		if(!attArr.length) return;

		const attr = Object.values(itemAttributes).find(att => att.id == attArr[0]).title;
		if(attr) return attr;
	}
</script>

<style lang="less" scoped>
	:deep(.cp-badge){font-weight: normal;}
	:deep(img){max-height: 120px;}
</style>
