<template>
	<article class="cd-store" :class="[{'active ': activeStore}, item?.available_qty == 0 ? 'cd-store-notavailable' : 'cd-store-available']">
		<div class="cd-store-title" @click="activeStore = !activeStore">
			<span v-if="item?.title2">{{item.title2}}</span>
			<span v-else>{{item.title}}</span>
		</div>
		<div class="cd-store-content">
			<div class="cd-store-availability" :class="{'last': item?.available_qty === 1, 'available': item?.available_qty > 1, 'unavailable': item?.available_qty < 1}">
				<div class="cd-store-i cd-store-address" v-if="item?.address" v-html="item.address" />
				<div class="cd-store-i cd-store-business-hour" v-if="item?.business_hour" v-html="item.business_hour" />
				<div class="cd-store-i cd-store-contact" v-if="item?.contact" v-html="item.contact" />
			</div>
		</div>
	</article>
</template>

<script setup>
	const props = defineProps({
		item: Object
	});
	const activeStore = ref(false);
</script>
