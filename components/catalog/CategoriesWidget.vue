<template>
	<BaseCatalogCategoriesWidget :fetch="{hierarhy_by_position: true}" v-slot="{items}">
		<div class="wrapper" :class="extraclass">
			<ul class="categories-widget">
				<template v-for="item in items" :key="item.id">
					<CatalogCategoryItem :item="item" />
				</template>
			</ul>
		</div>
	</BaseCatalogCategoriesWidget>
</template>

<script setup>
	const props = defineProps(['extraclass']);
</script>
