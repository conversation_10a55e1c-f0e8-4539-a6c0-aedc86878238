<template>
	<div class="ac">
		<div class="ac-wrapper" v-if="items?.length">
			<div class="ac-item" v-for="item in items" :key="item.id" :class="{'active': item.index == selectedIndex}" @click="addProduct(item)">
				<div class="ac-item-col ac-item-col1">
					<BaseUiImage :src="item.image_upload_path" alt="" default="/images/no-image-80.jpg" />
				</div>
				<div class="ac-item-col ac-item-col2">
					<div class="ac-item-title">{{item.title}}</div>
					<div class="ac-item-price red" v-if="item.price_custom > 0" :class="{'red': item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom}">
						<BaseUtilsFormatCurrency :price="item.price_custom" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps({
		items: {
			type: Array,
			required: true,
		},
		selectedIndex: Number,
		addProduct: Function
	})
</script>

<style lang="less" scoped>
	.ac{position: absolute; top: 100%; right: 0; left: 0; z-index: 1; background: #fff; box-shadow: 0 10px 20px rgba(0,0,0,0.2);}
	.ac-wrapper{position: relative; display: flex; flex-flow: column; max-height: 150px; overflow-y: auto;}
	.ac-item{position: relative; display: flex; align-items: center; padding: 10px 16px; gap: 16px;}
	.ac-item-col1{
		display: flex; align-items: center; justify-content: center; width: 60px; flex-grow: 0; flex-shrink: 0;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%;}
	}
	.ac-item-title{display: block; font-size: 14px; line-height: 1.4;}
	.ac-item-price{display: block; font-size: 14px; line-height: 1.4;}
</style>
