<template>
	<BaseCatalogProductsWidget :fetch="{id: itemsIds}" v-slot="{items: products}">
		<div class="c-items pd-instashop-items" v-if="products?.length">
			<CatalogIndexEntry v-for="product in products" :key="product.id" :item="product" list="" class="cp-list" mode="instashop" />
		</div>

		<WebshopInstashopAddAll :items="products" />
	</BaseCatalogProductsWidget>
</template>

<script setup>
	const model = defineModel();
	const props = defineProps(['items']);
	const itemsIds = [];
	if(props?.items){
		props.items.forEach(item => {
			itemsIds.push(item.id);
		});
	}
</script>

<style lang="less" scoped>
	.cp-rates{
		display: flex;
		:deep(.icon-star), :deep(.icon-star-empty){
			position: relative; margin-right: 2px;
			&:before{.icon-star(); font: 12px/12px @fonti; color: #d5d9d3;}
		}
		:deep(.icon-star:before){color: @yellow;}
	}

	.cp-list.instashop{
		padding: 12px 16px 12px 8px;
		@media (min-width: @h){
			&:hover{box-shadow: none; border-color: @green;}
		}
		.cp-col1{width: 120px; margin-right: 15px;}
		.cp-image{
			height: 120px; align-items: center; justify-content: center;
			:deep(img){margin: 0;}
		}
		.cp-brand{
			top: 0; height: 18px;
			:deep(img){max-height: 18px;}
		}
		.cp-col2{margin: 0; flex-flow: row; flex-wrap: inherit;}
		.cp-addtocart:before{display: none;}
		.cp-footer{flex-flow: column; align-items: flex-end; padding-top: 35px; width: 150px; flex-grow: 0; flex-shrink: 0; margin-left: 35px;}
		.cp-btn-addtocart{
			position: relative; width: 44px;
			&:before{.icon-cart(); font: 23px/1 @fonti; color: @white; z-index: 1;}
			span{display: none;}
		}
		.cp-price{font-size: 14px; justify-content: flex-end; text-align: right;}
		.cp-wishlist{right: -5px;}
		:deep(.cp-wishlist-btn:after){font-size: 18px;}
		@media (max-width: @l){
			.cp-footer{width: 125px; margin-left: 15px;}
		}
		@media (max-width: @t){
			.cp-col2{flex-flow: column; align-items: flex-start; justify-content: flex-start;}
			.cp-footer{width: 100%; margin-left: 0; padding-top: 15px; flex-flow: row; align-items: center; justify-content: space-between;}
			.cp-price{justify-content: flex-start; text-align: left;}
		}
		@media (max-width: @instashopT){
			:deep(.cp-wishlist-btn span){display: none;}
		}
		@media (max-width: 800px){
			padding: 12px 16px 12px 12px;
			.cp-addtocart{height: 40px; width: auto;}
			.cp-btn-addtocart{
				flex-grow: 0; width: 40px; height: 40px;
				&:before{font-size: 20px;}
			}
			.cp-qty{width: 85px;}
			.cp-col1{width: 112px; margin-right: 12px;}
			.cp-rate{height: auto;}
			.cp-rate-counter{margin-top: 0;}
			.cp-category{font-size: 10px; padding: 1px 0 5px;}
			.cp-title{margin-top: 0;}
			.cp-price, .cp-addtocart{margin-top: 0;}
		}
	}
</style>
