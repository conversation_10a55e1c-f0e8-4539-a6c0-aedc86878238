<template>
	<BaseCatalogWishlistWidget v-slot="{counter, wishlistUrl}">
		<ClientOnly>
			<div class="wishlist wishlist-widget" :class="{'active': counter > 0}">
				<BaseAuthUser v-slot="{user, urls}">
					<NuxtLink :to="user?.b2b ? urls.auth_wishlist : wishlistUrl">
						<span class="counter wishlist-counter wishlist_count" v-if="counter">{{counter}}</span>
					</NuxtLink>
				</BaseAuthUser>
			</div>
		</ClientOnly>
	</BaseCatalogWishlistWidget>
</template>

<script setup>
	const props = defineProps({
		item: Object,
		mode: String,
	});
</script>