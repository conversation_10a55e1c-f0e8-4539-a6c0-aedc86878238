<template>
	<BaseUiSwiper class="speciallist-items" :options="sliderOptions">
		<BaseUiSwiperSlide v-for="item in items" :key="item.id">
			<CatalogIndexEntry :key="item.id" :item="item" list="Besplatna dostava" itemListId="besplatna-dostava" class="cp-grid cp-rp no-shadow" />
		</BaseUiSwiperSlide>
	</BaseUiSwiper>
</template>

<script setup>
	const props = defineProps(['items', 'perPage']);
	const {mobileSmallBreakpoint} = inject('rwd');

	const sliderOptions = {
		slidesPerView: 4,
		slidesPerGroup: 4,
		navigation: {enabled: true},
		watchSlidesProgress: false,
		spaceBetween: -1,
		enabled: false,
		breakpoints: {
			760: {
				enabled: true,
			}
		}
	}
</script>

<style scoped lang="less">
	:deep(.cp){
		margin: 0;
		width: auto;
		@media (max-width: @m){
			width: 100%;
		}
	}
	:deep(.swiper){
		padding-bottom: 65px;
		@media (max-width: @t){
			padding-bottom: 0;
		}
	}
	:deep(.swiper-wrapper){
		@media (max-width: @m){
			display: flex; position: relative; padding: 1px 16px 0; width: 100%; box-sizing: border-box; overflow-x: auto;
			&::-webkit-scrollbar {-webkit-appearance: none; height: 0;}
			&::-webkit-scrollbar-thumb {background-color: transparent;}
		}
	}
	:deep(.swiper-slide){
		display: flex; height: auto;
		&:hover{z-index: 1; }
		@media (max-width: @m){display: flex; width: 140px!important; flex-grow: 0; flex-shrink: 0; opacity: 1; visibility: visible;}
	}
	:deep(.swiper-navigation){
		display: flex; justify-content: space-between; position: absolute; top: 0; right: 0; left: 0; bottom: 0;
		@media (max-width: @m){
			display: none;
		}
	}
	:deep(.swiper-button){
		cursor: pointer;
		&.swiper-button-disabled{cursor: default;}
		&:before{font-size: 25px; line-height: 25px;}
	}
	:deep(.swiper-button-prev){
		left: -55px;
	}
	:deep(.swiper-button-next){
		right: -55px;
	}
</style>
