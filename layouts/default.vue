<template>
	<link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
	<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
	<link rel="shortcut icon" href="/favicon.ico" />
	<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
	<meta name="apple-mobile-web-app-title" content="TZH" />
	<link rel="manifest" href="/site.webmanifest" />

	<Body :class="{'fixed-header': showElement && y > 270 && !disableFixed, 'active-nav': activeNav}" />
	<div class="page-wrapper">
		<ClientOnly>
			<BaseThemeUiPageLoadingIndicator track-color="#ABC075" bar-color="#809941" />
		</ClientOnly>
		<CmsLayoutAboveHeader />

		<CmsHeader v-if="showElement">
			<template #navBtn>
				<div class="btn-toggle-nav" :class="{'active': activeNav}" v-if="mobileBreakpoint" @click="activeNav = !activeNav"><span></span></div>
			</template>

			<template #afterHeader>
				<template v-if="isSearch">
					<BaseSearchResults :search-modules="[{module: 'catalog'}, {module: 'publish|01'}, {module: 'publish|02'}, {module: 'cms'}]" v-slot="{searchTerm}">
						<SearchResultsHeader :search-term="searchTerm" />
					</BaseSearchResults>
				</template>
				<CmsHomepageIntro v-if="isHomepage" />
				<CatalogIndexHeader v-if="showCategoriesWidget && !isQuickOrder" />
				<CatalogCategoriesWidget :extraclass="'categories c-categories'" v-if="showCategoriesWidget && !isQuickOrder" />
				<div class="wrapper quickorder-header" v-if="isQuickOrder">
					<BaseCmsLabel code="quick_order" tag="h1" class="title-quickorder" />
				</div>
				<PublishPostHeader v-if="isPublishDetail" />
				<PublishIndexHeader v-if="isPublishIndex && !isSearch" />
				<CatalogIndexWishlistHeader v-if="isIndexWishlistHeader" />
				<WebshopCartHeader v-if="isWebshopCartHeader" />
			</template>
		</CmsHeader>

		<slot />

		<CmsLayoutElements :instashopData="instashopData" :showElement="showElement" />
	</div>
</template>

<script setup>
	const route = useRoute();
	const {onMediaQuery, onScroll, scrollTo} = useDom();
	const {getInfo} = useInfo();
	const {addScript, waitForWindowProperty} = useMeta();
	const lang = useLang();
	const url = useUrl();
	const {matches: tabletBreakpoint} = onMediaQuery({query: '(max-width: 1250px)'});
	const {matches: tabletSmallBreakpoint} = onMediaQuery({query: '(max-width: 1030px)'});
	const {matches: mobileBreakpoint} = onMediaQuery({query: '(max-width: 990px)'});
	const {matches: mobileBreakpointInstashop} = onMediaQuery({query: '(max-width: 800px)'});
	provide('rwd', {mobileBreakpoint, tabletBreakpoint, tabletSmallBreakpoint, mobileBreakpointInstashop});

	const instashopData = useState('instashopData');

	// Workaround to move publish detail header to header slot
	const isPublishDetail = computed(() => {
		return route.meta.template === 'PublishDetail' || route.meta.template === 'PublishRecipesDetail' ? true : false;
	});

	// Workaround to move recipes index header to header slot
	const isPublishIndex = computed(() => {
		return route.meta.template === 'PublishRecipes' || route.meta.template === 'PublishAuthorDetail' ? true : false;
	});

	// Workaround to move wishlist header to header slot
	const isIndexWishlistHeader = computed(() => {
		return route.meta.template === 'CatalogWishlist' ? true : false;
	});

	// Workaround to move cart header to header slot
	const isWebshopCartHeader = computed(() => {
		return route.meta.template === 'WebshopShoppingCart' ? true : false;
	});

	const showCategoriesWidget = computed(() => {
		return route.meta.controller == 'catalog' && route.meta.contentType == 'category' && route.params.slug.length == 2 ? true : false;
	});

	const isHomepage = computed(() => {
		return route.meta.template === 'CmsHomepage' ? true : false;
	});

	/*const isSearch = computed(() => {
		return route.query.search_q  ? true : false;
	});*/
	const isSearch = computed(() => !!route.query.search_q && route.meta.template != 'CatalogDetail');

	const isQuickOrder = computed(() => {
		return route.query.special_view === 'order_form' ? true : false;
	});

	const showElement = computed(() => {
		if(!route?.meta?.action) return true;
		return (route.meta.controller == 'webshop' && ['login', 'customer', 'shipping', 'payment', 'review_order'].includes(route.meta.action)) ? false : true;
	});

	const disableFixed = computed(() => {
		if(!route?.meta?.action) return true;
		return (route.meta.controller != 'staticcontent') ? false : true;
	});

	const activeNav = ref(false);
	const {y} = onScroll({
		debounce: 150,
	});

	watch(
		() => route.fullPath,
		(newPath, oldPath) => {
			activeNav.value = false;
		}
	);

	//FIXME - dodana funkcija koja wrappa sve .btn - potrebno samo kratka provjera od strane @mlazar pošto se radi o default.vue
	function wrapBtnTextInSpan() {
		const buttons = document.querySelectorAll('.btn')

		buttons.forEach(btn => {
			const hasSpan = Array.from(btn.childNodes).some(
				node => node.nodeType === Node.ELEMENT_NODE && node.tagName === 'SPAN'
			)

			if (!hasSpan) {
				const textNodes = Array.from(btn.childNodes).filter(
					node => node.nodeType === Node.TEXT_NODE && node.textContent.trim() !== ''
				)

				if (textNodes.length > 0) {
					const span = document.createElement('span')
					textNodes.forEach(textNode => span.appendChild(textNode))
					btn.insertBefore(span, btn.firstChild)
				}
			}
		})
	}

	let observer

	onMounted(() => {
		wrapBtnTextInSpan()

		observer = new MutationObserver(() => {
			wrapBtnTextInSpan()
		})

		observer.observe(document.body, {
			childList: true,
			subtree: true
		})
	})

	onBeforeUnmount(() => {
		if (observer) observer.disconnect()
	})


	// Cookiebot
	const language = lang.get();
	let scriptId = '';
	if(language == 'hr'){
		scriptId = '0678daa3-47bb-4d6a-90d7-fc702811b908';
	}else if(language == 'si'){
		scriptId = '7fd78573-b58d-4c38-a9cb-b227f90a9fd9';
	}else if(language == 'en'){
		scriptId = 'e3663285-1e6a-4ee0-a89f-346c385cb81e';
	}else if(language == 'de'){
		scriptId = '17e36d90-e6b4-432c-a6d4-5302ed315656';
	}
	addScript({
		key: 'Cookiebot',
		src: 'https://consent.cookiebot.com/uc.js',
		dataAttributes: {
			'cbid': scriptId,
			'consentmode': 'disabled'
		},
		async: true
	})

	// Zopim live chat
	addScript({
		key: 'zopim',
		src: 'https://static.zdassets.com/ekr/snippet.js?key=377b60e3-102e-4c2e-ad35-0b90b9070c1a',
		id: 'ze-snippet'
	});
	waitForWindowProperty(
		'zE',
		zE => {
			addScript({
				key: 'zopimconfig',
				innerHTML: `zE('webWidget', 'setLocale', '${lang.get()}');window.zESettings = {webWidget: {color: {theme: '#ABC075',launcherText: '#FFFFFF'},launcher: {label: {'*': 'Korisnička podrška'}},chat: {title: {'*': 'Pomoć'},offlineForm: {greeting: {'*': 'Pomoć nije dostupna. Molimo kontaktirajte nas putem emaila ili telefona.',}}},position: {'horizontal': 'left'}}};`,
			})
		},
	);
</script>

<style lang="less">
	a#CybotCookiebotDialogPoweredbyCybot,div#CybotCookiebotDialogPoweredByText {display: none;}
	#CookiebotWidget .CookiebotWidget-body .CookiebotWidget-main-logo {display: none;}
</style>
