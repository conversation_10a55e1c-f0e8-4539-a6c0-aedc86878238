<template>
	<Body :class="{'fixed-header': y > 270, 'active-nav': activeNav}" />
	<div class="page-wrapper">
		<CmsHeader>
			<template #navBtn>
				<div class="btn-toggle-nav" :class="{'active': activeNav}" v-if="mobileBreakpoint" @click="activeNav = !activeNav"><span></span></div>
			</template>

			<template #afterHeader>
				<slot />
			</template>
		</CmsHeader>

		<LazyCmsTestimonials />
		<PublishInstashopWidget />
		<CmsLayoutElements :instashopData="instashopData" />
	</div>
</template>

<script setup>
	const {onMediaQuery} = useDom();
	const {matches: tabletBreakpoint} = onMediaQuery({query: '(max-width: 1250px)'});
	const {matches: tabletSmallBreakpoint} = onMediaQuery({query: '(max-width: 1030px)'});
	const {matches: mobileBreakpoint} = onMediaQuery({query: '(max-width: 990px)'});
	const {matches: mobileBreakpointInstashop} = onMediaQuery({query: '(max-width: 800px)'});
	provide('rwd', {mobileBreakpoint, tabletBreakpoint, tabletSmallBreakpoint, mobileBreakpointInstashop});
	const {onScroll, scrollTo} = useDom();

	const instashopData = useState('instashopData');

	const activeNav = ref(false);
	const {y} = onScroll({
		debounce: 150,
	});
</script>
