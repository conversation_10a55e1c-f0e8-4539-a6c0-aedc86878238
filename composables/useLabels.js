export function useLabels() {
	const {absoluteImagePath} = useText();
	const nuxtApp = useNuxtApp();
	const lang = useLang();
	const labels = nuxtApp.$appData?.labels;

	async function fetch(options) {
		const language = options?.lang ? options.lang : lang.get();
		return useApi('/api/nuxtapi/labels?lang=' + language, null, {cache: true}).then(res => {
			nuxtApp.$appData.labels = res.data;
			return res.data;
		});
	}

	const get = (code, d = '') => {
		if (labels) {
			// If default is set to 'code' then return the label code. Otherwise return whatever is set as default. If nothing is set as default, return empty string
			const defaultLabel = d === 'code' ? code : nuxtApp.$appData.labels[d] || d || '';
			let l = nuxtApp.$appData.labels[code] ? absoluteImagePath(nuxtApp.$appData.labels[code]) : defaultLabel;

			// Replace public/media path
			if (l) l = l.replace(/(\/media\/)/g, '/');

			return l;
		}
		return useLog('No labels available');
	};

	async function set(data) {
		return (nuxtApp.$appData.labels = data);
	}

	return {labels, fetch, get, set};
}
