export function useUrl() {
	const config = useAppConfig();

	// remove domain/host name from url
	function relative(url) {
		if (!url) {
			useLog('Missing url property', 'error');
			return;
		}
		return url.replace(/^.*\/\/[^\/]+:?[0-9]?/i, '');
	}

	// prepend domain/host name to url if not already available
	function absolute(url) {
		if (!url) {
			useLog('Missing url property', 'error');
			return;
		}
		const pattern = /^.*\/\/[^\/]+:?[0-9]?/i;
		return pattern.test(url) ? url : config.host + url;
	}

	// remove leading and trailing slash
	function stripSlashes(url) {
		if (!url) {
			useLog('Missing url property', 'error');
			return;
		}
		url = String(url).trim();
		if (url.startsWith('/')) {
			url = url.substring(1);
		}
		if (url.endsWith('/')) {
			url = url.substring(0, url.length - 1);
		}
		return url;
	}

	// extract query params from url and return as array
	function getUrlParams(url, options = {}) {
		const queryPart = url.split('?')[1];
		if (!queryPart) return [];

		const params = queryPart.split('&').map(param => {
			const [key, value] = param.split('=');
			return {key, value};
		});

		if (options?.mode == 'keys') {
			let keys = params.map(param => param.key);
			if (options.removeDuplicates) {
				keys = [...new Set(keys)];
			}
			return keys;
		}
		if (options?.mode == 'values') {
			let values = params.map(param => param.value);
			if (options.removeDuplicates) {
				values = [...new Set(values)];
			}
			return values;
		}

		return queryPart.split('&').map(param => {
			const [key, value] = param.split('=');
			return {[key]: decodeURIComponent(value)};
		});
	}

	// return array of url segments
	function getUrlSegments(url, options) {
		if (!url) {
			useLog('Missing url property', 'error');
			return [];
		}

		let output = url.split('/');
		output = output?.length ? output.filter(element => element !== undefined && element !== null && element !== '') : [];

		// ignore language segment
		const langs = ['hr', 'en', 'de', 'at', 'rs', 'si', 'it', 'ba', 'me'];
		if (config?.multilanguage && options?.ignoreLang && langs.includes(output[0])) {
			output.shift();
		}

		// return only lang segment
		if (config?.multilanguage && options?.onlyLang && langs.includes(output[0])) {
			output = [output[0]];
		}

		// if reverse option is set, reverse array
		if (options?.reverse) {
			output.reverse();
		}

		if (options?.offset && output.length) {
			output.splice(0, options.offset);
		}

		if (options?.limit && output.length) {
			output = output.slice(0, options.limit);
		}

		// if reverse option is set, reverse back
		if (options?.reverse) {
			output.reverse();
		}

		if (options?.append) {
			output.push(options.append);
		}

		if (options?.stringify) {
			output = output.join('/');
		}

		if (options?.addSlashes) {
			output = '/' + output + '/';
		}

		return output == '//' ? '/' : output;
	}

	function getLastUrlSegment(url) {
		return getUrlSegments(url, {reverse: true, limit: 1, stringify: true});
	}

	function stripLangFromUrl(url) {
		return getUrlSegments(url, {ignoreLang: true, stringify: true, addSlashes: true});
	}

	return {relative, absolute, stripSlashes, getUrlParams, getLastUrlSegment, getUrlSegments, stripLangFromUrl};
}
