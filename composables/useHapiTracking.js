export function useHapiTracking() {
	const config = useAppConfig();
	const endpoints = useEndpoints();
	const trackingConfig = config.hapi?.tracking;

	async function sendEvent(contentType, objectId, options) {
		if (process.server) return;
		if (!contentType || !objectId) return;
		if (!trackingConfig?.events?.includes(contentType)) return;

		const fetchOptions = {
			content_type: contentType,
			object_id: objectId,
			...options,
		};

		if (!endpoints.get('_post_hapi_misc_tracking')) {
			return console.error('HAPI tracking error: _post_hapi_misc_tracking endpoint not available!');
		}

		const res = await useApi(endpoints.get('_post_hapi_misc_tracking'), {
			method: 'POST',
			body: fetchOptions,
		});

		if (trackingConfig?.debug) console.log('Hapi event', fetchOptions, res);
		return res;
	}

	return {
		sendEvent,
	};
}
