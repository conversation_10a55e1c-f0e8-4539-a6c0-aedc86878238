export function useLang() {
	const config = useAppConfig();
	const {getUrlSegments} = useUrl();
	const lang = useState('lang', () => (config.lang ? config.lang : 'hr'));

	const langs = {
		hr: {code: 'hr', lang: 'hr', base_url: '/hr/', locale: 'hr-HR', currency: '€'},
		en: {code: 'en', lang: 'en', base_url: '/en/', locale: 'en-GB', currency: '€'},
		de: {code: 'de', lang: 'de', base_url: '/de/', locale: 'de-DE', currency: '€'},
		rs: {code: 'rs', lang: 'rs', base_url: '/rs/', locale: 'sr-RS', currency: 'RSD'},
		si: {code: 'si', lang: 'sl', base_url: '/si/', locale: 'sl-SI', currency: '€'},
		it: {code: 'it', lang: 'it', base_url: '/it/', locale: 'it-IT', currency: '€'},
		ba: {code: 'ba', lang: 'ba', base_url: '/ba/', locale: 'bs-BA', currency: 'BAM'},
		me: {code: 'me', lang: 'me', base_url: '/me/', locale: 'sr-RS', currency: '€'},
	};

	// get all data for specific language. If no payload, return current language data
	function getLang(payload) {
		return payload ? langs[payload] : langs[lang.value];
	}

	// get specified data for current language. If no payload, return current language code
	function get(payload) {
		return payload ? langs[lang.value][payload] : langs[lang.value].code;
	}

	// set current language
	function set(payload) {
		if (!payload) {
			useLog('useLang.set: Language is not provided.', 'error');
		}
		lang.value = payload;
	}

	function getLangFromUrl(url) {
		const urlSegments = getUrlSegments(url);
		if (urlSegments?.length && langs[urlSegments[0]]) {
			return urlSegments[0];
		}

		return lang.value;
	}

	function isDefaultLang() {
		return lang.value == config.lang;
	}

	return {langs, get, set, getLang, getLangFromUrl, isDefaultLang};
}
