export function useNewsletter() {
	const endpoints = useEndpoints();
	const {_route} = useNuxtApp();
	const {getLastUrlSegment} = useUrl();

	// fetch form fields
	async function fetchForm(list, config = {}) {
		const nlList = list ? list : 'list';
		return useApi('/api/nuxtapi/newsletter/', {method: 'POST', body: {code: nlList}}, {cache: true, ...config});
	}

	// subscribe
	async function subscribe(payload) {
		return useApi(endpoints.get('_post_hapi_newsletter_subscribe'), {
			method: 'POST',
			body: payload,
		});
	}

	// subscribe confirm
	async function confirmSubscribe() {
		const r = getLastUrlSegment(_route.path);
		const data = r.split('-');

		if (data) {
			return useApi(endpoints.get('_post_hapi_newsletter_subscribe_confirm'), {
				method: 'POST',
				body: {
					id: data[0],
					code: data[1],
				},
			});
		}

		useLog('useNewsletter:confirmSubscribe - url data not available!', 'error');
	}

	// unsubscribe
	async function unsubscribe(payload) {
		return useApi(endpoints.get('_post_hapi_newsletter_unsubscribe'), {
			method: 'POST',
			body: payload,
		});
	}

	// unsubscribe confirm
	async function confirmUnsubscribe(payload) {
		const r = getLastUrlSegment(_route.path);
		const data = r.split('-');

		if (data) {
			return useApi(endpoints.get('_post_hapi_newsletter_unsubscribe_confirm'), {
				method: 'POST',
				body: {
					id: data[0],
					code: data[1],
				},
			});
		}

		useLog('useNewsletter:confirmUnsubscribe - url data not available!', 'error');
	}

	return {fetchForm, subscribe, unsubscribe, confirmSubscribe, confirmUnsubscribe};
}
