const {sha256} = useUtils();
export function useFbCapi() {
	// https://developers.facebook.com/docs/marketing-api/conversions-api/
	const config = useAppConfig();
	const route = useRoute();
	const {getCartData} = useWebshop();
	const {gdprApproved} = useGdpr();
	const trackingConfig = config.facebook?.conversionApi;
	const fbTrackingData = useState('fbTrackingData', () => null);
	const env = process.env.NODE_ENV;

	async function sendEvent(event, customData) {
		if (process.server) return;
		if (trackingConfig.gdpr && !gdprApproved(trackingConfig.gdpr)) return;
		if (!trackingConfig.env.includes(env)) return;

		// Exit if event is not defined
		if (!event || !trackingConfig?.events?.includes(event)) return;

		const eventName = () => {
			if (trackingConfig?.[event]?.name) return trackingConfig?.[event]?.name; // Check if event name is available in tracking config
			if (event === 'pageView') return 'PageView';
			if (event === 'contact') return 'Contact';
			if (event === 'search') return 'Search';
			if (event === 'viewContent') return 'ViewContent';
			if (event === 'addToWishlist') return 'AddToWishlist';
			if (event === 'addToCart') return 'AddToCart';
			if (event === 'addPaymentInfo') return 'AddPaymentInfo';
			if (event === 'completeRegistration') return 'CompleteRegistration';
			if (event === 'customizeProduct') return 'CustomizeProduct';
			if (event === 'findLocation') return 'FindLocation';
			if (event === 'initiateCheckout') return 'InitiateCheckout';
			if (event === 'subscribe') return 'Subscribe';
			if (event === 'purchase') return 'Purchase';
			return event;
		};

		// If event has data fields defined, filter out the data
		const filteredEvents =
			Array.isArray(trackingConfig[event]?.data) && trackingConfig[event].data.length > 0
				? trackingConfig[event].data.reduce((result, key) => {
						if (key in customData) {
							result[key] = customData[key];
						}
						return result;
				  }, {})
				: {...customData};

		// Payload tracking data
		let customerData = {};
		const customer = getCartData()?.customer || null;
		if (customer?.email) customerData.em = await sha256(customer.email);
		if (customer?.first_name) customerData.fn = await sha256(customer.first_name);
		if (customer?.last_name) customerData.ln = await sha256(customer.last_name);
		if (customer?.phone) customerData.ph = await sha256(customer.phone);
		if (customer?.address?.zipcode) customerData.zp = await sha256(customer.address.zipcode);
		if (customer?.address?.city) customerData.ct = await sha256(customer.address.city);

		let eventData = {
			data: [
				{
					event_name: eventName(),
					event_time: Math.floor(new Date().getTime() / 1000), // Current time in seconds
					event_source_url: route.fullPath || '',
					action_source: 'website',
					user_data: {
						client_ip_address: '',
						client_user_agent: '',
						...customerData,
					},
					custom_data: filteredEvents,
				},
			],
		};

		// Events test code (debugging with facebook event manager: https://business.facebook.com/events_manager2)
		if (trackingConfig?.eventTestCode) {
			eventData.test_event_code = trackingConfig?.eventTestCode;
		}

		// Send data to Facebook
		const res = await $fetch('/api/nuxtapi/fbcapi', {
			method: 'POST',
			body: eventData,
		});
		if (trackingConfig?.debug) useLog([`Facebook Event: ${event}`, eventData, res]);
	}

	function setData(data) {
		fbTrackingData.value = data;
	}

	function getData() {
		return fbTrackingData.value;
	}

	return {sendEvent, fbTrackingData, setData, getData};
}
