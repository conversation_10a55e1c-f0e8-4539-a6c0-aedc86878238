export function useSweepstake() {
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const endpoints = useEndpoints();
	const lang = useLang();

	async function fetch(config = {}) {
		const urlParams = route.params.slug
			.slice(1)
			.filter(value => value !== '')
			.join('-');

		return await useApi(
			`${endpoints.get('_get_hapi_sweepstake_pages')}?lang=${lang.get()}&slug=` + urlParams,
			{
				method: 'GET',
			},
			{cache: true}
		);
	}

	return {fetch};
}
