export function useEventBus() {
	const bus = useState('eventBus', () => ({}));

	function emit(event, data = null) {
		bus.value = {
			event,
			data,
		};
	}

	// listen for any changes (event name and data) for specific event and call callback
	function subscribe(event, callback, options = {}) {
		watch(
			() => bus.value,
			() => {
				if (bus.value.event == event) {
					return callback(bus.value.data);
				}
			},
			{deep: true, ...options}
		);
	}

	function clearEventBus() {
		bus.value = {};
	}

	return {
		emit,
		bus,
		subscribe,
		clearEventBus,
	};
}
