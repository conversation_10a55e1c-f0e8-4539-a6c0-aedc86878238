export function useCurrency() {
	const endpoints = useEndpoints();
	const nuxtApp = useNuxtApp();

	// fetch currency data
	async function fetch() {
		const res = await useApi('/api/nuxtapi/currency/', null, {cache: true});
		nuxtApp.$appData.currency = res.data;
		return res.data;
	}

	// set different currency
	async function submitCurrency(code) {
		await useApi(`${endpoints.get('_post_hapi_catalog_currency')}`, {
			method: 'POST',
			body: {code},
		});
	}

	function setCurrency(data) {
		nuxtApp.$appData.currency = data;
	}

	// get currency data
	function getCurrency(data) {
		if (data && nuxtApp.$appData.currency[data]) {
			return nuxtApp.$appData.currency[data];
		}

		return nuxtApp.$appData.currency;
	}

	// wrap price elements
	function wrapPrice(price) {
		// Define a regular expression pattern to match the parts of the price
		const pattern = /(\€|\$|KM)?\s*([\d,\.]+)([.,])(\d+)\s*(\€|\$|KM)?/;

		// Search for the pattern in the price string
		const match = price.match(pattern);

		if (match) {
			// Extract the matched groups
			const currencyBefore = match[1] ? `<span class="p p-currency">${match[1]}</span>` : '';
			const currencyAfter = match[5] ? `<span class="p p-currency">${match[5]}</span>` : '';

			// Check for spaces around the matched parts
			const spaceBeforeNumber = price.startsWith(match[1] || '') ? price.charAt((match[1] || '').length) === ' ' : false;
			const spaceAfterNumber = price.endsWith(match[5] || '') ? price.charAt(price.length - (match[5] || '').length - 1) === ' ' : false;

			// Determine the format by checking the separators
			const separator = match[3];
			const integerPart = match[2];
			const decimalPart = match[4];
			let thousands = '';
			let hundreds = integerPart;

			if (separator === ',') {
				// EU format: decimal separator is a comma, thousands separator is a dot
				const parts = integerPart.split('.');
				if (parts.length > 1) {
					thousands = parts.slice(0, -1).join('.');
					hundreds = parts.slice(-1);
				}
			} else if (separator === '.') {
				// US format: decimal separator is a dot, thousands separator is a comma
				const parts = integerPart.split(',');
				if (parts.length > 1) {
					thousands = parts.slice(0, -1).join(',');
					hundreds = parts.slice(-1);
				}
			}

			// Wrap each part in a <span> tag with the appropriate CSS class
			const wrappedParts = [
				currencyBefore, // Currency symbol before
				spaceBeforeNumber ? ' ' : '', // Space before number if any
				thousands ? `<span class="p p-t">${thousands}</span>` : '', // Thousands part
				thousands ? `<span class="p p-sep p-${separator === ',' ? 'dot' : 'comma'}">${separator === ',' ? '.' : ','}</span>` : '', // Thousands separator
				`<span class="p p-h">${hundreds}</span>`, // Hundreds part
				`<span class="p p-sep p-${separator === ',' ? 'comma' : 'dot'}">${separator}</span>`, // Separator
				`<span class="p p-d">${decimalPart}</span>`, // Decimal part
				spaceAfterNumber ? ' ' : '', // Space after number if any
				currencyAfter, // Currency symbol after
			].filter(Boolean); // Remove empty parts

			// Combine the wrapped parts into a single string
			const wrappedPrice = wrappedParts.join('');

			return wrappedPrice;
		} else {
			// If the pattern does not match, return the original price
			return price;
		}
	}

	// format currency
	// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat
	function formatCurrency(value, options = {}) {
		const locale = options?.locale ? options.locale : 'hr-HR';
		const currency = options?.currency ? options.currency : getCurrency('display');

		const amount = new Intl.NumberFormat(locale, {
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		}).format(value);

		let output = options?.showCurrency == false ? amount : currency.replace('%s', amount);

		if (options?.wrap) {
			output = wrapPrice(output);
		}

		return output;
	}

	return {fetch, setCurrency, submitCurrency, getCurrency, formatCurrency};
}
