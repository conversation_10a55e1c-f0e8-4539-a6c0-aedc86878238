export function useAuth() {
	const endpoints = useEndpoints();
	const token = useToken();

	let webshop;
	if (__WEBSHOP__) webshop = useWebshop();

	const loggedIn = useState('loggedIn', () => false);
	const user = useState('user', () => null);

	async function login(options) {
		let loginData = options;

		return await useApi(`${endpoints.get('_post_hapi_auth_login')}`, {
			method: 'POST',
			body: loginData,
		}).then(async res => {
			if (res.data?.jwt) {
				token.set(res.data.jwt);
				setTimeout(async () => {
					await fetchIsLogin();
					await fetchUser();
					if (__WEBSHOP__) await webshop.fetchCart(); // Fetch cart after login to get updated loyalty and other user specific data
				}, 200);
			}
			return res.data;
		});
	}

	// check if user is logged in. HAPI docs https://hapi.marker.hr/#/Auth/get_v1_auth_is_login_
	async function fetchIsLogin(options) {
		const urlParams = {
			continue_as_guest: 1,
			update_customer_data: 1,
			...options,
		};
		const queryString = new URLSearchParams(urlParams).toString();
		const isLoggedIn = await useApi(`${endpoints.get('_get_hapi_auth_is_login')}?${queryString}`).then(res => (res.data ? res.data : null));
		loggedIn.value = isLoggedIn;
		return isLoggedIn;
	}

	// fetch user profile data
	async function fetchUser() {
		const res = await useApi(`${endpoints.get('_get_hapi_auth_profile')}`);
		user.value = res?.success && res.data ? res.data : null;
		return res;
	}

	function getUser() {
		return user.value;
	}

	// get user id from state
	function isLoggedIn() {
		return loggedIn?.value?.user_id ? true : false;
	}

	function resetUserData() {
		loggedIn.value = false;
		user.value = null;
	}

	// get user guest status from state
	function isGuest() {
		return loggedIn?.value?.continue_as_guest_exist;
	}

	// logout user
	async function logout() {
		return await useApi(`${endpoints.get('_post_hapi_auth_logout')}`, {method: 'POST'}).then(async res => {
			if (res.data?.jwt) token.set(res.data.jwt);
			setTimeout(async () => {
				await fetchIsLogin();
				if (__WEBSHOP__) await webshop.fetchCart();
			}, 500);
			user.value = null;
			return res.data;
		});
	}

	// fetch user coupons
	async function fetchCoupons() {
		return await useApi(`${endpoints.get('_get_hapi_auth_my_webshopcoupon')}?all=1`);
	}

	// add coupon
	async function addCoupon(options) {
		return await useApi(`${endpoints.get('_post_hapi_auth_my_webshopcoupon')}`, {
			method: 'POST',
			body: options,
		});
	}

	// remove coupon
	async function removeCoupon(options) {
		return await useApi(`${endpoints.get('_delete_hapi_auth_my_webshopcoupon')}`, {
			method: 'DELETE',
			body: options,
		});
	}

	// fetch user orders
	async function fetchOrders(options) {
		const urlParams = options ? (Object.keys(options).length ? '?' + new URLSearchParams(options).toString() : '') : '';
		return await useApi(`${endpoints.get('_get_hapi_auth_my_webshoporder')}${urlParams}`, null, {appendLang: true});
	}

	// fetch user form
	async function fetchForm(options) {
		const urlParams = options ? (Object.keys(options).length ? '?' + new URLSearchParams(options).toString() : '') : '';
		return await useApi(`${endpoints.get('_get_hapi_customer_fields')}${urlParams}`, null, {appendLang: true});
	}

	// signup user
	async function signup(options) {
		return await useApi(`${endpoints.get('_post_hapi_auth_signup')}`, {
			method: 'POST',
			body: options,
		});
	}

	// quick signup user
	async function quickSignup(options) {
		return await useApi(`${endpoints.get('_post_hapi_auth_signup_quick')}`, {
			method: 'POST',
			body: options,
		});
	}

	async function socialLogin(options) {
		let ep = endpoints.get('_get_hapi_auth_hybridauth');
		ep = ep.replace('%PROVIDER%', options.provider);
		return await useApi(ep);
	}

	// confirm signup
	async function confirmSignup(options) {
		let ep = endpoints.get('_get_hapi_auth_signup_confirm');
		ep = ep.replace('%ID%', options.id).replace('%CODE%', options.code);
		return await useApi(ep);
	}

	// send forgotten password email
	async function forgottenPassword(option) {
		return await useApi(`${endpoints.get('_post_hapi_auth_forgotten_password')}`, {
			method: 'POST',
			body: option,
		});
	}

	// send forgotten password email to external api (keycloak)
	async function forgottenPasswordApi(option) {
		return await useApi(`${endpoints.get('_post_hapi_auth_forgotten_password_api')}`, {
			method: 'POST',
			body: option,
		});
	}

	// validate user that is resetting password
	async function confirmForgottenPassword(options) {
		let ep = endpoints.get('_get_hapi_auth_forgotten_password_check');
		ep = ep.replace('%ID%', options.id).replace('%CODE%', options.code);
		return await useApi(ep, null, {appendLang: true});
	}

	// generate new password
	async function generateNewPassword(options) {
		return await useApi(`${endpoints.get('_post_hapi_auth_new_password')}`, {
			method: 'POST',
			body: options,
		});
	}

	// update user profile
	async function updateProfile(options) {
		return await useApi(`${endpoints.get('_post_hapi_auth_profile')}`, {
			method: 'POST',
			body: options,
		});
	}

	// change user password
	async function changePassword(options) {
		return await useApi(`${endpoints.get('_post_hapi_auth_change_password')}`, {
			method: 'POST',
			body: options,
		});
	}

	// fetch user comments
	async function fetchComments() {
		try {
			return await useApi(`${endpoints.get('_get_hapi_auth_my_comments')}?all=1`, null, {appendLang: true});
		} catch (err) {
			useLog(err, 'error');
		}
	}

	return {
		user,
		loggedIn,
		resetUserData,
		login,
		fetchIsLogin,
		fetchUser,
		getUser,
		isLoggedIn,
		isGuest,
		logout,
		fetchCoupons,
		addCoupon,
		removeCoupon,
		fetchOrders,
		fetchForm,
		signup,
		quickSignup,
		socialLogin,
		confirmSignup,
		forgottenPassword,
		forgottenPasswordApi,
		confirmForgottenPassword,
		generateNewPassword,
		updateProfile,
		changePassword,
		fetchComments,
	};
}
