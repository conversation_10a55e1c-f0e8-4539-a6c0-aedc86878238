export function useDom() {
	const {debounce} = useUtils();
	let domObserver = null;
	let observerCallbacks = new Map();
	let appendTimeouts = new Map(); // Store timeouts

	// Shared observer setup
	function setupDomObserver() {
		if (process.server || domObserver) return;

		domObserver = new MutationObserver(() => {
			observerCallbacks.forEach((callback, id) => {
				if (callback()) {
					observerCallbacks.delete(id);
				}
			});

			// Disconnect observer when no more callbacks
			if (observerCallbacks.size === 0) {
				domObserver.disconnect();
				domObserver = null;
			}
		});

		domObserver.observe(document.body, {
			childList: true,
			subtree: true,
		});
	}

	// get all sibling elements
	function getSiblings(e) {
		let siblings = [];
		if (!e.parentNode) {
			return siblings;
		}
		let sibling = e.parentNode.firstChild;
		while (sibling) {
			if (sibling.nodeType === 1 && sibling !== e) {
				siblings.push(sibling);
			}
			sibling = sibling.nextSibling;
		}
		return siblings;
	}

	// set focus on desired form field (field name). If field name is not provided, set focus on first field found in the form
	function setFieldFocus() {
		const form = document.querySelectorAll('form[data-autofocus]');
		if (form) {
			form.forEach(item => {
				const autofocusField = item.dataset.autofocus;
				const formField = autofocusField ? item.querySelector(`input[name="${autofocusField}"]`) : item.querySelector('input');
				if (formField) formField.focus();
			});
		}
	}

	// Helper function for DOM operations
	function performDomOperation(referenceSelector, targetSelector, operation) {
		if (process.server) return;
		if (!referenceSelector || !targetSelector) {
			return console.error('Reference selector and target selector are required.');
		}

		return new Promise(resolve => {
			const executeOperation = () => {
				const referenceElem = document.querySelector(referenceSelector);
				const targetElem = document.querySelector(targetSelector);

				if (referenceElem && targetElem) {
					operation(referenceElem, targetElem);
					resolve(true);
					return true;
				}
				return false;
			};

			// Try immediately first
			if (executeOperation()) return;

			// Add to observer callbacks
			const callbackId = Date.now() + Math.random();
			observerCallbacks.set(callbackId, executeOperation);
			setupDomObserver();

			// Safety timeout
			const timeoutId = setTimeout(() => {
				if (observerCallbacks.has(callbackId)) {
					observerCallbacks.delete(callbackId);
					resolve(false);

					if (observerCallbacks.size === 0 && domObserver) {
						domObserver.disconnect();
						domObserver = null;
					}
				}
				appendTimeouts.delete(callbackId);
			}, 5000);

			appendTimeouts.set(callbackId, timeoutId);
		});
	}

	function insertBefore(referenceSelector, targetSelector) {
		return performDomOperation(referenceSelector, targetSelector, (referenceElem, targetElem) => {
			if (targetElem.parentNode) {
				targetElem.parentNode.insertBefore(referenceElem, targetElem);
			}
		});
	}

	function insertAfter(referenceSelector, targetSelector) {
		return performDomOperation(referenceSelector, targetSelector, (referenceElem, targetElem) => {
			if (targetElem.parentNode) {
				if (targetElem.nextElementSibling) {
					targetElem.parentNode.insertBefore(referenceElem, targetElem.nextElementSibling);
				} else {
					targetElem.parentNode.appendChild(referenceElem);
				}
			}
		});
	}

	function appendTo(referenceSelector, targetSelector) {
		return performDomOperation(referenceSelector, targetSelector, (referenceElem, targetElem) => {
			targetElem.appendChild(referenceElem);
		});
	}

	function prependTo(referenceSelector, targetSelector) {
		return performDomOperation(referenceSelector, targetSelector, (referenceElem, targetElem) => {
			targetElem.insertBefore(referenceElem, targetElem.firstChild);
		});
	}

	// animate to section
	function scrollTo(targetSelector, options) {
		const targetElem = document.querySelector(targetSelector);

		const offsetValue = options?.offset ? options?.offset : 0;
		const behaviorValue = options?.behavior ? options?.behavior : 'smooth';

		if (targetElem) {
			const elementRect = targetElem.getBoundingClientRect();
			const absoluteTop = elementRect.top + window.scrollY;

			window.scrollTo({
				behavior: behaviorValue,
				top: absoluteTop - offsetValue,
			});
		}
	}

	/* 
		Click outside of elements
		onClickOutside(
			<query selector / ref>, 
			event: () => {...}, 
			{
				manual: false
			}
		);
	*/
	function onClickOutside(querySelectors, callback, options = {manual: false}) {
		if (process.server) return {init: () => {}, destroy: () => {}};

		if (!querySelectors) {
			return useLog('onClickOutside: querySelectors not provided.', 'error');
		}

		// Ensure querySelectors is an array
		if (!Array.isArray(querySelectors)) {
			querySelectors = [querySelectors];
		}

		let handleOutsideClick;
		let isListenerAdded = false;

		const init = () => {
			if (isListenerAdded) return; // Prevent duplicate listeners

			handleOutsideClick = event => {
				const clickedInside = querySelectors.some(selector => {
					let elements;

					// Handle ref or standard query selector
					if (typeof selector === 'string') {
						elements = document.querySelectorAll(selector);
					} else if (selector?.value instanceof HTMLElement) {
						elements = [selector.value];
					} else {
						return false;
					}

					return Array.from(elements).some(element => element.contains(event.target));
				});

				if (!clickedInside && callback) {
					callback(event);
				}
			};

			document.addEventListener('click', handleOutsideClick);
			isListenerAdded = true;
		};

		const destroy = () => {
			if (isListenerAdded && handleOutsideClick) {
				document.removeEventListener('click', handleOutsideClick);
				handleOutsideClick = null; // Reset for clean reattachment
				isListenerAdded = false;
			}
		};

		if (!options.manual) {
			onMounted(() => init());
			onBeforeUnmount(() => destroy());
		}

		return {init, destroy};
	}

	/**
	 * Scroll observer that detects scrolling direction and position
	 *
	 * This function allows you to track scrolling within a specific element or the window.
	 * It provides information about the scroll direction (up/down) and the current scroll position.
	 *
	 * @param {Object} options - Configuration options
	 * @param {number} [options.debounce=0] - Debounce time in milliseconds to limit how often the callback is called
	 * @param {Function} [options.callback] - Function called when scrolling occurs, receives {direction, y} as parameters
	 * @param {Ref|string} [options.element=null] - Element to observe scrolling within. Can be a Vue ref or a CSS selector string
	 * @param {boolean} [options.manual=false] - If true, lifecycle hooks won't be used and you need to call init() manually
	 *
	 * @returns {Object} An object containing:
	 *   - direction: Ref<string> - Current scroll direction ('up' or 'down')
	 *   - y: Ref<number> - Current scroll position
	 *   - init: Function - Function to manually initialize the scroll observer
	 *   - destroy: Function - Function to manually destroy the scroll observer
	 *
	 * @example
	 * // Example with all options
	 * const myElement = ref(null);
	 * const { direction, y, init, destroy } = onScroll({
	 *   element: myElement, // Can also be a CSS selector like '.scrollable-container'
	 *   debounce: 100, // Limit callback to once every 100ms
	 *   manual: false, // Manual control of initialization and cleanup
	 *   callback: ({ direction, y }) => {
	 *     console.log(`Scrolling ${direction} at position ${y}`);
	 *   }
	 * });
	 *
	 * // Initialize when ready (only needed if manual: true)
	 * init();
	 *
	 * // Clean up when done (only needed if manual: true)
	 * destroy();
	 */
	function onScroll(options) {
		const direction = ref(null);
		const y = ref(0);

		if (process.server) return {direction, y, init: () => {}, destroy: () => {}};

		let lastScrollY = 0;
		let scrollElement = null;
		let isInitialized = false;

		/**
		 * Handles scroll events and updates direction and position
		 */
		const onScrollHandler = () => {
			if (!scrollElement) return;

			const currentScrollY = scrollElement === window ? window.scrollY : scrollElement.scrollTop;
			y.value = currentScrollY;
			if (currentScrollY > lastScrollY) {
				direction.value = 'down';
			} else if (currentScrollY < lastScrollY) {
				direction.value = 'up';
			}
			lastScrollY = currentScrollY;
			if (typeof options?.callback === 'function') options.callback({direction: direction.value, y: y.value});
		};

		// Apply debounce if specified
		const debouncedOnScroll = options?.debounce ? debounce(onScrollHandler, options?.debounce) : onScrollHandler;

		/**
		 * Gets the element to observe scrolling within
		 * @returns {HTMLElement|Window} The element to observe
		 */
		const getElement = () => {
			// Handle ref
			if (options?.element?.value instanceof HTMLElement) {
				return options.element.value;
			}

			// Handle query selector
			if (typeof options?.element === 'string') {
				return document.querySelector(options.element);
			}

			// Default to window
			return window;
		};

		/**
		 * Initializes the scroll listener on the target element
		 */
		const initScrollListener = () => {
			if (isInitialized) return;

			// Get the scroll element
			scrollElement = getElement();

			// Add scroll event listener to the appropriate element
			scrollElement.addEventListener('scroll', debouncedOnScroll, {passive: true});

			// Initialize with current scroll position
			lastScrollY = scrollElement === window ? window.scrollY : scrollElement.scrollTop;
			y.value = lastScrollY;

			isInitialized = true;
		};

		/**
		 * Initializes the scroll observer
		 * This can be called manually if manual mode is enabled
		 */
		const init = () => {
			if (isInitialized) return;

			// Initialize immediately
			nextTick(() => {
				initScrollListener();
			});
		};

		/**
		 * Destroys the scroll observer and cleans up event listeners
		 * This can be called manually if manual mode is enabled
		 */
		const destroy = () => {
			if (scrollElement) {
				scrollElement.removeEventListener('scroll', debouncedOnScroll);
				scrollElement = null;
			}

			isInitialized = false;
		};

		// Use Vue lifecycle hooks if not in manual mode
		if (!options?.manual) {
			try {
				onMounted(() => init());
				onBeforeUnmount(() => destroy());
			} catch (error) {
				// If lifecycle hooks fail, provide manual initialization
				console.warn('Lifecycle hooks not available. Use init() and destroy() manually.');
			}
		}

		return {direction, y, init, destroy};
	}

	/*
		Element visibility observer
		const {isVisible} = onElementVisibility({
			target: <ref elements>,
			options: null, // https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API
			timeout: 0,
			callback: ({isVisible, id, visibleElements}) => {...}
		});
	*/
	function onElementVisibility(options) {
		const visibilityMap = ref({}); // Store visibility state per element
		if (process.server || !options?.target) return {visibilityMap};

		const targets = Array.isArray(options.target) ? options.target : [options.target];
		let observer = null;
		let timeoutIds = {};
		const elementIds = new Map();

		const getElementId = (element, index) => {
			if (elementIds.has(element)) return elementIds.get(element);
			let id = element.getAttribute('data-observer-id') || `element${index}`;
			elementIds.set(element, id);
			return id;
		};

		const handleIntersect = entries => {
			entries.forEach((entry, index) => {
				const id = getElementId(entry.target, index);
				const isVisible = entry.isIntersecting;

				// Update visibility state
				if (!visibilityMap.value || typeof visibilityMap.value !== 'object') visibilityMap.value = {};
				visibilityMap.value[id] = isVisible;

				const visibleElements = Object.keys(visibilityMap.value).filter(key => visibilityMap.value[key]) || [];

				if (typeof options?.callback === 'function') {
					if (options?.timeout) {
						if (timeoutIds[id]) clearTimeout(timeoutIds[id]);
						timeoutIds[id] = setTimeout(() => {
							options.callback({isVisible, id, visibleElements});
							timeoutIds[id] = null;
						}, options.timeout);
					} else {
						options.callback({isVisible, id, visibleElements});
					}
				}
			});
		};

		const initObserver = () => {
			if (observer) return; // Avoid re-initializing the observer
			observer = new IntersectionObserver(handleIntersect, options?.options || {});
			nextTick(() => {
				targets.forEach(target => {
					if (target.value) observer.observe(target.value);
				});
			});
		};

		if (targets?.length) {
			onMounted(() => {
				nextTick(() => initObserver());
			});
		}

		onBeforeUnmount(() => {
			if (observer) {
				targets?.forEach(target => {
					if (target.value) {
						observer.unobserve(target.value);
					}
				});
				observer.disconnect();
				observer = null;
			}
			Object.keys(timeoutIds).forEach(id => {
				if (timeoutIds[id]) clearTimeout(timeoutIds[id]);
			});
			timeoutIds = {};
			elementIds.clear();
		});

		// Returns boolean for single target, object for multiple targets
		const isElementVisible = computed(() => {
			if (targets.length === 1) {
				const firstKey = Object.keys(visibilityMap.value)[0];
				return firstKey ? visibilityMap.value[firstKey] : false;
			}
			return visibilityMap.value;
		});

		return {isVisible: isElementVisible};
	}

	/*
		Page leave observer
		const {hasLeftPage} = onPageLeave({
			callback: ({hasLeftPage}) => {...}
		});
	*/
	function onPageLeave(options) {
		const hasLeftPage = ref(false);
		if (process.server) return {hasLeftPage};

		const handleMouseOut = event => {
			if (!event.relatedTarget && !event.toElement) {
				hasLeftPage.value = true;
				if (typeof options?.callback === 'function') options.callback({hasLeftPage: hasLeftPage.value});
			}
		};

		const resetLeaveState = () => {
			hasLeftPage.value = false;
			if (typeof options?.callback === 'function') options.callback({hasLeftPage: hasLeftPage.value});
		};

		onMounted(() => {
			document.addEventListener('mouseout', handleMouseOut);
			document.addEventListener('mouseenter', resetLeaveState);
		});

		onBeforeUnmount(() => {
			document.removeEventListener('mouseout', handleMouseOut);
			document.removeEventListener('mouseenter', resetLeaveState);
		});

		return {hasLeftPage};
	}

	/*
		Resize observer
		const {width, height} = onResize({
			debounce: 0,
			callback: ({width, height}) => {...}
		});
	*/
	function onResize(options) {
		const width = ref(null);
		const height = ref(null);

		if (process.server) return {width, height};

		const setSize = () => {
			width.value = window.innerWidth;
			height.value = window.innerHeight;
		};

		const handleResize = () => {
			setSize();
			if (typeof options?.callback === 'function') options.callback({width: width.value, height: height.value});
		};

		const debouncedResize = options?.debounce ? debounce(handleResize, options.debounce) : handleResize;

		onMounted(() => {
			handleResize();
			window.addEventListener('resize', debouncedResize);
		});

		onBeforeUnmount(() => {
			window.removeEventListener('resize', debouncedResize);
		});

		return {width, height};
	}

	/*
		Media query observer
		const {matches} = onMediaQuery({
			query: '(min-width: 1024px)',
			timeout: 0,
			enter: () => {...},
			leave: () => {...}
		});
	*/
	function onMediaQuery(options) {
		const matches = ref(false);
		if (process.server) return {matches};

		if (!options?.query) {
			useLog('onMediaQuery: query not provided.', 'error');
			return {matches};
		}

		let mediaQueryList = null;
		let timeoutId = null;

		const updateMatch = () => {
			const currentMatch = mediaQueryList.matches;

			if (currentMatch !== matches.value) {
				matches.value = currentMatch;
				if (timeoutId) clearTimeout(timeoutId);
				if (currentMatch && typeof options.enter === 'function') {
					if (options.timeout) {
						timeoutId = setTimeout(() => {
							options.enter();
							timeoutId = null;
						}, options.timeout);
					} else {
						options.enter();
					}
				} else if (!currentMatch && typeof options.leave === 'function') {
					if (options.timeout) {
						timeoutId = setTimeout(() => {
							options.leave();
							timeoutId = null;
						}, options.timeout);
					} else {
						options.leave();
					}
				}
			}
		};

		onMounted(() => {
			mediaQueryList = window.matchMedia(options.query);
			if (mediaQueryList) {
				updateMatch();

				// Fallback to addListener for older browsers
				if (typeof mediaQueryList.addEventListener === 'function') {
					mediaQueryList.addEventListener('change', updateMatch);
				} else if (typeof mediaQueryList.addListener === 'function') {
					mediaQueryList.addListener(updateMatch);
				}
			}
		});

		onBeforeUnmount(() => {
			if (mediaQueryList) {
				if (typeof mediaQueryList.removeEventListener === 'function') {
					mediaQueryList.removeEventListener('change', updateMatch);
				} else if (typeof mediaQueryList.removeListener === 'function') {
					mediaQueryList.removeListener(updateMatch);
				}
			}
			if (timeoutId) clearTimeout(timeoutId);
		});

		return {matches};
	}

	/*
		Extract specific html elements from a string
		const elements = extractElements({
			source: '<p>hello</p><p>world</p>',
			tag: 'p'
			includeElement: true
		});
	*/
	function extractElements(options) {
		if (!options?.source || typeof options?.source !== 'string' || !options?.tag) return [];
		let matches;
		let elements = [];
		const regex = new RegExp(`<${options.tag}\\b[^>]*${options.tag === 'img' ? '/?' : ''}>${options.tag === 'img' ? '' : '(.*?)<\\/' + options.tag + '>'}`, 'g'); // Match self-closing and non-self-closing tags

		while ((matches = regex.exec(options.source))) {
			if (options?.includeElement === false) {
				// Return inner content
				elements.push(matches[1] ? matches[1].trim() : null);
			} else {
				// Return the full element
				elements.push(matches[0]);
			}
		}

		return elements.filter(el => el !== null);
	}

	/*
		Toggle password visibility
		togglePassword({
			toggler: 'toggle element selector',
			input: 'input element selector',
		})
	*/
	function togglePassword(options) {
		if (!options?.toggler || !options?.input) {
			console.error('Toggle password: toggle element or input not provided.');
			return;
		}

		document.addEventListener('change', event => {
			const toggler = event.target.closest(options.toggler);
			if (!toggler) return;

			const input = document.querySelector(options.input);
			if (input) {
				input.type = toggler.checked ? 'text' : 'password';
			}
		});
	}

	// Clean up function
	function cleanupDomObserver() {
		if (domObserver) {
			domObserver.disconnect();
			domObserver = null;
		}
		observerCallbacks.clear();
		// Clear all timeouts
		appendTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
		appendTimeouts.clear();
	}

	// Cleanup on component unmount
	onBeforeUnmount(() => {
		cleanupDomObserver();
	});

	// Cleanup on window resize
	if (process.client) {
		window.addEventListener('resize', cleanupDomObserver);
		onBeforeUnmount(() => {
			window.removeEventListener('resize', cleanupDomObserver);
		});
	}

	return {getSiblings, setFieldFocus, insertBefore, insertAfter, appendTo, prependTo, scrollTo, onClickOutside, onScroll, onElementVisibility, onPageLeave, onResize, onMediaQuery, extractElements, togglePassword};
}
