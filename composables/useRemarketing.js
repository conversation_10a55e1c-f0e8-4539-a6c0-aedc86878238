export function useRemarketing() {
	const config = useAppConfig();
	const trackingConfig = config.google?.remarketing;
	const {gdprApproved} = useGdpr();
	const env = process.env.NODE_ENV;

	function sendEvent(event, payload) {
		// Exit if event is not defined
		if (!event || !trackingConfig?.events?.includes(event)) return;
		if (trackingConfig.gdpr && !gdprApproved(trackingConfig.gdpr)) return;
		if (!trackingConfig.env.includes(env)) return;

		const data = {};

		const allowedData = data => {
			if (!trackingConfig[event]?.data?.length) return true;
			if (trackingConfig[event]?.data?.includes(data)) return true;
			return false;
		};

		if (Array.isArray(payload?.items)) {
			let ids = [];
			let codes = [];
			let total = 0;
			payload?.items.forEach(item => {
				if (item?.id) ids.push(item.id);
				if (item?.code) codes.push(item.code);
				if (item?.price_custom) total += Number(item.price_custom);
			});

			if (allowedData('id') && ids.length) data.dynx_itemid = ids;
			if (allowedData('id') && ids.length == 1) data.dynx_itemid = ids[0];

			if (allowedData('code') && codes.length) data.dynx_itemid2 = codes;
			if (allowedData('code') && codes.length == 1) data.dynx_itemid2 = codes[0];

			if (allowedData('total') && total) data.dynx_totalvalue = total;
		} else {
			if (allowedData('id') && payload?.items.id) data.dynx_itemid = payload.items.id;
			if (allowedData('code') && payload?.items.code) data.dynx_itemid2 = payload.items.code;
			if (allowedData('total') && payload?.items.price_custom) data.dynx_totalvalue = Number(payload.items.price_custom);
		}

		if (allowedData('pagetype')) data.dynx_pagetype = event;
		if (allowedData('total') && payload?.total) data.dynx_totalvalue = Number(payload.total);

		pushToDataLayer(event, data);
	}

	// Wait for window to load
	function waitForWindowAndExecute(action, interval = 1000, timeout = 20000) {
		let elapsedTime = 0;

		function checkWindow() {
			if (typeof window !== 'undefined' && typeof window?.dataLayer !== 'undefined') {
				action();
			} else if (elapsedTime < timeout) {
				elapsedTime += interval;
				setTimeout(checkWindow, interval);
			}
		}

		checkWindow();
	}

	// Push data to GTM dataLayer
	function pushToDataLayer(event, data) {
		if (process.server) return;

		waitForWindowAndExecute(
			() => {
				if (window?.dataLayer) {
					dataLayer.push(data);
					if (trackingConfig?.debug) console.log('remarketing', event, data);
				}
			},
			1000,
			20000
		); // Interval of 1000ms and timeout of 20000ms (20 seconds)
	}

	return {sendEvent};
}
