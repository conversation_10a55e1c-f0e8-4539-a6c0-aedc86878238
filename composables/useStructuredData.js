export function useStructuredData() {
	const config = useAppConfig();
	const {getInfo} = useInfo();
	const {getCurrency} = useCurrency();
	const {stripHtml, absoluteImagePath, formatDate, limitWords} = useText();

	// generate schema.org structured data if enabled
	function generateStructuredData(options) {
		let schema = '';

		// generate schema for homepage
		if (config.structuredData?.basic && options.controller == 'cms' && options.template == 'CmsHomepage') {
			schema = basicData();
		}

		// generate schema for publish category page
		if (options.controller == 'publish' && options.action == 'index' && options.contentType == 'category') {
			schema = publishCategoryData(options.data);
		}

		// generate schema for publish detail page
		if (options.controller == 'publish' && options.action == 'detail') {
			schema = publishDetailData(options.data);
		}

		// generate schema for catalog category page
		if (options.controller == 'catalog' && options.action == 'index' && options.contentType == 'category') {
			schema = catalogCategoryData(options.data);
		}

		// generate schema for product page
		if (options.controller == 'catalog' && options.action == 'detail') {
			schema = catalogDetailData(options.data);
		}

		return schema;
	}

	function basicData() {
		return `{
			"@context": "https://schema.org",
			"@type": "WebSite",
			"url": "${getInfo('site_url')}",
			"name": "${config.structuredData.basic.name}",
			"author": {
				"@type": "Organization",
				"name": "${config.structuredData.basic.organizationName}",
				"url": "${getInfo('site_url')}",
				${config.structuredData.basic.logo ? '"logo": "' + config.structuredData.basic.logo + '",' : ''}
				"contactPoint": {
					"@type": "ContactPoint",
					${config.structuredData.basic.phone ? '"telephone": "' + config.structuredData.basic.phone + '",' : ''}
					${config.structuredData.basic.email ? '"email": "' + config.structuredData.basic.email + '",' : ''}
					"contactType": "Customer Service"
				}
				${config.structuredData.basic.socialNetworkUrls ? ',"sameAs": [' + config.structuredData.basic.socialNetworkUrls + ']' : ''}
			}
		}`;
	}

	function publishCategoryData(data) {
		const bc = breadcrumbs(data);

		return `{
			"@context": "https://schema.org",
			"@graph": [
				{
					"@type": "CollectionPage",
					"name": "${data.title?.replace(/"/g, '\\"')}"
				},
				{
					"@type": "BreadcrumbList",
					"itemListElement": [${bc}]
				}
			]
		}`;
	}

	function breadcrumbs(data) {
		let bc = '';
		if (data?.breadcrumbs?.length) {
			const totalBreadcrumbs = data.breadcrumbs.length;
			data.breadcrumbs.forEach((el, index) => {
				bc += `
					{
						"@type": "ListItem",
						"position": ${index + 1},
						"item": {
							"@id": "${el.url}",
							"name": "${el.title?.replace(/"/g, '\\"')}"
						}
					}${index < totalBreadcrumbs - 1 ? ',' : ''}
				`;
			});
		}

		return bc;
	}

	function publishDetailData(data) {
		const bc = breadcrumbs(data);
		return `{
			"@context": "https://schema.org",
			"@graph": [
				{
					"@type": "BlogPosting",
					"headline": "${data.seo_title?.replace(/"/g, '\\"')}",
					"description": "${description(data)}",
					${author(data)}
					"publisher": {
						"@type": "Organization",
						"name": "${config.structuredData.basic.organizationName}",
						"logo": {
							${config.structuredData.basic.logo ? '"@type": "ImageObject",' : ''}
							${config.structuredData.basic.logo ? '"url": "' + config.structuredData.basic.logo + '"' : ''}
						}
					},
					"datePublished": "${formatDate(data.datetime_published, 'YYYY-MM-DD')}",
					"dateModified": "${formatDate(data.datetime_updated, 'YYYY-MM-DD')}"
					${rating(data)}
					${reviews(data)}
					,"mainEntityOfPage": {
						"@type": "WebPage",
						"@id": "${data.url}"
					},
					"image": {
						"@type": "ImageObject",
						"url": "${absoluteImagePath(data.main_image_upload_path)}"
					}
				},
				{
					"@type": "BreadcrumbList",
					"itemListElement": [${bc}]
				}
			]
		}`;
	}

	function catalogCategoryData(data) {
		const bc = breadcrumbs(data);

		return `{
			"@context": "https://schema.org",
			"@graph": [
				{
					"@type": "CollectionPage",
					"name": "${data.title?.replace(/"/g, '\\"')}"
				},
				{
					"@type": "BreadcrumbList",
					"itemListElement": [${bc}]
				}
			]
		}`;
	}

	function catalogDetailData(data) {
		const bc = breadcrumbs(data);

		return `{
			"@context": "https://schema.org",
			"@graph": [
				{
					"@type": "Product",
					"name": "${data.title?.replace(/"/g, '\\"')}",
					"sku": "${data.code}",
					"mpn": "${data.id}",
					"image": ["${absoluteImagePath(data.main_image_upload_path)}"],
					"description": "${description(data)}"
					${brand(data)}
					${rating(data)}
					${reviews(data)}
					,"offers": {
						"@type": "Offer",
						"url": "${data.url}",
						"priceCurrency": "${getCurrency('code')}",
						${price(data)},
						${availability(data)},
						"itemCondition": "https://schema.org/NewCondition",
						"seller": {
							"@type": "Organization",
							"name": "${config.structuredData.basic.organizationName}"
						}
					}
				},
				{
					"@type": "BreadcrumbList",
					"itemListElement": [${bc}]
				}
			]
		}`;
	}

	function author(data) {
		if (data.author) {
			return `"author": {
				"@type": "Person",
				"name": "${data.author}"
			},`;
		} else {
			return `"author": {
				"@type": "Person",
				"name": "${config.structuredData.basic.organizationName}"
			},`;
		}
	}

	function description(data) {
		let description = '';
		if (data.seo_description) {
			description = data.seo_description;
		} else if (data.short_description) {
			description = data.short_description;
		} else if (data.content) {
			description = data.content;
		}

		description = description.replace(/"/g, '\\"'); // escape double quotes
		return limitWords(stripHtml(description), 50);
	}

	function brand(data) {
		if (data.manufacturer_title) {
			return `,"brand": {
				"@type": "Brand",
				"name": "${data.manufacturer_title}"
			}`;
		}

		return '';
	}

	function rating(data) {
		if (data.feedback_rate_widget && Number(data.feedback_rate_widget?.rates_votes)) {
			return `,"aggregateRating": {
					"@type": "AggregateRating",
					"ratingValue": "${data.feedback_rate_widget.rates}",
					"reviewCount": "${data.feedback_rate_widget.rates_votes}"
				}`;
		}

		return '';
	}

	function reviews(data) {
		if (data.feedback_comment_widget?.items) {
			let reviews = '';
			const items = Object.values(data.feedback_comment_widget.items);
			const totalReviews = items.length;
			items.forEach((el, index) => {
				reviews += `{
						"@type": "Review",
						"author": {
							"@type": "Person",
							"name": "${el.display_name}"
						},
						${el.message ? '"reviewBody": "' + stripHtml(el.message) + '",' : ''}
						"reviewRating": {
							"@type": "Rating",
							"ratingValue": "${el.rate}"
							${data.feedback_rate_widget?.rates && ',"bestRating": "' + Math.ceil(data.feedback_rate_widget.rates) + '"'}
						}
					}${index < totalReviews - 1 ? ',' : ''}`;
			});

			return `,"review": [${reviews}]`;
		}

		return '';
	}

	function availability(data) {
		return data?.is_available ? '"availability": "https://schema.org/InStock"' : '"availability": "https://schema.org/OutOfStock"';
	}

	function price(data) {
		if (data.basic_price_custom && data.price_custom && data.basic_price_custom > data.price_custom) {
			return `
				"price": "${data.basic_price_custom}",
				"sale_price": "${data.price_custom}"
			`;
		} else {
			return `"price": "${data.price_custom}"`;
		}
	}

	return {generateStructuredData};
}
