export default function useRecaptcha() {
	const {getInfo} = useInfo();
	const {waitForWindowProperty} = useMeta();

	async function fetchRecaptchaToken(options) {
		const key = options?.key || getInfo('recaptcha_site_key');
		if (!key) return null;

		let token;

		if (window?.grecaptcha) {
			token = await window.grecaptcha.execute(key);
		} else {
			token = await new Promise((resolve, reject) => {
				waitForWindowProperty(
					'grecaptcha',
					async grecaptcha => {
						try {
							const action = options?.action || 'submit';
							const recaptchaToken = await grecaptcha.execute(key, {action});
							resolve(recaptchaToken);
						} catch (error) {
							reject(error);
						}
					},
					{
						onFailure: () => reject(new Error('window.grecaptcha not available. Check if recaptcha site key is provided')),
					}
				);
			});
		}

		return token;
	}

	return {
		fetchRecaptchaToken,
	};
}
