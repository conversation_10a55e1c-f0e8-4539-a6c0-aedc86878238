export function usePage() {
	const nuxtApp = useNuxtApp();
	const {absoluteImagePath} = useText();
	const {stripLangFromUrl} = useUrl();
	const route = nuxtApp._route;
	const lang = useLang();

	async function fetch(options = {}, config = {}) {
		// set fetch options
		let fetchOptions = {
			lang: options?.lang ? options.lang : lang.get(),
			...options,
		};

		// if slug and mode is not provided, use current route
		if (!options?.mode) {
			const slug = options?.slug ? options.slug : route.path;
			fetchOptions.slug = stripLangFromUrl(slug);
		}

		// append fetch options as url params and fetch data
		const urlParams = Object.keys(fetchOptions).length ? '?' + new URLSearchParams(fetchOptions).toString() : '';

		return await useApi(
			'/api/nuxtapi/pages/' + urlParams,
			{
				method: 'GET',
				transform(res) {
					if (options?.mode === 'search') return res.data;
					const page = res.data?.length ? res.data[0] : null;
					if (page) page.content = absoluteImagePath(page.content);
					return page;
				},
			},
			{cache: true, ...config, key: {slug: fetchOptions.slug}}
		);
	}

	return {fetch};
}
