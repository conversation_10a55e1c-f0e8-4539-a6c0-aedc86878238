export function useSiteform() {
	const endpoints = useEndpoints();

	// Fetch form fields
	/*
	 * types: 'contact', 'tellafriend'
	 */
	async function fetchForm(options) {
		if (!options?.type) useLog('Missing form type', 'error');
		const ep = endpoints.get('_get_hapi_siteform_form').replace('%TYPE%', options.type);
		return await useApi(ep, null, {cache: true, appendLang: true});
	}

	// Submit form data
	async function submitForm(options) {
		if (!options?.type || !options?.values) useLog('Missing required form options', 'error');
		return await useApi(`${endpoints.get('_post_hapi_siteform_form')}?type=${options.type}`, {
			method: 'POST',
			body: options.values,
		});
	}

	return {fetchForm, submitForm};
}
