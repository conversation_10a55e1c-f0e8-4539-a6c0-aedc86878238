export function useFeedback() {
	const nuxtApp = useNuxtApp();
	const endpoints = useEndpoints();
	const lang = useLang();

	// fetch notify form fields
	async function fetchNotifymeForm(contentType = nuxtApp.$appGlobalData?.feedbackType, id = nuxtApp.$appGlobalData?.entityId) {
		if (!contentType || !id) {
			useLog('fetchNotifymeForm: contentType or id not available', 'error');
			return null;
		}
		return useApi(`${endpoints.get('_get_hapi_feedback_notifyme_form')}?id=${contentType}-${id}-${lang.get()}`, null);
	}

	// comment rating
	async function rateComment(commentId, rate) {
		return useApi(endpoints.get('_post_hapi_feedback_rate_comment'), {
			method: 'POST',
			body: {
				id: commentId,
				review: rate,
			},
		});
	}

	// fetch comment form fields
	async function fetchCommentsForm(contentType = nuxtApp.$appGlobalData?.feedbackType, id = nuxtApp.$appGlobalData?.entityId) {
		if (!contentType || !id) {
			useLog('fetchCommentsForm: contentType or id not available', 'error');
			return null;
		}
		return useApi(`${endpoints.get('_get_hapi_feedback_comment_form')}?id=${contentType}-${id}-${lang.get()}`, null);
	}

	// submit comment - https://hapi.marker.hr/#/Feedback/post_v1_feedback_comment_
	async function submitComment(values) {
		return useApi(endpoints.get('_post_hapi_feedback_comment'), {
			method: 'POST',
			body: values,
		});
	}

	// submit notify
	async function submitNotifyme(values) {
		return useApi(endpoints.get('_post_hapi_feedback_notifyme'), {
			method: 'POST',
			body: values,
		});
	}

	return {fetchNotifymeForm, rateComment, fetchCommentsForm, submitComment, submitNotifyme};
}
