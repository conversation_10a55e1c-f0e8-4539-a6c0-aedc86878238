export function useCatalog() {
	const nuxtApp = useNuxtApp();
	const endpoints = useEndpoints();
	const appConfig = useAppConfig();
	const route = nuxtApp._route;
	const {absoluteImagePath} = useText();
	const {getLastUrlSegment, getUrlSegments} = useUrl();

	const category = useState('catalogCategory', () => null);
	const comparedProducts = useState('comparedProducts', () => []);
	const wishlistProducts = useState('wishlistProducts', () => []);
	const product = useState('product', () => null);

	async function fetchCategoryBySlug(options, config = {}) {
		let slug = options?.slug ? options.slug : route.path;
		slug = getUrlSegments(slug, {ignoreLang: true, offset: __CATALOG_SEO_URL__ || __CATEGORY_WITHOUT_BASE_URL__ ? 0 : 1, stringify: true});
		if (!slug) return null; // do not fetch category if slug is not provided

		const fetchOptions = {
			...options,
			slug: slug,
			mode: 'full',
			single: true,
		};

		const res = await useApi(
			'/api/nuxtapi/catalog/categories/',
			{
				method: 'POST',
				body: fetchOptions,
			},
			{cache: true, key: options, ...config}
		);

		category.value = res?.data?.length ? res.data[0] : null;
		return res;
	}

	// fetch categories
	async function fetchCategories(options, config = {}) {
		return await useApi(
			'/api/nuxtapi/catalog/categories/',
			{
				method: 'POST',
				body: options,
			},
			{cache: true, key: options, ...config}
		);
	}

	// fetch products
	async function fetchProducts(options, config = {}) {
		// Use hapi endpoint if special view is enabled (for single user last viewed products)
		const fetchUrl = config?.cache == false && options?.special_view ? endpoints.get('_post_hapi_catalog_products') : '/api/nuxtapi/catalog/products/';
		return await useApi(
			fetchUrl,
			{
				method: 'POST',
				body: options,
			},
			{cache: true, key: options, ...config}
		);
	}

	// fetch products price and qty (for users with special prices)
	async function fetchProductsPriceQty(options, config = {}) {
		return await useApi(
			endpoints.get('_post_hapi_catalog_products_price_qty'),
			{
				method: 'POST',
				body: options,
			},
			{cache: true, key: options, ...config}
		);
	}

	// process product data with price and qty
	async function updateProductsPriceQty(data) {
		// if data is object (single product)
		if (!Array.isArray(data) && data.id) {
			const productPrices = await fetchProductsPriceQty({ids: [data.id]});
			if (productPrices?.data?.items?.length) {
				data = {...data, ...productPrices.data.items[0], priceQtyLoaded: true};
			}
			return data;
		}

		// if data is array (product lists)
		if (Array.isArray(data) && data.length) {
			const productPrices = await fetchProductsPriceQty({ids: data.map(el => el.id)});

			let tempItems = [];
			if (productPrices?.data?.items?.length) {
				tempItems = data.map(el => {
					const product = productPrices.data.items.find(p => p.id == el.id);
					if (product) {
						el = {...el, ...product, priceQtyLoaded: true};
					}
					return el;
				});
			}
			return tempItems.length ? tempItems : data;
		}
	}

	// fetch lists
	async function fetchLists(options, config = {}) {
		return useApi(
			'/api/nuxtapi/catalog/lists/',
			{
				method: 'POST',
				body: options,
			},
			{cache: true, key: options, ...config}
		);
	}

	// fetch sellers
	async function fetchSellers(options, config = {}) {
		return await useApi(
			'/api/nuxtapi/catalog/sellers/',
			{
				method: 'POST',
				body: options,
			},
			{cache: true, key: options, ...config}
		);
	}

	// fetch product
	async function fetchProduct(options, config = {}) {
		const url = options?.item_slug ? options.item_slug : route.path;
		const slug = getLastUrlSegment(url);

		// get product id and slug from url
		const productUrl = slug.split('-');
		const productId = productUrl[productUrl.length - 1];
		const productSlug = productUrl.slice(0, -2).join('-');

		// set fetch options
		const fetchOptions = {
			...options,
			item_slug: productSlug,
			item_id: options?.item_id ? options.item_id : productId,
		};

		let data = await useApi(`/api/nuxtapi/catalog/product/`, {method: 'POST', body: fetchOptions}, {cache: true, key: fetchOptions, ...config}).then(res => (res?.data ? res.data : null));

		// keep the same state data if redirect url is the same as current route
		if (data?.redirect_url_without_domain && data.redirect_url_without_domain == route.fullPath && product.value) {
			return product.value;
		}

		// convert relative /upload/ image paths to absolute on dev environment
		if (data?.content) data.content = absoluteImagePath(data.content);

		// callback function to modify data
		if (appConfig?.catalog?.hooks?.mapProduct) {
			data = await appConfig.catalog.hooks.mapProduct(data);
		}

		// save product to state. This is needed to avoid duplicate fetching on product page
		product.value = data;
		return data;
	}

	// fetch manufacturers
	async function fetchManufacturers(options, config = {}) {
		const fetchOptions = {
			...options,
		};

		// deconstruct id parameter if passed as comma separated string
		if (options?.id && typeof options.id == 'string') {
			const ids = options.id?.split(',');
			fetchOptions.id = ids.filter(el => el != '');
		}

		return await useApi(
			'/api/nuxtapi/catalog/manufacturers/',
			{
				method: 'POST',
				body: fetchOptions,
				...config,
			},
			{cache: true, key: fetchOptions, ...config}
		);
	}

	let wishlistProductsTimeout;
	let wishlistRetryCount = 0;

	async function fetchWishlistProducts(options, config = {}) {
		if (wishlistProductsTimeout) clearTimeout(wishlistProductsTimeout);
		const retryTimeout = options?.retryTimeout || 2000;
		const maxRetries = options?.maxRetries || 5;
		if (wishlistRetryCount >= maxRetries) return;

		const slug = options ? (Object.keys(options).length ? '?' + new URLSearchParams(options).toString() : '') : '';
		const res = await useApi(`${endpoints.get('_get_hapi_catalog_my_wishlist')}${slug}`, null, {appendLang: true, ...config});
		const items = res.data?.items?.length ? res.data.items : [];

		// recheck if item is added or removed from wishlist (slow server response hack)
		if ((options?.retry == 'remove' && items.find(item => item.shopping_cart_code == options.shopping_cart_code)) || (options?.retry == 'add' && !items.find(item => item.shopping_cart_code == options.shopping_cart_code))) {
			wishlistRetryCount++;
			return new Promise(resolve => {
				wishlistProductsTimeout = setTimeout(async () => {
					resolve(await fetchWishlistProducts(options));
				}, retryTimeout);
			});
		}

		wishlistProducts.value = res?.data;
		wishlistRetryCount = 0;
		return res;
	}

	// Remove all wishlist products
	async function removeWishlistProducts(options, config = {}) {
		return await useApi(
			`${endpoints.get('_delete_hapi_catalog_wishlist')}`,
			{
				method: 'DELETE',
				body: {id: options || 0},
			},
			config
		);
	}

	// fetch wishlist categories
	async function fetchWishlistCategories(options, config = {}) {
		const res = await useApi(`${endpoints.get('_get_hapi_catalog_my_wishlist_categories')}?level_from=${options.level_from}&level_to=${options.level_to}`, null, {fappendLang: true, ...config});

		return res;
	}

	// get wishlist products from state
	function getWishlistProducts() {
		return wishlistProducts.value;
	}

	/*
		Add or remove product from wishlist
		{
			shopping_cart_code: '1234567890', // product code
			qty: 1, // 1 to add, 0 to remove
		}
	*/
	async function addRemoveWishlistProducts(options, config = {}) {
		return await useApi(
			`${endpoints.get('_post_hapi_catalog_wishlist')}`,
			{
				method: 'POST',
				body: {model: 'catalogproduct', code: options.shopping_cart_code, qty: options.qty},
			},
			config
		);
	}

	// fetch compare products
	let comparedProductsTimeout;
	let retryCount = 0;
	async function fetchComparedProducts(options, config = {}) {
		if (comparedProductsTimeout) clearTimeout(comparedProductsTimeout);
		const retryTimeout = options?.retryTimeout || 2000;
		const maxRetries = options?.maxRetries || 5;
		if (retryCount >= maxRetries) return;

		const time = new Date().getTime();
		const res = await useApi(`${endpoints.get('_get_hapi_catalog_compare')}?mode=normal&time=${time}`, null, config);
		const items = res.data?.items?.length ? res.data.items : [];

		// recheck if item is added or removed from compare list (slow server response hack)
		if ((options?.retry == 'remove' && items.find(item => item.id == options.itemId)) || (options?.retry == 'add' && !items.find(item => item.id == options.itemId))) {
			retryCount++;
			return new Promise(resolve => {
				comparedProductsTimeout = setTimeout(async () => {
					resolve(await fetchComparedProducts(options));
				}, retryTimeout);
			});
		}

		comparedProducts.value = items;
		retryCount = 0;
		return res;
	}

	// get compared products from state
	function getComparedProducts() {
		return comparedProducts.value;
	}

	// add to compare
	async function addToCompare(options, config = {}) {
		return await useApi(
			`${endpoints.get('_post_hapi_catalog_compare')}`,
			{
				method: 'POST',
				body: {shopping_cart_code: options.shopping_cart_code},
			},
			config
		);
	}

	// remove from compare
	async function removeFromCompare(options, config = {}) {
		return await useApi(
			`${endpoints.get('_delete_hapi_catalog_compare')}`,
			{
				method: 'DELETE',
				body: {shopping_cart_code: options.shopping_cart_code},
			},
			config
		);
	}

	return {
		fetchCategoryBySlug,
		fetchCategories,
		fetchProducts,
		fetchProductsPriceQty,
		updateProductsPriceQty,
		fetchProduct,
		fetchLists,
		fetchSellers,
		fetchManufacturers,
		removeWishlistProducts,
		fetchWishlistProducts,
		fetchWishlistCategories,
		getWishlistProducts,
		addRemoveWishlistProducts,
		comparedProducts,
		fetchComparedProducts,
		getComparedProducts,
		addToCompare,
		removeFromCompare,
	};
}
