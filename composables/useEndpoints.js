export function useEndpoints() {
	const config = useAppConfig();
	const nuxtApp = useNuxtApp();
	const endpoints = nuxtApp.$appData?.endpoints;

	async function fetch(options) {
		return useApi(`/${config.api}/${config.apiVersion}/misc/endpoints/?version=${config.apiVersion}`, null, {cache: true}).then(res => {
			nuxtApp.$appData.endpoints = res.data;
			return res.data;
		});
	}

	const get = endpoint => (endpoint ? endpoints[endpoint] : endpoints);
	const set = data => (nuxtApp.$appData.endpoints = data);

	return {endpoints, fetch, get, set};
}
