export function useUtils() {
	function debounce(fn, delay) {
		let timer;
		return function (...args) {
			clearTimeout(timer);
			timer = setTimeout(() => {
				fn(...args);
			}, delay);
		};
	}

	async function sha256(input) {
		const encoder = new TextEncoder();
		const data = encoder.encode(input);
		const hashBuffer = await crypto.subtle.digest('SHA-256', data);
		return Array.from(new Uint8Array(hashBuffer))
			.map(byte => byte.toString(16).padStart(2, '0'))
			.join('');
	}

	async function detectCountryByIP() {
		try {
			const response = await useApi('https://api.country.is/');
			if (response?.country) {
				return response.country.toLowerCase();
			}
		} catch (error) {
			console.error('IP country detection failed:', error);
		}
		return null;
	}

	return {debounce, sha256, detectCountryByIP};
}
