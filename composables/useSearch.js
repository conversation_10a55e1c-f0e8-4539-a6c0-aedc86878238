export function useSearch() {
	const searchResults = useState('searchResults', () => null);
	const searchProductsCount = useState('searchProductsCounter', () => 0);

	function setSearchProductsCount(data) {
		searchProductsCount.value = data;
	}

	const getSearchResults = computed(() => {
		return searchResults.value;
	});

	return {
		searchResults,
		searchProductsCount,
		setSearchProductsCount,
		getSearchResults,
	};
}
