export function usePublish() {
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {absoluteImagePath} = useText();
	const {getUrlSegments} = useUrl();

	const category = useState('publishCategory', () => {});
	const post = useState('publishPost', () => {});

	// fetch category by slug. If slug is not provided, route path is used
	async function fetchCategoryBySlug(options, config = {}) {
		let slug = options?.slug ? options.slug : route.path;
		slug = getUrlSegments(slug, {ignoreLang: true, stringify: true});
		const fetchOptions = {
			...options,
			slug: slug,
			mode: 'full',
			hierarhy_by_position: true,
			include_subcategories: true,
		};

		const res = await useApi(
			'/api/nuxtapi/publish/categories/',
			{
				method: 'POST',
				body: fetchOptions,
			},
			{cache: true, key: fetchOptions, ...config}
		);

		category.value = res?.data?.length ? res.data[0] : null;
		return res;
	}

	// fetch categories
	async function fetchCategories(options, config = {}) {
		return await useApi(
			'/api/nuxtapi/publish/categories/',
			{
				method: 'POST',
				body: options,
			},
			{cache: true, key: options, ...config}
		);
	}

	// get local state category
	function getCategory() {
		return category.value;
	}

	// fetch posts
	async function fetchPosts(options, config = {}) {
		return await useApi(
			'/api/nuxtapi/publish/posts/',
			{
				method: 'POST',
				body: options,
			},
			{cache: true, key: options, ...config}
		);
	}

	// fetch single post
	async function fetchPost(options, config = {}) {
		const url = options?.item_slug ? options.item_slug : route.path;

		const category = getUrlSegments(url, {ignoreLang: true, offset: 1, limit: 1, reverse: true, stringify: true});
		const slug = getUrlSegments(url, {ignoreLang: true, limit: 1, reverse: true, stringify: true});

		// separate post id from slug
		const postUrl = slug.split('-');
		const postId = postUrl.pop();
		const postSlug = postUrl.join('-');
		const mode = options?.mode ? options.mode : 'full';

		// set fetch options
		const fetchOptions = {
			...options,
			item_slug: postSlug,
			category_slug: category,
			item_id: postId,
			mode: mode,
		};

		// fetch post
		const res = await useApi(
			`/api/nuxtapi/publish/post/`,
			{
				method: 'POST',
				body: fetchOptions,
				transform(response) {
					// convert relative /upload/ image paths to absolute on dev environment
					if (response.data.content) response.data.content = absoluteImagePath(response.data.content);

					// save product to state. This is needed to avoid duplicate fetching on product page
					post.value = response.data;
					return response.data;
				},
			},
			{cache: true, key: fetchOptions, ...config}
		);

		// keep the same state data if redirect url is the same as current route
		if (res?.redirect_url_without_domain && res.redirect_url_without_domain == route.fullPath && post.value) {
			return post.value;
		}

		post.value = res;
		return res;
	}

	return {fetchCategories, fetchCategoryBySlug, fetchPosts, getCategory, fetchPost};
}
