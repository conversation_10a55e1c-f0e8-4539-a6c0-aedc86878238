export function useFaq() {
	// fetch faq categories
	async function fetchCategories(options, config = {}) {
		return await useApi(
			'/api/nuxtapi/faq/categories/',
			{
				method: 'POST',
				body: options,
			},
			{cache: true, key: options, ...config}
		);
	}

	// fetch questions
	async function fetchQuestions(options, config = {}) {
		return await useApi(
			'/api/nuxtapi/faq/questions/',
			{
				method: 'POST',
				body: options,
			},
			{cache: true, key: options, ...config}
		);
	}

	// fetch question
	async function fetchQuestion(options, config = {}) {
		return await useApi(
			'/api/nuxtapi/faq/question/',
			{
				method: 'POST',
				body: options,
			},
			{cache: true, key: options, ...config}
		);
	}

	return {fetchCategories, fetchQuestions, fetchQuestion};
}
