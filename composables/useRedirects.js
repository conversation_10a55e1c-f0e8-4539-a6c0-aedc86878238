export function useRedirects() {
	const nuxtApp = useNuxtApp();

	// fetch list of redirects
	async function fetchRedirects(options, config = {}) {
		return useApi(
			'/api/nuxtapi/redirects/',
			{
				deep: false,
				...options,
			},
			{cache: true, ...config}
		);
	}

	function findRedirect(redirects, currentPath) {
		let redirectItems = [];
		if (!redirects?.length) return redirectItems;

		for (const item of redirects) {
			let regexString = '';
			const oldPath = item.old_path.trim();
			item.redirectType = 'regular';

			// If exact match is found, return redirect object
			if (oldPath == currentPath) {
				redirectItems.push(item);
				continue;
			}

			const basePath = currentPath.split('?');
			if (basePath.length && oldPath == basePath[0]) {
				redirectItems.push(item);
				continue;
			}

			/*
				Partial match with (slug+) in old_path. For example 
				old_path: /proizvodi/(slug+)-katalog/
				new_path: /katalog/(slug+)-katalog/
				current url: /proizvodi/url-kataloga-katalog/
				redirect url: /katalog/url-kataloga-katalog/
			*/
			const suffixMatch = item.old_path.match(/^(.+\/)\(slug\+\)-(.+)\/$/);
			if (suffixMatch) {
				const basePath = suffixMatch[1]; // e.g., "/izdelki/"
				const suffix = suffixMatch[2]; // e.g., "katalog"
				const regexString = `^${basePath}([^/]+)-${suffix}/?$`;

				// Check if current url matches this pattern
				const suffixRegex = currentPath.match(regexString);
				if (suffixRegex) {
					item.redirectType = 'regex';

					// Replace (slug+) with captured slug in new_path
					if (item.new_path.includes('(slug+)')) {
						item.new_path = item.new_path.replace('(slug+)', suffixRegex[1]);
					}

					redirectItems.push(item);
				}
				continue;
			}

			// Get regex patern from old_path (for patterns at the end)
			const itemRegex = item.old_path.match(/\(.*\)\/?$/);

			// If regex patern is not found, exit
			if (!itemRegex) continue;

			// Get path without regex from old_path
			const itemPath = oldPath.replace(itemRegex[0], '');

			// Match any number of url segments after itemPath, including itemPath. Match: /PATH/, /PATH/slug1/slug2/...
			if (itemRegex[0] == '(.*)') regexString = `^${itemPath}(.*)/?$`;

			// Match any number of url segments after itemPath, excluding itemPath. Match: /PATH/slug1/slug2/... No match: /PATH/
			if (itemRegex[0] == '(.+)') regexString = `^${itemPath}(.+)/?$`;

			// Match one level deep url segments after itemPath, including itemPath. Match: /PATH/, /PATH/slug1/
			if (itemRegex[0] == '(slug*)') regexString = `^${itemPath}[^/]*/?$`;

			// Match one level deep url segments after itemPath, excluding itemPath. Match: /PATH/slug1/ No match: /PATH/, /PATH/slug1/slug2/...
			if (itemRegex[0] == '(slug+)') regexString = `^${itemPath}[^/]+/?$`;

			// Match number after itemPath including itemPath. Match: /PATH/, /PATH/page/1/...
			if (itemRegex[0] == '(id*)') regexString = `^${itemPath}(\\d*)/?$`;

			// Match number after itemPath excluding itemPath. Match: /PATH/page/1/... No match: /PATH/
			if (itemRegex[0] == '(id+)') regexString = `^${itemPath}(\\d+)/?$`;

			// Match slug and number. Match: /PATH/slug-1/. No match: /PATH/slug/, /PATH/1-slug/
			if (itemRegex[0] == '(slug+)-(id+)') regexString = `^${itemPath}[a-zA-Z0-9_+-]+-(\\d+)/$`;

			// check if current url matches regex pattern and return redirect object
			const regex = currentPath.match(regexString);
			if (regex) {
				item.redirectType = 'regex';

				/*
					If redirect is found and url contains '(1)', replace (1) with matched regex.
					Original url: /brend/ktm/
					Old redirect rule: /brend/(.*)
					New redirect rule: /proizvodi/proizvodjaci/(1)
					Redirect url: /proizvodi/proizvodjaci/ktm/
				*/
				if (item.new_path.includes('(1)')) {
					item.new_path = item.new_path.replace('(1)', regex[1]);
				}

				if (regex[0] == currentPath) {
					redirectItems.push(item);
					continue;
				}
			}
		}

		if (!redirectItems?.length) return null;
		const redirect = redirectItems.find(item => item.redirectType === 'regular');
		return redirect || redirectItems[0];
	}

	function isExternalRedirect(redirect) {
		let external = false;
		if (redirect.new_path.startsWith('http://') || redirect.new_path.startsWith('https://') || redirect.new_path.startsWith('//')) {
			external = true;
		}
		return external;
	}

	function getRedirects() {
		return nuxtApp.$appData?.redirects || [];
	}

	function setRedirects(data) {
		nuxtApp.$appData.redirects = data;
	}

	return {fetchRedirects, getRedirects, setRedirects, findRedirect, isExternalRedirect};
}
