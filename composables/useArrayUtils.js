export function useArrayUtils() {
	/*
		Sort array by field or array of fields (form fields)
		const data = sortByField(
			source:Array,
			newOrder: String|Array,
			options = {sortType: 'asc/desc'}
		);
	*/
	function sortByField(data, newOrder, options = {}) {
		const {sortType = 'asc'} = options;
		const isPredefinedOrder = Array.isArray(newOrder);
		const isDescending = sortType.toLowerCase() === 'desc';

		return data.sort((a, b) => {
			if (isPredefinedOrder) {
				const aOrder = newOrder.indexOf(a.name);
				const bOrder = newOrder.indexOf(b.name);
				if (aOrder === -1) return 1;
				if (bOrder === -1) return -1;
				return isDescending ? bOrder - aOrder : aOrder - bOrder;
			} else {
				const field = newOrder;
				const aValue = a[field];
				const bValue = b[field];

				let comparison = 0;
				if (typeof aValue === 'string' && typeof bValue === 'string') {
					comparison = aValue.localeCompare(bValue);
				} else if (typeof aValue === 'number' && typeof bValue === 'number') {
					comparison = aValue - bValue;
				} else if (aValue instanceof Date && bValue instanceof Date) {
					comparison = aValue - bValue;
				}

				return isDescending ? -comparison : comparison;
			}
		});
	}

	// check if object is empty
	function isObjectEmpty(source, options = {}) {
		if (Object.keys(source).length === 0) return true;
		if (options?.ignoreArrays) return Object.values(source).every(value => Array.isArray(value) && value.length === 0);
	}

	// Remove empty values from an object
	function cleanObject(obj) {
		return Object.entries(obj).reduce((acc, [key, value]) => {
			if (
				value !== null && // Exclude null values
				value !== undefined && // Exclude undefined values
				(Array.isArray(value) ? value.length > 0 : true) && // Exclude empty arrays
				(typeof value === 'object' && !Array.isArray(value) // Recurse for nested objects
					? Object.keys(cleanObject(value)).length > 0
					: true)
			) {
				acc[key] = typeof value === 'object' && !Array.isArray(value) ? cleanObject(value) : value;
			}
			return acc;
		}, {});
	}

	// remove empty elements from array
	function removeEmptyElements(source) {
		return source?.length ? source.filter(element => element !== undefined && element !== null && element !== '') : [];
	}

	function compareArrays(arr1, arr2, options) {
		// filter out empty elements
		if (options?.removeEmptyElements) {
			arr1 = arr1.filter(Boolean);
			arr2 = arr2.filter(Boolean);
		}

		// match mode comparison. arr1 must contain all elements of arr2
		// arr1 [1, 2, 3, 4, 5], arr2 [1, 2, 3] - returns true
		if (options?.mode === 'match') {
			return arr2.every(elem => arr1.includes(elem));
		}

		// exact match comparison. arr1 and arr2 must be equal
		if (arr1.length !== arr2.length) {
			return false;
		}
		let sortedArr1 = arr1.slice().sort();
		let sortedArr2 = arr2.slice().sort();
		for (let i = 0; i < sortedArr1.length; i++) {
			if (sortedArr1[i] !== sortedArr2[i]) {
				return false;
			}
		}
		return true;
	}

	function getLastArrayElement(array) {
		return array[array.length - 1];
	}

	function removeLastArrayElement(array) {
		return array.slice(0, array.length - 1);
	}

	return {sortByField, isObjectEmpty, cleanObject, removeEmptyElements, compareArrays, getLastArrayElement, removeLastArrayElement};
}
