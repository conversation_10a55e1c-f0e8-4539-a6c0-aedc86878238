export function useGdpr() {
	const endpoints = useEndpoints();
	const {emit} = useEventBus();
	const cookie = useCookie('gdpr_cookie', {
		encode(value) {
			return value;
		},
		decode(value) {
			return value;
		},
	});

	// fetch user consents
	async function fetchConsents() {
		return useApi(`${endpoints.get('_get_hapi_gdpr_consents')}`);
	}

	// fetch gdpr form
	async function fetchTemplate(options) {
		const template = options?.template ? options.template : 'cookie';
		return useApi('/api/nuxtapi/gdpr/template/', {method: 'POST', body: {template}}, {cache: true});
	}

	function gdprCookie() {
		return cookie.value === undefined ? null : cookie.value;
	}

	function gdprApproved(mode) {
		if (cookie.value === undefined) return null;
		const consents = cookie.value?.length ? cookie.value.split('|') : [];

		// return array of consents if no mode is provided
		if (!mode) return consents;

		// return boolean if mode is provided
		return consents.includes(mode);
	}

	function onGdprUpdate(event, callback, options = {}) {
		watch(
			cookie,
			data => {
				if (data === undefined) {
					return callback(null);
				}
				const consents = data?.length ? data.split('|') : [];

				// if event is set to 'all', return all consents
				if (event == 'all') {
					return callback(consents);
				}

				// if event is set to a specific consent, return that consent
				if (consents.includes(event)) {
					return callback(event);
				} else {
					return callback(null);
				}
			},
			{deep: true, immediate: true, ...options}
		);
	}

	// submit gdpr form
	async function submit(options) {
		const res = await useApi(`${endpoints.get('_post_hapi_gdpr_consents')}`, {
			method: 'POST',
			body: options,
		});
		emit('gdprSubmit', {options, res});
		return res;
	}

	return {fetchConsents, fetchTemplate, gdprCookie, gdprApproved, submit, onGdprUpdate};
}
