export function useLog(message, type = 'log', options = {}) {
	if (!message) return;

	function log() {
		const msg = Array.isArray(message) ? message : [message];
		if (type == 'log') {
			return console.log(...msg);
		}
		if (type == 'error') {
			return console.error(...msg);
		}
		if (type == 'warn') {
			return console.warn(...msg);
		}
		if (type == 'debug') {
			return console.debug(...msg);
		}
	}

	// in production, log only on client side
	const env = process.env.NODE_ENV;
	if (!options?.devOnly && env == 'production' && process.client) {
		return log();
	}

	// in development, log always
	if (env == 'development') {
		return log();
	}
}
