export function useModal() {
	const modals = useState('modals', () => ({}));

	// get all active modals
	function activeModals() {
		return modals.value;
	}

	// create new modal and set current scroll position
	function open(name, payload = {}) {
		/*
		if (typeof window !== 'undefined') {
			const scrollOffset = window.scrollY;
			payload.scrollOffset = scrollOffset;
		}
		*/
		modals.value[name] = payload;
		return payload;
	}

	// remove modal by name
	function close(name) {
		// if modal name is not provided, close all modals
		if (!name) {
			modals.value = {};
			return;
		}

		// if modal name is provided, close that modal and restore scroll position
		/*
		if (typeof window !== 'undefined') {
			const scrollOffset = modals.value[name].scrollOffset;
			delete modals.value[name];

			if (!scrollOffset) return;
			setTimeout(() => {
				window.scrollTo({top: scrollOffset, behavior: 'instant'});
			}, 50);
		} else {
			delete modals.value[name];
		}
		*/

		delete modals.value[name];
	}

	// get modal by name
	function get(name) {
		return modals.value[name];
	}

	return {modals, activeModals, close, open, get};
}
