export function useImages() {
	const config = useAppConfig();

	// add /upload/ prefix to file path
	function filePath(payload) {
		if (__GENERATE_THUMBS__) {
			const path = payload.split('/');
			if (!path.includes('upload')) {
				return '/upload/' + payload;
			}

			return payload;
		}
	}

	/*
		get all image urls from data based on provided field names
		data: object or array of image elements
		urlFields: preset image url fields to extract
	*/
	const getFileUrls = (data, urlFields) => {
		if (__GENERATE_THUMBS__) {
			if (!data?.length && !urlFields?.length) {
				useLog('getFileUrls - missing data or urlFields', 'error');
				return null;
			}

			let fileUrls = [];

			const handleUrlField = (item, urlField, relatedId) => {
				const isArray = urlField.split('.');
				if (isArray[1]) {
					// if subfield contains array of images (eg. product detail images - item.images)
					if (item[isArray[0]]?.length) {
						return item[isArray[0]].forEach(imageItem => {
							if (imageItem?.id && imageItem[isArray[1]]) {
								fileUrls.push({
									related: imageItem.id,
									field: `[${isArray[0]}]${isArray[1]}`,
									file: filePath(imageItem[isArray[1]]),
								});
							}
						});
					}

					// if subfield contains single image (eg. nested cart items - item.item.image)
					if (item[isArray[0]] && item[isArray[0]][isArray[1]]) {
						fileUrls.push({
							related: item.id,
							field: `[${isArray[0]}]${isArray[1]}`,
							file: filePath(item[isArray[0]][isArray[1]]),
						});
					}
				}

				if (relatedId && urlField && item[urlField]) {
					fileUrls.push({
						related: relatedId,
						field: urlField,
						file: filePath(item[urlField]),
					});
				}
			};

			if (Array.isArray(data) && data?.length) {
				data.forEach(el => {
					urlFields?.forEach(urlField => {
						handleUrlField(el, urlField, el?.id);
					});
				});
				return fileUrls;
			}

			if (urlFields?.length) {
				urlFields.forEach(urlField => {
					handleUrlField(data, urlField, data?.id);
				});
			}

			return fileUrls;
		}
	};

	// generate thumbs config
	const prepareThumbsConfig = (files, thumbsConfig) => {
		if (__GENERATE_THUMBS__) {
			let thumbs = [];
			files.forEach(file => {
				thumbsConfig.forEach(thumb => {
					// check if thumb is related to file (if related attribute is set in preset config). If not, skip it
					const matchField = file.field.match(/\[([^\]]+)\]/);
					const fileField = matchField ? file.field.replace(matchField[0], matchField[1] + '.') : file.field;
					if (!thumb.related || (thumb.related?.length && thumb.related.includes(fileField))) {
						thumbs.push({
							alias: `${file.related}-${file.field}_${thumb.alias}`,
							config: thumb.config,
							url: file.file,
						});
					}
				});
			});

			return thumbs;
		}
	};

	// create new images array with id, alias, subarray and data
	function processImages(images) {
		if (__GENERATE_THUMBS__) {
			let items = [];

			if (Object.values(images)?.length) {
				Object.entries(images).forEach(image => {
					const key = image[0].split('-');
					const imageId = key[0];
					const alias = key[1];
					const subArray = alias.match(/\[([^\]]+)\]/);

					items.push({
						id: imageId,
						alias: subArray ? alias.replace(subArray[0], '') : alias,
						subarray: subArray ? subArray[1] : null,
						data: image[1],
					});
				});
			}

			return items;
		}
	}

	/*
		append images back to provided data
		data - data to append images to
		images - images to append returned from API
	*/
	function appendTo(data, images) {
		if (__GENERATE_THUMBS__) {
			const imgs = processImages(images);
			const appendImages = item => {
				if (imgs?.length) {
					imgs.forEach(image => {
						if (image.subarray && item[image.subarray]?.length) {
							// if subfield contains array of images
							item[image.subarray].forEach(obj => {
								if (obj?.id == image?.id) obj[image.alias] = image.data;
							});
						} else if (image.subarray && item[image.subarray]) {
							// if subfield contains single image
							item[image.subarray][image.alias] = image.data;
						} else {
							if (item?.id == image?.id) item[image.alias] = image.data;
						}
					});
				}
			};

			// if data is array
			if (Array.isArray(data)) {
				data.forEach(item => appendImages(item));
				return true;
			}

			// if data is object
			appendImages(data);
		}
	}

	/*
		Generate thumbs
		Params:
		
		data:Array,Object,
		imageFields: Array,
		thumbs: Array
			{
				alias: String,
				config: {
					width: Number,
					height: Number,
					crop: Boolean
				},
			},
		],
	*/
	async function generateThumbs(options) {
		if (__GENERATE_THUMBS__) {
			//useLog('NUXT BASE WARNING! generateThumbs() is deprecated and will be removed in future. Migration docs: ', 'warn');
			const dataType = Array.isArray(options?.data) ? 'Array' : 'Object';
			if ((dataType === 'Array' && !options?.data?.length) || (dataType === 'Object' && !options?.data)) return null;

			const append = options && 'append' in options ? options.append : true;

			let imageFieldsPreset = options?.imageFields ? options.imageFields : [];
			if (options?.preset && config.thumbPresets[options.preset]) {
				imageFieldsPreset = config.thumbPresets[options.preset].imageFields;
			}
			if (!imageFieldsPreset?.length) {
				useLog(`Image fields not provided or preset is not defined: ${options?.preset}`, 'error');
				return null;
			}

			const files = getFileUrls(options.data, imageFieldsPreset); // extract image urls from data
			if (!files?.length) {
				// image url is not provided. Thumb cannot be generated
				return null;
			}

			const thumbPresets = options.preset ? config.thumbPresets[options.preset].thumbs : options.thumbs;
			if (!thumbPresets?.length) {
				useLog('Thumbs presets not provided', 'error');
				return null;
			}

			const thumbsConfig = prepareThumbsConfig(files, thumbPresets); // generate/prepare thumbs config
			if (!thumbsConfig?.length) {
				useLog('Thumbs config not provided', 'error');
				return null;
			}

			const fetchConfig = {
				method: 'POST',
				body: thumbsConfig,
				deep: false,
			};

			const fetchKey = {preset: options.preset, thumbs: thumbsConfig};
			const dataKey = options?.dataKey ? options.dataKey : null;
			try {
				const res = await useApi('/api/nuxtapi/misc/thumbs/', fetchConfig, {cache: true, key: fetchKey, dataKey: dataKey, lang: false});
				const data = res?.data;
				if (data) {
					if (append) appendTo(options.data, data);
					return data;
				} else {
					return null;
				}
			} catch (e) {
				return null;
			}
		}
	}

	// extract image urls from string into array
	function extractImageUrls(payload) {
		if (!payload || typeof payload !== 'string') return [];
		let m;
		let i = 1;
		let urls = [];
		const regex = /<img.*?src="([^">]*\/([^">]*?))".*?>/g;
		while ((m = regex.exec(payload))) {
			if (m[1]) {
				urls.push({
					id: 'imageurl' + i,
					url: m[1],
				});
				i++;
			}
		}

		return urls;
	}

	return {filePath, generateThumbs, extractImageUrls};
}
