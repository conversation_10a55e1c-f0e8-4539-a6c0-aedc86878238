export function useToken() {
	const tokenCookie = useCookie('hapi_auth_token', {
		maxAge: 60 * 60 * 24 * 365, // 1 year
	});

	const config = useAppConfig();
	async function generate(options = Object) {
		const tokenId = options?.tokenId ? '?set_token_id=' + options.tokenId : '';
		const tokenHash = options?.tokenHash ? '&token_hash=' + options.tokenHash : '';
		//keycloak logout param
		const tokenForceLogout = options?.tokenForceLogout ? '?force_logout=1' : '';

		return await $fetch(`${config.host}/${config.api}/${config.apiVersion}/auth/generate-token/` + tokenId + tokenHash + tokenForceLogout).then(res => {
			set(res.data.jwt);
			useLog(['Generated new token ' + res.data.jwt], 'debug');
			return res.data.jwt;
		});
	}

	function get() {
		return tokenCookie.value ? tokenCookie.value : null;
	}

	function set(tokenValue) {
		tokenCookie.value = tokenValue;
	}

	function removeToken() {
		tokenCookie.value = null;
	}

	async function generateServerToken() {
		try {
			return await $fetch('/api/nuxtapi/token?regenerate=1');
		} catch (err) {
			useLog(err);
		}
	}

	return {get, generate, set, removeToken, generateServerToken};
}
