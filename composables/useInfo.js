export function useInfo() {
	const nuxtApp = useNuxtApp();
	const info = nuxtApp.$appData?.info;

	// fetch info data
	async function fetch() {
		const res = await useApi('/api/nuxtapi/info/', null, {cache: true});
		nuxtApp.$appData.info = res?.data ? res.data : null;
		return res?.data ? res.data : null;
	}

	// get info data
	function getInfo(data) {
		if (!data) return info;
		return nuxtApp?.$appData?.info?.[data] ? nuxtApp.$appData.info[data] : '';
	}

	function set(data) {
		nuxtApp.$appData.info = data;
	}

	return {info, fetch, getInfo, set};
}
