export function useProfile() {
	const user = ref(null);

	const fetchUser = async () => {
		try {
			const auth = useAuth();
			user.value = await auth.getUser();
		} catch (e) {
			console.error('Error fetching user:', e);
		}
	};

	onMounted(fetchUser);

	const b2b = computed(() => user.value?.b2b === '1');
	const isLoggedIn = computed(() => !!user.value);
	const hasLoyalty = computed(() => !!user.value?.loyalty_code);
	//const loyaltyDiscount = computed(() => !!user.value?.loyalty_code)

	return {user, b2b, isLoggedIn, hasLoyalty};
}
