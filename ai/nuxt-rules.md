# Nuxt Instructions

-   Nuxt 3 provides auto imports, so there is no need to manually import 'ref', 'useState', or 'useRouter'.
-   Use <script setup> composition API syntax for Vue components
-   Use <BaseUiImage> component for images.
-   Use useApi composable to fetch HAPI data.
-   Always check if there are reusable components in nuxt-base/components folder.
-   Use PascalCase for component file names (e.g., components/MyComponent.vue).
-   Use useState for state management.
-   Use provide/inject for dependency injection when appropriate.
-   Never edit or create components in nuxt-base workspace if not asked for.
-   Use BaseFormSiteForm component for contact forms.
-   Use BaseCmsRotator component for rotators: <BaseCmsRotator :fetch="{code: ''}" v-slot="{items}"/>.
-   Use BaseCmsLabel component for labels: <BaseCmsLabel code=""/>.
-   Use get() function from use<PERSON>abels composable to get labels within script.
-   Use BaseLocationPoints v-slot="{items}"> to fetch location points and BaseLocationGoogleMap to show points on Google map.
-   When manipulating DOM elements, use nuxt-base/composables/useDom composable to find proper functions.
-   When manipulating text strings, use nuxt-base/composables/useText composable to find proper functions.
-   When creating components, order components in the following way:
    -   <template>
    -   <script setup>
    -   <style>
