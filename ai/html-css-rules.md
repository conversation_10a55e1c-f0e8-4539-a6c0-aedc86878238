# HTML Instructions

-   Use https://placehold.co/ for image placeholders

# CSS Instructions

-   Use `px` for sizing
-   Prefer `LESS` over standard `CSS`
-   Write CSS rules in a single-line format with proper indentation:
    ```
    .button{padding: 10px; background-color: blue; color: white;}
    ```
-   When creating responsive media queries, check \_vars.less file for defined size variables
-   Use dash naming convention when creating css classes: .foo-bar{}
-   Use tab for indentation. Tab size is 4 spaces.
