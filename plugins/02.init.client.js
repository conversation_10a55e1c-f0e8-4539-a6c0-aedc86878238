export default defineNuxtPlugin(async nuxtApp => {
	if (nuxtApp.$isbot) return;

	const config = useAppConfig();
	const token = useToken();
	const auth = useAuth();
	const webshop = useWebshop();
	const {fetchRedirects, getRedirects, setRedirects} = useRedirects();

	if (!token.get()) {
		await token.generate();
	}

	const tasks = [];

	// fetch redirects
	if (!getRedirects()?.length) {
		tasks.push(fetchRedirects().then(redirects => setRedirects(redirects?.data)));
	}

	// Fetch abandonment data
	if (nuxtApp._route.query.abandonment && nuxtApp._route.query.saveconfirm) {
		tasks.push(
			webshop.fetchAbandonment({
				abandonment: nuxtApp._route.query.abandonment,
				saveconfirm: nuxtApp._route.query.saveconfirm,
			})
		);
	}

	// Handle user authentication
	if (__AUTH__) {
		const authTask = auth
			.fetchIsLogin()
			.then(async isLoggedin => {
				if (isLoggedin?.user_id) {
					await auth.fetchUser();
				}
			})
			.catch(e => {
				useLog(e);
			});
		tasks.push(authTask);
	}

	await Promise.all(tasks);

	/* 
		Set global click event listener and scroll to hash anchor with delay (make sure DOM is updated)
		This is to ensure scroll to hash works without page reload
	*/
	if (typeof window !== 'undefined') {
		let hashTimeout = null;
		window.addEventListener('click', event => {
			if (hashTimeout) clearTimeout(hashTimeout);
			const anchor = event.target.closest('a');
			const linkHash = anchor?.hash;
			if (linkHash) {
				hashTimeout = setTimeout(() => {
					// Decode the hash to handle special characters correctly, preserve spaces
					let decodedHash = decodeURIComponent(linkHash);
					decodedHash = decodedHash.replace('#', '');
					const element = document.querySelector('[id="' + decodedHash + '"]');
					if (element) {
						const elementRect = element.getBoundingClientRect();
						const absoluteTop = elementRect.top + window.scrollY;
						window.scrollTo({
							behavior: 'smooth',
							top: absoluteTop - 80,
						});
					}
				}, 400);
			}
		});
	}
});
