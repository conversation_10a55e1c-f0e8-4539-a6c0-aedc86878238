export default defineNuxtPlugin(nuxtApp => {
	if (nuxtApp.$isbot) return;

	if (process.server) {
		nuxtApp.vueApp.directive('interpolation', {});
		return;
	}

	const config = useAppConfig();
	const modal = useModal();
	const page = usePage();
	const {emit} = useEventBus();
	const router = nuxtApp.$router;
	const sHost = config.host;
	let links = [];

	// hijack link click and process as local or external
	async function navigate(e) {
		const item = e.currentTarget;
		const link = item.closest('a');
		const url = new URL(link.href);
		const mode = url.searchParams.get('mode');
		const linkClass = link.getAttribute('class');

		// close modals if clicked link is inside a modal
		if (Object.keys(modal.activeModals()).length > 0 && mode != 'quick') {
			modal.close();
		}

		// if link has ?mode parameter, open modal defined in parameter
		// if mode is 'quick' then fetch page from the link url and open 'quick' modal
		if (mode) {
			e.preventDefault();
			const pathname = url.pathname;

			// if mode is 'quick' then fetch page and open modal
			if (mode == 'quick') {
				const res = await page.fetch({'slug': pathname});
				if (!res) return null;
				modal.open('quick', res);
				return res;
			}

			// open custom modal
			modal.open(mode);
			return;
		}

		// if link has class 'fancybox' then open modal with gallery
		if (linkClass?.includes('fancybox')) {
			e.preventDefault();
			const gallery = link.getAttribute('rel');
			let items = [];
			const startIndex = link.getAttribute('data-index') ? link.getAttribute('data-index') : 0;

			if (gallery) {
				// if rel attribute is defined, get all elements with rel set to the same gallery
				const galleryElements = document.querySelectorAll(`a[rel="${gallery}"]`);
				galleryElements.forEach((item, i) => {
					if (item.href) {
						const img = item.querySelector('img');
						items.push({
							id: `item-${i}`,
							title: img?.alt ? img.alt : item.title,
							url: item.href,
							thumb: item.getAttribute('data-thumb'),
							type: item.href.includes('youtube.com') ? 'youtube' : 'image',
						});
					}
				});
			} else {
				// if rel attribute is not defined, get only the current link
				if (link.href) {
					const img = link.querySelector('img');
					items.push({
						id: 'item-0',
						title: img?.alt ? img.alt : link.title,
						url: link.href,
						thumb: link.getAttribute('data-thumb'),
						type: link.href.includes('youtube.com') ? 'youtube' : 'image',
					});
				}
			}

			// open modal with name defined in data-modal attribute. If not defined, open 'quick' modal
			const modalName = link.getAttribute('data-modal') ? link.getAttribute('data-modal') : 'quick';
			modal.open(modalName, {
				'items': items,
				'startIndex': Number(startIndex),
			});
			return;
		}

		// internal link
		if (item.origin === sHost || item.host === window.location.host) {
			e.preventDefault();
			if (router?.currentRoute?.value?.path === url.pathname) emit('catalogProductsUpdate');
			const route = url.href.replace(item.origin, ''); // remove origin from url but keep query params
			router.push(route);
		}
	}

	// get all links and set event listeners for each one
	function addListeners(links) {
		for (let i = 0; i < links.length; i++) {
			// check if the link has an href attribute
			if (!links[i].hasAttribute('href')) {
				continue;
			}

			// ignore links with file extension
			const href = links[i].getAttribute('href');
			if (/\.[^/\s]+$/.test(href) && !links[i].classList.contains('fancybox')) {
				continue;
			}

			// for improved security `rel="noopener"` will be added automatically if target is `_blank`
			const target = links[i].getAttribute('target');
			if (target && target === '_blank') {
				const rel = links[i].getAttribute('rel') || '';
				const attributes = rel.includes('noopener') ? rel : rel + ' noopener';
				links[i].setAttribute('rel', attributes);
			}
			links[i].addEventListener('click', navigate, false);
		}
	}

	// clean up event listeners
	function removeListeners(links) {
		for (let i = 0; i < links.length; i++) {
			links[i].removeEventListener('click', navigate, false);
		}
		links = [];
	}

	// wrap each .table element with a div.table-wrapper
	function wrapTables() {
		const allTables = document.querySelectorAll('.table');
		if (!allTables?.length) return;
		const tables = [...allTables].filter(function (table) {
			return !table.parentNode.classList.contains('table-wrapper');
		});

		if (tables.length > 0) {
			for (let i = 0; i < tables.length; i++) {
				if (!tables[i].parentNode.classList.contains('table-wrapper')) {
					const tableWrapper = document.createElement('div');
					tableWrapper.classList.add('table-wrapper');
					tables[i].parentNode.insertBefore(tableWrapper, tables[i]);
					tableWrapper.appendChild(tables[i]);
				}
			}
		}
	}

	nuxtApp.vueApp.directive('interpolation', {
		updated() {
			removeListeners(links);
			addListeners(links);
			//wrapTables();
		},
		mounted(el) {
			links = el.getElementsByTagName('a');
			addListeners(links);
			wrapTables();
		},
		beforeUnmount(el) {
			el.removeEventListener('click', removeListeners(links));
		},
	});
});
