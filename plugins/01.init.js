export default defineNuxtPlugin(async nuxtApp => {
	nuxtApp.$appData = {};
	const config = useAppConfig();

	// do not load anything if url is on ignored list (bot protection)
	const currentUrl = nuxtApp.$router.currentRoute.value.fullPath;
	if (config.redirectIgnoredUrls.includes(currentUrl)) {
		nuxtApp.$isbot = true;
		return;
	}

	const labels = useLabels();
	const apiRoutes = useApiRoutes();
	const currency = useCurrency();
	const info = useInfo();
	const lang = useLang();
	const {findRedirect, isExternalRedirect} = useRedirects();
	const endpoints = useEndpoints();
	const initData = await $fetch('/api/nuxtapi/checkInitData/');

	// get token from cache or generate new one
	if (process.server && !initData?.token) {
		try {
			await $fetch('/api/nuxtapi/token/');
		} catch (err) {
			throw createError({
				statusCode: err.statusCode,
				statusMessage: `Error ${err.statusCode}`,
			});
		}
	}

	// check if endpoints are in cache. If not, fetch them from api
	if (initData?.endpoints?.success) {
		endpoints.set(initData.endpoints.data);
	} else {
		await fetchEndpoints();
	}

	// set current language
	if (config?.multilanguage) {
		const currentLanguage = lang.getLangFromUrl(nuxtApp._route.path);
		lang.set(currentLanguage);
	}

	// check if url is in redirects list and redirect if necessary
	if (process.server) {
		const redirects = await $fetch('/api/nuxtapi/redirects/');
		if (redirects?.data?.length) {
			const redirect = findRedirect(redirects.data, nuxtApp._route.fullPath);
			if (redirect) {
				const newPath = redirect.new_path ? redirect.new_path.trim().replace('(.*)', '') : null;
				if (newPath) {
					return navigateTo(newPath, {
						redirectCode: redirect?.type ? redirect.type : 301,
						external: isExternalRedirect(redirect),
					});
				}
			}
		}
	}

	// load other initial app data
	//await getHeaders();
	await loadData();

	async function fetchEndpoints() {
		try {
			const ep = await $fetch('/api/nuxtapi/endpoints/');
			if (ep?.data) {
				return endpoints.set(ep.data);
			}
		} catch (err) {
			throw createError({
				statusCode: err.statusCode,
				statusMessage: `Error ${err.statusCode}`,
			});
		}
	}

	async function getHeaders() {
		return await $fetch(`${config.host}/${config.api}/${config.apiVersion}/misc/headers-check/`);
	}

	async function loadData() {
		const fetchPromises = [];

		if (initData?.currency?.data) {
			currency.setCurrency(initData.currency.data);
		} else {
			fetchPromises.push(currency.fetch());
		}

		if (initData?.labels?.data) {
			labels.set(initData.labels.data);
		} else {
			fetchPromises.push(labels.fetch());
		}

		if (initData?.info?.data) {
			info.set(initData.info.data);
		} else {
			fetchPromises.push(info.fetch());
		}

		if (initData?.routes?.data) {
			apiRoutes.setRoutes(initData.routes.data);
		} else {
			fetchPromises.push(apiRoutes.fetchRoutes());
		}

		// fetch data only if there are no cached data
		if (fetchPromises.length) {
			const results = await Promise.allSettled(fetchPromises);
			if (results?.length) {
				for (const result of results) {
					if (result.status === 'rejected') {
						throw createError({
							statusCode: result.reason?.statusCode ? result.reason.statusCode : 500,
							message: result.reason?.statusMessage ? result.reason.statusMessage : '',
						});
					}
				}
			}
		}

		// Import project template files and provide all template names to appData
		const templateFiles = import.meta.glob('@/components/views/**/*.vue');
		const templates = Object.keys(templateFiles).map(path =>
			path
				.replace('/components/views/', '')
				.replace('.vue', '')
				.split('/')
				.map(el => el.charAt(0).toUpperCase() + el.slice(1))
				.filter(el => el !== 'Index')
				.join('')
		);
		nuxtApp.$appData.templates = templates;
	}
});
