export default defineNuxtPlugin(async nuxtApp => {
	const idleTime = toRaw(nuxtApp._appConfig).idleReloadTimeout;
	if (!idleTime) return;

	const {debounce} = useUtils();
	let timer = null;
	const resetTimer = () => {
		if (timer) clearTimeout(timer);
		startTimer();
	};

	const debouncedResetTimer = debounce(resetTimer, 500);

	const startTimer = () => {
		timer = setTimeout(() => {
			reloadNuxtApp({ttl: 2000});
		}, idleTime);
	};

	const onActivity = () => {
		debouncedResetTimer();
	};

	const addActivityListeners = () => {
		window.addEventListener('mousemove', onActivity);
		window.addEventListener('keydown', onActivity);
		window.addEventListener('click', onActivity);
		window.addEventListener('scroll', onActivity, {passive: true});
		window.addEventListener('touchstart', onActivity, {passive: true});
		window.addEventListener('touchmove', onActivity, {passive: true});
	};

	addActivityListeners();
	startTimer();
});
