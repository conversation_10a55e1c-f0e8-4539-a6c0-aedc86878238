import {promises as fs} from 'fs';
import path from 'path';
const fileExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.pdf', '.doc', '.docx', '.zip'];

export default defineEventHandler(async event => {
	const {req, res} = event.node;
	const urlPath = req.url;
	const isFile = fileExtensions.some(ext => urlPath.includes(ext));
	const filePath = path.join(process.cwd(), 'public', urlPath);

	if (urlPath.startsWith('/_nuxt/')) {
		setResponseHeader(event, 'Cache-Control', 'no-cache, must-revalidate');
		setResponseHeader(event, 'Pragma', 'no-cache');
		setResponseHeader(event, 'Expires', '0');
	}

	if (isFile || urlPath === '/site.webmanifest') {
		try {
			await fs.access(filePath);
			res.end();
		} catch (err) {
			res.statusCode = 404;
			res.end('File not found');
		}
	}
});
