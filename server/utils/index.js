const config = useAppConfig();
const storage = useStorage();

export async function getCachedData(key) {
	const data = await storage.getItem(`db:${key}`);
	if (data) logMessage({mode: 'return', data: key});
	return data;
}

export async function cacheData(key, cacheData, options = {}) {
	let enabled = config.cache.enabled;

	// do not disable token and endpoints caching
	if (['token', 'endpoints'].includes(key)) enabled = true;

	if (cacheData && enabled) {
		const storageOptions = {
			ttl: options.ttl ? options.ttl * 60 : 600, // 10 minutes if not specified
		};

		await storage.setItem(`db:${key}`, cacheData, storageOptions);
		return logMessage({mode: 'add', cacheData: key});
	}
}

export function logMessage(options) {
	if (!config.cache.debug) return;

	if (options.mode == 'remove') console.log(`Cache: removed "${options.data}" data.`);
	if (options.mode == 'removeOutdated') console.log(`Cache: removed outdated "${options.data}" data.`);
	if (options.mode == 'add') console.log(`Cache: "${options.data}" cached.`);
	if (options.mode == 'return') console.log(`Cache: returning cached "${options.data}" data.`);
}

// API data returned as string should be converted to number
export const utilsNumericFields = [
	'price',
	'price_custom',
	'basic_price',
	'basic_price_custom',
	'price_b2b',
	'price_b2b_custom',
	'return_fee',
	'discount',
	'discount_custom',
	'price_with_shipping',
	'extra_price_dynamicprice',
	'extra_price_cart_dynamicprice',
	'coupon_price',
	'discount_percent_custom',
	'discount_percent',
	'shipping_min_price',
	'price_custom_prices_cart',
	'price_custom_prices_cart_category',
	'price_custom_prices_cart_expire',
	'price_custom_prices_cart_shopping_cart_code',
	'tax',
	'package_qty',
	'available_qty',
	'limited_qty',
	'total_images',
];

// use browser cookie if available. If not, use cached server cookie
export async function getAuthHeader(event) {
	let cookie = getCookie(event, 'hapi_auth_token');
	if (!cookie) {
		cookie = await storage.getItem('db:token');
	}
	//console.log('getAuthHeader', getRequestURL(event).pathname, cookie);
	return 'Bearer ' + cookie;
}
