export default defineEventHandler(async event => {
	const body = await readBody(event);
	const sanitizedSlug = body.slug ? body.slug.replace(/[^a-zA-Z0-9]/g, '') : null;
	const cacheKey = sanitizedSlug ? `staticcontent:page.${sanitizedSlug}.${body.lang}` : null;

	// return cached data if available
	const data = await getCachedData(cacheKey);
	if (data) return data;

	const config = useAppConfig();
	const storage = useStorage();

	let errorLog = '';
	try {
		const endpoints = await storage.getItem('db:endpoints');
		const auth = await getAuthHeader(event);

		let queryString = '';
		Object.entries(body).forEach(([key, value]) => {
			if (Array.isArray(value)) {
				value = value.join(',');
			}
			if (queryString !== '') queryString += '&';
			queryString += `${key}=${value}`;
		});
		const url = new URL(`${config.host}${endpoints.data._get_hapi_staticcontent_pages}?${queryString}`);

		// if not, fetch the data from the API
		let response;
		try {
			response = await $fetch(url.toString(), {
				headers: {
					'Authorization': auth,
				},
			});
		} catch (error) {
			errorLog = 'Error while fetching static content page.';
			throw error;
		}

		try {
			if (response?.success) {
				const ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['staticContent'];
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing static content page to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message + ' ' + JSON.stringify(body),
		});
	}
});
