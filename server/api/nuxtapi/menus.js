import {hash} from 'ohash';
export default defineEventHandler(async event => {
	const body = await readBody(event);
	const reqUrl = getRequestURL(event);
	const cacheKey = 'menus:' + body.code + '.' + hash(['api-fetch', reqUrl.pathname, body]) + '.' + body.lang;
	const data = await getCachedData(cacheKey);
	if (data) return data;

	const config = useAppConfig();
	const storage = useStorage();

	let errorLog = '';
	try {
		const endpoints = await storage.getItem('db:endpoints');
		const auth = await getAuthHeader(event);

		let queryString = '';
		Object.entries(body).forEach(([key, value]) => {
			if (Array.isArray(value)) {
				value = value.join(',');
			}
			if (queryString !== '') queryString += '&';
			queryString += `${key}=${value}`;
		});
		const url = new URL(`${config.host}${endpoints.data._get_hapi_cms_menus}?${queryString}`);

		let response;
		try {
			response = await $fetch(url, {
				headers: {
					'Authorization': auth,
				},
			});
		} catch (error) {
			errorLog = 'Error while fetching menus.';
			throw error;
		}

		try {
			if (response?.success) {
				const ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['menus'];
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing menus to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message + ' ' + JSON.stringify(body),
		});
	}
});
