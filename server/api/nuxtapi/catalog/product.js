import {hash} from 'ohash';
export default defineEventHandler(async event => {
	const body = await readBody(event);
	const cacheKey = `catalog:product:${body.item_id}.${hash(['api-fetch', body])}.${body.lang}`;
	const data = await getCachedData(cacheKey);
	if (data) return data;

	const config = useAppConfig();
	const storage = useStorage();
	const endpoints = await storage.getItem('db:endpoints');
	const auth = await getAuthHeader(event);

	const url = new URL(`${config.host}${endpoints.data._get_hapi_catalog_product}`);
	Object.entries(body).forEach(([key, value]) => {
		url.searchParams.append(key, value);
	});

	let errorLog = '';
	try {
		let response;
		try {
			response = await $fetch(url, {
				headers: {
					'Authorization': auth,
				},
			});
		} catch (error) {
			errorLog = 'Error while fetching product.';
			throw error;
		}

		try {
			if (response?.data?.id) {
				// Map and convert API numeric fields to number
				utilsNumericFields.forEach(field => {
					if (response.data[field]) response.data[field] = Number(response.data[field]);
				});

				const ttl = config.cache?.refresh['catalogProduct'] ?? 1;
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing product to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message + ' ' + JSON.stringify(body),
		});
	}
});
