import {hash} from 'ohash';

export default defineEventHandler(async event => {
	//const method = await getMethod(event);
	const body = await readBody(event);
	const reqUrl = getRequestURL(event);
	const cacheKey = 'products.' + hash(['api-fetch', reqUrl.pathname, body]) + '.' + body.lang;
	let errorLog = '';

	if (!body.ignoreCache) {
		const data = await getCachedData(`catalog:products:${cacheKey}`);
		if (data) return data;
	}

	const config = useAppConfig();
	const storage = useStorage();

	const requestBody = {...body};
	if ('ignoreCache' in requestBody) delete requestBody.ignoreCache;

	try {
		const endpoints = await storage.getItem('db:endpoints');
		const auth = await getAuthHeader(event);
		let response;
		try {
			// Build URL based on fetch property. If multiple lists are requested, add multiple_lists=1 to the URL
			let apiUrl = `${config.host}${endpoints.data._post_hapi_catalog_products}`;
			if (Array.isArray(body.list_code) && body.list_code.length > 1) {
				apiUrl += apiUrl.includes('?') ? '&multiple_lists=1' : '?multiple_lists=1';
			}
			response = await $fetch(apiUrl, {
				headers: {'Authorization': auth},
				method: 'POST',
				body: body,
			});
		} catch (error) {
			errorLog = 'Error while fetching products.';
			throw error;
		}

		// Map and convert API numeric fields to number for both single and multi-list responses
		const convertNumericFields = items => {
			if (!Array.isArray(items)) return;
			items.forEach(item => {
				Object.entries(item).forEach(([key, value]) => {
					if (utilsNumericFields.includes(key)) item[key] = Number(value);
				});
			});
		};
		if (response.data?.items) {
			convertNumericFields(response.data.items);
		} else if (response.data && typeof response.data === 'object') {
			Object.values(response.data).forEach(list => {
				convertNumericFields(list);
			});
		}

		try {
			if (response?.success && !body.ignoreCache) {
				const ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['catalogProducts'];
				await cacheData(`catalog:products:${cacheKey}`, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing products to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message + ' ' + JSON.stringify(body),
		});
	}
});
