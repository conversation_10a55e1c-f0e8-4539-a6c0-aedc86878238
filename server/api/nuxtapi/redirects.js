export default defineEventHandler(async event => {
	const cacheKey = 'redirects';
	const data = await getCachedData(cacheKey);
	if (data) return data;

	const config = useAppConfig();
	const storage = useStorage();

	let errorLog = '';
	try {
		const endpoints = await storage.getItem('db:endpoints');
		const auth = await getAuthHeader(event);

		let response;
		try {
			response = await $fetch(`${config.host}${endpoints.data._get_hapi_cms_redirects}`, {
				headers: {
					'Authorization': auth,
				},
			});
		} catch (error) {
			errorLog = 'Error while fetching redirects.';
			throw error;
		}

		try {
			if (response?.success) {
				// Remove id, type, exclude_keywords from response to reduce payload size
				const cleanedData = [];
				for (let i = 0; i < response.data.length; i++) {
					const {old_path, new_path} = response.data[i];
					cleanedData.push({old_path, new_path});
				}
				response.data = cleanedData;
				const ttl = cleanedData.length === 0 ? 1 : config.cache?.refresh['redirects'];
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing redirects to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message,
		});
	}
});
