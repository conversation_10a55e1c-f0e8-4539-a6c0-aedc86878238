export default defineEventHandler(async event => {
	const body = await readBody(event);
	const cacheKey = `newsletter:newsletter.${body.lang}`;
	const data = await getCachedData(cacheKey);
	if (data) return data;

	if (!body?.code) return;

	const config = useAppConfig();
	const storage = useStorage();

	let errorLog = '';
	try {
		const endpoints = await storage.getItem('db:endpoints');
		const auth = await getAuthHeader(event);

		let url = new URL(`${config.host}${endpoints.data._get_hapi_newsletter_form}`);
		url = url.toString().replace('%CODE%', body.code);

		let response;
		try {
			response = await $fetch(url, {
				headers: {
					'Authorization': auth,
				},
			});
		} catch (error) {
			errorLog = 'Error while fetching newsletter.';
			throw error;
		}

		try {
			if (response?.success) {
				const ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['newsletter'];
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing newsletter to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message + ' ' + JSON.stringify(body),
		});
	}
});
