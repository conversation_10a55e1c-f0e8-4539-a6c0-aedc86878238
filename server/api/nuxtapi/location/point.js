export default defineEventHandler(async event => {
	const body = await readBody(event);
	const cacheKey = `location:point:${body.id}.${body.lang}`;
	const data = await getCachedData(cacheKey);
	if (data) return data;

	const config = useAppConfig();
	const storage = useStorage();
	const endpoints = await storage.getItem('db:endpoints');
	const auth = await getAuthHeader(event);

	let errorLog = '';
	try {
		let response;
		try {
			response = await $fetch(`${config.host}${endpoints.data._get_hapi_location_point.replace('%ID%', body.id)}`, {
				headers: {
					'Authorization': auth,
				},
			});
		} catch (error) {
			errorLog = 'Error while fetching location point.';
			throw error;
		}

		try {
			if (response?.success) {
				const ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['locationPoint'];
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing location point to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message + ' ' + JSON.stringify(body),
		});
	}
});
