export default defineEventHandler(async event => {
	const body = await readBody(event);

	const cacheKey = `gdpr:template.${body.template}.${body.lang}`;
	const data = await getCachedData(cacheKey);
	if (data) return data;

	const config = useAppConfig();
	const storage = useStorage();

	let errorLog = '';
	try {
		const endpoints = await storage.getItem('db:endpoints');
		const auth = await getAuthHeader(event);
		let url = new URL(`${config.host}${endpoints.data._get_hapi_gdpr_form}`);
		url = url.toString().replace('%TEMPLATE%', body.template);

		let response;
		try {
			response = await $fetch(`${url}?lang=${body.lang}`, {
				headers: {
					'Authorization': auth,
				},
			});
		} catch (error) {
			errorLog = 'Error while fetching gdpr template.';
			throw error;
		}

		try {
			if (response?.success) {
				const ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['gdprTemplate'];
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing gdpr template to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message + ' ' + JSON.stringify(body),
		});
	}
});
