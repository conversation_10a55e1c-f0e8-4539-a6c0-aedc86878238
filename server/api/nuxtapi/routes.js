export default defineEventHandler(async event => {
	const cacheKey = 'routes';
	const data = await getCachedData(cacheKey);
	if (data) return data;

	const config = useAppConfig();
	const storage = useStorage();

	let errorLog = '';
	try {
		const endpoints = await storage.getItem('db:endpoints');
		const auth = await getAuthHeader(event);

		let response;
		try {
			response = await $fetch(`${config.host}${endpoints.data._get_hapi_misc_routes}`, {
				headers: {
					'Authorization': auth,
				},
			});
		} catch (error) {
			errorLog = 'Error while fetching routes.';
			throw error;
		}

		try {
			if (response?.success) {
				let ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['routes'];
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing routes to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message,
		});
	}
});
