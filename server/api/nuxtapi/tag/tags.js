import {hash} from 'ohash';

export default defineEventHandler(async event => {
	const body = await readBody(event);
	const reqUrl = getRequestURL(event);
	const cacheKey = 'tag:tags.' + hash(['api-fetch', reqUrl.pathname, body]) + '.' + body.lang;
	const data = await getCachedData(cacheKey);
	if (data) return data;

	const config = useAppConfig();
	const storage = useStorage();

	let errorLog = '';
	try {
		const endpoints = await storage.getItem('db:endpoints');
		const auth = await getAuthHeader(event);

		let response;
		try {
			response = await $fetch(`${config.host}${endpoints.data._post_hapi_tag_tags}`, {
				headers: {'Authorization': auth},
				method: 'POST',
				body: body,
			});
		} catch (error) {
			errorLog = 'Error while fetching tags.';
			throw error;
		}

		try {
			if (response?.success) {
				const ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['tag'];
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing tags to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message + ' ' + JSON.stringify(body),
		});
	}
});
