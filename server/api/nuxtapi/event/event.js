export default defineEventHandler(async event => {
	const body = await readBody(event);
	const cacheKey = `event:event:${body.id}.${body.lang}`;
	const data = await getCachedData(cacheKey);
	if (data) return data;

	const config = useAppConfig();
	const storage = useStorage();
	const endpoints = await storage.getItem('db:endpoints');
	const auth = await getAuthHeader(event);

	let errorLog = '';
	try {
		let queryString = '';
		Object.entries(body).forEach(([key, value]) => {
			if (Array.isArray(value)) {
				value = value.join(',');
			}
			if (queryString !== '') queryString += '&';
			queryString += `${key}=${value}`;
		});
		const url = new URL(`${config.host}${endpoints.data._get_hapi_event}?${queryString}`);

		let response;
		try {
			response = await $fetch(url.toString().replace('%ID%', body.id), {
				headers: {'Authorization': auth},
			});
		} catch (error) {
			errorLog = 'Error while fetching event.';
			throw error;
		}

		try {
			if (response?.success) {
				const ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['event'];
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing event to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message + ' ' + JSON.stringify(body),
		});
	}
});
