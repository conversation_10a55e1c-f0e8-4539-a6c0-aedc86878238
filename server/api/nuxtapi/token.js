export default defineEventHandler(async event => {
	const query = getQuery(event); // get query params
	const cacheKey = 'token';
	const regenerate = query.regenerate || 0; // get regenerate param

	if (!regenerate) {
		const token = await getCachedData(cacheKey);
		if (token) return token;
	}

	const config = useAppConfig();

	let errorLog = '';
	try {
		let response;
		try {
			response = await $fetch(`${config.host}/${config.api}/${config.apiVersion}/auth/generate-token/`);
		} catch (error) {
			errorLog = 'Error while fetching token.';
		}

		try {
			if (response?.data?.jwt) {
				await cacheData(cacheKey, response.data.jwt, {ttl: config.cache?.refresh['token']});
				return response.data.jwt;
			} else {
				return null;
			}
		} catch (error) {
			errorLog = 'Error while storing token to cache.';
		}
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message,
		});
	}
});
