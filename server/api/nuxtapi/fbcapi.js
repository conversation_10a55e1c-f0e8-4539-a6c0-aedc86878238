export default defineEventHandler(async event => {
	const config = useRuntimeConfig();
	const accessToken = config.fbAccessToken;
	const pixelId = config.fbPixelIds?.default;
	if (!pixelId || !accessToken) {
		return {success: false, message: 'Missing pixel ID or access token.'};
	}

	let requestBody = await readBody(event);
	const headers = await getRequestHeaders(event);
	const ip = headers['x-forwarded-for'] || '';
	const userAgent = headers['user-agent'] || '';

	try {
		// Add IP address and user agent to the payload
		requestBody = {
			...requestBody,
			data: requestBody.data.map(item => {
				return {
					...item,
					user_data: {
						...item.user_data,
						client_ip_address: ip,
						client_user_agent: userAgent,
					},
				};
			}),
		};

		const response = await $fetch(`https://graph.facebook.com/v21.0/${pixelId}/events?access_token=${accessToken}`, {
			method: 'POST',
			body: requestBody,
		});
		return {success: true, response: response};
	} catch (error) {
		return {success: false, status: error.status, message: error.message};
	}
});
