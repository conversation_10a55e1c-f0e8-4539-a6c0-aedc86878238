export default defineEventHandler(async event => {
	//const method = await getMethod(event);
	const storage = useStorage();
	const endpoints = await storage.getItem('db:endpoints');
	const body = await readBody(event);
	const url = endpoints.data._get_hapi_faq_question.replace('%ID%', body.id);

	const cacheKey = `faq:question:${body.id}.${body.lang}`;
	const data = await getCachedData(cacheKey);
	if (data) return data;

	const config = useAppConfig();

	let errorLog = '';
	try {
		const auth = await getAuthHeader(event);
		let response;
		try {
			response = await $fetch(`${config.host}${url}`, {
				headers: {
					'Authorization': auth,
				},
				method: 'GET',
			});
		} catch (error) {
			errorLog = 'Error while fetching faq question.';
			throw error;
		}

		try {
			if (response?.success) {
				const ttl = !response.data?.id ? 1 : config.cache?.refresh['faqQuestion'];
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing faq question to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message + ' ' + JSON.stringify(body),
		});
	}
});
