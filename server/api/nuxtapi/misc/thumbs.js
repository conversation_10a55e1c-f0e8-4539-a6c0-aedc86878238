import {hash} from 'ohash';

export default defineEventHandler(async event => {
	//const method = await getMethod(event);
	const body = await readBody(event);
	const reqUrl = getRequestURL(event);
	const cacheKey = 'thumbs:thumbs.' + hash(['api-fetch', reqUrl.pathname, body]);

	const data = await getCachedData(cacheKey);
	if (data) return data;

	const config = useAppConfig();

	let errorLog = '';
	try {
		const auth = await getAuthHeader(event);
		let response;
		try {
			response = await $fetch(`${config.host}/${config.api}/${config.apiVersion}/misc/generate-thumbnail/`, {
				headers: {'Authorization': auth},
				method: 'POST',
				body: body,
			});
		} catch (error) {
			errorLog = 'Error while fetching thumbs.';
			throw error;
		}

		try {
			if (response?.success) {
				const ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['thumbs'];
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing thumbs to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message + ' ' + JSON.stringify(body),
		});
	}
});
