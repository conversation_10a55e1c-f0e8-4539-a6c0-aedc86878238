export default defineEventHandler(async event => {
	const storage = useStorage();
	const keys = await readBody(event);

	if (keys.length === 0) {
		if (process.env.NODE_ENV === 'development') console.log('Clear cache: no key provided.');
		return;
	}

	if (keys.includes('all')) {
		const nitroCache = useStorage('cache');
		await nitroCache.clear();

		const allKeys = await storage.getKeys('db');
		const retainKeys = ['db:token', 'db:endpoints'];
		const keysToRemove = allKeys.filter(key => !retainKeys.includes(key));
		await removeFolderItems(keysToRemove);
		return;
	}

	if (keys.includes('nitro:routes')) {
		const nitroCache = useStorage('cache');
		await nitroCache.clear();
	}

	keys.forEach(async key => {
		if (key) {
			const items = await storage.getKeys(`db:${key}`);
			if (items?.length) await removeFolderItems(items);
			if (!items?.length) await removeItem(key);
		}
	});

	async function removeFolderItems(folder) {
		for (const item of folder) {
			await storage.removeItem(item);
		}
	}
	async function removeItem(item) {
		await storage.removeItem(`db:${item}`);
	}
});
