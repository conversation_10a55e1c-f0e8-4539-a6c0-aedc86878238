export default defineEventHandler(async event => {
	const storage = useStorage();

	let data = {};
	try {
		const results = await Promise.all([storage.getItem('db:token'), storage.getItem('db:endpoints'), storage.getItem('db:routes'), storage.getItem('db:currency'), storage.getItem('db:labels'), storage.getItem('db:info')]);

		data = {
			token: results[0],
			endpoints: results[1],
			routes: results[2],
			currency: results[3],
			labels: results[4],
			info: results[5],
			allValid: results.every(item => item !== null && item !== undefined),
		};
	} catch (err) {
		if (process.env.NODE_ENV == 'development') console.log('Non existing init data', err);
		data = {
			token: false,
			endpoints: false,
			routes: false,
			currency: false,
			labels: false,
			info: false,
			allValid: false,
		};
	}

	return data;
});
