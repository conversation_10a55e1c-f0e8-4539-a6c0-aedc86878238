export default defineEventHandler(async event => {
	const storage = useStorage();
	const queryParams = getQuery(event);
	let slug = queryParams.slug;
	const sanitizedSlug = slug ? slug.replace(/[^a-zA-Z0-9]/g, '') : null;

	const cacheKey = sanitizedSlug ? `pages:page.${sanitizedSlug}.${queryParams.lang}` : null;

	// check if page is cached
	if (cacheKey) {
		const data = await getCachedData(cacheKey);
		if (data) return data;
	}

	const config = useAppConfig();

	let errorLog = '';
	try {
		const endpoints = await storage.getItem('db:endpoints');
		const auth = await getAuthHeader(event);
		const url = new URL(`${config.host}${endpoints.data._get_hapi_cms_pages}`);

		// append the query parameters to the URL
		Object.entries(queryParams).forEach(([key, value]) => {
			url.searchParams.append(key, value);
		});

		// if not, fetch the data from the API
		let response;
		try {
			response = await $fetch(url.toString(), {
				headers: {
					'Authorization': auth,
				},
			});
		} catch (error) {
			errorLog = 'Error while fetching pages.';
			throw error;
		}

		try {
			if (cacheKey && response?.success) {
				const ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['cmsPages'];
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing pages to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message + ' ' + JSON.stringify(queryParams),
		});
	}
});
