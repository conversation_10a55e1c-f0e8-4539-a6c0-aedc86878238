export default defineEventHandler(async event => {
	const data = await getCachedData('currency');
	if (data) return data;

	const config = useAppConfig();
	const storage = useStorage();

	let errorLog = '';
	try {
		const endpoints = await storage.getItem('db:endpoints');
		const auth = await getAuthHeader(event);
		let response;
		try {
			response = await $fetch(`${config.host}${endpoints.data._get_hapi_catalog_currency}`, {
				headers: {
					'Authorization': auth,
				},
			});
		} catch (error) {
			errorLog = 'Error while fetching currency.';
			throw error;
		}

		try {
			if (response?.success) {
				const ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['currency'];
				await cacheData('currency', response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing currency to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message,
		});
	}
});
