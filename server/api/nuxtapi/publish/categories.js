import {hash} from 'ohash';

export default defineEventHandler(async event => {
	const config = useAppConfig();
	const body = await readBody(event);
	const reqUrl = getRequestURL(event);
	let cacheSlug = '';
	if (body.slug) cacheSlug = body.slug.replace('/', '.') + '.';
	const cacheKey = 'publish:categories:categories.' + cacheSlug + hash(['api-fetch', reqUrl.pathname, cacheSlug, body]) + '.' + body.lang;

	const data = await getCachedData(cacheKey);
	if (data) return data;

	const storage = useStorage();

	let errorLog = '';
	try {
		const endpoints = await storage.getItem('db:endpoints');
		const auth = await getAuthHeader(event);
		let response;
		try {
			response = await $fetch(`${config.host}${endpoints.data._post_hapi_publish_categories}`, {
				headers: {
					'Authorization': auth,
				},
				method: 'POST',
				body: body,
			});
		} catch (error) {
			errorLog = 'Error while fetching publish categories.';
			throw error;
		}

		try {
			if (response?.success) {
				const ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['publishCategories'];
				await cacheData(cacheKey, response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing publish categories to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message + ' ' + JSON.stringify(body),
		});
	}
});
