export default defineEventHandler(async event => {
	const data = await getCachedData('endpoints');
	if (data) return data;

	const config = useAppConfig();

	let errorLog = '';
	try {
		const auth = await getAuthHeader(event);
		let response;
		try {
			response = await $fetch(`${config.host}/${config.api}/${config.apiVersion}/misc/endpoints/?version=${config.apiVersion}`, {
				headers: {
					'Authorization': auth,
				},
			});
		} catch (error) {
			errorLog = 'Error while fetching endpoints.';
			throw error;
		}

		try {
			if (response?.success) {
				const ttl = !Object.values(response.data).length ? 1 : config.cache?.refresh['endpoints'];
				await cacheData('endpoints', response, {ttl});
			}
		} catch (error) {
			errorLog = 'Error while storing endpoints to cache.';
		}

		return response;
	} catch (error) {
		throw createError({
			statusCode: error.status,
			message: errorLog + ' ' + error.message,
		});
	}
});
