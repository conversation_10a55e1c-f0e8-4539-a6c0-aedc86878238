<template>
	<Title>Nalog za plaćanje</Title>
	<BaseWebshopPaymentTransfer v-if="order?.cart?.payment_transfer_data" :data="order.cart.payment_transfer_data" />
</template>

<script setup>
	definePageMeta({
		layout: 'blank',
	});

	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {order, fetchOrder} = useWebshop();

	onMounted(async () => {
		if (route?.query?.order_identificator) {
			const urlOrderIdentificator = route.query.order_identificator.split('|');
			const orderIdentificator = urlOrderIdentificator?.length > 1 ? `${urlOrderIdentificator[0]}-${urlOrderIdentificator[1]}` : urlOrderIdentificator[0];
			await fetchOrder({code: orderIdentificator});
		}

		if (route.query.mode == 'pdf') {
			await nextTick(() => {
				window.print();
			});
		}
	});
</script>
