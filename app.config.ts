export default defineAppConfig({
	host: 'https://tzh.markerheadless.info',
	lang: 'hr',
	baseCompatibility: '1.30.0',
	cache: {
		enabled: process.env.NODE_ENV == 'development' ? false : true,
	},
	publish: {
		postsResponseFields: ['id', 'url_without_domain', 'title', 'category_title', 'headline', 'main_image_thumbs', 'main_image_title', 'main_image_description', 'images', 'short_description', 'feedback_comment_widget', 'feedback_rate_widget', 'category_url_without_domain', 'attributes_summary'],
	},
	catalog: {
		quickOrder: true,
		productsResponseFields: [
			'id',
			'code',
			'is_available',
			'priority_details',
			'category_title',
			'discount_percent',
			'discount_percent_custom',
			'payment_prices',
			'price_custom',
			'basic_price',
			'basic_price_custom',
			'price_b2b_custom',
			'basic_price_b2b_custom',
			'loyalty_price',
			'rate',
			'price_rate_custom',
			'url_without_domain',
			'main_image_thumbs',
			'main_image_title',
			'main_image_description',
			'title',
			'is_available',
			'attributes_special',
			'extra_price_lowest',
			'manufacturer_code',
			'manufacturer_url_without_domain',
			'manufacturer_title',
			'manufacturer_main_image_upload_path',
			'manufacturer_main_image_thumbs',
			'shopping_cart_code',
			'status',
			'feedback_comment_widget',
			'feedback_rate_widget',
			'type',
			'available_qty',
			'package_qty',
			'qty',
			'warehouses',
			'attributeitems_special',
			'attributes_special',
			'images',
			'element_video',
			'seo_h1',
			'category_url_without_domain',
			'limited_qty',
		],
	},
	layouts: [
		{
			template: 'CmsAbout',
			layout: 'about',
		},
	],
});
