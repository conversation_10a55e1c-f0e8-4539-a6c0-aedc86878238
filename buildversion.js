const fs = require('fs');

// Function to update the version in .env file
function updateVersionInEnv(version) {
	const filename = '.env';

	let content = '';

	// Check if the file exists
	if (fs.existsSync(filename)) {
		// Read the content from the file if it exists
		content = fs.readFileSync(filename, 'utf8');
	}

	// Split the content into lines
	let lines = content.split('\n');

	// Find and update the version line
	const versionPrefix = 'VITE_BUILD_VERSION=';
	let found = false;
	lines = lines.map(line => {
		if (line.startsWith(versionPrefix)) {
			found = true;
			return `${versionPrefix}${version}`;
		}
		return line;
	});

	// If the version line was not found, append it
	if (!found) {
		lines.push(`${versionPrefix}${version}`);
	}

	// Join the lines back into a single string and write back to file
	fs.writeFileSync(filename, lines.join('\n'));
}

// Generate the version string
const date = new Date();
const year = date.getFullYear();
const month = String(date.getMonth() + 1).padStart(2, '0');
const day = String(date.getDate()).padStart(2, '0');
const hours = String(date.getHours()).padStart(2, '0');
const minutes = String(date.getMinutes()).padStart(2, '0');
const version = `${day}${month}${year}-${hours}${minutes}`;

// Update the version in .env.local
updateVersionInEnv(version);
