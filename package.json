{"private": true, "scripts": {"build": "node ..\\nuxt-base\\buildversion.js && nuxt build", "build-mac": "node ../nuxt-base/buildversion.js; nuxt build", "devs": "nuxt dev --port 3000 --https", "dev": "nuxt dev --host 0.0.0.0", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxt/devtools": "1.6.1", "nuxt": "3.14.1592"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@vueform/slider": "^2.1.10", "less": "^4.2.0", "less-loader": "^12.2.0", "swiper": "^11.1.15", "vee-validate": "4.14.7"}}