{"private": true, "scripts": {"build": "node ..\\nuxt-base\\buildversion.js && nuxt build", "build-mac": "node ../nuxt-base/buildversion.js; nuxt build", "dev": "nuxt dev", "network": "nuxt dev --host 0.0.0.0", "profiler": "node --prof .output/server/index.mjs", "inspect": "node --inspect .output/server/index.mjs", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "analyze": "nuxt build --analyze"}, "devDependencies": {"@nuxt/devtools": "2.3.2", "nuxt": "3.16.2", "nuxt-delay-hydration": "^1.3.8"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@vueform/slider": "^2.1.10", "@vuepic/vue-datepicker": "^8.3.1", "less": "^4.3.0", "less-loader": "^12.2.0", "swiper": "^11.2.6", "vee-validate": "4.15.0"}}