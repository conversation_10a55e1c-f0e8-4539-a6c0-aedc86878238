<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('content'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	<?php echo Arr::get($cms_page, 'content'); ?>

	<?php /* if (sizeof($items)): ?>
		<?php foreach ($items as $item): ?>
		<div class="news">
			<h2><?php echo $item['title']; ?></h2>
			<?php if (isset($item['url_manage']) AND $item['url_manage']): ?>
				<a href="<?php echo $item['url_manage']; ?>">prijava / odjava</a>
			<?php endif; ?>
			<?php if (isset($item['url_archive']) AND $item['url_archive']): ?>
				<a href="<?php echo $item['url_archive']; ?>">arhiva</a>
			<?php endif; ?>
		</div>
		<div class="clear"></div>
		<?php endforeach; ?>
	<?php else: ?>
		<?php echo Arr::get($cmslabel, 'no_newsletter_lists'); ?>
	<?php endif; */ ?>

<?php $this->endblock('content'); ?>
