<?php if (!isset($newsletter_form)) {$newsletter_form = Widget_Newsletter::form(array('lang' => $info['lang'], 'code' => 'list'));} ?>

<?php if ($newsletter_form): ?>
	<?php $nl_image = Html::image_from_string(Arr::get($cmslabel, 'newsletter_popup_image'), '', 1); ?>
	<?php if(!empty($nl_image)): ?>
		<style>.quick-fancybox-leave .fancybox-inner{background-image: url('<?php echo $nl_image; ?>'); background-size: cover;}</style>
	<?php endif; ?>
	<div id="newsletter_popup" style="display: none">
		<div class="nw-leaving">
			<div class="nw-leaving-logo"></div>
			<div class="nw-leaving-cnt">
				<div class="nw-leaving-title"><?php echo Arr::get($cmslabel, 'newsletter_widget_title'); ?></div>
				<div class="nw-leaving-subtitle"><?php echo Arr::get($cmslabel, 'newsletter_widget_description'); ?></div>

				<form class="nw-leaving-form" novalidate action="<?php echo $newsletter_form['url_manage']; ?>" method="POST" id="newsletter_subscribe_<?php echo $newsletter_form['id']; ?>_popup" data-newsletter_leaving="<?php echo $newsletter_form['url_manage']; ?>" data-newsletter_leaving_ignores="<?php echo Utils::app_absolute_url($info['lang'], 'webshop'); ?>*">
					<div class="nw-fields nw-leaving-fields">
						<input type="hidden" name="lang" value="<?php echo $info['lang']; ?>"  />
                        <input type="hidden" name="site" value="<?php echo $info['site_id']; ?>"  />
						<input type="hidden" name="list" value="<?php echo $newsletter_form['id']; ?>"  />
						<input type="hidden" name="event" value="<?php echo Arr::get($_GET, 'event', 'leave'); ?>" />
						<input type="hidden" name="first_name" value="" />
						<input type="hidden" name="last_name" value="" />
						<input class="nw-input" type="email" name="email" placeholder="<?php echo Arr::get($cmslabel, 'your_email'); ?>" />
						<button class="btn btn-red nw-button" type="submit"><span><?php echo Arr::get($cmslabel, 'send', 'Pošalji'); ?></span></button>
					</div>
					<div id="field-error-email" class="nw-error nw-leaving-error" style="display: none"></div>

					<?php if (!empty($newsletter_form['gdpr_accept_label'])): ?>
						<div class="nw-checkbox nw-leaving-checkbox">
							<input type="hidden" name="gdpr_accept" value="0" />
							<input type="checkbox" name="gdpr_accept" value="1" id="gdpr_accept-1" />
							<label for="gdpr_accept-1"><span><?php echo str_replace(['<p>', '</p>'], " ", $newsletter_form['gdpr_accept_label']); ?></span></label>
							<span id="field-error-newsletter_gdpr_accept" class="error nw-gdpr-error gdpr_accept-error" style="display: none"></span>
						</div>
					<?php endif; ?>
				</form>
				<div class="nw-success newsletter_subscribe_success" style="display: none;"><?php echo Arr::get($cmslabel, 'success_subscribe'); ?></div>
			</div>
		</div>
	</div>
<?php endif; ?>