<?php if (!isset($newsletter_form)) {$newsletter_form = Widget_Newsletter::form(array('lang' => $info['lang'], 'code' => 'list'));} ?>
<?php $mode = (isset($mode))? $mode : ''; ?>
<?php $class = (isset($class))? ' '.$class : ''; ?>

<?php if ($newsletter_form): ?>
<div class="nw nw-bottom <?php if($mode == 'main'): ?>nw-bottom-custom<?php endif; ?><?php if($class): ?><?php echo $class; ?><?php endif; ?>" id="newsletter">
	<div class="nw-col nw-col1" data-css="lazyload" data-original="/media/images/bg-green.jpg">
		<div class="support">
			<p class="support-title"><?php echo Arr::get($cmslabel, 'footer_user_support'); ?></p>
			<?php echo Arr::get($cmslabel, 'support'); ?>
		</div>
		<div class="social">
			<?php echo Arr::get($cmslabel, 'contact_social'); ?>
		</div>
	</div>

	<?php $nl_image = Html::image_from_string(Arr::get($cmslabel, 'newsletter_image'), '/media/images/newsletter.jpg', 1); ?>
	<div class="nw-col nw-col2<?php if($nl_image != '/media/images/newsletter.jpg'): ?> custom-image<?php endif; ?>" data-css="lazyload" data-original="<?php echo $nl_image; ?>">
		<div class="nw-body">	
			<!-- Newsletter Subscribe -->
			<div class="nw-title"><?php echo Arr::get($cmslabel, 'newsletter_widget_title'); ?></div>
			<div class="nw-cnt"><?php echo Arr::get($cmslabel, 'newsletter_subtitle'); ?></div>
			<form class="nw-form" action="<?php echo $newsletter_form['url_manage']; ?>" method="POST" id="newsletter_subscribe_<?php echo $newsletter_form['id']; ?>">
				<div class="nw-fields">
					<input type="hidden" name="lang" value="<?php echo $info['lang']; ?>"  />
					<input type="hidden" name="site" value="<?php echo $info['site_id']; ?>"  />
					<input type="hidden" name="list" value="<?php echo $newsletter_form['id']; ?>"  />
					<input type="hidden" name="first_name" value="" />
					<input type="hidden" name="last_name" value="" />
					<input class="nw-input" type="text" name="email" placeholder="<?php echo Arr::get($cmslabel, 'enter_email'); ?>" />
					<button class="nw-button" type="submit"><?php echo Arr::get($cmslabel, 'newsletter_signup', 'Pošalji'); ?></button>
				</div>
				<div id="field-error-email" class="nw-error newsletter-error" style="display: none;"></div>

				<?php if (!empty($newsletter_form['gdpr_accept_label'])): ?>
					<div class="nw-checkbox">
						<input type="hidden" name="gdpr_accept" value="0" />
						<input type="checkbox" name="gdpr_accept" value="1" id="gdpr_accept-1" />
						<label for="gdpr_accept-1"><span><?php echo str_replace(['<p>', '</p>'], " ", $newsletter_form['gdpr_accept_label']); ?></span></label>
						<span id="field-error-newsletter_gdpr_accept" class="error nw-gdpr-error gdpr_accept-error" style="display: none"></span>
					</div>
				<?php endif; ?>
			</form>
			<div class="nw-success newsletter_subscribe_success" style="display: none;"><?php echo Arr::get($cmslabel, 'success_subscribe'); ?></div>
		</div>
	</div>
</div>
<?php endif; ?>