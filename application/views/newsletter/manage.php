<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['item' => $item]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-newsletter-login<?php $this->endblock('page_class'); ?>

<?php $this->block('h1'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1', $item['seo_h1']); ?></h1>
<?php $this->endblock('h1'); ?>

<?php $this->block('content'); ?>
	<div class="content-wrapper">
		<div class="content">
			<?php echo Arr::get($cms_page, 'content', $item['content']); ?>

			<?php if (Arr::get($_GET, 'success') === 'subscribe_confirm'): ?>
				<p class="global-success"><?php echo Arr::get($cmslabel, "success_subscribe_confirm"); ?></p>
			<?php elseif (Arr::get($_GET, 'success') === 'subscribe_confirm_coupon'): ?>
				<p class="global-success"><?php echo Arr::get($cmslabel, "success_subscribe_confirm_coupon"); ?></p>
			<?php elseif ($info['message_type'] AND $info['message']): ?>
				<?php if (is_array($info['message'])): ?>
					<p class="global-<?php echo $info['message_type']; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
				<?php else: ?>
					<p class="global-<?php echo $info['message_type']; ?>"><?php echo Arr::get($cmslabel, "{$info['message_type']}_{$info['message']}"); ?></p>
				<?php endif; ?>
			<?php endif; ?>
		</div>
	</div>
    
<?php $this->endblock('content'); ?>