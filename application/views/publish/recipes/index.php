<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title_full'])) ? $kind['seo_title_full'] : Arr::get($cms_page, 'seo_title')).((!empty($pagination->current_page) AND $pagination->current_page > 1) ? sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages) : '')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['kind' => $kind, 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-recipes<?php if($q): ?> page-search white-bg<?php endif; ?><?php $this->endblock('page_class'); ?>

<?php $this->block('after_header'); ?>
	<?php if($q): ?>
		<div class="s-header-wrapper">
			<h1 class="s-h1"><span class="s-headline"><?php echo Arr::get($cmslabel, 'search_headline'); ?></span><span class="s-keyword"><?php echo $q; ?></span></h1>
			
			<?php 
			$catalog_search_url = Utils::app_absolute_url($info['lang'], 'catalog');
			$search_url = Utils::app_absolute_url($info['lang'], 'search');
			$search_content = Arr::get($_GET, 'search_content');
			$search_totals = Widget_Search::totals($info['lang'], $q, $search_content);
			$other_results = array_sum($search_totals); 
			$blog_category = Widget_Publish::category($info['lang'], 'blog');
			$blog_url = (!empty($blog_category['url'])) ? $blog_category['url'] : '';
			?>
			<ul class="content-menu s-nav">
				<li><a href="<?php echo $catalog_search_url; ?>?search_q=<?php echo $q; ?>"><?php echo Arr::get($cmslabel, "search_catalog"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'catalog', 0); ?>)</span></a></li>
				<?php if (!empty($blog_url)): ?>
					<li><a href="<?php echo $blog_url; ?>?search_q=<?php echo $q; ?>"><?php echo Arr::get($cmslabel, "search_publish"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish.01', (($items AND $search_content == 'publish.01') ? count($items['publish.01']) : 0)); ?>)</span></a></li>
				<?php endif; ?>
				<li class="selected"><a href="?search_q=<?php echo $q; ?>"><?php echo Arr::get($cmslabel, "search_recipes"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish.02', (($items AND $search_content == 'publish.02') ? count($items['publish.02']) : 0)); ?>)</span></a></li>
				<li<?php if($search_content == 'cms'): ?> class="selected"<?php endif; ?>><a href="<?php echo $search_url; ?>?search_q=<?php echo $q; ?>&search_content=cms"><?php echo Arr::get($cmslabel, "search_cms"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'cms', (($items AND $search_content == 'cms') ? count($items['cms']) : 0)); ?>)</span></a></li>
			</ul>
		</div>
	<?php else: ?>
		<div class="wrapper p-recipes-header">
			<div class="bc p-recipes-bc">
				<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $kind['breadcrumbs']); ?>
				<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
			</div>

			<h1 class="p-recipes-title"><?php echo $kind['seo_h1']; ?></h1>
			<div class="p-recipes-description"><?php echo $kind['content']; ?></div>
			<a class="btn-toggle-filter btn-toggle-recipe-filter" href="javascript:void(0);"><span><span class="filter-icon"></span><?php echo Arr::get($cmslabel, 'filters'); ?></span></a>
		</div>
	<?php endif; ?>
<?php $this->endblock('after_header'); ?>

<?php $this->block('main'); ?>
	<div class="wrapper">
		<?php if($q): ?>
			<a class="btn-toggle-filter btn-toggle-search-filter" href="javascript:void(0);"><span><span class="filter-icon"></span><?php echo Arr::get($cmslabel, 'filters'); ?></span></a>
		<?php endif; ?>
		<div class="df p-recipes-row">
			<?php $hello_bar_rotator = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'hello_bar', 'limit' => 1, 'ignore_hidden_element' => true]); ?>
			<?php $hello_bar_rotator_hidden = false; ?>
			<?php if(!empty($hello_bar_rotator)): ?>
				<?php foreach ($hello_bar_rotator as $hello_bar): ?>
					<?php if($hello_bar['coupon_code'] != null AND !empty($user->b2b)) : ?>
						<?php $hello_bar_rotator_hidden = true; ?>
					<?php endif; ?>
				<?php endforeach; ?>
			<?php endif; ?>
			<aside class="p-recipes-sidebar<?php if (!empty($hello_bar_rotator)): ?> recipes-sidebar-hellobar<?php endif; ?><?php if($hello_bar_rotator_hidden): ?> hello-bar-none<?php endif; ?>">
				<?php $search_fields = Widget_Publish::search_filters(array('lang' => $info['lang'], 'filters' => $filters, 'search_id' => ((isset($kind['search_id']) AND $kind['search_id']) ? $kind['search_id'] : 1))); ?>
				<?php $active_filters = (!empty($search_fields['_basic']['selected'])) ? $search_fields['_basic']['selected'] : []; ?>
				<?php if (count($items) > 0 OR $active_filters): ?>
					<?php echo View::factory('catalog/widget/filter', ['search_fields' => $search_fields, 'active_filters' => $active_filters, 'class' => 'cf-recipes', 'mode' => 'recipes']); ?>
				<?php endif; ?>
			</aside>
	
			<div class="p-recipes-main">
				<div id="items_<?php echo $kind['code']; ?>_layout">
					<?php echo View::factory('publish/recipes/index_layout', [
						'kind' => $kind,
						'q' => '',
						'items' => $items,
						'items_per_page' => $items_per_page,
						'items_all' => $items_all,
						'items_total' => $items_total,
						'child_categories' => $child_categories,
						'child_categories_published' => $child_categories_published,
						'pagination' => $pagination,
					]); ?>
				</div>
			</div>
		</div>
	</div>
<?php $this->endblock('main'); ?>