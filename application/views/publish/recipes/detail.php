<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item, 'schema_org' => true]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-recipe-detail white-bg<?php $this->endblock('page_class'); ?>

<?php $this->block('after_header'); ?>
	<div class="wrapper pd-recipe-header-wrapper">
		<div class="pd-recipe-info-container"></div>
		<div class="bc pd-recipe-bc">
			<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
			<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
		</div>
		<div class="df aic pd-recipe-intro">
			<?php if (isset($item['attributes']) AND $item['attributes']): ?>
				<?php foreach ($item['attributes'] AS $item_attribute): ?>
					<?php if ($item_attribute['attribute_code'] == 'vrsta_jela'): ?>
						<div class="pd-recipe-category"><?php echo $item_attribute['title']; ?></div>
					<?php endif; ?>
				<?php endforeach; ?>
			<?php endif; ?>

			<?php if (!empty($item['feedback_rate_widget'])): ?>
				<div class="pd-recipe-rates">
					<?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
				</div>
			<?php endif; ?>

			<?php $comment_status = (!empty($item['feedback_comment_widget'])) ? $item['feedback_comment_widget']['comments_status'] : 0; ?>
			<?php if($comment_status > 1): ?>
				<div class="pd-recipe-link-comments"><a href="#comments"><?php echo Arr::get($cmslabel, 'view_comments'); ?></a> <span class="green counter">(<?php echo ($item['feedback_comment_widget']['comments_by_lang']) ? $item['feedback_comment_widget']['comments_by_lang'] : 0; ?>)</span></div>
			<?php endif; ?>			
		</div>
		<h1 class="pd-recipe-title"><?php echo $item['seo_h1'] ?></h1>
	</div>
<?php $this->endblock('after_header'); ?>	

<?php $this->block('main'); ?>
	<div class="df wrapper pd-recipe-wrapper">
		<div class="pd-recipe-main">
			<?php if(!empty($item['main_image'])): ?>
				<div class="pd-recipe-image">
					<span><img <?php echo Thumb::generate($item['main_image'], array('width' => 1000, 'height' => 510, 'crop' => true, 'default_image' => '/media/images/no-image-780.jpg', 'html_tag' => true)); ?> alt="" /></span>
				</div>
			<?php endif; ?>
			
			<div class="pd-recipe-sidebar-m"></div>
			<!-- Post content and related documents -->
			<div class="lists custom-list pd-recipe-cnt">
				<div class="df pd-recipe-info">
					<?php $category_url = (!empty($item['category_parents'])) ? reset($item['category_parents'])['url'] : $item['category_url']; ?>
					<a class="pd-all-articles" href="<?php echo $category_url; ?>"><?php echo Arr::get($cmslabel, 'all_recipes'); ?></a>
					<div class="pd-recipe-date"><?php echo Date::humanize($item['datetime_published'], 'custom', 'd. F Y.', '', $info['lang']); ?></div>
				</div>

				<?php echo $item['content']; ?>

				<?php $documents = Utils::get_files('publish', $item['id'], '-image', $info['lang']); ?>
				<?php if ($documents): ?>
					<ul class="pd-documents">
						<?php foreach ($documents as $file): ?>
							<li><a href="<?php echo $file['url']; ?>" title="<?php echo Text::meta($file['description']); ?>"><?php echo Text::meta($file['title']); ?></a></li>
						<?php endforeach; ?>
					</ul>
				<?php endif; ?>
				
				<?php $images = Utils::get_files('publish', $item['id'], 'image', $info['lang'], 1); ?>
				<?php if ($images): ?>
					<div class="pd-thumbs">
						<?php foreach ($images as $file): ?>
							<a href="<?php echo $file['url']; ?>" class="fancybox" rel="gallery" title="<?php echo Text::meta($file['title']); ?><?php if($file['description']): ?> - <?php endif; ?><?php echo Text::meta($file['description']); ?>">
								<img <?php echo Thumb::generate($file['file'], ['width' => 750, 'default_image' => '/media/images/no-image-500.jpg', 'placeholder' => '/media/images/no-image-500.jpg']); ?> alt="<?php echo Text::meta($file['description']); ?>" />
							</a>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>
				
				<?php if(!empty($item['seo_keywords_tags'])): ?>
					<div class="tags pd-recipe-tags">
						<?php foreach ($item['seo_keywords_tags'] as $tag): ?>
							<?php if ($tag['id'] == 5): ?>
								<?php continue; ?>
							<?php endif ?>
							<a href="<?php echo $tag['url'] ?>"><?php echo $tag['title'] ?></a><span class="comma">, </span>
						<?php endforeach ?>
					</div>
				<?php endif; ?>

				<?php echo View::factory('cms/widget/share', ['item' => isset($item) ? $item : [], 'class' => 'pd-recipe-share']); ?>

				<?php if($comment_status > 1): ?>
					<div class="comments pd-recipe-comments" id="comments">
						<?php if($comment_status > 2): ?>
							<div class="comments-title"><?php echo Arr::get($cmslabel, 'add_recipe_comment'); ?></div>
						<?php endif; ?>
						<?php echo View::factory('feedback/comments', ['item' => $item['feedback_comment_widget'], 'mode' => 'recipe']); ?>
					</div>
				<?php endif; ?>	

				<!-- Related posts -->
				<?php 
				$related_items = Widget_Publish::publishes(['lang' => $info['lang'], 'related_code' => 'related', 'category_code' => 'recipe', 'related_item_id' => $item['id'], 'limit' => 6, 'extra_fields' => ['short_description']]);
				if (!$related_items AND !empty($item['seo_keywords_tags_ids'])) { 
					$related_items = Widget_Publish::publishes(['lang' => $info['lang'], 'related_tags_ids' => $item['seo_keywords_tags_ids'], 'category_code' => 'recipe', 'id_exclude' => $item['id'], 'limit' => 6, 'sort' => 'related_tag_total', 'extra_fields' => ['short_description']]); 
				}
				if (!$related_items) {
					$related_items = Widget_Publish::publishes(['lang' => $info['lang'], 'category_code' => $item['category_code'], 'category_code' => 'recipe', 'id_exclude' => $item['id'], 'limit' => 6, 'extra_fields' => ['short_description']]); 
				}
				?>
				<?php if ($related_items): ?>
					<div class="pd-recipe-related">
						<div class="pd-recipe-related-title"><?php echo Arr::get($cmslabel, 'recipe_related'); ?></div>
						<div class="pd-recipe-related-items">
							<?php echo View::factory('publish/recipes/index_entry', ['items' => $related_items, 'mode' => 'list', 'class' => 'gray-shadow']); ?>
						</div>
					</div>
				<?php endif; ?>
			</div>
		</div>

		<aside class="pd-recipe-sidebar">
			<div class="pd-recipe-sidebar-top">
				<?php 
					$attributes = (!empty($item['attributes'])) ? array_map(function($element) {
	                    return $element;
	                }, $item['attributes']) : [];
				?>

				<?php if(!empty($attributes)): ?>
					<div class="rcd-attributes">
						<?php if(!empty($item['attributes_summary']['vrijeme_pripreme']['content'])): ?>
							<div class="pd-recipe-attr pd-recipe-attr-time">
								<span class="pd-recipe-attr-title"><?php echo $item['attributes_summary']['vrijeme_pripreme']['title']; ?>:</span>
								<span class="pd-recipe-attr-value"><?php echo $item['attributes_summary']['vrijeme_pripreme']['content']; ?></span>
							</div>
						<?php endif; ?>

						<?php if (!empty($item['attributes'])): ?>
							<?php foreach ($item['attributes'] AS $item_attribute): ?>
								<?php if ($item_attribute['attribute_code'] == 'slozenost'): ?>
									<div class="pd-recipe-attr pd-recipe-attr-complexity<?php if(empty($item['element_ingredients'])): ?> last<?php endif; ?>">
										<span class="pd-recipe-attr-image">
											<img loading="lazy" src="<?php echo Utils::file_url($item_attribute['image']); ?>" alt="<?php echo $item_attribute['attribute_title']; ?>" width="30" height="30">
										</span>
										<span class="pd-recipe-attr-title"><?php echo $item_attribute['attribute_title']; ?></span>
										<span class="pd-recipe-attr-value"><?php echo $item_attribute['title']; ?></span>
									</div>
								<?php endif; ?>
							<?php endforeach; ?>
						<?php endif; ?>
						
						<?php if(!empty($item['element_ingredients'])): ?>
							<div class="pd-recipe-attr pd-recipe-attr-ingredients">
								<div class="pd-recipe-attr-title pd-recipe-attr-ingredients-title">
									<?php echo Arr::get($cmslabel, 'ingredients'); ?>
									<?php if(!empty($item['attributes_summary']['broj_sastojaka']['content'])): ?>
										<span class="pd-recipe-attr-counter">(<?php echo $item['attributes_summary']['broj_sastojaka']['content']; ?>)</span>
									<?php endif; ?>
								</div>
								<?php echo $item['element_ingredients']; ?>
							</div>
						<?php endif; ?>	
					</div>
				<?php endif; ?>
				<?php $related_products = (Kohana::config('app.catalog.use_productrelatedpublishes')) ? Widget_Catalog::products(['lang' => $info['lang'], 'related_publish_id' => $item['id'], 'limit' => 0]) : []; ?>
				<?php if (!empty($related_products)): ?>
					<a class="btn btn-orange btn-show-related" href="#related-products"><span><?php echo Arr::get($cmslabel, 'show_used_products'); ?></span></a>
				<?php endif; ?>
			</div>

			<!-- Related products -->
			<?php if (!empty($related_products)): ?>
				<?php $ga4_related_products = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $related_products, 'item_list_name' => Arr::get($cmslabel, 'used_products', 'Koristili smo'), 'item_list_id' => 'used_products']) ?>
				<div class="pd-recipe-related-products" id="related-products" data-ga4_events_info='<?php echo $ga4_related_products;?>'>
					<div class="pd-recipe-related-products-title"><?php echo Arr::get($cmslabel, 'used_products', 'Koristili smo'); ?></div>
					<div class="pd-recipe-products">
						<?php echo View::factory('catalog/index_entry', ['items' => $related_products, 'mode' => 'list', 'class' => 'cp-related', 'list' => 'Recepti', 'item_list_name' => Arr::get($cmslabel, 'used_products', 'Koristili smo'), 'item_list_id' => 'used_products']); ?>
					</div>
					<div class="pd-recipe-products-btns">
						<div class="related-products-add-message product_message_list"></div>
						<a class="btn btn-orange btn-add-all" href="javascript:cmswebshop.shopping_cart.add_special_list('related', 'all', 1, 'simple', 'simple_loader');"><span><?php echo Arr::get($cmslabel, 'all_to_cart'); ?></span></a>
					</div>
				</div>
			<?php endif; ?>
		</aside>

	</div>
<?php $this->endblock('main'); ?>