<?php $mode = (isset($mode)) ? $mode : ''; ?>
<?php $class = (isset($class)) ? ' '.$class : ''; ?>
<?php foreach ($items as $item): ?>
	<?php $rc_attrs = $item['attributes_summary']; ?>
	<a href="<?php echo $item['url']; ?>" class="pp-recipe<?php if($mode == 'list'): ?> pp-recipe-list<?php endif; ?><?php echo $class; ?>">
		<?php
			$img_w = 320;
			$img_h = 165;
			if($mode == 'list') {
				$img_w = 260;
				$img_h = 135;
			}
		?>
		<figure class="pp-recipe-image">
			<?php $image_alt = ($item['main_image_description']) ? Text::meta($item['main_image_description']) : $item['title']; ?>
			<span><img loading="lazy" <?php echo Thumb::generate($item['main_image'], array('width' => $img_w, 'height' => $img_h, 'crop' => true, 'default_image' => '/media/images/no-image-320.jpg', 'html_tag' => true)); ?><?php if(!empty($item['main_image_title'])): ?> title="<?php echo Text::meta($item['main_image_title']); ?>"<?php endif; ?> alt="<?php echo str_replace('"', '', $image_alt); ?>" /></span>
		</figure>
		<span class="pp-recipe-cnt">
			<span class="pp-recipe-rate">
				<?php if(!empty($item['feedback_rate_widget']) AND $item['feedback_rate_widget']['rates_status'] > 1): ?>
					<?php if (isset($item['feedback_rate_widget'])): ?>
						<?php if($item['feedback_rate_widget']['rates_votes'] > 0): ?>
							<?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
						<?php endif; ?>
					<?php endif; ?>
                    <?php if(!empty($item['feedback_comment_widget']['comments_by_lang']) AND $item['feedback_comment_widget']['comments_by_lang'] > 0): ?>
						<span class="pp-recipe-comments-counter">(<?php echo $item['feedback_comment_widget']['comments_by_lang']; ?>)</span>
					<?php endif; ?>
				<?php endif; ?>
			</span>

			<span class="pp-recipe-category"><?php echo $item['category_title']; ?></span>
			<span class="pp-recipe-title"><?php echo $item['title']; ?></span>

			<?php if(!empty($rc_attrs)): ?>
				<span class="df pp-recipe-attrs">
					<?php foreach ($rc_attrs as $rc_attr): ?>
						<?php if($rc_attr['code'] == 'vrijeme_pripreme'): ?>
							<span class="pp-recipe-attr pp-recipe-time"><?php echo Arr::get($cmslabel, 'preparation'); ?>: <strong><?php echo $rc_attr['content']; ?></strong></span>
						<?php endif; ?>
						<?php if($rc_attr['code'] == 'broj_sastojaka'): ?>
							<span class="pp-recipe-attr pp-recipe-ingredients"><?php echo Arr::get($cmslabel, 'ingredients'); ?>: <strong><?php echo $rc_attr['content']; ?></strong></span>
						<?php endif; ?>
					<?php endforeach; ?>
				</span>
			<?php endif; ?>
		</span>
	</a>
<?php endforeach; ?>