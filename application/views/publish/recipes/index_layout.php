<?php if (sizeof($items)): ?>
	<div class="p-items p-items-recipes" id="items_<?php echo $kind['code']; ?>" data-infinitescroll="items_<?php echo $kind['code']; ?>" data-infinitescroll_next_page="<?php echo $pagination->next_page; ?>" data-infinitescroll_total_pages="<?php echo $pagination->total_pages; ?>" data-infinitescroll_auto_trigger="1">
		<?php echo View::factory('publish/recipes/index_entry', ['items' => $items]); ?>
	</div>

	<?php echo $pagination; ?>
	<?php if ($pagination): ?>
		<div class="load-more-container">
			<a href="javascript:void(0);" class="btn load-more btn-load-more btn-load-more-recipes" style="display: none;"><?php echo str_replace(array('%PER_PAGE%', '%TOTAL%', '%NEXT_PAGE_FIRST_ITEM%', '%NEXT_PAGE_LAST_ITEM%'), array($pagination->items_per_page, $pagination->total_items, $pagination->next_page_first_item, $pagination->next_page_last_item), Arr::get($cmslabel, 'load_more_recipes')); ?></a>
		</div>
	<?php endif; ?>
<?php else: ?>
	<?php echo Arr::get($cmslabel, 'no_recipes'); ?>
<?php endif; ?>