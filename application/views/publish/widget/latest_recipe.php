<?php $category = Widget_Publish::category($info['lang'], 'recipe'); ?>
<?php $categories = Widget_Publish::categories(array('lang' => $info['lang'], 'level_range' => '2.2', 'start_position' => $category['position_h'])); ?>
<?php $items = Widget_Publish::publishes(array('lang' => $info['lang'], 'category_code' => $category['code'], 'limit' => 8)); ?>	

<div class="tabs-m-container m-pwr-list">
	<select class="tabs-m" onclick="if (this.value) location=this.value;">
		<option value="" selected><?php echo Arr::get($cmslabel, 'show_all_recipes', 'Prikaži sve recepte'); ?></option>	
		<?php foreach ($categories as $category_item): ?>
			<option value="<?php echo $category_item['url']; ?>"><?php echo $category_item['title']; ?></option>
		<?php endforeach; ?>
		<option value="<?php echo $category['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all_recipes', 'Prikaži sve recepte'); ?></option>				
	</select>
</div>

<ul class="pwr-list">
	<?php foreach ($categories as $category_item): ?>
		<li><a href="<?php echo $category_item['url']; ?>"><?php echo $category_item['title']; ?></a></li>
	<?php endforeach; ?>
	<li><a href="<?php echo $category['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all_recipes', 'Prikaži sve recepte'); ?></a></li>
</ul>

<div class="pwr-items owl-carousel">
	<?php echo View::factory('publish/recipes/index_entry',  array('items' => $items)); ?>
</div>
			
<div class="pw-show-all">
	<a href="<?php echo $category['url']; ?>" class="btn btn-gray pw-show-all-btn"><?php echo Arr::get($cmslabel, 'show_all_recipes', 'Prikaži sve recepte'); ?></a>
</div>