<?php $category = Widget_Publish::category($info['lang'], 'blog'); ?>
<?php if(!empty($category)): ?>
	<div class="pw-container">
		<?php $items = Widget_Publish::publishes(array('lang' => $info['lang'], 'category_code' => $category['code'], 'limit' => 5, 'extra_fields' => ['short_description'])); ?>	
		<div class="pw-group-container">
			<?php
				$i = 1; 
				$ii = 1; 
			?>
			<?php foreach ($items as $item): ?>
				<?php if($i == 1): ?>
					<a href="<?php echo $item['url']; ?>" class="pw-last clear<?php if(count($items) > 1): ?> special<?php endif; ?>">
						<div class="pwl-left">
							<span>
								<?php if($info['user_device'] == 'm'): ?>
									<img <?php echo Thumb::generate($item['main_image'], array('width' => 640, 'height' => 180, 'crop' => true, 'default_image' => '/media/images/no-image-500.jpg', 'placeholder' => '/media/images/no-image-500.jpg')); ?> alt="" />
								<?php else: ?>
									<img <?php echo Thumb::generate($item['main_image'], array('width' => 710, 'height' => 440, 'crop' => true, 'default_image' => '/media/images/no-image-500.jpg', 'placeholder' => '/media/images/no-image-500.jpg')); ?> alt="" />
								<?php endif; ?>
							</span>
							<div class="pwl-category"><?php echo $item['category_title']; ?></div>
						</div>
						<div class="pw-right">
							<?php if(!empty($item['headline'])): ?>
								<div class="pw-headline"><?php echo $item['headline']; ?></div>
							<?php endif; ?>
							<div class="pw-title-top"><?php echo $item['title'] ?></div>
							<div class="pw-cnt"><?php echo $item['short_description']; ?></div>
							<div class="btn btn-gray"><?php echo Arr::get($cmslabel, 'read_more', 'Pročitaj više'); ?></div>
						</div>
					</a>
				<?php endif; ?>		
			<?php $i++; ?>
			<?php endforeach; ?>

			<div class="pw-items fz0">	
				<?php foreach ($items as $item): ?>
					<?php if($ii > 1): ?>
						<a href="<?php echo $item['url']; ?>" class="pw-item <?php echo $i; ?>">
							<figure class="pw-image lloader">
								<span>
									<?php if($info['user_device'] == 'm'): ?>
										<img <?php echo Thumb::generate($item['main_image'], array('width' => 650, 'height' => 300, 'crop' => true, 'default_image' => '/media/images/no-image-250.jpg', 'placeholder' => '/media/images/no-image-250.jpg')); ?> alt="" />
									<?php else: ?>	
										<img <?php echo Thumb::generate($item['main_image'], array('width' => 280, 'height' => 200, 'crop' => true, 'default_image' => '/media/images/no-image-250.jpg', 'placeholder' => '/media/images/no-image-250.jpg')); ?> alt="" />
									<?php endif; ?>
								</span>
							</figure>
							<div class="pw-category"><span><?php echo $item['category_title']; ?></span></div>
							<?php if(!empty($item['headline'])): ?>
								<div class="pw-headline"><?php echo $item['headline']; ?></div>
							<?php endif; ?>
							<div class="pw-title"><?php echo $item['title'] ?></div>
						</a>
					<?php endif; ?>
					<?php $ii++; ?>
				<?php endforeach; ?>
			</div>
			<div class="pw-show-all">
				<a href="<?php echo $category['url']; ?>" class="btn btn-gray pw-show-all-btn"><?php echo Arr::get($cmslabel, 'show_all_advices', 'Prikaži sve savjete'); ?></a>
			</div>
		</div>
		<div class="m-pw-group-container">	
			<div class="pw-items fz0 owl-carousel">	
				<?php foreach ($items as $item): ?>
					<?php if($ii > 1): ?>
						<a href="<?php echo $item['url']; ?>" class="pw-item <?php echo $i; ?>">
							<figure class="pw-image lloader">
								<span><img <?php echo Thumb::generate($item['main_image'], array('width' => 650, 'height' => 400, 'crop' => true, 'default_image' => '/media/images/no-image-250.jpg', 'placeholder' => '/media/images/no-image-250.jpg')); ?> alt="" /></span>
							</figure>
							<div class="pw-category"><span><?php echo $item['category_title']; ?></span></div>
							<div class="pw-headline"><?php echo $item['headline']; ?></div>
							<div class="pw-title"><?php echo $item['title'] ?></div>
						</a>
					<?php endif; ?>
					<?php $ii++; ?>
				<?php endforeach; ?>
			</div>
			<div class="pw-show-all">
				<a href="<?php echo $category['url']; ?>" class="btn btn-gray pw-show-all-btn"><?php echo Arr::get($cmslabel, 'show_all_advices', 'Prikaži sve savjete'); ?></a>
			</div>
		</div>	
	</div>
<?php endif; ?>