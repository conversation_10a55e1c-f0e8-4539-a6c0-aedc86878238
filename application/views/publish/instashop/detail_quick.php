<?php $this->extend('default_quick'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('extrahead'); ?>
	<?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('page_class'); ?> instashop-detail<?php $this->endblock('page_class'); ?>

<?php $this->block('content_layout'); ?>
	<?php 
	$main_image = Utils::get_files('publish', $item['id'], 'image', $info['lang'], 0, 1); 
	$main_image = reset($main_image);

	$imagemaps = Arr::get($main_image, 'imagemaps');
	$related_items = [];

	if ($imagemaps) {
		$related_item_ids = array_map(function($el) {
			return (Arr::get($el, 'model') === 'catalogproduct') ? Arr::get($el, 'id') : 0;
		}, $imagemaps);
		sort($imagemaps);

		$related_items = ($related_item_ids) ? Widget_Catalog::products(array('lang' => $info['lang'], 'id' => $related_item_ids, 'limit' => 6)) : [];
		if ($related_items) {
			foreach ($related_items AS $related_item_id => $related_item_data) {
				foreach ($imagemaps AS $imagemap_i => $imagemap) {
					if (!empty($imagemap['model']) AND $imagemap['model'] AND !empty($imagemap['id']) AND $imagemap['id'] == $related_item_id) {
						$related_items[$related_item_id]['position'] = $imagemap_i + 1;
						$related_items[$related_item_id]['image_map'] = [
							'i' => $imagemap_i + 1,
							'relative_x' => $imagemap['posRelative']['x'],
							'relative_y' => $imagemap['posRelative']['y'],
						];
						unset($imagemaps[$imagemap_i]);
					}
				}
			}
			$related_items = Arr::orderby($related_items, 'position');
		}
	}
	?>
	
	<div class="pd-instashop-body">
		<div class="pd-instashop-col1">
			<?php $instashop_category = Widget_Publish::category($info['lang'], 'instashop'); ?>
			<?php $instashop_url = (!empty($_GET['ref']) AND $_GET['ref'] == 'home') ? Utils::homepage($info['lang'])."#instashop" : $instashop_category['url']; ?>
			<?php $ref = (!empty($_GET['ref']) AND $_GET['ref'] == 'home') ? '&ref=home': ''; ?>
			<a class="fancybox-item fancybox-close pd-instashop-close" target="_parent" onclick="parent.$.fancybox.close();" href="<?php echo $instashop_url; ?>"></a>

			<div class="instashop-nav">
				<!-- Previous post -->
				<?php $newer_items = Widget_Publish::publishes(array('lang' => $info['lang'], 'sort' => 'next', 'id_exclude' => $item['id'], 'datetime_published' => $item['datetime_published'], 'category_position' => $item['category_position_h'], 'limit' => 1)); ?>
				<?php if ($newer_items): ?>
					<?php foreach($newer_items as $archive_item): ?>
						<a class="instashop-nav-btn instashop-nav-prev" href="<?php echo $archive_item['url']; ?>?mode=quick<?php echo $ref; ?>"><span><?php echo $archive_item['title']; ?></span></a>
					<?php endforeach; ?>
				<?php endif; ?>

				<!-- Next post -->
				<?php $older_items = Widget_Publish::publishes(array('lang' => $info['lang'], 'sort' => 'prev', 'id_exclude' => $item['id'], 'datetime_published' => $item['datetime_published'], 'category_position' => $item['category_position_h'], 'limit' => 1)); ?>
				<?php if ($older_items): ?>
					<?php foreach($older_items as $archive_item): ?>
						<a class="instashop-nav-btn instashop-nav-next" href="<?php echo $archive_item['url']; ?>?mode=quick<?php echo $ref; ?>"><span><?php echo $archive_item['title']; ?></span></a>
					<?php endforeach; ?>
				<?php endif; ?>
			</div>

			<?php if (!empty($item['main_image'])): ?>
				<div class="pd-instashop-image">
					<div class="pd-instashop-points">
						<?php if ($related_items): ?>
							<?php foreach ($related_items AS $related_item_data): ?>
								<?php if (!empty($related_item_data['image_map'])): ?>
									<div class="pd-instashop-point" data-id="<?php echo $related_item_data['id']; ?>" style="top:<?php echo $related_item_data['image_map']['relative_y']?>%; left:<?php echo $related_item_data['image_map']['relative_x']?>%;">
										<span class="pd-instashop-point-num"><span><?php echo ($related_item_data['image_map']['i']); ?></span></span>
										<span class="pd-instashop-point-tip"><a href="<?php echo $related_item_data['url']; ?>" target="_parent"><?php echo Arr::get($cmslabel, 'instashop_detail', 'Prikaži u web trgovini'); ?></a></span>
									</div>
								<?php endif; ?>
							<?php endforeach ?>
						<?php endif; ?>
					</div>
					<img <?php echo Thumb::generate($item['main_image'], ['width' => 720, 'height' => 720, 'crop' => true, 'default_image' => '/media/images/no-image-500.jpg', 'html_tag' => true]); ?>  alt="<?php echo Text::meta($item['main_image_description']); ?>" />
				</div>
			<?php endif; ?>
		</div>

		<div class="pd-instashop-col2">
			<?php if ($related_items): ?>
				<div class="c-items<?php if($info['user_device'] != 'm'): ?> c-items3<?php endif; ?> pd-instashop-items">
					<?php echo View::factory('catalog/index_entry', ['items' => $related_items, 'mode' => 'instashop', 'class' => 'cp-instashop']); ?>
				</div>
			<?php endif; ?>
		</div>
	</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('extrabody'); ?>
<script>
	setTimeout(function() {
		if(parent.document.body.classList.contains('quick')) {
			document.querySelector('.pd-instashop-close').classList.add('active');
		}

		const close = parent.document.querySelector('.pd-instashop-close');
		if(close) close.classList.add('hide');
	}, 500);
</script>
<?php $this->endblock('extrabody'); ?>