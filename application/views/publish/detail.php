<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item, 'schema_org' => true]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-article-detail<?php $this->endblock('page_class'); ?>

<?php $this->block('after_header'); ?>
	<div class="pd-header">
		<div class="pd-wrapper">
			<div class="bc bc-pd">
				<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
				<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
			</div>
			<h1 class="pd-title"><?php echo $item['seo_h1'] ?></h1>
			<?php if(!empty($item['headline'])): ?>
				<div class="pd-headline"><?php echo $item['headline']; ?></div>
			<?php endif; ?>

			<?php if ($item['main_image']): ?>
				<div class="pd-hero-image">
					<!-- Previous post -->
					<?php $newer_items = Widget_Publish::publishes(array('lang' => $info['lang'], 'sort' => 'next', 'id_exclude' => $item['id'], 'datetime_published' => $item['datetime_published'], 'category_position' => $item['category_position_h'], 'limit' => 1)); ?>
					<?php if ($newer_items): ?>
						<?php foreach($newer_items as $archive_item): ?>
							<a class="pd-navigation pd-navigation-prev" href="<?php echo $archive_item['url']; ?>">
								<span><span><?php echo $archive_item['title']; ?></span></span>
							</a>
						<?php endforeach; ?>
					<?php endif; ?>

					<!-- Next post -->
					<?php $older_items = Widget_Publish::publishes(array('lang' => $info['lang'], 'sort' => 'prev', 'id_exclude' => $item['id'], 'datetime_published' => $item['datetime_published'], 'category_position' => $item['category_position_h'], 'limit' => 1)); ?>
					<?php if ($older_items): ?>
						<?php foreach($older_items as $archive_item): ?>
							<a class="pd-navigation pd-navigation-next" href="<?php echo $archive_item['url']; ?>">
								<span><span><?php echo $archive_item['title']; ?></span></span>
							</a>
						<?php endforeach; ?>
					<?php endif; ?>

					<img <?php echo Thumb::generate($item['main_image'], ['width' => 1230, 'height' => 630, 'crop' => true, 'default_image' => '/media/images/no-image-1230.jpg', 'html_tag' => true]); ?>  alt="<?php echo Text::meta($item['main_image_description']); ?>" />
				</div>
			<?php endif; ?>
		</div>
	</div>		
<?php $this->endblock('after_header'); ?>

<?php $this->block('main'); ?>
	<?php $comment_status = (!empty($item['feedback_comment_widget'])) ? $item['feedback_comment_widget']['comments_status'] : 0; ?>
	<div class="pd-body">
		<div class="pd-content-wrapper">		

			<?php if(!empty($item['short_description'])): ?>
				<div class="pd-short-description">
					<?php echo $item['short_description']; ?>
				</div>
			<?php endif; ?>

			<div class="pd-info">
				<a class="pd-all-articles" href="<?php echo $item['category_url']; ?>"><?php echo Arr::get($cmslabel, 'all_articles'); ?></a>
				<div class="pd-date">
					<?php echo Date::humanize($item['datetime_published'], 'custom', 'd. F Y.', '', $info['lang']); ?>
				</div>
				<?php if($comment_status > 1): ?>
					<div class="pd-info-link pd-info-link-comments"><a href="#comments"><?php echo Arr::get($cmslabel, 'view_comments'); ?></a> <span class="green counter">(<?php echo $item['feedback_comment_widget']['comments_by_lang']; ?>)</span></div>
				<?php endif; ?>
				
				<?php $related_products = (Kohana::config('app.catalog.use_productrelatedpublishes')) ? Widget_Catalog::products(['lang' => $info['lang'], 'related_publish_id' => $item['id'], 'limit' => 0]) : []; ?>
				<?php if(!empty($related_products)): ?>
					<div class="pd-info-link pd-info-link-products"><a href="#related"><?php echo Arr::get($cmslabel, 'related_products'); ?></a> <span class="green counter">(<?php echo count($related_products); ?>)</span></div>
				<?php endif; ?>
			</div>
				
			<!-- Post content and related documents -->
			<div class="lists pd-desc">
				<?php echo $item['content']; ?>

				<?php $images = Utils::get_files('publish', $item['id'], 'image', $info['lang'], 1); ?>
				<?php if ($images): ?>
					<div class="pd-thumbs">
						<?php foreach ($images as $file): ?>
							<a href="<?php echo $file['url']; ?>" class="fancybox" rel="gallery" title="<?php echo Text::meta($file['title']); ?><?php if($file['description']): ?> - <?php endif; ?><?php echo Text::meta($file['description']); ?>">
								<img <?php echo Thumb::generate($file['file'], ['width' => 740, 'default_image' => '/media/images/no-image-500.jpg', 'html_tag' => true]); ?> alt="<?php echo Text::meta($file['description']); ?>" />
							</a>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>

				<?php $documents = Utils::get_files('publish', $item['id'], '-image', $info['lang']); ?>
				<?php if ($documents): ?>
					<ul class="pd-documents">
						<?php foreach ($documents as $file): ?>
							<li><a href="<?php echo $file['url']; ?>" title="<?php echo Text::meta($file['description']); ?>"><?php echo Text::meta($file['title']); ?></a></li>
						<?php endforeach; ?>
					</ul>
				<?php endif; ?>

				<?php if(!empty($item['seo_keywords_tags'])): ?>
					<div class="tags">
						<?php foreach ($item['seo_keywords_tags'] as $tag): ?>
							<a href="<?php echo $tag['url'] ?>"><?php echo $tag['title'] ?></a><span class="comma">, </span>
						<?php endforeach ?>
					</div>
				<?php endif; ?>	
		
				<div class="pd-footer">
					<?php echo View::factory('cms/widget/share', ['item' => isset($item) ? $item : [], 'class' => 'pd-share']); ?>
					<?php if(!empty($item['author'])): ?>
						<div class="pd-author">
							<div class="pd-author-name" itemprop="name"><strong><?php echo Arr::get($cmslabel, 'author'); ?>:</strong> <?php echo $item['author']; ?></div>
							<a href="<?php echo $item['author_url']; ?>" class="pd-all-author-posts"><?php echo Arr::get($cmslabel, 'all_author_posts'); ?></a>
						</div>
					<?php endif; ?>
				</div>
			</div>

			<?php if($comment_status > 1): ?>
				<div class="comments pd-comments" id="comments">
					<?php if($comment_status > 2): ?>
						<div class="comments-title"><?php echo Arr::get($cmslabel, 'add_article_comment'); ?></div>
					<?php endif; ?>
					<?php echo View::factory('feedback/comments', ['item' => $item['feedback_comment_widget']]); ?>
				</div>
			<?php endif; ?>		
		
			<?php if (!empty($related_products)): ?>
                <?php $ga4_related_products = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $related_products, 'item_list_name' => Arr::get($cmslabel, 'related_products', 'Povezani proizvodi'), 'item_list_id' => 'related_products']) ?>
				<!-- Related products -->
				<div class="pd-related-products" id="related" data-ga4_events_info='<?php echo $ga4_related_products;?>'>
					<div class="subtitle related-title"><?php echo Arr::get($cmslabel, 'related_products', 'Povezani proizvodi'); ?></div>
					<div class="related-items">
						<?php echo View::factory('catalog/index_entry', ['items' => $related_products, 'mode' => 'list', 'list' => 'Blog', 'item_list_name' => Arr::get($cmslabel, 'related_products', 'Povezani proizvodi'), 'item_list_id' => 'related_products']); ?>
					</div>
					<div class="pd-related-btns">
						<div class="related-products-add-message product_message_list"></div>
						<a class="btn btn-orange btn-add-all" href="javascript:cmswebshop.shopping_cart.add_special_list('related', 'all', 1, 'simple', 'simple_loader');"><span><?php echo Arr::get($cmslabel, 'all_to_cart'); ?></span></a>
					</div>
				</div>
			<?php endif; ?>		
		</div>
	</div>
	
	<?php $related_items = Widget_Publish::publishes(['lang' => $info['lang'], 'related_code' => 'related', 'related_item_id' => $item['id'], 'category_code' => 'blog', 'extra_fields' => ['short_description'], 'limit' => 5]); ?>
	<?php $related_recipe_items = Widget_Publish::publishes(['lang' => $info['lang'], 'related_code' => 'related', 'related_item_id' => $item['id'], 'category_code' => 'recipe', 'extra_fields' => ['short_description'], 'limit' => 5]); ?>
	<?php if (!empty($related_items) OR !empty($related_recipe_items)): ?>
		<div class="pd-related">
			<div class="pd-content-wrapper">
				<?php if (!empty($related_items)): ?>
					<!-- Related posts -->		
					<div class="pd-related-posts">
						<div class="subtitle related-title"><?php echo Arr::get($cmslabel, 'publish_related'); ?></div>
						<?php echo View::factory('publish/index_entry', ['items' => $related_items, 'mode' => 'small', 'class' => 'pp-related']); ?>
					</div>
				<?php endif; ?>

				<!-- Related recipes -->
				<?php if (!empty($related_recipe_items)): ?>	
					<div class="pd-related-recipes">
						<div class="subtitle related-title"><?php echo Arr::get($cmslabel, 'publish_related_recipes'); ?></div>
						<?php echo View::factory('publish/recipes/index_entry', ['items' => $related_recipe_items, 'mode' => 'list']); ?>
					</div>
				<?php endif; ?>
			</div>
		</div>
	<?php endif; ?>
<?php $this->endblock('main'); ?>

<?php $this->block('page_class'); ?> page-publish-detail<?php if (!$item['main_image']): ?> no-image<?php endif; ?><?php if(empty($related_items) AND empty($related_recipe_items)): ?> white-bg<?php endif; ?><?php $this->endblock('page_class'); ?>