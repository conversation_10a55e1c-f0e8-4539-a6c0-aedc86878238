<?php if(!empty($item)): ?>
	<?php $mode = (isset($mode)) ? $mode : ''; ?>
	<?php $class = (isset($class)) ? ' '.$class : ''; ?>
	<?php $cls = ''; ?>
	<?php $img_w = 470; ?>
	<?php $img_h = 240; ?>
	<?php 
		if($mode == 'small') {
			$cls = ' pp-small';
			$img_w = 260;
			$img_h = 135;
		}
		if($mode == 'big') {
			$cls = ' pp-big';
			$img_w = 800;
			$img_h = 410;
		}
		if($mode == 'big2') {
			$cls = ' pp-big pp-big-horizontal';
			$img_w = 715;
			$img_h = 370;
		}
	?>
	<a class="pp<?php echo $cls; ?><?php echo $class; ?>" href="<?php echo $item['url']; ?>">
		<figure class="pp-image">
			<span><img<?php if($mode != 'big'): ?> loading="lazy"<?php endif; ?> <?php echo Thumb::generate($item['main_image'], array('width' => $img_w, 'height' => $img_h, 'crop' => true, 'default_image' => '/media/images/no-image-'.$img_w.'.jpg', 'html_tag' => true)); ?><?php if(!empty($item['main_image_title'])): ?> title="<?php echo Text::meta($item['main_image_title']); ?>"<?php endif; ?> alt="<?php echo ($item['main_image_description']) ? Text::meta($item['main_image_description']) : $item['title']; ?>" /></span>
		</figure>
		<span class="pp-cnt">
			<span class="pp-category"><?php echo $item['category_title']; ?></span>
			<span class="pp-title"><?php echo $item['title']; ?></span>
			<?php if(!empty($item['short_description'])): ?>
				<span class="pp-short-desc"><?php echo strip_tags($item['short_description']); ?></span>
			<?php endif; ?>
		</span>
	</a>
<?php endif; ?>