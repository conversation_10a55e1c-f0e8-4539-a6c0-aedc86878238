<?php if (sizeof($items)): ?>
	<?php if(empty($q)): ?>
		<?php $latest_blog_post = array_shift($items); ?>
		<?php if($kind['level'] == 1): ?>
			<div class="pos-r p-intro">
				<div class="pw-title"><span><?php echo Arr::get($cmslabel, 'homepage_health'); ?></span></div>
				<?php $publish_categories = Widget_Publish::categories(['lang' => $info['lang'], 'start_position' => $kind['position_h']]); ?>
				<?php if(!empty($publish_categories)): ?>
					<ul class="p-nav">
						<?php foreach($publish_categories as $publish_category_item): ?>
							<li><a href="<?php echo $publish_category_item['url']; ?>"><?php echo $publish_category_item['title']; ?></a></li>	
						<?php endforeach; ?>
					</ul>
				<?php endif; ?>
				<div class="p-intro-items">
					<div class="p-intro-col p-intro-col1">
						<?php echo View::factory('publish/index_entry_single', ['item' => $latest_blog_post, 'mode' => 'big', 'class' => 'pp-hover2']); ?>
					</div>
					<div class="p-intro-col p-intro-col2">
						<div class="p-intro-title"><?php echo Arr::get($cmslabel, 'special_articles_title'); ?></div>
						<?php $most_read_items = Widget_Publish::publishes(['lang' => $info['lang'], 'category_code' => $kind['code'], 'sort' => 'most_view', 'limit' => 4, 'extra_fields' => ['short_description']]); ?>
						<?php if(!empty($most_read_items)): ?>
							<?php foreach($most_read_items as $most_read_item): ?>
								<?php echo View::factory('publish/index_entry_single', ['item' => $most_read_item, 'mode' => 'small', 'class' => 'pp-hover2']); ?>
							<?php endforeach; ?>
						<?php endif; ?>
					</div>
				</div>
			</div>
		<?php else: ?>
			<div class="pos-r p-intro">
				<div class="pw-title p-title"><span><?php echo $kind['title']; ?></span></div>
				<?php $parent_category = (!empty($kind['parents'])) ? reset($kind['parents']) : ''; ?>
				<?php $publish_categories = Widget_Publish::categories(['lang' => $info['lang'], 'start_position' => (!empty($parent_category['position_h'])) ? $parent_category['position_h'] : '']); ?>
				<?php if(!empty($publish_categories)): ?>
					<ul class="p-nav">
						<?php foreach($publish_categories as $publish_category_item): ?>
							<?php if($publish_category_item['url'] != $info['url']): ?>
								<li><a href="<?php echo $publish_category_item['url']; ?>"><?php echo $publish_category_item['title']; ?></a></li>	
							<?php endif; ?>
						<?php endforeach; ?>
						<li><a href="<?php if (!empty($parent_category['url'])): echo $parent_category['url']; endif; ?>"><?php echo Arr::get($cmslabel, 'show_all_posts'); ?></a></li>
					</ul>
				<?php endif; ?>

				<div class="p-intro-items">
					<?php echo View::factory('publish/index_entry_single', ['item' => $latest_blog_post, 'mode' => 'big2', 'class' => 'pp-hover2']); ?>
				</div>
			</div>
		<?php endif; ?>
	<?php endif; ?>

	<div class="p-index-items">
		<div class="p-items" id="items_<?php echo $kind['code']; ?>" data-infinitescroll="items_<?php echo $kind['code']; ?>" data-infinitescroll_next_page="<?php echo $pagination->next_page; ?>" data-infinitescroll_total_pages="<?php echo $pagination->total_pages; ?>" data-infinitescroll_auto_trigger="1">
			<?php $class = (!empty($q)) ? 'gray-shadow' : ''; ?>
			<?php echo View::factory('publish/index_entry', ['items' => $items, 'pagination' => $pagination, 'class' => $class]); ?>
		</div>

		<?php echo $pagination; ?>
		<?php if ($pagination): ?>
			<div class="load-more-container">
				<a href="javascript:void(0);" class="btn load-more btn-load-more btn-load-more-publish" style="display: none;"><span><?php echo str_replace(array('%PER_PAGE%', '%TOTAL%', '%NEXT_PAGE_FIRST_ITEM%', '%NEXT_PAGE_LAST_ITEM%'), array($pagination->items_per_page, $pagination->total_items, $pagination->next_page_first_item, $pagination->next_page_last_item), Arr::get($cmslabel, 'load_more_publish')); ?></span></a>
			</div>
		<?php endif; ?>
	</div>
<?php else: ?>
	<div class="no-publish">
		<?php echo Arr::get($cmslabel, 'no_publish'); ?>
	</div>
<?php endif; ?>