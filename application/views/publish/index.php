<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title_full'])) ? $kind['seo_title_full'] : Arr::get($cms_page, 'seo_title')).((!empty($pagination->current_page) AND $pagination->current_page > 1) ? sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages) : '')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['kind' => $kind, 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-publish<?php if(!empty($kind['level'])): ?> page-publish-level<?php echo $kind['level']; ?><?php endif; ?><?php if(!empty($kind_content) AND $kind_content == 'tag'): ?> page-publish-tags<?php endif; ?><?php if($q): ?> page-search white-bg<?php endif; ?><?php $this->endblock('page_class'); ?>

<?php if(!empty($q)): ?>
	<?php $this->block('after_header'); ?>
		<div class="s-header-wrapper">
			<h1 class="s-h1"><span class="s-headline"><?php echo Arr::get($cmslabel, 'search_headline'); ?></span><span class="s-keyword"><?php echo $q; ?></span></h1>
			
			<?php 
				$catalog_search_url = Utils::app_absolute_url($info['lang'], 'catalog');
				$search_url = Utils::app_absolute_url($info['lang'], 'search');
				$search_content = Arr::get($_GET, 'search_content');
				$search_totals = Widget_Search::totals($info['lang'], $q, $search_content);
				$other_results = array_sum($search_totals); 
				$recipes_category = Widget_Publish::category($info['lang'], 'recipe');
				$recipes_url = (!empty($recipes_category['url'])) ? $recipes_category['url'] : '';
			?>
			<ul class="s-nav">
				<li><a href="<?php echo $catalog_search_url; ?>?search_q=<?php echo $q; ?>"><?php echo Arr::get($cmslabel, "search_catalog"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'catalog', 0); ?>)</span></a></li>
				<li class="selected"><a href="?search_q=<?php echo $q; ?>"><?php echo Arr::get($cmslabel, "search_publish"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish.01', (($items AND $search_content == 'publish.01') ? count($items['publish.01']) : 0)); ?>)</span></a></li>
				<li><a href="<?php echo $recipes_url; ?>?search_q=<?php echo $q; ?>"><?php echo Arr::get($cmslabel, "search_recipes"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish.02', (($items AND $search_content == 'publish.02') ? count($items['publish.02']) : 0)); ?>)</span></a></li>
				<li<?php if($search_content == 'cms'): ?> class="selected"<?php endif; ?>><a href="<?php echo $search_url; ?>?search_q=<?php echo $q; ?>&search_content=cms"><?php echo Arr::get($cmslabel, "search_cms"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'cms', (($items AND $search_content == 'cms') ? count($items['cms']) : 0)); ?>)</span></a></li>
			</ul>
		</div>
	<?php $this->endblock('after_header'); ?>
<?php endif; ?>

<?php $this->block('main'); ?>
	<div class="wrapper wrapper-publish">
		<?php if(!$q): ?>
			<div class="bc">
				<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $kind['breadcrumbs']); ?>
				<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
			</div>
		<?php endif; ?>

		<div id="items_<?php echo $kind['code']; ?>_layout">
			<?php echo View::factory('publish/index_layout', [
				'kind' => $kind,
				'q' => (!empty($q)) ? $q : '',
				'items' => $items,
				'items_per_page' => $items_per_page,
				'items_all' => $items_all,
				'items_total' => $items_total,
				'child_categories' => $child_categories,
				'child_categories_published' => $child_categories_published,
				'pagination' => $pagination,
			]); ?>
		</div>
	</div>
<?php $this->endblock('main'); ?>