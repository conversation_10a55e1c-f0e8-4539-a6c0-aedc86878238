<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-publish page-author<?php $this->endblock('page_class'); ?>

<?php $this->block('after_header'); ?>
	<div class="wrapper wrapper-author-intro">
		<h1 class="p-author-title"><?php echo $item['seo_h1'] ?></h1>
		<?php if(!empty($item['short_description']) OR !empty($item['content'])): ?>
			<div class="p-author-desc">
				<?php echo $item['short_description']; ?>
				<?php echo $item['content']; ?>
			</div>
		<?php endif; ?>
	</div>
<?php $this->endblock('after_header'); ?>

<?php $this->block('main'); ?>
	<div class="wrapper wrapper-publish-author">
		<?php $latest_publishes = Widget_Publish::publishes(array('lang' => $info['lang'], 'author_id' => $item['id'], 'limit' => 0)); ?>
		<?php if(!empty($latest_publishes)): ?>
			<div class="p-items">
				<?php echo View::factory('publish/index_entry', ['items' => $latest_publishes]); ?>
			</div>
		<?php endif; ?>	
	</div>
<?php $this->endblock('main'); ?>