<?php $this->extend('cms/default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-faq nw-main<?php $this->endblock('page_class'); ?>

<?php $this->block('h1'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
<?php $this->endblock('h1'); ?>

<?php $this->block('content'); ?>
	<h1 class="f-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	<?php echo Arr::get($cms_page, 'content'); ?>

	<?php $faq_categories = Widget_Faq::questions(['lang' => $info['lang']]); ?>
	<?php if(!empty($faq_categories)): ?>
		<?php foreach ($faq_categories AS $faq_category): ?>
			<?php if(!empty($faq_category)): ?>
				<div class="faq-cat">
					<h2 class="faq-cat-title"><?php echo $faq_category['title'] ?></h2>
					<div class="faq-items">
						<div class="faq-items">
							<?php foreach ($faq_category AS $faq_question): ?>
								<?php if(!empty($faq_question['title'])): ?>
									<div class="fp">
										<div class="fp-title">
											<span class="toggle-icon"></span><?php echo $faq_question['title']; ?>
										</div>
										<div class="fp-content">
											<?php echo $faq_question['content']; ?>
										</div>
									</div>
								<?php endif; ?>
							<?php endforeach; ?>
						</div>
					</div>
				</div>
			<?php endif; ?>
		<?php endforeach; ?>
	<?php endif; ?>

	<?php echo View::factory('cms/widget/share', ['item' => isset($cms_page) ? $cms_page : []]); ?>
<?php $this->endblock('content'); ?>

<?php $this->block('after_main_wrapper'); ?>
	<?php echo View::factory('newsletter/widget/manage', ['mode' => 'main']); ?>
<?php $this->endblock('after_main_wrapper'); ?>

<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>

<?php $this->block('extrabody'); ?>
	<script type="module" src="/media/components/stencil.esm.js"></script>
	<script nomodule src="/media/components/stencil.js"></script>
<?php $this->endblock('extrabody'); ?>