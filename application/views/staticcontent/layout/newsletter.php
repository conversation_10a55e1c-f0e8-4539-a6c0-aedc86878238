<?php $product_priorities = (isset($product_priorities)) ? $product_priorities : Kohana::config('app.catalog.product_priorities'); ?>
<?php $i = 1; ?>
<?php foreach ($items AS $item): ?>
	<?php if (isset($product_priorities)): ?>
		<?php $priority = Arr::get($product_priorities, (isset($item['priority_2']) ? $item['priority_2'] : '')); ?>
	<?php endif; ?>
	<?php if($i == 1): ?>
		<table cellpadding="0" cellspacing="0" width="100%" valign="top">
			<tr valign="top">
	<?php endif; ?>
	
	<td width="33%" valign="top" align="center" style="font:12px/15px Arial, sans-serif;border:1px solid #E0E2DB;border-bottom:0;border-right: 0;<?php if($i % 3 == 0): ?>border-right: 1px solid #E0E2DB;<?php endif; ?>color:#304823;">
		<table cellpadding="0" cellspacing="0" width="180" height="40">
			<tr>
				<td align="left" width="40" height="40">
					<?php if ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']): ?>
						<div mc:hideable>
							<img src="<?php echo Utils::homepage($info['lang']); ?>media/images/space.gif" style="display: block;" width="10" height="10" alt="">
							<table cellpadding="0" cellspacing="0" style="width:40px;height:30px;">
								<tr>
									<td style="border-radius:2px;color:#ffffff;background:#BD444D;text-align:center;font:11px/12px Arial, sans-serif;height:30px;text-transform:uppercase;font-weight:bold;" mc:edit="product<?php echo $i; ?>_discount_percent">-<?php echo $item['discount_percent']; ?>%</td>
								</tr>
							</table>
						</div>
					<?php endif; ?>
				</td>
				<td align="center" height="40">
					<div mc:hideable>
						<img src="<?php echo Utils::homepage($info['lang']); ?>media/images/space.gif" style="display: block;" width="10" height="10" alt="">
						<table cellpadding="0" cellspacing="0">
							<tr>
								<td mc:edit="product<?php echo $i; ?>_brand">
									<?php if(!empty($item['manufacturer_main_image'])): ?>	
										<img style="display: block; vertical-align: top; width: auto; height: auto; max-height: 20px;" border="0" <?php echo Thumb::generate($item['manufacturer_main_image'], array('width' => 55, 'height' => 20, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true, 'ignore_webp' => true)); ?> alt="<?php echo $item['manufacturer_title']; ?>" />
									<?php else: ?>
										<?php if (!empty($item['manufacturer_title'])): ?><?php echo $item['manufacturer_title']; ?><?php endif; ?>
									<?php endif; ?>
								</td>
							</tr>
						</table>
					</div>
				</td>
				<td align="right" width="40" height="40">
					<img src="<?php echo Utils::homepage($info['lang']); ?>media/images/space.gif" style="display: block;" width="10" height="10" alt="">
					<?php if(!empty($product_priorities)): ?>
						<?php $priority = Arr::get($product_priorities, $item['priority_2']); ?>
						<?php if(!empty($priority)): ?>
							<div mc:hideable>
								<table cellpadding="0" cellspacing="0" style="width:40px;height:30px;">
									<tr>
										<td style="border-radius:2px;color:#ffffff;background:#809941;text-align:center;font:10px/11px Arial, sans-serif;height:30px;text-transform:uppercase;font-weight:bold;" mc:edit="product<?php echo $i; ?>_new"><?php echo Arr::get($priority, 'title'); ?></td>
									</tr>
								</table>
							</div>
						<?php endif; ?>
					<?php endif; ?>
				</td>
			</tr>
		</table>
		<table cellpadding="0" cellspacing="0" width="100%" height="180">
			<tr>
				<td mc:edit="product<?php echo $i; ?>_image" align="center" valign="middle">
					<a href="<?php echo $item['url']; ?>">
						<img style="max-width: 100%; max-height: 180px; width: auto; height: auto;" border="0" <?php echo Thumb::generate($item['main_image'], array('width' => 180, 'height' => 180, 'default_image' => '/media/images/no-image-300.jpg', 'html_tag' => true, 'ignore_webp' => true)); ?><?php if(!empty($item['main_image_title'])): ?> title="<?php echo Text::meta($item['main_image_title']); ?>"<?php endif; ?> alt="<?php echo ($item['main_image_description']) ? Text::meta($item['main_image_description']) : $item['title']; ?>" />
					</a>
				</td>
			</tr>
		</table>
		<div style="font:11px/12px Arial,sans-serif;margin: 5px 20px 4px;color:#809941;font-weight:bold; text-transform: uppercase;text-align: left;" mc:edit="product<?php echo $i; ?>_category"><?php echo $item['category_title']; ?></div>
		<div style="font:12px/15px Arial, sans-serif;margin:5px 20px 10px;color:#304823;text-align: left;min-height: 45px;" mc:edit="product<?php echo $i; ?>_title">
			<a style="color:#304823;text-decoration:none;display:block;" href="<?php echo $item['url']; ?>"><?php echo $item['title']; ?></a>
		</div>
		<table cellpadding="0" cellspacing="0" width="160">
			<tr>
				<?php if ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price']): ?>
					<td align="left" valign="top" style="padding-top:10px;">
						<span style="font:11px/12px Arial, sans-serif;margin:0;padding:0;color:#375126;text-decoration:line-through;" mc:edit="product<?php echo $i; ?>_discount">
							<?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($item['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</span>
						<img src="<?php echo Utils::homepage($info['lang']); ?>media/images/space.gif" width="5" height="5" alt="">
						<span style="font:15px/17px Arial, sans-serif;color:#BD444D;font-weight:bold;" mc:edit="product<?php echo $i; ?>_price">
							<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</span>
					</td>
				<?php else: ?>
					<td align="left" valign="top" style="padding-top:10px;">
						<span style="font:15px/17px Arial, sans-serif;color:#304823;font-weight:bold;" mc:edit="product<?php echo $i; ?>_price">
							<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</span>
					</td>
				<?php endif; ?>
			</tr>
		</table>
		<img src="<?php echo Utils::homepage($info['lang']); ?>media/images/space.gif" width="15" height="15" alt="">
	</td>

	<?php if($i % 3 == 0 OR $i == count($items)): ?>
		</tr>
	</table>
	<?php endif; ?>
	<?php if($i % 3 == 0 AND $i != count($items)): ?>
		<table cellpadding="0" cellspacing="0" width="100%" valign="top">
			<tr>
	<?php endif; ?>
	<?php $i++; ?>
<?php endforeach; ?>