<?php if (!empty($cmsmenu)): ?>
	<nav class="ln-nav-container ln-nav-container2<?php if (!empty($item['large_space'])): ?> spacing<?php endif ?>">
		<div class="wrapper ln-nav-wrapper">
			<a class="btn-ln-toggle-nav" href="javascript:toggleBox('.ln-nav-container2');"><?php echo Arr::get($cmslabel, 'menu'); ?><span class="toggle-icon"></span></a>
			<ul class="ln-nav ln-nav2">
				<?php foreach ($cmsmenu AS $menu_id => $menu_title): ?>
					<li><a href="#<?php echo $menu_id; ?>"><?php echo $menu_title; ?></a></li>
				<?php endforeach; ?>
			</ul>
		</div>
	</nav>
<?php endif; ?>