<?php $mode = (isset($mode)) ? $mode : ''; ?>

<?php if(!empty($item)): ?>
	<?php $items = $item['items']; ?>
	<?php $content = $item['content']; ?>
	<?php $can_comments = $item['can_comments']; ?>
	<?php $comments_by_lang = $item['comments_by_lang']; ?>
<?php endif; ?>

<?php if (!empty($can_comments)): ?>
	<div id="comment-0">
		<div id="comment_form" class="comment-form-container">
			<?php $form = Widget_Feedback::comment_form($_POST, $info); ?>
			<?php if ($form['request_login'] AND ! $user): ?>
				<?php echo Arr::get($cmslabel, 'comment_request_login'); ?>
			<?php else: ?>
				<form class="clear comment-form form-inline form-label" action="#comment_form" method="post" name="comment_form" id="comment_add_form_<?php echo $content; ?>">
					<input type="hidden" name="id" value="<?php echo $content; ?>" />
					<input type="hidden" name="comment_id" value="" />
					<input type="hidden" name="parent_id" value="0" />
					<input type="hidden" name="lang" value="<?php echo $info['lang']; ?>" />
					<?php foreach ($form['customer_fields'] as $field): ?>
						<?php if ($field[0] == ':'): ?>
							
						<?php else: ?>
							<p class="field comment-field comment-field-<?php echo $field; ?>">
								<?php $label = (Arr::get($cmslabel, 'form_comments_'.$field)) ? Arr::get($cmslabel, 'form_comments_'.$field) : Arr::get($cmslabel, $field); ?>
								<?php echo $form['item']->input($field, 'form'); ?>
								<label for="field-<?php echo $field; ?>"><?php echo $label; ?></label>
								<span id="field-error-<?php echo $field; ?>" class="error" style="display: none"></span>
							</p>
							<?php if($field == 'email'): ?>
								<p class="comment-field comment-field-rate">
									<label><?php echo (Arr::get($cmslabel, 'your_rate_'.$mode)) ? Arr::get($cmslabel, 'your_rate_'.$mode) : Arr::get($cmslabel, 'your_rate'); ?></label>
									<span class="comment-field-stars">
										<?php for ($rate = 1; $rate <= 5; $rate++): ?>
											<span class="comment-rate-item comment-rate-<?php echo $rate; ?> add_comment_rate" data-comment_rate="<?php echo $rate; ?>">
												<input type="radio" id="comment-rate<?php echo $rate; ?>" name="rate" value="<?php echo $rate ?>">
												<label for="comment-rate<?php echo $rate; ?>"><?php echo $rate ?></label>
											</span>
										<?php endfor; ?>
									</span>
								</p>
							<?php endif; ?>
						<?php endif; ?>
					<?php endforeach; ?>
					<div class="buttons comment-buttons">
						<button class="btn btn-green btn-send-comment" type="submit"><?php echo Arr::get($cmslabel, 'send', 'Pošalji komentar'); ?></button>
						<div class="comment-note"><?php echo Arr::get($cmslabel, 'comment_note'); ?></div>

						<a id="cancel-edit" class="btn-link" href="javascript:cmsfeedback.replaycomment(0);" style="display: none"><?php echo Arr::get($cmslabel, 'comment_edit_cancel', 'Odustani'); ?></a>
						<a id="cancel-replay-to" class="btn-comment-cancel" href="javascript:cmsfeedback.replaycomment(0);cancelReplayForm();" style="display: none"><?php echo Arr::get($cmslabel, 'comment_replay_cancel', 'Zatvori'); ?></a>
					</div>
					<div class="comment-form-note"><?php echo Arr::get($cmslabel, 'comment_form_note'); ?></div>
				</form>
			<?php endif; ?>
			<div class="comment_success comment-success" data-feedback_ignore_scroll="1" style="display: none">
				<div class="comments-success-cnt"><?php echo Arr::get($cmslabel, 'comment_success'); ?></div>
				<a class="btn" id="comment_add_new" href="javascript:cmsfeedback.replaycomment(0);"><span><?php echo Arr::get($cmslabel, 'comment_add_new'); ?></span></a>
			</div>
		</div>
	</div>
<?php endif; ?>

<div id="comment-list" class="comments-list">
	<?php if (!empty($items)): ?>
		<div class="comments-title comments-list-title"><?php echo Arr::get($cmslabel, 'comments'); ?> <span class="comments-counter">(<?php echo $comments_by_lang; ?>)</span></div>

		<div data-comments_container="1" class="comment-items">
			<?php foreach ($items as $item): ?>
				<?php echo View::factory('feedback/comment_entry', ['item' => $item]); ?>
			<?php endforeach; ?>
		</div>
		<input type="hidden" name="comments-list-lastcheck" id="lastcheck" value="<?php echo time(); ?>" />
		<input type="hidden" name="comments-list-page" id="comments-list-page" value="1" />
        <div class="comments">
            <?php if ($comments_by_lang > Kohana::config('app.feedback.comments.per_page')) : ?>
            <a data-load_more="1" data-limit="<?php echo Kohana::config('app.feedback.comments.per_page');?>" data-offset="<?php echo Kohana::config('app.feedback.comments.per_page');?>" data-id="<?php echo Arr::get($item, 'object_id', 0); ?>"
               class="btn btn-load-more-comments"
               href="javascript:void(0);"><?php echo Arr::get($cmslabel, 'load_more'); ?></a>
            <?php endif; ?>
        </div>

	<?php else: ?>
		<div class="no-comments"><?php echo Arr::get($cmslabel, 'no_comments'); ?></div>
	<?php endif; ?>	
</div>