<article class="clear comment<?php if ($item['manager']): ?> comment-manager<?php endif; ?><?php if ($item['parent_id']): ?> comment-child<?php endif; ?>" id="comment-<?php echo $item['id']; ?>">
	<div class="comment-col comment-content">
    	<div class="comment-header">
			<?php if (!empty($item['rate'])): ?>
				<span class="comment-rate">
					<?php for ($rate = 1; $rate <=5; $rate++): ?>
						<span class="icon-star<?php if ($rate <= $item['rate']): ?> icon-star-active<?php endif ?>"></span>		
					<?php endfor; ?>
				</span>
			<?php endif; ?>
    		<span class="comment-username"><strong><?php echo $item['display_name']; ?></strong></span>, 
    		<span class="comment-date"><?php echo Date::humanize($item['datetime_created'], 'custom', 'd. F Y.','', $info['lang']); ?></span>
    	</div>
		<div class="comment-message" id="comment-message-<?php echo $item['id']; ?>"><?php echo $item['message']; ?></div>
	</div>
</article>
<span id="comment_delete_success_<?php echo $item['id']; ?>" style="display: none"><?php echo Arr::get($cmslabel, 'comment_delete_success'); ?></span>
<div class="clear"></div>