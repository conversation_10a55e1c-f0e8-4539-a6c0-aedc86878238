<div class="cd-unavailable">
	<h2><?php echo Arr::get($cmslabel, 'not_available', 'Oba<PERSON><PERSON><PERSON>i me kada postane dostupno'); ?></h2>
	<div class="cdu-subtitle"><?php echo Arr::get($cmslabel, 'not_available_subtitle'); ?></div>
	<div class="cd-notify-form-container">
		<div id="notifyme-<?php echo $form_content; ?>">
			<form action="#notifyme_form" method="post" name="notifyme_form" class="cd-notify-form" id="notifyme_add_form_<?php echo $form_content; ?>">
				<div class="cd-notify-form-fields">
					<input type="hidden" name="id" value="<?php echo $form_content; ?>" />
					<input type="text" name="email" placeholder="<?php echo Arr::get($cmslabel, 'enter_email'); ?>" <?php if ($info['user_id'] AND $info['user_email']): ?>value="<?php echo $info['user_email']; ?>"<?php endif; ?>>
					<button type="submit" class="btn btn-orange btn-notify"><span><?php echo Arr::get($cmslabel, 'notifyme'); ?></span></button>
				</div>
				<span id="field-error-email" class="field_error error" style="display: none"><?php echo Arr::get($cmslabel, 'error_email', 'error_email'); ?></span>
			</form>
			<div class="notifyme_success message" style="display: none">
				<?php echo Arr::get($cmslabel, 'notifyme_catalog_ty'); ?>
			</div>
		</div>
	</div>
</div>