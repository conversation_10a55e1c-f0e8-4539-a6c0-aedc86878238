<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-contact nw-main<?php $this->endblock('page_class'); ?>

<?php $this->block('content'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	<div class="df contact-row">
		<div class="contact-col contact-col1">
			<?php echo Arr::get($cms_page, 'content'); ?>
		</div>
		<div class="contact-col contact-col2">
			<div class="support support-contact">
				<p class="support-title support-title-contact"><?php echo Arr::get($cmslabel, 'footer_user_support'); ?></p>
				<?php echo Arr::get($cmslabel, 'support'); ?>
			</div>
			<div class="social social-contact">
				<?php echo Arr::get($cmslabel, 'contact_social'); ?>			
			</div>
			<div class="contact-links contact-links-job">
				<?php echo Arr::get($cmslabel, 'job_applications'); ?>
			</div>
			<div class="contact-links contact-links-wholesale">
				<?php echo Arr::get($cmslabel, 'wholesale'); ?>
			</div>
			
			<a class="btn btn-locations" href="<?php echo Utils::app_absolute_url($info['lang'], 'location', ''); ?>"><span><?php echo Arr::get($cmslabel, 'locations_info'); ?></span></a>
		</div>
	</div>
<?php $this->endblock('content'); ?>

<?php $this->block('after_main_wrapper'); ?>
	<?php $map_points = Widget_Location::points(['lang' => $info['lang']]); ?>
	<?php echo View::factory('location/widget/map', ['map_points' => $map_points]); ?>
	<?php echo View::factory('newsletter/widget/manage', ['mode' => 'main']); ?>
<?php $this->endblock('after_main_wrapper'); ?>

<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>
<?php $this->block('sidebar_bottom'); ?> <?php $this->endblock('sidebar_bottom'); ?>

<?php $this->block('extrabody'); ?>
	<?php if ($map_points): ?>
		<?php echo Html::media('cmslocation,infobox', 'js'); ?>
	<?php endif; ?>
<?php $this->endblock('extrabody'); ?>