<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-about<?php $this->endblock('page_class'); ?>

<?php $this->block('after_header'); ?>
	<div class="main">
		<div class="df main-wrapper">
			<div class="main-body">
				<div class="fg1 main-content about-content">
					<div class="about-header">
						<h1 class="about-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
						<?php if(Arr::get($cmslabel, 'slogan')): ?>
							<p class="about-slogan"><?php echo Arr::get($cmslabel, 'slogan'); ?></p>
						<?php endif; ?>
					</div>
					<div class="about-image-placeholder"></div>
					<?php echo Arr::get($cms_page, 'content'); ?>
				</div>
			</div>
			<aside class="sidebar">
				<?php $about_images = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'about', 'limit' => 10]); ?>
				<?php if($about_images): ?>
					<div class="about-images slick-arrow2">
						<?php foreach ($about_images as $about_image): ?>
							<div class="about-image">
								<?php if($about_image['link']): ?><a href="<?php echo $about_image['link']; ?>"><?php endif; ?>
								<img loading="lazy" <?php echo Thumb::generate($about_image['image'], array('width' => 700, 'height' => 770, 'crop' => true, 'default_image' => '/media/images/no-image-250.jpg', 'html_tag' => true)); ?> alt="" />
								<?php if($about_image['link']): ?></a><?php endif; ?>
							</div>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>		
			</aside>
		</div>
	</div>

	<?php if(!empty(Arr::get($cmslabel, 'about_fact'))): ?>
		<div class="about-fact">
			<span><?php echo Arr::get($cmslabel, 'about_fact'); ?></span>
		</div>
	<?php endif; ?>
<?php $this->endblock('after_header'); ?>

<?php $this->block('main'); ?>
	<?php echo View::factory('cms/widget/testimonials'); ?>
	<?php echo View::factory('publish/widget/instashop'); ?>
<?php $this->endblock('main'); ?>