<?php $this->extend('cms/homepage'); ?>

<?php $this->block('promo'); ?>
	<?php $promo = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'homepage_promo', 'limit' => 3]); ?>
	<?php if(!empty($promo)): ?>
		<div class="wrapper wrapper-promo">
			<?php $i = 1; ?>
			<?php foreach ($promo as $promo_item): ?>
				<?php
					$img_w = 360;
					$img_h = 550;
					if($promo_item['position'] == 2) {
						$img_w = 700;
					}
				?>
				<div class="hp-promo hp-promo<?php echo $i; ?>">
					<a class="lloader" href="<?php echo $promo_item['link']; ?>">
						<picture>
							<?php if(!empty($promo_item['image_2'])): ?>
								<source srcset="<?php echo Thumb::generate($promo_item['image_2'], $img_w, $img_w, true, 'thumb', TRUE, '/media/images/no-image-500.jpg'); ?>" media="(max-width: 700px)">
							<?php endif; ?>
							<img loading="lazy" <?php echo Thumb::generate($promo_item['image'], array('width' => $img_w, 'height' => $img_h, 'crop' => true, 'default_image' => '/media/images/no-image-50.jpg', 'placeholder' => '/media/images/no-image-50.jpg')); ?> alt="" />
						</picture>
					</a>
				</div>
				<?php $i++; ?>
			<?php endforeach; ?>
		</div>
	<?php endif; ?>
<?php $this->endblock('promo'); ?>