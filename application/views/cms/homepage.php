<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-homepage<?php $this->endblock('page_class'); ?>

<?php $this->block('after_header'); ?>
	<?php $this->block('promo'); ?>
		<?php $hero_items = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'hero', 'limit' => 4]); ?>
		<?php if(!empty($hero_items)): ?>

			<div class="wrapper slider">
				<div class="slider-items glide__track" data-glide-el="track">
					<?php if($info['user_device'] == 'm'): ?><div class="glide__slides"><?php endif; ?>
					<?php $i = 1; ?>
					<?php foreach ($hero_items as $hero_item): ?>
                        <?php
                        $ga4_data = Google4::create_event('select_promotion', ['cart_info' => $shopping_cart, 'items' => [], 'promotion_info' => $hero_item]);
                        $ga4_data_view = Google4::create_event('view_promotion', ['cart_info' => $shopping_cart, 'items' => [], 'promotion_info' => $hero_item]);
                        ?>
						<a class="slider-item" href="<?php echo $hero_item['link']; ?>" data-create_ga4_event='<?php echo $ga4_data; ?>' data-tracking_gtm_promo_view="<?php echo $hero_item['id']; ?>|<?php echo $hero_item['title']; ?>|banner<?php echo $i; ?>|slot<?php echo $i; ?>" data-tracking_gtm_promo_click="<?php echo $hero_item['id']; ?>|<?php echo $hero_item['title']; ?>|banner<?php echo $i; ?>|slot<?php echo $i; ?>">
							<picture data-ga4_events_info='<?php echo $ga4_data_view; ?>'>
								<?php if(!empty($hero_item['image_2'])): ?>
									<source srcset="<?php echo Thumb::generate($hero_item['image_2'], 760, 1500, true, 'thumb', TRUE, '/media/images/no-image-500.jpg'); ?>" media="(max-width: 700px)">
								<?php endif; ?>
								<img<?php if($i != 1): ?> loading="lazy"<?php endif; ?> data-lazy="<?php echo Thumb::generate($hero_item['image'], 1480, 550, true, 'thumb', TRUE, '/media/images/no-image-1480.jpg'); ?>" <?php echo Thumb::generate($hero_item['image'], array('width' => 1480, 'height' => 550, 'crop' => true, 'default_image' => '/media/images/no-image-1480.jpg', 'html_tag' => true)); ?> alt="" />
							</picture>
						</a>

					<?php $i++; ?>
					<?php endforeach; ?>
					<?php if($info['user_device'] == 'm'): ?></div><?php endif; ?>
				</div>

				<?php if($info['user_device'] == 'm'): ?>
					<span class="glide__arrows" data-glide-el="controls">
						<button class="slick-prev slick-arrow" data-glide-dir="<">prev</button>
						<button class="slick-next slick-arrow" data-glide-dir=">">next</button>
					</span>

					<ul class="slick-dots" data-glide-el="controls[nav]">
						<?php $b = 0; ?>
						<?php foreach ($hero_items as $hero_item): ?>
							<li><button data-glide-dir="=<?php echo $b; ?>"></button></li>
							<?php $b++; ?>
						<?php endforeach; ?>
					</ul>
				<?php endif; ?>

				<?php if($info['user_device'] != 'm'): ?>
					<div class="slider-nav">
						<?php $i = 0; ?>
						<?php foreach ($hero_items as $hero_item): ?>
							<div data-slide-index="<?php echo $i; ?>" class="slider-nav-item active">
								<?php $hero_nav_image = (!empty($hero_item['image_3'])) ? $hero_item['image_3'] : $hero_item['image']; ?>
								<div class="slider-nav-item-image"><img loading="lazy" <?php echo Thumb::generate($hero_nav_image, array('width' => 100, 'height' => 100, 'crop' => true, 'default_image' => '/media/images/no-image-60.jpg', 'html_tag' => true)); ?> alt="" /></div>
								<div class="slider-nav-item-title"><?php echo $hero_item['title']; ?></div>
								<div class="slider-nav-progress"></div>
							</div>
							<?php $i++; ?>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>
			</div>
		<?php endif; ?>
	<?php $this->endblock('promo'); ?>

	<?php $homepage_benefits = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'homepage_intro_benefits', 'limit' => 3]); ?>				
	<?php if(!empty($homepage_benefits)): ?>
		<div class="df wrapper benefits">
			<?php foreach ($homepage_benefits as $homepage_benefit): ?>	
				<div class="benefit <?php echo $homepage_benefit['title2']; ?>">
					<?php if(!empty($homepage_benefit['link'])): ?><a href="<?php echo $homepage_benefit['link']; ?>"><span><?php endif; ?>
						<?php echo $homepage_benefit['title']; ?>
					<?php if(!empty($homepage_benefit['link'])): ?></span></a><?php endif; ?>
				</div>
			<?php endforeach; ?>
		</div>
	<?php endif; ?>

	<?php echo View::factory('catalog/widget/categories'); ?>
<?php $this->endblock('after_header'); ?>

<?php $this->block('main'); ?>
	<!-- Special products -->
    <?php $loyalty_discount = (!empty($user) AND !empty($user->loyalty_discount_percent)) ? $user->loyalty_discount_percent : 0; ?>
	<?php $most_buy = Widget_Catalog::speciallist($info['lang'], 'most_buy', true); ?>
	<?php if(!empty($most_buy)): ?>
		<?php $most_buy_items = Widget_Catalog::products(['lang' => $info['lang'], 'list_code' => $most_buy['code'], 'sort' => 'list_position', 'limit' => 10, 'loyalty_discount_cache' => $loyalty_discount]); ?>
	<?php endif; ?>

	<?php $best_buy = Widget_Catalog::speciallist($info['lang'], 'best_buy', true); ?>
	<?php if(!empty($best_buy)): ?>
		<?php $best_buy_items = Widget_Catalog::products(['lang' => $info['lang'], 'list_code' => $best_buy['code'], 'sort' => 'list_position', 'limit' => 10, 'loyalty_discount_cache' => $loyalty_discount]); ?>
	<?php endif; ?>

	<?php $new = Widget_Catalog::speciallist($info['lang'], 'new', true); ?>
	<?php if(!empty($new)): ?>
		<?php $new_items = Widget_Catalog::products(['lang' => $info['lang'], 'list_code' => $new['code'], 'sort' => 'list_position', 'limit' => 10, 'loyalty_discount_cache' => $loyalty_discount]); ?>
	<?php endif; ?>	
	
	<?php if(!empty($most_buy_items) OR !empty($best_buy_items) OR !empty($new_items)): ?>
		<div class="cw">
			<div class="wrapper">
				<div class="m-tabs cw-tabs">
					<ul class="tabs">
						<?php if(!empty($most_buy_items)): ?>
							<li><a href="#tab1"><?php echo $most_buy['title']; ?></a></li>
						<?php endif; ?>
						<?php if(!empty($best_buy_items)): ?>
							<li class="tab-best-buy"><a href="#tab2"><?php echo $best_buy['title']; ?></a></li>
						<?php endif; ?>
						<?php if(!empty($new_items)): ?>
							<li><a href="#tab3"><?php echo $new['title']; ?></a></li>
						<?php endif; ?>
					</ul>
					<div class="tabs-content">
						<?php if(!empty($most_buy_items)): ?>
                            <?php $ga4_data = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $most_buy_items, 'item_list_name' => $most_buy['title'], 'item_list_id' => 'most_buy']); ?>
							<div class="tab" id="tab1" data-ga4_events_info='<?php echo $ga4_data; ?>'>
								<div class="tab-toggle"><?php echo $most_buy['title']; ?></div>
								<div class="tab-content" data-tracking_gtm_tabs="1">
									<div class="c-items cw-items blazy-container">
										<?php echo View::factory('catalog/index_entry', ['items' => $most_buy_items, 'list' => $most_buy['title'], 'item_list_name' => $most_buy['title'], 'item_list_id' => 'most_buy']); ?>
									</div>
									<div class="tab-btns">
										<a class="btn" href="<?php echo $most_buy['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all'); ?></a>
									</div>
								</div>
							</div>
						<?php endif; ?>
						<?php if(!empty($best_buy_items)): ?>
                            <?php $ga4_data = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $best_buy_items, 'item_list_name' => $best_buy['title'], 'item_list_id' => 'best_buy']); ?>
                            <div class="tab tab-best-buy" id="tab2" data-ga4_events_info='<?php echo $ga4_data; ?>'>
								<div class="tab-toggle"><?php echo $best_buy['title']; ?></div>
								<div class="tab-content" style="visibility: hidden" data-tracking_gtm_tabs="1">
									<div class="c-items cw-items blazy-container">
										<?php echo View::factory('catalog/index_entry', ['items' => $best_buy_items, 'list' => $best_buy['title'], 'item_list_name' => $best_buy['title'], 'item_list_id' => 'best_buy']); ?>
									</div>
									<div class="tab-btns">
										<a class="btn" href="<?php echo $best_buy['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all'); ?></a>
									</div>
								</div>
							</div>
						<?php endif; ?>
						<?php if(!empty($new_items)): ?>
                            <?php $ga4_data = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $new_items, 'item_list_name' => $new['title'], 'item_list_id' => 'new']); ?>
                            <div class="tab" id="tab3" data-ga4_events_info='<?php echo $ga4_data; ?>'>
								<div class="tab-toggle"><?php echo $new['title']; ?></div>
								<div class="tab-content" style="visibility: hidden" data-tracking_gtm_tabs="1">
									<div class="c-items cw-items blazy-container">
										<?php echo View::factory('catalog/index_entry', ['items' => $new_items, 'list' => $new['title'], 'item_list_name' => $new['title'], 'item_list_id' => 'news']); ?>
									</div>
									<div class="tab-btns">
										<a class="btn" href="<?php echo $new['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all'); ?></a>
									</div>
								</div>
							</div>
						<?php endif; ?>
					</div>
				</div>
			<?php endif; ?>
		</div>
	</div>

	<?php echo View::factory('catalog/widget/manufacturers', ['mode' => 'homepage']); ?>

	<!-- Latest posts -->
	<?php $publish_category = Widget_Publish::category($info['lang'], 'blog'); ?>
	<?php if(!empty($publish_category)): ?>
		<?php $latest_blog_posts = Widget_Publish::publishes(['lang' => $info['lang'], 'category_code' => $publish_category['code'], 'limit' => 5, 'extra_fields' => ['short_description']]); ?>
		<?php if(!empty($latest_blog_posts)): ?>
			<div class="pw">
				<div class="wrapper wrapper-pw">
					<div class="pw-title"><?php echo Arr::get($cmslabel, 'homepage_health'); ?></div>
					<?php $publish_categories = Widget_Publish::categories(['lang' => $info['lang'], 'start_position' => $publish_category['position_h']]); ?>
					<?php if(!empty($publish_categories)): ?>
						<div class="pw-nav-container">
							<ul class="pw-nav">
								<?php foreach($publish_categories as $publish_category_item): ?>
									<li><a href="<?php echo $publish_category_item['url']; ?>"><?php echo $publish_category_item['title']; ?></a></li>	
								<?php endforeach; ?>
								<li><a href="<?php echo $publish_category['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all_posts'); ?></a></li>
							</ul>
						</div>
					<?php endif; ?>
					<?php $blog_i = 0; ?>
					<?php foreach($latest_blog_posts as $latest_blog_post): ?>
						<div class="pw-col pw-col1">
							<?php echo View::factory('publish/index_entry_single', ['item' => $latest_blog_post, 'mode' => 'big']); ?>
						</div>
						<div class="pw-col pw-col2">
						<?php $blog_i++; ?>
						<?php if ($blog_i == 1) {break;} ?>
					<?php endforeach; ?>
					<?php foreach($latest_blog_posts as $latest_blog_post): ?>
						<?php if ($blog_i) {$blog_i--; continue;} ?>
						<?php echo View::factory('publish/index_entry_single', ['item' => $latest_blog_post, 'mode' => 'small']); ?>
					<?php endforeach; ?>
					</div>
				</div>
			</div>
		<?php endif; ?>
	<?php endif; ?>

	<?php echo View::factory('cms/widget/testimonials'); ?>

	<!-- Recipes -->
	<?php $recipes_category = Widget_Publish::category($info['lang'], 'recipe'); ?>
	<?php if(!empty($recipes_category)): ?>
		<?php $recipe_items = Widget_Publish::publishes(array('lang' => $info['lang'], 'category_code' => $recipes_category['code'], 'limit' => 6)); ?>	
		<?php if(!empty($recipe_items)): ?>
			<div class="pw pw-recipes" data-css="lazyload" data-original="/media/images/homepage.jpg">
				<div class="wrapper wrapper-recipes df">
					<div class="pw-recipes-col pw-recipes-col1">
						<div class="pw-recipes-title"><?php echo Arr::get($cmslabel, 'blog_wrapper'); ?></div>
						<div class="recipes-sidebar-title"><?php echo Arr::get($cmslabel, 'recipes_by_category'); ?></div>
						<?php $recipes_categories = Widget_Publish::categories(['lang' => $info['lang'], 'start_position' => $recipes_category['position_h']]); ?>
						<?php if(!empty($recipes_categories)): ?>
							<ul class="nav-recipes nav-recipes-category">
								<?php foreach ($recipes_categories as $recipe_category): ?>
									<li><a href="<?php echo $recipe_category['url']; ?>"><?php echo $recipe_category['title']; ?></a></li>
								<?php endforeach; ?>
								<li><a href="<?php echo $recipes_category['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all'); ?></a></li>
							</ul>
						<?php endif; ?>

						<?php $recipes_filter_menu = Widget_Cms::menu(['lang' => $info['lang'], 'code' => 'recipes', 'level_range' => '1.2', 'hierarhy_by_position' => true]); ?>
						<?php if(!empty($recipes_filter_menu)): ?>
							<div class="recipes-sidebar-filters">
								<?php foreach($recipes_filter_menu as $recipes_menu_item): ?>
									<div class="recipes-sidebar-title"><?php echo $recipes_menu_item['title']; ?></div>
									<?php $recipes_level2 = (!empty($recipes_menu_item['children'])) ? $recipes_menu_item['children'] : [] ?>
									<?php if(!empty($recipes_level2)): ?>
										<ul class="nav-recipes">
											<?php foreach($recipes_level2 as $recipes_level2_item): ?>
												<li><a href="<?php echo $recipes_level2_item['url']; ?>"><?php echo $recipes_level2_item['title']; ?></a></li>
											<?php endforeach; ?>
										</ul>
									<?php endif; ?>
								<?php endforeach; ?>
							</div>
						<?php endif; ?>
					</div>
					<div class="pw-recipes-col pw-recipes-col2">
						<?php if(!empty($recipe_items)): ?>
							<div class="p-items p-items-recipes pw-recipe-items blazy-container">
								<?php echo View::factory('publish/recipes/index_entry',  array('items' => $recipe_items)); ?>
							</div>
							<div class="pw-btns">
								<a class="btn" href="<?php echo $recipes_category['url']; ?>"><span><?php echo Arr::get($cmslabel, 'show_all_recipes'); ?></span></a>
							</div>
						<?php endif; ?>
					</div>
				</div>
			</div>
		<?php endif; ?>
	<?php endif; ?>

	<?php echo View::factory('publish/widget/instashop'); ?>
<?php $this->endblock('main'); ?>

<?php $this->block('extrabody'); ?>
	<?php if($info['user_device'] == 'm'): ?>
		<?php echo Html::media('/media/glide.min.js', 'js'); ?>
		<script>
			var mobileSlider = new Glide('.slider', {
				rewind: true,
				gap: 0,
				type: 'carousel',
				animationDuration: 300,
				autoplay: 4000
			}).mount();

			mobileSlider.on('run.after', function() {
				if (typeof Blazy === 'function') {
					bLazy.revalidate();
				}
			});
		</script>
	<?php endif; ?>
<?php $this->endblock('extrabody'); ?>