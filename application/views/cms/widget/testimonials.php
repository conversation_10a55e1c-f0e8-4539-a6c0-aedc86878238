<!-- Testimonials -->
<?php $testimonials = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'testimonials', 'limit' => 3]); ?>
<?php if(!empty($testimonials)): ?>	
	<div class="testimonials">
		<div class="wrapper wrapper-testimonials">
			<div class="ts-col ts-col1">
				<div class="ts-items slick-arrow2">
					<?php foreach ($testimonials as $testimonial): ?>
						<div class="ts-item">
							<div class="ts">
								<figure class="ts-image lloader">
									<span><img data-lazy="<?php echo Thumb::generate($testimonial['image'], 180, 180, true, 'thumb', TRUE, '/media/images/no-image-50.jpg'); ?>" <?php echo Thumb::generate($testimonial['image'], array('width' => 180, 'height' => 180, 'crop' => true, 'default_image' => '/media/images/no-image-50.jpg', 'placeholder' => '/media/images/no-image-50.jpg')); ?> alt="" /></span>
								</figure>
								<div class="ts-cnt">
									<?php if(!empty($testimonial['image_2'])): ?>
										<img <?php echo Thumb::generate($testimonial['image_2'], array('width' => 105, 'height' => 20, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> alt="" />
									<?php endif; ?>
									<div class="ts-comment"><?php echo $testimonial['content']; ?></div>
									<div class="ts-name"><?php echo $testimonial['title']; ?></div>
								</div>
							</div>
						</div>
					<?php endforeach; ?>
				</div>
			</div>
			<div class="ts-col ts-col2" data-css="lazyload" data-original="/media/images/bg-green.jpg">
				<div class="ts-col2-cnt">
					<p><img data-css="lazyload" data-original="/media/images/loyalty-card2-<?php echo $info['lang']; ?>.png" src="/media/images/no-image-50.jpg" width="231" height="158" alt=""></p>
					<?php echo Arr::get($cmslabel, 'loyalty_box'); ?>
				</div>
			</div>
		</div>
	</div> 
<?php endif; ?>