<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> main-offset-sm page-checkout page-checkout-step1 page-checkout-login<?php $this->endblock('page_class'); ?>

<?php $this->block('header_body'); ?>
	<div class="checkout-return">
		<a class="btn-return-home" href="<?php echo Utils::homepage($info['lang']); ?>"><?php echo Arr::get($cmslabel, 'return_to_homepage'); ?></a>
	</div>
<?php $this->endblock('header_body'); ?>

<?php $this->block('header_content'); ?>
	<h1 class="checkout-title"><?php echo Arr::get($cmslabel, 'checkout_title'); ?></h1>
<?php $this->endblock('header_content'); ?>

<?php $this->block('extrahead'); ?>
	<?php echo Html::media('/media/cart.css', 'css'); ?>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('content_layout'); ?>
<?php foreach($products as $product_code => $product_data):?>
    <div>
        <span data-tracking_gtm_products="Checkout|<?php echo $product_data['code']; ?>">
            <span style="display: none;" data-product_code="<?php echo $product_code; ?>"><?php echo $product_data['code']; ?></span>
            <span style="display: none;" data-product_title="<?php echo $product_code ?>"><?php echo $product_data['title'] ?></span>
            <span style="display: none;" data-product_price="<?php echo $product_code;; ?>"><?php echo Utils::currency_format($product_data['price']); ?></span>
            <span style="display: none;" data-product_manufacturer_title="<?php echo $product_code; ?>"><?php echo trim(Arr::get($product_data, 'manufacturer_title')); ?></span>
            <span style="display: none;" data-product_category_title="<?php echo $product_code; ?>"><?php echo trim(Arr::get($product_data, 'category_title')); ?></span>
        </span>
    </div>
<?php endforeach;?>
	<div class="df wc-row">
		<?php if (!$user): ?>
			<div class="wc-col wc-col1 wc-step1-col1">
				<h2 class="wc-subtitle wc-subtitle-login"><?php echo Arr::get($cmslabel, 'guest_checkout', 'Želite kupiti bez registracije?'); ?></h2>
				<div class="wc-cnt"><?php echo Arr::get($cms_page, 'content'); ?></div>
				<form data-tracking_gtm_checkout_advanced="1|Nastavi kao gost" class="form-label wc-guest-form ajax_siteform ajax_siteform_loading" method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" id="webshop_form_login">
					<input type="hidden" name="guest_checkout" value="1" />
					<?php $error = ($message_type AND $message) ? "{$message_type}_{$message}" : ""; ?>
					<p class="field field-email">
						<input type="text" name="email" id="id_email" />
						<label for="id_email"><?php echo Arr::get($cmslabel, 'email_addres'); ?></label>
						<span id="field-error-email" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
					</p>
					<button class="btn btn-checkout btn-wc-guest" type="submit" name="guest_checkout" value="1"><span><?php echo Arr::get($cmslabel, 'continue_without_signup', 'Nastavi kao gost'); ?></span></button>
				</form>
			</div>
			<div class="wc-col wc-col2 wc-step1-col2">
				<h2 class="wc-subtitle wc-subtitle-login"><?php echo Arr::get($cmslabel, 'have_account', 'Već imate korisnički račun?'); ?></h2>
				<div class="wc-cnt"><?php echo Arr::get($cmslabel, 'login_to_buy', 'Prijavite se putem emaila za brzu kupnju'); ?></div>
				<form data-tracking_gtm_checkout_advanced="1|Prijava" class="form-label wc-login-form step1 ajax_siteform ajax_siteform_loading" method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" id="webshop_form_login">
					<?php $error = ($message_type AND $message) ? "{$message_type}_{$message}" : ""; ?>
					<input type="hidden" name="login" value="1" />
					<div class="global_success global-success" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'success_forgotten_password'); ?></div>
					<div class="global_error global-error" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'error_forgotten_password'); ?></div>

					<p class="field field-email">
						<input type="text" name="email" id="field-email" />
						<label for="id_email1"><?php echo Arr::get($cmslabel, 'email_addres'); ?></label>
						<span id="field-error-email" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
					</p>
					<p class="field field-password">
						<input type="password" name="password" id="field-password" />
						<label for="id_password"><?php echo Arr::get($cmslabel, 'password'); ?></label>
					</p>
					<p class="remember field-remember">
						<input type="checkbox" name="remember" id="id_rememberme" value="1" checked />
						<label for="id_rememberme"><?php echo Arr::get($cmslabel, 'remember'); ?></label>
					</p>
					
					<div class="a-btns">
						<button class="btn btn-checkout btn-wc-login" type="submit"><span><?php echo Arr::get($cmslabel, 'login', 'Prijava'); ?></span></button>
						<div class="a-links">
							<a class="a-links-forgotten" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>"><?php echo Arr::get($cmslabel, 'forgotten_password'); ?></a>
						</div>
					</div>
				</form>
					
				<?php echo View::factory('auth/widget/social_login'); ?>
			</div>
		<?php else: ?>
			<form class="ajax_siteform" method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" id="webshop_form_login">	
				<p class="loggedin-next-step"><button class="btn-big" type="submit" name="already_login" value="1"><?php echo Arr::get($cmslabel, 'goto_step2_button', 'Nastavi na sljedeći korak'); ?></button></p>
			</form>
		<?php endif; ?>
	</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('bottom'); ?> <?php $this->endblock('bottom'); ?>
<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>