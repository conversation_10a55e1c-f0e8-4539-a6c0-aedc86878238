<!-- Predl<PERSON><PERSON>rzi @$cmslabel - error supressing indicator -->
<?php

$shopping_cart_items = Webshop::shopping_cart_items();
$cart_mutual_warehouses = [];
$cart_product_types = [];
if (!empty($shopping_cart_items)) {
    $product_ids = [];
    foreach ($shopping_cart_items as $item_sc_code => $qty) {
        list($product_id, $variation_id) = explode('_', $item_sc_code);
        array_push($product_ids, $product_id);
    }

    $item_warehouses = ($product_ids)
        ? DB::select_array(['id', 'warehouses_ids', 'type'])
            ->from('catalog_products')
            ->where('id', 'IN', $product_ids)
            ->execute()
            ->as_array('id')
        : [];


    if (!empty($item_warehouses)) {
        $warehouses_data = [];
        foreach ($item_warehouses as $item_id => $item_warehouse) {
            array_push($cart_product_types, Arr::get($item_warehouse, 'type', ''));
            $data = Text::db_to_array(Arr::get($item_warehouse, 'warehouses_ids', ''));
            foreach ($data as $item_warehouse_data) {
                list($warehouse_id, $warehouse_qty) = explode('=', $item_warehouse_data);
                $warehouses_data[$item_id][$warehouse_id] = $warehouse_qty;
            }
        }
        $cart_mutual_warehouses = array_reduce($warehouses_data, function ($carry, $item) {
            if (is_null($carry)) {
                return array_keys($item);
            }
            return array_intersect($carry, array_keys($item));
        }, null);
    }
}
?>
<?php
$location_points_config = [
    'lang' => $info['lang'],
    'filters' => [
        'id' => (!empty($cart_product_types) AND in_array('pickup', $cart_product_types)) ? $cart_mutual_warehouses : [],
        'available_pickup' => true,
    ],
    'return' => 'dropdown',
];

if (Kohana::config("app.catalog.list_use_locations")) {
    $location_points_config['check_forced'] = true;
}
if (Kohana::config('app.webshop.personal_pickup_only_distance_points')) {
    $location_points_config['filters']['distance_point'] = true;
}
if (Kohana::config("app.webshop.location_visible_ignore")) {
    $location_points_config['only_visible'] = false;
}

$locations = Location::points($location_points_config);
?>
<?php if ($locations): ?>
    <span><?php echo Arr::get(@$cmslabel, 'shipping_location'); ?></span>
    <?php if (count($locations) == 1): ?>
        <?php echo Form::select('shipping_pickup_location', $locations, Arr::get($customer_data, 'shipping_pickup_location'), ['id' => 'field-shipping_pickup_location', 'data-shipping_pickup_location' => 1]); ?>
    <?php else: ?>
        <?php echo Form::select('shipping_pickup_location', Arr::merge(['' => Arr::get(@$cmslabel, 'select_shipping_pickup_location', '-')], $locations), Arr::get($customer_data, 'shipping_pickup_location'), ['id' => 'field-shipping_pickup_location', 'data-shipping_pickup_location' => 1]); ?>
    <?php endif; ?>
    <span data-shipping_pickup_location="1" id="field-error-shipping_pickup_location" class="field_error error" style="display: none"></span>

    <?php if ($shipping_time_info AND $shopping_cart_status): ?>
        <?php $time_infos = Webshop::shipping_expected_delivery($shipping_time_info, time(), $shopping_cart_status); ?>
        <?php if ($time_infos): ?>
            <?php if (Kohana::config('app.catalog.use_available_qty_warehouse') AND Kohana::config('app.webshop.warehouse_master')): ?>
                <?php foreach ($time_infos AS $time_info_id => $time_info_data): ?>
                    <span class="shipping_locationid_detail"
                          data-shipping_locationid_detail="<?php echo $time_info_id; ?>">
                        <?php
                        echo str_replace(
                            ['%DAY%', '%DATE%', '%HOUR_FROM%', '%HOUR_TO%'], [
                            Arr::get(@$cmslabel, 'day_' . $time_info_data[0], 'day_' . $time_info_data[0]),
                            date('d.m.Y', $time_info_data[1]),
                            date('H', $time_info_data[1]),
                            date('H', $time_info_data[2]),
                        ], Arr::get(@$cmslabel, 'expected_shipping_time', ''));

                        ?>
                    </span>
                <?php endforeach; ?>
            <?php else: ?>
                <?php $time_info_data = $time_infos; ?>
                <?php if (Kohana::config('app.webshop.warehouse_same_shipping_period')): ?>
                    <span class="shipping_locationid_detail active">
                <?php else: ?>
                    <span class="shipping_locationid_detail" data-shipping_locationid_detail="0">
                <?php endif; ?>
                <?php
                echo str_replace(
                    ['%DAY%', '%DATE%', '%HOUR_FROM%', '%HOUR_TO%'], [
                    Arr::get(@$cmslabel, 'day_' . $time_info_data[0], 'day_' . $time_info_data[0]),
                    date('d.m.Y', $time_info_data[1]),
                    date('H', $time_info_data[1]),
                    date('H', $time_info_data[2]),
                ], Arr::get(@$cmslabel, 'expected_shipping_time', ''));

                ?>
                </span>
            <?php endif; ?>
        <?php endif; ?>
    <?php endif; ?>
<?php endif; ?>