<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> main-offset-sm page-checkout page-checkout-step4<?php $this->endblock('page_class'); ?>

<?php $this->block('extrahead'); ?>
	<?php echo Html::media('/media/cart.css', 'css'); ?>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('header_body'); ?>
	<div class="checkout-return">
		<a class="btn-return-home" href="<?php echo Utils::homepage($info['lang']); ?>"><?php echo Arr::get($cmslabel, 'return_to_homepage'); ?></a>
	</div>
<?php $this->endblock('header_body'); ?>

<?php $this->block('header_content'); ?>
	<h1 class="checkout-title"><?php echo Arr::get($cmslabel, 'checkout_title'); ?></h1>
<?php $this->endblock('header_content'); ?>

<?php $this->block('content_layout'); ?>
	<form class="step4 df wc-row form-label ajax_siteform ajax_siteform_loading" action="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'create_order', FALSE); ?>" method="post" name="webshop" id="webshop_form" data-tracking_gtm_checkout_advanced="4|<?php echo Utils::token_id(); ?>">
		<div class="wc-col wc-col1 wc-step4-col1">
			<div class="step step2 completed-step">
				<a class="step_link2" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'shipping'); ?>">
					<?php echo Arr::get($cmslabel, 'step1'); ?></span>
					<span class="change-data"><?php echo Arr::get($cmslabel, 'change_data'); ?></span>
				</a>
			</div>
			<div class="step step3 completed-step">
				<a class="step_link2" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>">
					<?php echo Arr::get($cmslabel, 'step2'); ?></span>
					<span class="change-data"><?php echo Arr::get($cmslabel, 'change_data'); ?></span>
				</a>
			</div>
			<div class="wc-col-cnt">
				<h2 class="wc-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h2>

				<div class="ct-review">
					<div class="ct-review-title"><?php echo Arr::get($cmslabel, 'step1'); ?><a class="ct-review-title-btn" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'shipping'); ?>"><?php echo Arr::get($cmslabel, 'change'); ?></a></div>
					<div class="ct-review-value">
						<?php if(Arr::get($customer_data, 'first_name') OR Arr::get($customer_data, 'last_name')): ?>
							<div><?php echo Arr::get($customer_data, 'first_name'); ?> <?php echo Arr::get($customer_data, 'last_name'); ?></div>
						<?php endif; ?>

						<?php if(Arr::get($customer_data, 'oib')): ?>
							<div><?php echo Arr::get($cmslabel, 'oib'); ?>: <?php Arr::get($customer_data, 'oib'); ?></div>
						<?php endif; ?>	

						<?php if(Arr::get($customer_data, 'address')): ?>
							<div><?php echo Arr::get($customer_data, 'address'); ?></div>
						<?php endif; ?>

						<?php if(Arr::get($customer_data, 'zipcode') OR Arr::get($customer_data, 'city')): ?>
							<div><?php echo Arr::get($customer_data, 'zipcode'); ?> <?php echo Arr::get($customer_data, 'city'); ?></div>
						<?php endif; ?>

						<?php if(Arr::get($customer_data, 'phone')): ?>
							<div><?php echo Arr::get($customer_data, 'phone'); ?></div>
						<?php endif; ?>

						<?php if(Arr::get($customer_data, 'country')): ?>
							<div><?php echo (Arr::get($customer_data, 'country')) ? Jelly::query('webshopcountry', Arr::get($customer_data, 'country'))->select()->description($info['lang']) : ''; ?></div>
						<?php endif; ?>

						<?php if(Arr::get($customer_data, 'email')): ?>
							<div><?php echo Arr::get($customer_data, 'email'); ?></div>
						<?php endif; ?>

						<?php if(Arr::get($customer_data, 'message')): ?>
							<div><?php echo Arr::get($customer_data, 'message'); ?></div>
						<?php endif; ?>
					</div>
				</div>

				<?php if (Arr::get($customer_data, 'b_same_as_shipping') != 1 AND Arr::get($customer_data, 'b_first_name')): ?>
					<div class="ct-review">
						<div class="ct-review-title"><?php echo Arr::get($cmslabel, 'bill_address'); ?><a class="ct-review-title-btn" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'shipping'); ?>"><?php echo Arr::get($cmslabel, 'change'); ?></a></div>
						<div class="ct-review-value">				
							<?php if(Arr::get($customer_data, 'b_first_name') OR Arr::get($customer_data, 'b_last_name')): ?>
								<div><?php echo Arr::get($customer_data, 'b_first_name'); ?> <?php echo Arr::get($customer_data, 'b_last_name'); ?></div>
							<?php endif; ?>

							<?php if(Arr::get($customer_data, 'b_oib')): ?>
								<div><?php echo Arr::get($cmslabel, 'oib'); ?>: <?php Arr::get($customer_data, 'b_oib'); ?></div>
							<?php endif; ?>	

							<?php if(Arr::get($customer_data, 'b_address')): ?>
								<div><?php echo Arr::get($customer_data, 'b_address'); ?></div>
							<?php endif; ?>

							<?php if(Arr::get($customer_data, 'b_zipcode') OR Arr::get($customer_data, 'b_city')): ?>
								<div><?php echo Arr::get($customer_data, 'b_zipcode'); ?> <?php echo Arr::get($customer_data, 'b_city'); ?></div>
							<?php endif; ?>

							<?php if(Arr::get($customer_data, 'b_country')): ?>
								<div><?php echo (Arr::get($customer_data, 'b_country')) ? Jelly::query('webshopcountry', Arr::get($customer_data, 'b_country'))->select()->description($info['lang']) : ''; ?></div>
							<?php endif; ?>

							<?php if(Arr::get($customer_data, 'b_phone')): ?>
								<div><?php echo Arr::get($customer_data, 'b_phone'); ?></div>
							<?php endif; ?>

							<?php if(Arr::get($customer_data, 'b_email')): ?>
								<div><?php echo Arr::get($customer_data, 'b_email'); ?></div>
							<?php endif; ?>

						<?php if(Arr::get($customer_data, 'b_message')): ?>
							<div><?php echo Arr::get($customer_data, 'b_message'); ?></div>
						<?php endif; ?>
						</div>
					</div>
				<?php endif; ?>

				<?php if (Arr::get($customer_data, 'b_r1') == 1 OR Arr::get($customer_data, 'b_company_name')): ?>
					<div class="ct-review cart-total-shipping">
						<div class="ct-review-title"><?php echo Arr::get($cmslabel, 'r1_address'); ?><a class="ct-review-title-btn" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'shipping'); ?>"><?php echo Arr::get($cmslabel, 'change'); ?></a></div>
						<div class="ct-review-value">
							<?php if(!empty(Arr::get($customer_data, 'b_company_name'))): ?>
								<div><?php echo Arr::get($customer_data, 'b_company_name'); ?></div>
							<?php endif; ?>
							<?php if(!empty(Arr::get($customer_data, 'b_company_oib'))): ?>
								<div><?php echo Arr::get($cmslabel, 'company_oib'); ?>: <?php echo Arr::get($customer_data, 'b_company_oib'); ?></div>
							<?php endif; ?>
							<?php if(Arr::get($customer_data, 'b_company_address')): ?>
								<div><?php echo Arr::get($customer_data, 'b_company_address'); ?></div>
							<?php endif; ?>
						</div>
					</div>
				<?php endif; ?>

				<div class="ct-review cart-total-shipping">
					<div class="ct-review-title"><?php echo Arr::get($cmslabel, 'shipping_type'); ?><a class="ct-review-title-btn" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>"><?php echo Arr::get($cmslabel, 'change'); ?></a></div>
					<div class="ct-review-value"><?php echo $shipping_display; ?></div>
				</div>

				<div class="ct-review cart-total-payment">
					<div class="ct-review-title"><?php echo Arr::get($cmslabel, 'payment'); ?><a class="ct-review-title-btn" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>"><?php echo Arr::get($cmslabel, 'change'); ?></a></div>
					<div class="ct-review-value cart_info_total_items_ways_of_payment"><?php echo $payment_display; ?></div>
				</div>
 
				<div class="ct-review cart-total-payment">
					<div class="ct-review-title"><?php echo Arr::get($cmslabel, 'cart_totals'); ?><a class="ct-review-title-btn" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>"><?php echo Arr::get($cmslabel, 'change'); ?></a></div>
					<?php echo View::factory('webshop/widget/total', array('shopping_cart_info' => $shopping_cart_info)); ?>
				</div>

				<?php if (isset($shopping_cart_info['total_extra_shipping_min_total_error']) AND $shopping_cart_info['total_extra_shipping_min_total_error']): ?>			
					<div class="minprice-tooltip">	
					<?php echo str_replace(array(
							'%TOTAL_MIN%', 
							'%TOTAL_MISSING%', 
							), 
						array(
							Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
							Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total_missing'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total_missing'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),

						),
						Arr::get($cmslabel, 'minimal_order_price_full'));
					?>
					</div>
					<a href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>" class="btn btn-large btn-finish"><?php echo Arr::get($cmslabel, 'edit_shopping_cart'); ?></a> 
				<?php else: ?>
					<div class="wc-terms">
						<?php if (isset($accept_terms_field) AND isset($accept_terms_error)): ?>
							<div class="webshop-alert-terms" data-accept_terms="note">
								<span><?php echo Arr::get($cmslabel, 'terms_error', 'Prije plaćanja ste obvezni prihvatiti uvjete kupnje'); ?></span>
							</div>
							<?php echo View::factory('webshop/widget/accept_terms', array('accept_terms_field' => $accept_terms_field, 'accept_terms_error' => $accept_terms_error)); ?>
						<?php endif; ?>

						<?php if (isset($accept_terms_2_field) AND isset($accept_terms_2_error)): ?>
							<?php echo View::factory('webshop/widget/accept_terms_2', array('accept_terms_field' => $accept_terms_2_field, 'accept_terms_error' => $accept_terms_2_error)); ?>
						<?php endif; ?>
					</div>

					<?php if (!empty($gdpr_template['content'])): ?>
						<?php echo str_replace(['<p>', '</p>'], " ", $gdpr_template['content']); ?>
					<?php endif; ?>

					<button type="submit" class="btn btn-orange btn-finish">
						<span><?php echo Arr::get($cmslabel, 'confirm_order'); ?></span>
					</button>
				<?php endif; ?>
			</div>
		</div>

		<!-- Column 2 -->
		<div class="wc-col wc-col2 wc-step4-col2">
			<div class="wc-coupons">
				<div class="wc-col-cnt">
					<?php echo View::factory('webshop/widget/coupon', array('shopping_cart_info' => $shopping_cart_info)); ?>
				</div>
			</div>

			<?php echo View::factory('webshop/widget/shopping_cart_items_small', array('shopping_cart_info' => $shopping_cart_info, 'products' => $products, 'products_status' => $products_status)); ?>		
		</div>
	</form>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('bottom'); ?> <?php $this->endblock('bottom'); ?>
<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>