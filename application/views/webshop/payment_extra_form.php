<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', array('cms_page' => isset($cms_page) ? $cms_page : array())); ?><?php $this->endblock('seo'); ?>

<?php $this->block('content'); ?>
	<div class="kekspay-payment">
		<h3><?php echo Arr::get($cmslabel, 'kekspay_title'); ?></h3>
		<span id="field-error-payment" class="field_error error" style="display: none"></span>
		<div id="redirect" data-redirect_url="<?php echo Utils::app_absolute_url('hr', 'webshop', 'payment_on_hold'); ?>"></div>
		<div id="qr_code" data-bill_id="<?php echo $response_data['bill_id']; ?>">
			<?php echo Arr::get($cmslabel, 'kekspay_qrcode'); ?>
			<img src="<?php echo $response_data['qr_code']; ?>" alt="Keks Pay QR">
		</div>
		<p><a class="btn btn-kekspay" href="<?php echo $response_data['link']; ?>" id="kekspay_payment_url"><?php echo Arr::get($cmslabel, 'kekspay_pay'); ?></a><br> <?php echo Arr::get($cmslabel, 'kekspay_time_remaining'); ?> [<span id="kekspay_counter">02:00</span>]</p>
		<?php echo str_replace('%payment-hold-url%',  "javascript:location.reload();", Arr::get($cmslabel, 'kekspay_already_paid') ); ?>
	</div>
<?php $this->endblock('content'); ?>