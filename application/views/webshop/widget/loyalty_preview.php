<?php
// Proveri da li je kupac B2b bez obzira na to da li je prijav<PERSON>jen ili ne
$client_email = '';
$client_is_b2b = false;

if (!empty($info['user_email'])) {
    $client_email = $info['user_email'];
} elseif (!empty($user->email)) {
    $client_email = $user->email;
} elseif (!empty($customer_data['email'])) {
    $client_email = $customer_data['email'];
}

if (!empty($client_email)) {
    $client_is_b2b = User::data_single($client_email, 'b2b', 'email');
}
?>
<?php if (!$info['user_b2b'] AND !$client_is_b2b AND Kohana::config('app.loyalty.use_loyalties') AND isset($shopping_cart_info['total_extra_loyalty_new_discount_percent']) AND isset($shopping_cart_info['total_extra_loyalty_new'])): ?>
	<div class="ww-loyalty">
		<?php $field_id = (!empty($shopping_cart_preview)) ? 'loyalty_request_new_preview' : 'loyalty_request_new'; ?>
		<input type="checkbox" name="loyalty_request_new" id="field-<?php echo $field_id; ?>" value="1" <?php if (!empty($customer_data['loyalty_request_new'])): ?>checked<?php endif; ?>> 
		<label for="field-<?php echo $field_id; ?>">
			<?php if ($shopping_cart_info['total_extra_loyalty_new'] > 0): ?>
			<?php echo str_replace('%AMOUNT%', '<span class="value cart_info_total_extra_loyalty_new">'.Utils::currency_format($shopping_cart_info['total_extra_loyalty_new'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_loyalty_new'], ['currency_code' => $currency['code'], 'display_format' => 'standard']).'</span>', Arr::get($cmslabel, 'join_loyalty_label')) ?>
			<?php else: ?>
				<span class="no-discount"><?php echo Arr::get($cmslabel, 'loyalty_no_discount_confirm'); ?></span>
			<?php endif; ?>
		</label>
	</div>
<?php endif; ?>