<?php if (isset($current_step)): ?>
	<div class="steps">
		<?php switch ($current_step): ?><?php case 1: ?>
			<div class="step step2"><span><a class="step_link" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'shipping'); ?>"><span class="num">1. </span><span class="label"><?php echo Arr::get($cmslabel, 'step2'); ?></span></a></span></div>
			<div class="step step3"><div><a class="step_link" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>"><span class="num">2. </span><span class="label"><?php echo Arr::get($cmslabel, 'step3'); ?></span></a></div></div>
		<?php break; case 2: ?>
			<div class="step step2 current-step"><div><span class="num">1. </span><span class="label"><?php echo Arr::get($cmslabel, 'step2'); ?></span></div></div>
			<div class="step step3"><div><a class="step_link" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>"><span class="num">2. </span><span class="label"><?php echo Arr::get($cmslabel, 'step3'); ?></span></a></div></div>
		<?php break; case 3: ?>
			<div class="step step2 completed-step"><div><a class="step_link" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'shipping'); ?>"><span class="num">1. </span><span class="label"><?php echo Arr::get($cmslabel, 'step2'); ?></span></a></div></div>
			<div class="step step3 current-step"><div><span class="num">2. </span><span class="label"><?php echo Arr::get($cmslabel, 'step3'); ?></span></div></div>
		<?php break; endswitch; ?>
	</div>
<?php endif; ?>