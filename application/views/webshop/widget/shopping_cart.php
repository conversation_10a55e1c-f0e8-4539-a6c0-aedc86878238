<?php if (!empty($info['controller']) AND !empty($info['action']) AND $info['controller'] == 'webshop' AND $info['action'] == 'shopping_cart'): ?>
	<div class="ww cart<?php if ($shopping_cart['total_items']): ?> active<?php endif; ?>" data-shoppingcart_active="1" data-shoppingcart_url="<?php echo Utils::app_absolute_url($info['lang'], 'webshop'); ?>" >
		<a class="ww-items" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop'); ?>">
			<span class="ww-counter">
				<span class="value total-items cart_info_item_count total_items"><?php echo $shopping_cart['item_count']; ?></span>
			</span>
		</a>

		<!-- Min order alert -->
		<span class="cart_info_total_items_base" style="position: absolute; top: -500px;"><?php echo @$shopping_cart['total_items_base']; ?></span>
	</div>
<?php else: ?>
	<div class="ww cart<?php if ($shopping_cart['total_items']): ?> active<?php endif; ?>" data-shoppingcart_active="1" data-shoppingcart_url="<?php echo Utils::app_absolute_url($info['lang'], 'webshop'); ?>" >
		<a class="ww-items toggle-cart" href="javascript:void(0);" data-shopping_cart_preview_active="1">
			<span class="ww-counter">
				<span class="value total-items cart_info_item_count total_items"><?php echo $shopping_cart['item_count']; ?></span>
			</span>
		</a>

		<!-- Cart preview -->
		<div class="ww-preview shopping_cart_preview" data-disable_refresh="1" data-disable_html_preview="0">
			<?php echo View::factory('webshop/widget/shopping_cart_preview', [
					'shopping_cart' => $shopping_cart,
					'shopping_cart_products' => $shopping_cart_products,
					'shopping_cart_status' => $shopping_cart_status,
				]);
			?>
		</div>

		<!-- Min order alert -->
		<span class="cart_info_total_items_base" style="position: absolute; top: -500px;"><?php echo @$shopping_cart['total_items_base']; ?></span>
	</div>
<?php endif; ?>