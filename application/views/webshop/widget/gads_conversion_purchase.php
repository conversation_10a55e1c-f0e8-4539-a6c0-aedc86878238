<script type="text/javascript">
    var dataLayer = window.dataLayer || [];
    dataLayer.push({
        <?php if (!empty($event)): ?>'event': '<?php echo $event; ?>',<?php endif; ?>
        'transaction_id': '<?php echo($order->id ?? ''); ?>',
        'value': <?php echo(number_format($order->total * $order->exchange, 2, '.', '') ?? 0.00); ?>,
        'shipping': <?php echo(number_format($order->get_shipping_price() * $order->exchange, 2, '.', '') ?? 0.00); ?>,
        'tax': <?php echo(number_format($order->total_tax * $order->exchange, 2, '.', '') ?? 0.00); ?>,
        'currency': '<?php echo($order->price_currency->code ?? 0.00); ?>',
        'email': '<?php echo($order->email ?? ''); ?>',
        'phone_number': '<?php echo($order->phone ?? '');?>',
        'address': [
            {
                'first_name': '<?php echo str_replace("'", '"', ($order->first_name ?? '')); ?>',
                'last_name': '<?php echo str_replace("'", '"', ($order->last_name ?? '')); ?>',
                'street': '<?php echo str_replace("'", '"', ($order->address ?? '')); ?>',
                'city': '<?php echo str_replace("'", '"', ($order->city ?? '')); ?>',
                'region': '<?php echo Arr::get($cmslabel, 'city', 'Grad') . ' ' . str_replace("'", '"', ($order->city ?? '')); ?>',
                'country': '<?php echo(strtoupper($order->country->code) ?? ''); ?>',
                'postal_code': '<?php echo str_replace("'", '"', ($order->zipcode ?? '')); ?>',
            }
        ],
    });
</script>