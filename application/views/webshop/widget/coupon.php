<?php $mode = isset($mode) ? $mode : ''; ?>
<?php if (Kohana::config('app.webshop.use_coupons')): ?>
	<?php $have_coupon = (isset($shopping_cart_info['total_extra_coupon_code']) AND count($shopping_cart_info['total_extra_coupon_code'])); ?>
	<?php $have_coupon_product = (isset($shopping_cart_info['total_extra_coupon_product_code']) AND count($shopping_cart_info['total_extra_coupon_product_code'])); ?>	
	<?php /*
		FIXME PROG http://tzh.markerdev.info/webshop/
		
		1. Doda se kupon mtest
		2. Označi se checkbox za loyalty
		3. <PERSON>pon mtest je obračunat... se makne i prikaže se opet forma za unos
		4. Ako opet ideš dodati kupon, javi se greška "Već ste iskoristili maksimalan broj kupona po narudžbi"

		Ako se ide ovim redoslijedom onda je ok:
		1. <PERSON><PERSON><PERSON><PERSON> se checkbox za loyalty
		2. <PERSON><PERSON><PERSON><PERSON> kupon
		3. <PERSON><PERSON><PERSON><PERSON> "Kupon mtest nije obračunat zbog ograničenja."

		U prvom scenariju bi se isto trebala prikazati greška nakon što se prikaže forma za unos i kada pokušaš opet dodati kupon.
		Sad javi da je iskorišten maksimanal broj kupona, a nigdje nije naveden kupon pa je malo zbunjujuće
	
		Ista situacija je i kad se prijavljen i imaš loyalty. Prikaže se lista dostupnih kupona ali ih nemožeš koristiti jer javlja da je iskorišten maksimalan broj. https://snipboard.io/0rlUT6.jpg
		Trebala bi biti poruka o ograničenju.
	*/ ?>
	<div class="ww-coupons<?php if($mode == 'preview'): ?> ww-coupons-preview<?php else: ?> ww-coupons-cart<?php endif; ?><?php if ($have_coupon OR $have_coupon_product): ?> active<?php endif; ?>" data-coupon_active="webshop_coupon">
		<?php if($mode == 'preview'): ?>
			<label for="coupon_code" class="ww-coupons-label ww-preview-coupons-label"><?php echo Arr::get($cmslabel, 'coupon_have_coupon', 'Imaš kupon ili poklon bon?'); ?></label>
		<?php endif; ?>

		<?php if($mode == 'preview'): ?><div class="ww-coupons-container"><?php endif; ?>
			<!-- Add coupon form -->
			<div class="ww-coupons-form">
				<?php if($mode != 'preview'): ?>
					<label for="coupon_code" class="ww-coupons-label"><?php echo Arr::get($cmslabel, 'coupon_have_coupon', 'Imaš kupon ili poklon bon?'); ?></label>
				<?php endif; ?>
				<div class="ww-coupons-fields">
					<input type="text" name="coupon_code" id="coupon_code" value="" placeholder="<?php echo Arr::get($cmslabel, 'coupon_enter_code'); ?>" />
					<a class="ww-btn-add ww-coupons-add" href="javascript:cmscoupon.set('webshop_coupon')"><span><?php echo Arr::get($cmslabel, 'coupon_btn_add', 'Dodaj'); ?></span></a>
				</div>
				<div class="error coupon_message coupon-message" style="display: none"></div>
			</div>

			<!-- Used coupon -->
			<div class="ww-coupons-active">
				<span class="ww-coupons-title"><?php echo str_replace('%COUPON_CODE%', implode(',', $shopping_cart_info['total_extra_coupon_code']), Arr::get($cmslabel, 'coupon_included_code')); ?></span>
				<a class="ww-coupon-delete" href="javascript:cmscoupon.remove('webshop_coupon', '_all');"> <span><?php echo Arr::get($cmslabel, 'coupon_remove', 'Ukloni'); ?></span></a>
			</div>
				
			<!-- List of available coupons -->
			<?php $coupons = Webshop::coupons(array('lang' => $info['lang'], 'only_my' => TRUE, 'user_email' => $info['user_email'])); ?>
			<?php if($coupons): ?>
				<div class="ww-coupons-list">
					<h5 class="ww-coupons-list-title"><?php echo Arr::get($cmslabel, 'coupon_available', 'Dostupni kuponi'); ?></h5>
					<table class="ww-coupons-table">
					<?php foreach ($coupons AS $coupon): ?>	
						<tr class="<?php if (!empty($active_coupons) AND in_array($coupon['code'], $active_coupons)): ?>active<?php endif; ?>" data-coupon_active="webshop_coupon_<?php echo $coupon['code']; ?>">
							<td class="col-code"><?php echo $coupon['code']; ?></td>
							<td class="col-type"><?php echo ($coupon['type'] == 'f') ? Utils::currency_format($coupon['coupon_price']).Utils::get_second_pricetag_string($coupon['coupon_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']) : ($coupon['coupon_percent']*100).'%'; ?></td>
							<td class="col-link">
								<a class="btn-coupon-add" href="javascript:cmscoupon.set('webshop_coupon', '<?php echo $coupon['code']; ?>');"><span><?php echo Arr::get($cmslabel, 'coupon_use', 'Koristi'); ?></span></a>
							</td>
						</tr>
					<?php endforeach; ?>
					</table>
				</div>	
			<?php endif; ?>
		<?php if($mode == 'preview'): ?></div><?php endif; ?>
	</div>
<?php endif; ?>