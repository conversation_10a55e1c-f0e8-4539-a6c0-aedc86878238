<?php $mode = (isset($mode))? $mode : ''; ?>
<?php $free_delivery_list = Widget_Catalog::speciallist($info['lang'], 'checkout_suggestion', true, ['cache_lifetime' => 60]); ?>
<?php $shopping_cart_categories = array_unique(array_values($shopping_cart['product_categories'])); ?>
<?php $shopping_cart_items = Webshop::shopping_cart_items(); ?>
<?php $shopping_cart_product_ids = []; ?>
<?php
foreach ($shopping_cart_items as $id => $qty) {
    list($item_id, $variation) = explode('_', $id);
    array_push($shopping_cart_product_ids, $item_id);
}
?>
<?php if (!empty($free_delivery_list)): ?>
	<?php $free_delivery_list_items = Widget_Catalog::products(['lang' => $info['lang'], 'list_code' => $free_delivery_list['code'], 'limit' => 12, 'sort' => 'list_position', 'category_id' => $shopping_cart_categories, 'id_exclude' => array_values($shopping_cart_product_ids), 'exclude_new' => true]); ?>
	<?php if(!empty($free_delivery_list_items)): ?>
    <?php $ga4_data = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $free_delivery_list_items, 'item_list_name' => 'Besplatna dostava', 'item_list_id' => 'besplatna-dostava']); ?>
		<div class="cart_info_total_extra_shipping_to_free_box free-delivery-widget<?php if (!empty($shopping_cart['total_extra_shipping_to_free'])): ?> active<?php endif; ?><?php if($mode == 'payment'): ?> payment<?php endif; ?>" data-box_mode="class">
			<div class="fdw-title">
				<?php echo Arr::get($cmslabel, 'free_delivery_widget_title'); ?>
				<a class="btn" href="<?php echo $free_delivery_list['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all'); ?></a>
			</div>
			<div class="fdw-container" data-ga4_events_info='<?php echo $ga4_data; ?>'>
				<div class="fwd-slider slick-carousel slick-arrow3 blazy-container">
					<?php echo View::factory('catalog/index_entry', ['items' => $free_delivery_list_items, 'class' => 'no-shadow cp-rp', 'list' => 'Besplatna dostava', 'item_list_name' => 'Besplatna dostava', 'item_list_id' => 'besplatna-dostava']); ?>
				</div>
			</div>	
		</div>
	<?php endif; ?>
<?php endif; ?>