<?php $class = (isset($class)) ? ' '.$class : ''; ?>

<!-- Free delivery counter -->
<div id="free-deliery" class="free-delivery-container<?php echo $class; ?> cart_info_total_extra_shipping_to_free_box" <?php if (empty($shopping_cart_info['total_extra_shipping_to_free'])): ?> style="display: none"<?php endif; ?>>
    <?php if (empty($shopping_cart_info['total_items_free_shipping'])) : ?>
        <div class="free-delivery-title"><?php echo Arr::get($cmslabel, 'min_total_missing'); ?>
            <strong class="w-missing-shipping-value cart_info_total_extra_shipping_to_free">
                <?php echo Utils::currency_format($shopping_cart_info['total_extra_shipping_to_free'] * $currency['exchange'], $currency['display']); ?>
                <?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_to_free'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
            </strong>
        </div>
        <div class="free-delivery-scale">
            <div class="free-delivery-scale-amount">
                <span class="cart_info_total_items">
                    <?php echo Utils::currency_format($shopping_cart_info['total_items'] * $currency['exchange'], $currency['display']); ?>
                    <?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_items'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
                </span> /
                <span class="cart_info_total_extra_shipping_free_above">
                    <?php echo Utils::currency_format($shopping_cart_info['total_extra_shipping_free_above'] * $currency['exchange'], $currency['display']); ?>
                    <?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_free_above'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
                </span>
            </div>
            <div class="free-delivery-scale-bar cart_info_total_extra_shipping_to_free_percent" style="width: <?php echo $shopping_cart_info['total_extra_shipping_to_free_percent']; ?>;"></div>
        </div>
    <?php endif; ?>
</div>
