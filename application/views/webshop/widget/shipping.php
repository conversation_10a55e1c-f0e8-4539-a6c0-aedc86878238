<?php
$step = (isset($step)) ? $step : '';
$errorcode = Arr::get($_GET, 'errorcode');
$shippings = Webshop::shippings(['lang' => $info['lang']]);
$selected_shipping = $shopping_cart_info['selected_shipping'];

$shipping_address = Html::customer_address($customer_data, $info['lang'], Arr::extract($cmslabel, ['oib']));
$shipping_address .= '<br><a href="'.Utils::app_absolute_url($info['lang'], 'webshop', 'shipping').'#webshop_form" class="btn-change-address"><span>'.Arr::get($cmslabel, 'change_shipping_address', 'Promijeni adresu dostave').'</span></a>';

//wolt
$promise_delivery_eta = '';
if (!empty($customer_data['shipment_promise_data']['promise_estimated_delivery_time'])) {
    $promise_eta_minutes = $customer_data['shipment_promise_data']['promise_estimated_delivery_time'];
    $promise_delivery_eta = time() + ($promise_eta_minutes * 60);
}
$active_wolt_venue_id = Arr::get($customer_data, 'shipment_promise_venue_id', '');
?>

<input type="hidden" name="country" value="<?php echo Arr::get($customer_data, 'country'); ?>">
<?php if ($errorcode == 'invalid_parcel_locker_id'): ?>
    <p class="error global-error"><?php echo Arr::get($cmslabel, $errorcode, 'Neispravan paketomat. Molimo vas odaberite drugi paketomat.'); ?></p>
<?php endif; ?>
<?php foreach ($shippings AS $shipping): ?>
	<div class="shipping-row<?php if (!isset($shopping_cart_info['available_shippings_option'][$shipping['id']])): ?> not-available<?php endif; ?>">
		<?php $shipping_selected = ($selected_shipping == $shipping['id'] AND isset($shopping_cart_info['available_shippings_option'][$shipping['id']])); ?>
		<input type="radio" name="shipping" value="<?php echo $shipping['id']; ?>" id="field-shipping-<?php echo $shipping['id']; ?>" <?php if ($shipping_selected): ?>checked<?php endif; ?><?php if (!isset($shopping_cart_info['available_shippings_option'][$shipping['id']])): ?>disabled<?php endif; ?> <?php if (!empty($shipping['parcel_locker'])): ?>data-parcel_locker_initiator="<?php echo $shipping['id']; ?>"<?php endif; ?>>
		<label for="field-shipping-<?php echo $shipping['id']; ?>"><?php echo $shipping['title']; ?></label>
		<span class="shipping_info shipping_info_<?php echo $shipping['id']; ?> price cart_info_total_extra_shipping" <?php if ($shipping_selected): ?>style="display: none"<?php endif; ?>>
			<?php if (!$shopping_cart_info['total_extra_shipping']): ?>
				<?php echo Arr::get($cmslabel, 'free'); ?>
			<?php else: ?>
				<?php echo Utils::currency_format($shopping_cart_info['total_extra_shipping'] * $currency['exchange'], $currency['display']); ?>
				<?php echo Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
			<?php endif; ?>
		</span>

		<?php if (!empty($shipping['description'])): ?>
			<div class="shipping-note">
				<?php echo $shipping['description']; ?>
			</div>
		<?php endif; ?>

		<div class="shipping-note shipping-data shipping_info shipping_info_<?php echo $shipping['id']; ?>" <?php if (!$shipping_selected): ?>style="display: none"<?php endif; ?>>
			<?php if ($shipping['show_shipping_address']): ?>
				<div class="shipping-address">
					<?php echo $shipping_address; ?>
				</div>
			<?php endif; ?>
            <?php if ($shipping['api_class'] == 'woltdriveapi' AND $shipping['id'] == $active_wolt_venue_id): ?>
                <?php $active_error_code = Arr::get($_GET, 'errorcode', ''); ?>
                <span id="field-error-scheduled_dropoff" class="field_error error error-wolt" <?php if (empty($active_error_code) OR $active_error_code != 'venue_closed'): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, 'error_venue_closed', ''); ?></span>
                <div class="wolt-delivery-checkbox" data-delivery_time_box>
                    <?php if (!empty($promise_delivery_eta)): ?>
                        <?php echo Arr::get($cmslabel, 'estimated_delivery_time', 'Predviđeno vrijeme dostave: ') . ' <strong>' . Date::humanize($promise_delivery_eta, 'custom', 'd.m., H:i') .'</strong>'; ?>
                    <?php endif; ?><br />

                    <?php if (Kohana::config('app.webshop.shipping_use_shipment_promise_dropoff_scheduler')): ?>
                        <?php $stored_date = date('Y-m-d', Arr::get($customer_data, 'scheduled_dropoff_time', 0)); ?>
                        <?php $stored_time = date('H:i', Arr::get($customer_data, 'scheduled_dropoff_time', 0)); ?>
                        <?php $schedule_data = Utils::generate_wolt_scheduler($shipping['id'], $info['lang']); ?>
                        <?php $scheduler = (!empty($schedule_data['schedule'])) ? $schedule_data['schedule'] : []; ?>
                        <?php $min_preparation_time = (!empty($schedule_data['min_preparation_time'])) ? $schedule_data['min_preparation_time'] : 0; ?>
                        <?php $min_delivery_time = (!empty($schedule_data['min_delivery_time'])) ? $schedule_data['min_delivery_time'] : 0; ?>

                        <input type="checkbox" name="scheduled_dropoff" id="scheduled-dropoff" value="1" <?php if (!empty($customer_data['scheduled_dropoff'])): ?> checked <?php endif; ?> />
                        <label for="scheduled-dropoff" style="margin: 12px;"><?php echo Arr::get($cmslabel, 'wolt_delivery_time', 'Želim drugačije vrijeme isporuke'); ?></label>

                        <div class="delivery-time-input" data-delivery_time_input <?php if(empty($customer_data['scheduled_dropoff'])): ?>style="display: none;"<?php endif; ?>>
                            <div class="select-wrapper">
                                <span style="display: none;" class="select-time-title"><?php echo Arr::get($cmslabel, 'select_time'); ?></span>
                                <select data-wolt_date_picker name="scheduled_dropoff_date" data-min_preparation_time="<?php echo $min_preparation_time; ?>" data-min_delivery_time="<?php echo $min_delivery_time; ?>">
                                    <?php $i = 1; ?>
                                    <?php foreach ($scheduler as $option_value => $option_data): ?>
                                        <option <?php if ((!empty($stored_date) AND $option_value == $stored_date) OR (empty($stored_date) AND $i == 1)): ?> selected <?php endif; ?>
                                                data-date_opening_minute="<?php echo $option_data['opening_hour_minute']; ?>"
                                                data-date_opening_hour="<?php echo $option_data['opening_hour']; ?>"
                                                data-date_closing_minute="<?php echo $option_data['closing_hour_minute']; ?>"
                                                data-date_closing_hour="<?php echo $option_data['closing_hour']; ?>"
                                                value="<?php echo $option_value; ?>"><?php echo (!empty($label)) ? $label : $option_data['alternative_format']; ?></option>
                                        <?php $i++; ?>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="input-wrapper">
                                <input type="text" data-wolt_time_picker id="field-hour" name="scheduled_dropoff_time" <?php if(!empty($stored_time)): ?>value="<?php echo $stored_time; ?>" <?php endif; ?> placeholder="Vrijeme"/>
                                <div class="sw-datepicker sw-start-date-datepicker"></div>
                            </div>
                        </div>
                        <span id="field-error-scheduled_dropoff_time" class="field_error error error-scheduled-time"<?php if (empty($active_error_code) OR $active_error_code != 'venue_min_schedule_time'): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, 'error_venue_min_schedule_time', ''); ?></span>
                    <?php endif; ?>
                </div>

                <?php if (Kohana::config('app.webshop.shipping_use_shipment_promise') AND Kohana::config('app.webshop.shipping_use_shipment_promise_cod')): ?>
                    <div class="wolt-delivery-checkbox" data-prepared_cash_box>
                        <?php $prepared_cash = Arr::get($customer_data, 'prepared_cash', 0); ?>
                        <?php $prepared_cash_amount = Arr::get($customer_data, 'prepared_cash_amount', 0); ?>
                        <input type="checkbox" name="prepared_cash" value="1" id="no-prepared-cash-amount" <?php if (!empty($prepared_cash)): ?> checked <?php endif; ?>>
                        <label for="no-prepared-cash-amount"><?php echo Arr::get($cmslabel, 'prepared_cash_amount' , 'Nemam pripremljen točan iznos za naplatu'); ?></label><br/>
                        <div class="cash-amount" data-cash_amount_input <?php if(empty($customer_data['prepared_cash'])): ?>style="display: none;"<?php endif; ?>>
                            <span class="label-wolt"><?php echo Arr::get($cmslabel, 'prepared_cash'); ?></span>
                            <span class="input-cash"><input type="number" name="prepared_cash_amount" <?php if (!empty($prepared_cash_amount)): ?>value="<?php echo $prepared_cash_amount; ?>"<?php endif; ?>/></span>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
			<?php if (isset($shipping['widget_content']) AND $shipping['widget_content']): ?>
				<?php echo $shipping['widget_content']; ?>

				<?php $locations = Widget_Location::points(['lang' => $info['lang'], 'available_pickup' => TRUE]); ?>
				<?php if(!empty($locations) AND empty($shipping['parcel_locker'])): ?>
					<?php foreach($locations as $location): ?>
						<div class="shipping-location-address" data-id="<?php echo $location['id']; ?>"><strong><?php echo Arr::get($cmslabel, 'address'); ?>:</strong> <?php echo strip_tags($location['address']); ?></div>
					<?php endforeach; ?>
				<?php endif; ?>
			<?php endif; ?>
		</div>
	</div>
<?php endforeach; ?>

<?php if($step != 'shipping'): ?>
	<div class="field checkout-field-message">
		<textarea id="field-message" name="message" cols="40" rows="8" class="field_text field_text_message"><?php echo Arr::get($customer_data, 'message'); ?></textarea>
		<label for="field-message" class="label-message"><?php echo Arr::get($cmslabel, 'checkout_message', 'Napomena'); ?></label>
		<span id="field-error-message" class="field_error error" style="display: none"></span>
	</div>
<?php endif; ?>