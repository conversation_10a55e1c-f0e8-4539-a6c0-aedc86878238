<?php if (!empty($shopping_cart_info['total_extra_extraitem_priority_order_code'])): ?>
    <?php $shopping_cart_info['total_extra_extraitem_priority_order_id'] = $shopping_cart_info['total_extra_extraitem_priority_order_id'][0]; ?>
    <?php $priority_order = Webshop::extra_items(['filters' => ['code' => 'priority_order']]); ?>
    <?php $priority_order = reset($priority_order); ?>
    <?php $class = (isset($class)) ? ' ' . $class : ''; ?>
    <?php $customer_data = Utils::data('customer_data'); ?>
    <div class="priority-order<?php echo $class; ?>" id="extraitem-<?php echo $shopping_cart_info['total_extra_extraitem_priority_order_id']; ?>" data-id="<?php echo $shopping_cart_info['total_extra_extraitem_priority_order_id']; ?>">
        <input type="checkbox" name="extraitem_priority" id="field-priority" data-id="<?php echo $shopping_cart_info['total_extra_extraitem_priority_order_id']; ?>" <?php echo (is_array(Arr::get($customer_data, 'extraitems')) AND in_array($shopping_cart_info['total_extra_extraitem_priority_order_id'], Arr::get($customer_data, 'extraitems'))) ? "checked" : "" ?>>
        <label for="field-priority"><?php echo str_replace(['<p>', '</p>'], ['', ''], str_replace('%PRIORITY_ORDER_PRICE%', Utils::currency_format($priority_order['total'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($priority_order['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']), Arr::get($cmslabel, 'priority_order'))); ?></label>
    </div>
<?php endif; ?>