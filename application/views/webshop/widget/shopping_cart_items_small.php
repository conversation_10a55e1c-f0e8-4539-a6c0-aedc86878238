<div class="shoppingcart_items_small ww-cart">
    <div class="wc-col-cnt wc-col-cart">
        <div class="ww-cart-header">
            <h2 class="wc-title w-cart-title"><?php echo Arr::get($cmslabel, 'products_in_shopping_cart', 'U košarici'); ?> <span class="counter">(<?php echo count($products); ?>)</span></h2>
            <a class="w-btn-change" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>"><span><?php echo Arr::get($cmslabel, 'change_cart', '<PERSON><PERSON>je<PERSON> sad<PERSON> k<PERSON>'); ?></span></a>
        </div>

        <!-- Cart items -->
        <div class="ww-cart-btns">
            <a class="btn-toggle-cart" href="javascript:toggleBox(['.ww-cart-items', '.btn-toggle-cart']);"><?php echo Arr::get($cmslabel, 'toggle_cart_items'); ?></a>
        </div>
        <div class="ww-cart-items">
            <?php $i=1; ?>
            <?php
            if (!empty($products)) {
                $products = Arr::merge($products, $products_status);
                $pickup_products = array_filter(array_map(function ($element) {
                    return (in_array($element['type'], Kohana::config('app.catalog.product_type.pickup_only'))) ? $element['shopping_cart_code'] : NULL;
                }, $products));
            }
            ?>
            <?php if (!empty($pickup_products)) : ?>
                <?php $locations = Widget_Location::points(['lang' => $info['lang'], 'available_pickup' => TRUE]); ?>
            <?php endif;?>
            <?php foreach ($products as $product_code => $product_data): ?>
                <?php $product_status = $products_status[$product_code]; ?>
                <?php echo View::factory('webshop/shopping_cart_entry', ['product_status' => $product_status, 'product_code' => $product_code, 'product_data' => $product_data, 'i' => $i, 'mode' => 'checkout', 'locations' => (!empty($locations) ? $locations : [])]); ?>
                <?php $i++; ?>
            <?php endforeach; ?>
        </div>
    </div>
</div>