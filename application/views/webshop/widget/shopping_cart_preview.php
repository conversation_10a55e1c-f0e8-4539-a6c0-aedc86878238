<div class="ww-preview-header">
	<div class="ww-preview-title<?php if ($shopping_cart['total_items']): ?> active<?php endif; ?>" data-shoppingcart_active="1"><?php echo Arr::get($cmslabel, 'in_cart'); ?>
		<span class="ww-preview-count">(<span class="cart_info_item_count"><?php echo $shopping_cart['item_count']; ?></span>)</span>
	</div>
	<a class="ww-preview-close" href="javascript:void(0);" data-shopping_cart_preview_active="1"></a>
</div>

<div class="ww-preview-items-container shoppingcart_items_small">
	<?php $shopping_cart_products = (!empty($shopping_cart_products)) ? array_reverse($shopping_cart_products) : []; ?>
	<!-- Cart items -->
	<div class="ww-preview-items">
		<div class="shopping_cart_preview_items">
			<div class="ww-preview-table">
				<?php foreach ($shopping_cart_products as $product_code => $product_data): ?>
				<?php $i = 1;?>
					<?php $product_status = $shopping_cart_status[$product_code]; ?>
					<div id="product-<?php echo $product_code; ?>" class="ww-item">
						<div class="ww-image">
							<a href="<?php echo $product_data['url']; ?>">
								<img <?php echo Thumb::generate($product_data['main_image'], array('width' => 50, 'height' => 50, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-50.jpg')); ?> alt="<?php echo $product_data['title']; ?>"/>
							</a>
						</div>
						<div class="ww-cnt">
							<div class="ww-title">
								<a href="<?php echo $product_data['url']; ?>"><?php echo $product_data['title']; ?></a>
							</div>
							<div class="df aic ww-bottom">
								<div class="ww-price">
									<?php if (round($product_status['total_basic'], 2) > round($product_status['total'], 2)): ?>
										<div class="ww-old-price">
											<span><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></span>
											<?php echo Utils::get_second_pricetag_string($product_status['total_basic'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
										</div>
										<div class="ww-discount-price">
											<?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?>
											<?php echo Utils::get_second_pricetag_string($product_status['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
										</div>
										<?php if(!empty($product_status['extra_price_lowest']) AND $product_status['extra_price_lowest'] > 0): ?>
											<div class="ww-lowest-price">
												<?php echo Arr::get($cmslabel, 'lowest_price'); ?>:
												<?php echo Utils::currency_format($product_status['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
												<?php echo Utils::get_second_pricetag_string($product_status['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</div>
										<?php endif; ?>
									<?php else: ?>
										<?php if (!empty($user->b2b)): ?>
											<div class="wp-current-price">
												<?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?>
												<?php echo Utils::get_second_pricetag_string($product_status['total_basic'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</div>
										<?php else: ?>
											<?php if (!empty($shopping_cart['total_extra_loyalty']) AND !empty($product_status['total_items_loyalty']) AND $product_data['type'] != 'coupon'): ?>
												<div class="ww-old-price">
													<span><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></span>
													<?php echo Utils::get_second_pricetag_string($product_status['total_basic'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
												</div>
												<div class="ww-discount-price">
													<?php echo Utils::currency_format($product_status['total_items_loyalty'] * $currency['exchange'], $currency['display']); ?>
													<?php echo Utils::get_second_pricetag_string($product_status['total_items_loyalty'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
												</div>
											<?php else: ?>
												<div class="ww-current-price">
													<?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?>
													<?php echo Utils::get_second_pricetag_string($product_status['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
												</div>
											<?php endif; ?>
										<?php endif; ?>
									<?php endif; ?>
								</div>
								<a class="wp-btn ww-btn-delete" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', 'remove', 5);" title="<?php echo Arr::get($cmslabel, 'remove_product', 'Obriši'); ?>"></a>
								<div class="ww-qty-container">
									<div class="wp-qty ww-qty">
										<?php $package_qty = (!empty($product_data['package_qty'])) ? $product_data['package_qty'] : 1; ?>
										<a class="wp-btn-qty wp-btn-dec" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '-', 5, 1, 0, 0, <?php echo $package_qty ?>);"><span class="toggle-icon"></span></a>
										<input class="wp-input-qty product_qty_input" type="text" name="qty_webshop_preview[<?php echo $product_code; ?>]" value="<?php echo $product_status['qty']; ?>"/>
										<a class="wp-btn-qty wp-btn-inc" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '+', 5, 1, 0, 0, <?php echo $package_qty ?>);"><span class="toggle-icon"></span></a>
										<span class="wp-message ww-message product_message product_message_<?php echo $product_code; ?>" style="display: none"></span>
									</div>
								</div>
							</div>
						</div>
					</div>
				<?php $i++;?>
				<?php endforeach; ?>
			</div>
		</div>
		<div class="empty-shopping-cart-preview empty_shopping_cart"><?php echo Arr::get($cmslabel, 'empty_shopping_cart'); ?></div>
	</div>
</div>

<div class="ww-preview-bottom">
	<?php echo View::factory('webshop/widget/coupon', array('shopping_cart_info' => $shopping_cart, 'mode' => 'preview')); ?>
	<?php echo View::factory('webshop/widget/priority_order', array('shopping_cart_info' => $shopping_cart, 'class' => 'quick-priority-order')); ?>
	<?php echo View::factory('webshop/widget/loyalty_preview', array('shopping_cart_info' => $shopping_cart, 'shopping_cart_preview' => true, 'customer_data' => Arr::extract($customer_data, ['loyalty_request_new']))); ?>
	<?php echo View::factory('webshop/widget/total', ['shopping_cart' => $shopping_cart, 'shopping_cart_info' => $shopping_cart, 'mode' => 'preview']); ?>

	<!-- Min order alert -->
	<?php if (isset($shopping_cart['total_extra_shipping_min_total_error']) AND $shopping_cart['total_extra_shipping_min_total_error']): ?>
		<div class="ww-minprice minprice-tooltip" style="display:none;">
			<?php
			echo str_replace(array(
				'%TOTAL_MIN%',
				'%TOTAL_MISSING%',
			), array(
				Utils::currency_format($shopping_cart['total_extra_shipping_min_total'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_min_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
				Utils::currency_format($shopping_cart['total_extra_shipping_min_total_missing'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_min_total_missing'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
			), Arr::get($cmslabel, 'minimal_order_price_full'));

			?>
		</div>
	<?php endif; ?>

	<div class="ww-preview-buttons">
		<a class="btn btn-orange ww-btn-view" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>"><span><?php echo Arr::get($cmslabel, 'view_shopping_cart'); ?></span></a>
		<?php /* ?><a class="btn btn-orange ww-btn-finish" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>"><span><?php echo Arr::get($cmslabel, 'finish_shopping'); ?></span></a> */?>
	</div>

	<?php echo View::factory('webshop/widget/shipping_bar', ['shopping_cart_info' => $shopping_cart]); ?>
</div>