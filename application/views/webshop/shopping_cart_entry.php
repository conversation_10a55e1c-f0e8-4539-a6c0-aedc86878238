<?php $mode = (isset($mode)) ? $mode : ''; ?>
<?php $pickup_only = (!empty($product_data['type']) AND in_array($product_data['type'], Kohana::config('app.catalog.product_type.pickup_only'))); ?>
<div class="wp<?php if (!empty($pickup_only)): ?> wp-pickup-only<?php endif; ?>" id="product_details_<?php echo $product_code; ?>">
	<input type="hidden" name="product_price" value="<?php echo $product_status['price'] ?>" />
	<input type="hidden" name="product_total" value="<?php echo $product_status['total'] ?>" />
    <span data-tracking_gtm_impression="<?php echo $i; ?>|<?php echo $product_data['code']; ?>">
	<span style="display: none;" data-product_code="<?php echo $product_code; ?>"><?php echo Arr::get($product_data, 'code'); ?></span>
	<span style="display: none;" data-product_title="<?php echo $product_code; ?>"><?php echo Arr::get($product_data, 'title'); ?></span>
	<span style="display: none;" data-product_category_title="<?php echo $product_code; ?>"><?php echo Arr::get($product_data, 'category_title'); ?></span>
	<span style="display: none;" data-product_category="<?php echo $product_code; ?>"><?php echo 'Kategorija ' . Arr::get($product_data, 'category_title'); ?></span>
	<span style="display: none;" data-product_manufacturer_title="<?php echo $product_code; ?>"><?php echo Arr::get($product_data, 'manufacturer_title'); ?></span>
	<span style="display: none;" data-product_price="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['price']); ?></span>
    </span>

	<div class="wp-image">
		<figure>
			<a href="<?php echo $product_data['url']; ?>">
				<img <?php echo Thumb::generate($product_data['main_image'], array('width' => 70, 'height' => 70, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-50.jpg')); ?> alt="<?php echo $product_data['title']; ?>" />
			</a>
		</figure>
	</div>

	<div class="wp-content">
		<div class="wp-cnt">
			<div class="wp-title">
				<a href="<?php echo $product_data['url']; ?>"><?php echo $product_data['title']; ?></a>
				<?php /*if ($product_data['attributes']): ?>
					<span class="wp-attribute"><?php echo $product_data['attributes']; ?></span>
				<?php endif;*/ ?>
			</div>
			<?php /* ?><div class="wp-code"><?php echo Arr::get($cmslabel, 'code', 'Šifra'); ?>: <?php echo (!empty($product_data['variation_code'])) ? $product_data['variation_code'] : $product_data['code']; ?></div> */?>
			<?php if(!empty($product_data['product_code'])): ?><div class="wp-code"><?php echo Arr::get($cmslabel, 'code'); ?>: <?php echo $product_data['product_code']; ?></div><?php endif; ?>
            <?php if (!empty($pickup_only)): ?>
                <?php
                $pickup_available_count = count(Arr::get($product_status, 'warehouses', []));
                $locations = (!empty($locations)) ? $locations : Widget_Location::points(['lang' => $info['lang'], 'available_pickup' => TRUE]);
                if (!empty($locations)) {
                    $location_titles = '';
                    $location_ids = array_keys(Arr::get($product_status, 'warehouses', []));
                    $last_key = end($location_ids);
                    foreach ($locations as $location_id => $location_data) {
                        if (in_array($location_id, array_keys(Arr::get($product_status, 'warehouses', [])))) {
                            $location_titles .= (($location_id == $last_key) ? $location_data['title'] : $location_data['title'] . ', ');
                        }
                    }
                }
                ?>
                <span class="cp-badge wp-badge"><?php echo Arr::get($cmslabel, 'pickup_only'); ?> (<?php echo $pickup_available_count; ?>): <?php echo (!empty($location_titles) ? $location_titles : ''); ?></span>
            <?php endif; ?>
			<?php if (!empty($product_data['variation'])): ?>
				<div class="wp-variations">
					<?php if ($product_data['variation_attributes']): ?>
						<?php $variation_items = explode(',', $product_data['variation_attributes']) ?>
						<?php foreach ($variation_items as $variation_item): ?>
							<p><?php echo $variation_item ?></p>
						<?php endforeach; ?>
					<?php elseif ($product_data['variation_title']): ?>
						<?php echo $product_data['variation_title']; ?>
					<?php elseif ($product_data['variation_code']): ?>
						<?php echo $product_data['variation_code']; ?>
					<?php endif; ?>
				</div>
			<?php endif; ?>

			<?php if (!empty($product_data['coupon_price'])): ?>
				<?php echo Arr::get($cmslabel, 'coupon_value', 'Vrijednost kupona'); ?>: <?php echo $product_data['coupon_price']; ?>
			<?php elseif (!empty($product_data['bonus_total'])): ?>
				<?php echo Arr::get($cmslabel, 'bonus_total_value'); ?>: <?php echo $product_data['bonus_total']; ?>
			<?php elseif ($product_data['type'] == 'download'): ?>
				<?php echo Arr::get($cmslabel, 'download_files', 'Preuzimanje datoteka'); ?>
			<?php endif; ?>

			<?php if($mode != 'checkout'): ?>
				<div class="wp-btns">
					<?php if(!empty($pickup_only)): ?>
						<a class="wp-btn wp-btn-delete" href="javascript:cmswebshop.shopping_cart.remove_all('<?php echo $product_code; ?>');"><span><?php echo Arr::get($cmslabel, 'remove_product', 'Obriši'); ?></span></a>
					<?php else: ?>
						<a class="wp-btn wp-btn-delete" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', 'remove');"><span><?php echo Arr::get($cmslabel, 'remove_product', 'Obriši'); ?></span></a>
					<?php endif; ?>
					<?php if (isset($product_data['wishlist_widget']) AND $product_data['wishlist_widget'] AND !$product_data['wishlist_widget']['active']): ?>
						<a class="wp-btn wp-wishlist wishlist_set_<?php echo $product_data['wishlist_widget']['content']; ?>" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', 'remove');cmswishlist.set('<?php echo $product_data['wishlist_widget']['content']; ?>');" data-wishlist_operation="+|removelink"><?php echo Arr::get($cmslabel, 'move_to_wishlist'); ?></a>
					<?php endif; ?>
				</div>
			<?php endif; ?>
		</div>
		<?php if($mode != 'checkout'): ?>
			<div class="wp-qty-container">
				<div class="wp-qty">
					<?php $package_qty = (!empty($product_data['package_qty'])) ? $product_data['package_qty'] : 1; ?>
					<a class="wp-btn-qty wp-btn-dec" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '-', 0, 1, 0, 0, <?php echo $package_qty; ?>);"><span class="toggle-icon"></span></a>
					<input class="wp-input-qty product_qty_input" tabindex="<?php echo $i; ?>" type="text" name="qty[<?php echo $product_code; ?>]" value="<?php echo $product_status['qty']; ?>" />
					<a class="wp-btn-qty wp-btn-inc" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '+', 0, 1, 0, 0, <?php echo $package_qty; ?>);"><span class="toggle-icon"></span></a>
				</div>
				<?php $unit = (!empty($product_data['unit'])) ? Inflector::units($product_data['unit'], $info['lang']) : Arr::get($cmslabel, 'unit', 'kom'); ?>
				<span class="wp-unit"><?php echo $unit; ?></span>
				<span class="wp-message product_message product_message_<?php echo $product_code; ?>" style="display: none"></span>
			</div>
		<?php endif; ?>
		<div class="wp-total">
			<?php if (round($product_status['total_basic'], 2) > round($product_status['total'], 2)): ?>
				<div class="wp-old-price">
					<span class="product_total_basic" data-display_format="<?php echo Kohana::config('app.utils.kn_euro_conversion.display_format.none'); ?>" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></span>
					<span class="product_total_basic_second" data-display_format="<?php echo Kohana::config('app.utils.kn_euro_conversion.display_format.none'); ?>" data-currency_format="full_price_currency" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total_basic'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
				</div>
				<div class="wp-discount-price">
					<span class="product_total" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span>
					<span class="product_total_second" data-currency_format="full_price_currency" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
				</div>
			<?php else: ?>
				<?php if (!empty($user->b2b)): ?>
					<div class="wp-current-price">
						<span class="product_total" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></span>
						<span class="product_total_second" data-currency_format="full_price_currency" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total_basic'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
					</div>
				<?php else: ?>
					<?php /* 
					 	FIXME PROG na cijenu treba vjerojatno dodati neki data tag da se ispravno izračuna cijena 
						Scenarij:
						1. U incognito dodaš u košaricu proizvod http://tzh.markerdev.info/alfaalfa-lucerna-sjemenke-za-klijanje-organske-250g-nutrigold-proizvod-54145/
						2. U košarici označiš kvačicu za loyalty
						3. Cijena proizvoda se ažurira ali krivo. Ako se refreša s stranica, onda je ok https://snipboard.io/AeKDd7.jpg
					*/ ?>

                    <?php if (!empty($shopping_cart['total_extra_loyalty']) AND !empty($product_status['total_items_loyalty'] AND $product_data['type'] != 'coupon')): ?>
						<div class="wp-old-price">
							<span class="product_total_basic" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></span>
							<span class="product_total_basic_second" data-currency_format="full_price_currency" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total_basic'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
						</div>
						<div class="wp-discount-price">
							<span class="product_total" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total_items_loyalty'] * $currency['exchange'], $currency['display']); ?></span>
							<span class="product_total_second" data-currency_format="full_price_currency" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total_items_loyalty'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
						</div>
					<?php else: ?>
						<div class="wp-current-price">
							<span class="product_total" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span>
							<span class="product_total_second" data-currency_format="full_price_currency" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
						</div>
					<?php endif; ?>
				<?php endif; ?>
			<?php endif; ?>
			<?php $price = (!empty($shopping_cart['total_extra_loyalty']) AND !empty($product_status['loyalty_price']) AND $product_data['type'] != 'coupon') ? $product_status['loyalty_price'] : $product_status['price']; ?>

			<div class="wp-qty-count" data-shoppingcart_product_qty_box="<?php echo $product_code; ?>" <?php if ((float) $product_status['qty'] == 1): ?> style="display: none;"<?php endif; ?>>
				<?php if (!empty($product_status['related_item_price_qty'])): ?>
					<?php if ($product_status['related_item_price_qty'] == $product_status['qty']): ?>
						<span class="product_qty"><?php echo $product_status['qty']; ?></span> x 
						<?php echo Utils::currency_format($product_status['related_item_price'] * $currency['exchange'], $currency['display']); ?>
						<?php echo Utils::get_second_pricetag_string($product_status['related_item_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
					<?php else: ?>
					<?php $qty = ($product_status['qty'] - $product_status['related_item_price_qty']); ?>
						<?php $price = ((!empty($shopping_cart['total_extra_loyalty']) AND !empty($product_status['loyalty_price'])) ? $product_status['loyalty_price'] : $product_status['price']); ?>
						<span class="product_qty_1"> <?php echo $product_status['related_item_price_qty']; ?></span> x <?php echo Utils::currency_format($product_status['related_item_price'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($product_status['related_item_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?> + <span class="product_qty_2"><?php echo $qty; ?></span> x <?php echo Utils::currency_format($price * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
					<?php endif; ?>
				<?php else: ?>
					<span class="product_qty"><?php echo $product_status['qty']; ?></span> x 
					<?php echo Utils::currency_format($price * $currency['exchange'], $currency['display']); ?>
					<?php echo Utils::get_second_pricetag_string($price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
				<?php endif; ?>
			</div>

			<?php if ($product_status['total_basic'] > $product_status['total']): ?>
				<?php if(!empty($product_status['extra_price_lowest']) AND $product_status['extra_price_lowest'] > 0): ?>
					<div class="wp-lowest-price">
						<?php echo Arr::get($cmslabel, 'lowest_price'); ?>:
						<?php echo Utils::currency_format($product_status['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
						<?php echo Utils::get_second_pricetag_string($product_status['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
					</div>
				<?php endif; ?>
			<?php endif; ?>
		</div>
	</div>	
</div>