<div class="ww-coupons ww-auth-coupons">
	<!-- Add Coupon Form -->
	<div class="ww-coupons-form ww-auth-coupons-form" data-form="save_for_later_webshop_coupon">
		<label class="ww-coupons-label" for="coupon_code"><?php echo Arr::get($cmslabel, 'coupon_have_coupon'); ?></label>
		<div class="ww-coupons-add ww-auth-coupons-add">
			<input class="ww-coupons-input" type="text" name="coupon_code" id="coupon_code" placeholder="<?php echo Arr::get($cmslabel, 'coupon_enter_code'); ?>" />
			<a class="btn btn-blue ww-btn-add ww-coupons-add" href="javascript:cmscoupon.save_for_later('webshop_coupon')"><span><?php echo Arr::get($cmslabel, 'coupon_btn_add', 'Dodaj'); ?></span></a>
		</div>
		<div class="coupon_message coupon-message" style="display: none"></div>
	</div>

	<!-- List of available coupons -->
	<div class="clear ww-coupons-list ww-auth-coupons-list">
		<table class="ww-coupons-table ww-auth-coupons-table" id="webshop_coupon_list" <?php if (!count($items)): ?>style="display: none"<?php endif; ?>>
			<?php if (sizeof($items)): ?>
				<?php foreach ($items as $item): ?>
					<?php echo View::factory('webshop/widgetlist/coupon_entry', ['item' => $item]); ?>
				<?php endforeach; ?>
			<?php endif; ?>
		</table>
	</div>
</div>