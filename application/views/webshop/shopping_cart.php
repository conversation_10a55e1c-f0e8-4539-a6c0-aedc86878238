<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-cart white-bg<?php $this->endblock('page_class'); ?>

<?php $this->block('after_header'); ?>
	<div class="wrapper wishlist-header w-header">
		<h1 class="wishlist-title w-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?><span class="wishlist-title-counter w-title-counter"><span class="cart_info_item_count total_items<?php if ($shopping_cart['total_items']): ?> active<?php endif; ?>" data-shoppingcart_active="1"><?php echo $shopping_cart['item_count']; ?></span></span></h1>
	</div>
<?php $this->endblock('after_header'); ?>

<?php $this->block('main'); ?>
    <style>
        /*   FIXME INTEG rijesiti css */
        .disabled {
            pointer-events: none;
            opacity: 0.5;
        }
    </style>
<?php
if (!empty($products)) {
    $products = Arr::merge($products, $products_status);
    $pickup_products = array_filter(array_map(function ($element) {
        return (in_array($element['type'], Kohana::config('app.catalog.product_type.pickup_only'))) ? $element['shopping_cart_code'] : NULL;
    }, $products));

    $customer_data = Utils::data('customer_data');

    if (!empty($products_status)) {
        $available_for_pickup_products = array_filter(array_map(function ($element) use ($customer_data) {
            return (in_array($element['type'], Kohana::config('app.catalog.product_type.pickup_only')) and in_array(Arr::get($customer_data, 'shipping_pickup_location', 0), array_keys($element['warehouses']))) ? $element : NULL;
        }, $products));

        $not_available_for_pickup_products = array_filter(array_map(function ($element) use ($customer_data) {
            return (in_array($element['type'], Kohana::config('app.catalog.product_type.pickup_only')) and !in_array(Arr::get($customer_data, 'shipping_pickup_location', 0), array_keys($element['warehouses']))) ? $element : NULL;
        }, $products));
    }
}
?>

<div class="wrapper">
	<?php if (sizeof($products)): ?>
		<div id="view_cart" class="df w-row">
			<div class="w-col w-col1">
				<div class="w-col1-cnt">
					<?php echo Arr::get($cms_page, 'content'); ?>
					<form action="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'set_qty', FALSE); ?>" method="post" name="shopping_cart">
						<?php echo View::factory('webshop/widget/loyalty_new', array('shopping_cart_info' => $shopping_cart, 'shopping_cart_preview' => true, 'customer_data' => Arr::extract($customer_data, ['loyalty_request_new']))); ?>
						<!-- Cart table -->
                        <?php /*
						<div class="w-col-pickup<?php if (!empty($pickup_products)): ?> active<?php endif; ?>" data-pickup_only_message>
							<?php echo Arr::get($cmslabel, 'pickup_only_products_message'); ?>
						</div> */ ?>

                        <?php if (!empty($pickup_products)) : ?>
                            <div class="wp-pickup-products">
                                <div class="status-info-label"><?php echo Arr::get($cmslabel, 'pickup_info'); ?></div>
                                <div class="wp-pickup-products-info"><?php echo Arr::get($cmslabel, 'cart_availability_onlyinoffice'); ?></div>
                                <?php $locations = Widget_Location::points(['lang' => $info['lang'], 'available_pickup' => TRUE]); ?>
                                <?php if (!empty($locations) and empty($shipping['parcel_locker'])): ?>
                                    <span class="wp-pickup-select-label">
										<?php echo Arr::get($cmslabel, 'select_store'); ?>
									</span>
                                    <select data-shipping_pickup_location="1" id="field-shipping_pickup_location" name="shipping_pickup_location">
                                        <option value=""><?php echo Arr::get($cmslabel, 'select_store_label'); ?></option>
                                        <?php foreach ($locations as $location): ?>
                                            <option <?php if (!empty($customer_data['shipping_pickup_location']) AND $customer_data['shipping_pickup_location'] == $location['id']) : ?>selected<?php endif; ?> value="<?php echo $location['id']; ?>"> <?php echo strip_tags($location['title']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>


						<div id="items_shoppingcart">
                            <?php
                            if (!empty($available_for_pickup_products)) {
                                $products = array_diff_key($products, $available_for_pickup_products);
                            }
                            if (!empty($not_available_for_pickup_products)) {
                                $products = array_diff_key($products, $not_available_for_pickup_products);
                            }
                            ?>
                            <?php if (!empty($available_for_pickup_products)) : ?>
                                <?php $i=1; ?>
                                <?php foreach ($available_for_pickup_products as $product_code => $product_data_available_for_pickup): ?>
                                    <?php echo View::factory('webshop/shopping_cart_entry', ['product_code' => $product_code, 'product_data' => $product_data_available_for_pickup, 'product_status' => $products_status[$product_code], 'i' => $i, 'locations' => (!empty($locations) ? $locations : [])]); ?>
                                    <?php $i++; ?>
                                <?php endforeach; ?>
                            <?php endif; ?>

                            <?php $i=1; ?>
                            <?php foreach ($products as $product_code => $product_data): ?>
                                <?php echo View::factory('webshop/shopping_cart_entry', ['product_code' => $product_code, 'product_data' => $product_data, 'product_status' => $products_status[$product_code], 'i' => $i, 'locations' => (!empty($locations) ? $locations : [])]); ?>
                                <?php $i++; ?>
                            <?php endforeach; ?>

                            <?php if (!empty($not_available_for_pickup_products)) : ?>
                                <div class="wp-notavailable-for-pickup-products">
                                    <div class="wp-pickup-notavailable"><?php echo str_replace('%TOTAL_PRODUCTS%', count($not_available_for_pickup_products), Arr::get($cmslabel, 'unable_to_complete_order')); ?></div>
                                    <?php $codes = implode(',', array_keys($not_available_for_pickup_products)); ?>
                                    <a class="wp-btn wp-btn-delete wp-pickup-btn-delete" href="javascript:cmswebshop.shopping_cart.remove_all('<?php echo $codes; ?>');"><span><?php echo Arr::get($cmslabel, 'remove_products', 'Ukloni ove proizvode iz košarice'); ?></span></a>
                                    <?php $i=1; ?>
                                    <?php foreach ($not_available_for_pickup_products as $product_code => $product_data_not_available_for_pickup): ?>
                                        <?php echo View::factory('webshop/shopping_cart_entry', ['product_code' => $product_code, 'product_data' => $product_data_not_available_for_pickup, 'product_status' => $products_status[$product_code], 'i' => $i, 'locations' => (!empty($locations) ? $locations : [])]); ?>
                                        <?php $i++; ?>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
						</div>

						<!-- free delivery widget -->
		    			<?php echo View::factory('webshop/widget/free_delivery'); ?>

						<?php $wishlist_items = Widget_Catalog::products(array('lang' => $info['lang'], 'special_view' => 'wishlist', 'limit' => 0)); ?>
						<div class="w-wishlist" id="view_wishlist" <?php if (!$wishlist_items): ?>style="display: none"<?php endif; ?>>
							<div class="w-wishlist-btns w-wishlist-header">
								<h2 class="wishlist-title w-wishlist-title">
									<?php echo Arr::get($cmslabel, 'wishlist'); ?>
									<span class="wishlist-title-counter w-title-counter"><span class="wishlist_count<?php if ($wishlist AND $wishlist['total_items'] > 0): ?> active<?php endif; ?>" data-wishlist_active="1"><?php echo $wishlist['total_items']; ?></span></span>
								</h2>
								<a class="btn btn-white btn-move-to-cart" href="javascript:cmswebshop.shopping_cart.add_special_list('wishlist', 'all');"><span><?php echo Arr::get($cmslabel, 'move_all_to_cart'); ?></span></a>
							</div>
							<div id="items_wishlist" class="cart-wishlist-items">
								<?php echo View::factory('catalog/index_entry_wishlist', array('items' => $wishlist_items, 'list' => 'Lista želja', 'ga4_list' => 'Besplatna dostava')); ?>
							</div>
							<?php if(count($wishlist_items) > 5): ?>
								<div class="w-wishlist-btns w-wishlist-footer">
									<a class="btn btn-white btn-move-to-cart" href="javascript:cmswebshop.shopping_cart.add_special_list('wishlist', 'all');"><span><?php echo Arr::get($cmslabel, 'move_all_to_cart'); ?></span></a>
								</div>
							<?php endif; ?>
						</div>
					</form>
				</div>
			</div>
			<div class="w-col w-col2">
				<div class="w-col2-cnt-top"></div>
				<div class="w-col-cnt w-col2-cnt">
					<!-- Coupon -->
					<?php echo View::factory('webshop/widget/coupon', array('shopping_cart_info' => $shopping_cart_info)); ?>
					<?php echo View::factory('webshop/widget/priority_order',  array('shopping_cart_info' => $shopping_cart_info)); ?>
					<?php echo View::factory('webshop/widget/total', array('shopping_cart_info' => $shopping_cart_info)); ?>
					
					<!-- Finish shopping -->
                    <a class="btn btn-orange w-btn-finish cart-finish-shopping <?php if ((empty($customer_data['shipping_pickup_location']) AND !empty($available_for_pickup_products)) OR !empty($not_available_for_pickup_products)) : ?>disabled<?php endif;?>"  href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>#webshop_form"><span><?php echo Arr::get($cmslabel, 'finish_shopping', 'Dovrši kupovinu'); ?></span></a>

                    <?php if ((empty($customer_data['shipping_pickup_location']) AND !empty($available_for_pickup_products)) OR !empty($not_available_for_pickup_products)) : ?>
                        <div class="wp-unable-to-completeorder"><?php echo Arr::get($cmslabel, 'unable_to_complete_order_2', 'Za ovu narudžbu moguća je samo Wolt dostava ili osobno preuzimanje u odabranoj dostupnoj poslovnici');?></div>
                    <?php endif;?>

					<?php echo View::factory('webshop/widget/shipping_bar', ['shopping_cart_info' => $shopping_cart_info]); ?>

					<!-- Min order alert -->
					<?php if (isset($shopping_cart_info['total_extra_shipping_min_total_error']) AND $shopping_cart_info['total_extra_shipping_min_total_error']): ?>
						<div class="w-minprice minprice-tooltip" style="display:none;">
							<?php echo str_replace(array(
									'%TOTAL_MIN%',
									'%TOTAL_MISSING%',
									),
								array(
									Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
									Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total_missing'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total_missing'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
								),
								Arr::get($cmslabel, 'minimal_order_price_full'));
							?>
						</div>
					<?php endif; ?>
				</div>
			</div>
		</div>
		<div id="empty_shopping_cart" class="empty-cart" style="display: none;"><?php echo Arr::get($cmslabel, 'empty_shopping_cart'); ?></div>
	<?php else: ?>
		<div class="empty-cart"><?php echo Arr::get($cmslabel, 'empty_shopping_cart'); ?></div>
	<?php endif; ?>
</div>
<?php $this->endblock('main'); ?>