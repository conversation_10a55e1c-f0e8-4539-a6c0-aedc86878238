<?php $this->extend('default_mail_html'); ?>
<?php $this->block('title'); ?><?php echo $title; ?> | <?php echo Kohana::config('app')->get('sitename'); ?><?php $this->endblock('title'); ?>
<?php $this->block('header'); ?><?php echo Arr::get($cmslabel, (!empty($gift_mode) AND $gift_mode == 'userbirthday') ? 'birthday_header' : 'gift_header', Arr::get($cmslabel, 'mail_header', '')); ?><?php $this->endblock('header'); ?>

<?php $this->block('content'); ?>

    <p><?php if (isset($item->user->last_name) AND $item->user->last_name): ?><?php echo Arr::get($cmslabel, 'dear_mr_mrs', __('Poštovani g/gđo')); ?> <?php echo $item->user->last_name; ?>,<?php else: ?><?php echo Arr::get($cmslabel, 'dear', __('Poštovani')); ?>,<?php endif; ?></p>

<?php
$default_label = 'mail_gift';
$default_text = __('<p>%GIFT_FROM% Vam šalje poklon bon za webshop <a href="%SITE_URL%">%SITE_DOMAIN%</a> u iznosu od <strong>%DISCOUNT_VALUE%</strong>.<br/>Kod za korištenje kupona je</p><p style="text-align: center"><strong style="font-size: 32px;">%COUPON_CODE%</strong></p>');

if (!empty($gift_mode) AND $gift_mode == 'expire') {
    $extra_label = "mail_gift_mode_{$gift_mode}";
    $default_text = __('<p>%GIFT_FROM% Vam šalje podsjetnik za poklon bon za webshop <a href="%SITE_URL%">%SITE_DOMAIN%</a> u iznosu od <strong>%DISCOUNT_VALUE%</strong>.<br/>Kod za korištenje kupona je</p><p style="text-align: center"><strong style="font-size: 32px;">%COUPON_CODE%</strong></p>');
} elseif (!empty($gift_mode) AND $gift_mode == 'userbirthday') {
    $extra_label = "mail_user_{$gift_mode}";
    $default_text = __('<p>%GIFT_FROM% Vam šalje rođendanski poklon bon za webshop <a href="%SITE_URL%">%SITE_DOMAIN%</a> u iznosu od <strong>%DISCOUNT_VALUE%</strong>.<br/>Kod za korištenje kupona je</p><p style="text-align: center"><strong style="font-size: 32px;">%COUPON_CODE%</strong></p>');
} elseif (!empty($gift_mode) AND in_array($gift_mode, ['existingcustomerorder', 'registereduserloyalty'])) {
    $extra_label = "mail_$gift_mode";
    $default_text = __('<p>%GIFT_FROM% Vam šalje poklon bon za webshop <a href="%SITE_URL%">%SITE_DOMAIN%</a> za <strong>besplatnu dostavu</strong>.<br/>Kod za korištenje kupona je</p><p style="text-align: center"><strong style="font-size: 32px;">%COUPON_CODE%</strong></p>');
} elseif(!empty($item->productview)) {
    $extra_label = "mail_gift_mode_productview";
    $default_text = __('<p>%GIFT_FROM% Vam poklon bon za proizvod <strong>%COUPON_DESCRIPTION%</strong> <a href="%SITE_URL%">%SITE_DOMAIN%</a> u iznosu od <strong>%DISCOUNT_VALUE%</strong>.<br/>Kod za korištenje kupona je</p><p style="text-align: center"><strong style="font-size: 32px;">%COUPON_CODE%</strong></p>');
} else {
    $extra_label = $default_label;
}

if (!isset($currency_code)) {
    $currency_code = (time() < strtotime((isset($kn_euro_conversion_date)) ? $kn_euro_conversion_date : '2023-01-01 00:00:00')) ? 'HRK' : 'EUR';
}

echo str_replace(
// params
    [
        '%SITE_URL%',
        '%SITE_DOMAIN%',
        '%GIFT_FROM%',
        '%DISCOUNT_VALUE%',
        '%COUPON_CODE%',
        '%COUPON_ACTIVE_TO%',
        '%COUPON_VALUE%',
        '%COUPON_MIN_ORDER_TOTAL%',
        '%COUPON_DESCRIPTION%',
    ],
    // data
    [
        $site_url,
        $site_domain,
        $item->gift_from,
        (($item->type == 'f') ? Utils::currency_format($item->coupon_value, $currency_display) . Utils::get_second_pricetag_string($item->coupon_value * $item->exchange, ['currency_code' => $currency_code]) : (int) $item->coupon_value . '%'),
        $item->code,
        ((!empty($item->datetime_expire)) ? date('d.m.Y, H:i', $item->datetime_expire) . 'h' : '-'),
        (($item->type == 'f') ? Utils::currency_format($item->coupon_value, $currency_display) . Utils::get_second_pricetag_string($item->coupon_value * $item->excahnge, ['currency_code' => $currency_code]) : (int) $item->coupon_value . '%'),
        Utils::currency_format($item->min_order_total, $currency_display) . Utils::get_second_pricetag_string($item->min_order_total * $item->exchange, ['currency_code' => $currency_code]),
        $item->description,
    ],
    // label
    Arr::get($cmslabel, $extra_label, Arr::get($cmslabel, $default_label, $default_text)));

?>

<?php if (!empty($item->datetime_expire)): ?>
    <?php
    echo str_replace(
        ['%COUPON_ACTIVE_TO%'], [($lang == 'si' ? date('j. n. Y', $item->datetime_expire) . ', do ' . date('H:i', $item->datetime_expire) . ' ure' : date('d.m.Y, H:i', $item->datetime_expire) . ($lang == 'de' ? ' Uhr' : 'h'))], Arr::get($cmslabel, 'mail_gift_expire', __('<p>Vrijedi do <strong>%COUPON_ACTIVE_TO%</strong></p>')));

    ?>
<?php endif; ?>

<?php if (!empty($item->gift_message)): ?>
    <?php
    echo str_replace(
        ['%GIFT_FROM%', '%GIFT_MESSAGE%'], [$item->gift_from, nl2br($item->gift_message)], Arr::get($cmslabel, 'mail_gift_message', __('<p>Poruka uz poklon bon:<br>%GIFT_MESSAGE%</p>')));

    ?>
<?php endif; ?>

<?php if (Kohana::config('app.webshop.coupon_use_save_for_later')): ?>
    <p style="text-align: center">
        <a href="<?php echo Utils::app_absolute_url($lang, 'auth', '', FALSE); ?>?save_coupon=<?php echo $item->code; ?>" target="_blank" style="padding: 4px 15px; margin: 10px 0 0 0; font-size: 12px; text-decoration:none"><?php echo Arr::get($cmslabel, 'mail_gift_save', __('Spremi kupon u svoj korisnički profil')) ?></a>
    </p>
<?php endif; ?>

<?php echo Arr::get($cmslabel, 'gift_contact', Arr::get($cmslabel, 'mail_contact', '')); ?>
<?php $this->endblock('content'); ?>

<?php $this->block('footer'); ?>
<?php echo Arr::get($cmslabel, 'gift_footer', Arr::get($cmslabel, 'mail_footer', '')); ?>
<?php if (isset($tracking_cms_url) AND $tracking_cms_url): ?><img src="<?php echo $tracking_cms_url; ?>" width="1" height="1" border="0" alt="" /><?php endif; ?>
<?php $this->endblock('footer'); ?>