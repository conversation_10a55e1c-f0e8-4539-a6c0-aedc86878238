<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', array('cms_page' => isset($cms_page) ? $cms_page : array())); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> main-offset-sm page-checkout page-checkout-step3<?php $this->endblock('page_class'); ?>

<?php $this->block('extrahead'); ?>
	<?php echo Html::media('/media/cart.css', 'css'); ?>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('header_body'); ?>
	<div class="checkout-return">
		<a class="btn-return-home" href="<?php echo Utils::homepage($info['lang']); ?>"><?php echo Arr::get($cmslabel, 'return_to_homepage'); ?></a>
	</div>
<?php $this->endblock('header_body'); ?>

<?php $this->block('header_content'); ?>
	<h1 class="checkout-title"><?php echo Arr::get($cmslabel, 'checkout_title'); ?></h1>
<?php $this->endblock('header_content'); ?>

<?php $this->block('content_layout'); ?>
	<form class="step3 df wc-row form-label wc-form ajax_siteform ajax_siteform_loading" action="#webshop_form" method="post" name="webshop" id="webshop_form" data-webshop_autocomplete="zipcode_city_location" data-tracking_gtm_checkout_advanced="3|<?php echo Utils::token_id(); ?>">
		<!-- Column 1 -->
		<div class="wc-col wc-col1 wc-step3-col1">
			<div class="step step3 completed-step step3-step-shipping">
				<a class="step_link" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'shipping'); ?>">
					<?php echo Arr::get($cmslabel, 'step1'); ?>
					<span class="change-data"><?php echo Arr::get($cmslabel, 'change_data'); ?></span>
				</a>
			</div>
			<div class="wc-col-cnt">
				<h1 class="wc-title"><?php echo Arr::get($cmslabel, 'step2'); ?></h1>
				
				<?php if (sizeof($errors) > 0): ?>
					<p class="error global-error"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
				<?php endif; ?>	

				<div class="shipping-options">
					<h2 class="wc-subtitle"><?php echo Arr::get($cmslabel, 'checkout_shipping'); ?></h2>				
					<?php echo View::factory('webshop/widget/shipping', array('shopping_cart_info' => $shopping_cart_info, 'customer_data' => $customer_data)); ?>
				</div>

				<!-- Payment options -->
				<h2 class="wc-subtitle"><?php echo Arr::get($cmslabel, 'checkout_payment'); ?></h2>
				<div class="payment-options">
					<?php foreach ($customer_fields as $field): ?>
						<?php if (isset($errors[$field][0]) AND ($error = $errors[$field][0])): ?>
							<p class="error"><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></p>
						<?php endif; ?>
						<div class="field-<?php echo $field; ?>">	
							<?php if ($field == 'payment'): ?>
								<?php if (sizeof($available_payments_none) == 1): ?>
									<div class="cart_info_payment_box_normal <?php if ($shopping_cart_info['total'] > 0): ?>active<?php endif; ?>">
										<?php echo Form::select_as_radio2($field, $available_payments, ($shopping_cart_info['total'] > 0) ? $data->payment->id : NULL); ?>
									</div>
									<div class="cart_info_payment_box_0 <?php if ($shopping_cart_info['total'] == 0): ?>active<?php endif; ?>">
										<?php echo Form::select_as_radio2($field, $available_payments_none, ($shopping_cart_info['total'] == 0) ? key($available_payments_none) : NULL); ?>
									</div>
								<?php else: ?>
									<?php echo Form::select_as_radio2($field, $available_payments, $data->payment->id); ?>
								<?php endif; ?>
							<?php else: ?>
								<?php echo $data->input($field, 'form'); ?>
							<?php endif; ?>
							<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?> *<?php endif; ?></label>
						</div>
					<?php endforeach; ?>

					<?php echo Arr::get($cms_page, 'content'); ?>
				</div>

                <div class="global_error global-error"<?php if ( ! count($errors) AND empty(Arr::get($_GET, 'errorcode', ''))): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
                <?php if (Arr::get($_GET, 'errorcode', '') == 'missing_parcel_locker_id') : ?>
                    <div class="global-error global-error"><?php echo Arr::get($cmslabel, 'missing_parcel_locker', 'Treba odabrati paketomat'); ?></div>
                <?php endif ?>
				
				<div class="payment-col1">
					<?php if ( ! Kohana::config('app.webshop.bill_on_shipping_step')): ?>
						<?php if (sizeof($customer_bill_fields)): ?>
							<?php foreach ($customer_bill_fields as $field): ?>
								<?php if (isset($errors[$field][0]) AND ($error = $errors[$field][0])): ?>
									<p class="error"><?php echo Arr::get(@$cmslabel, "error_{$error}", $error); ?></p>
								<?php endif; ?>
								<p class="field-<?php echo $field; ?>">
									<label for="field-<?php echo $field; ?>"><?php echo @$cmslabel[substr($field, 2, strlen($field))]; ?><?php if (in_array($field, $request_fields)): ?> *:<?php endif; ?></label>
									<?php echo $data->input($field, 'form'); ?>
								</p>
							<?php endforeach; ?>
							<?php foreach ($default_bill_value as $field => $value): ?>
								<input type="hidden" name="default-<?php echo $field; ?>" value="<?php echo $value; ?>" />
							<?php endforeach; ?>
						<?php else: ?>
							<?php echo Arr::get($cmslabel, 'same_as_shipping'); ?>
						<?php endif; ?>
					<?php endif; ?>

					<div class="section payment-section">
						<?php if (isset($shopping_cart_info['total_extra_shipping_min_total_error']) AND $shopping_cart_info['total_extra_shipping_min_total_error']): ?>
							<div class="minprice-tooltip">
							<?php echo str_replace(array(
									'%TOTAL_MIN%',
									'%TOTAL_MISSING%',
									),
								array(
									Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
									Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total_missing'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total_missing'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
								),
								Arr::get($cmslabel, 'minimal_order_price_full'));
							?>
							</div>
							<a href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>" class="btn btn-orange btn-large"><?php echo Arr::get($cmslabel, 'edit_shopping_cart'); ?></a>
						<?php else: ?>
							<?php if (isset($accept_terms_field) AND isset($accept_terms_error)): ?>
								<?php /*
								<div class="terms-container">
									<div class="newwindow"><?php echo Arr::get($cmslabel, 'accept_terms_new_window'); ?></div>
									<iframe src="/uvjeti-kupnje/?mode=quick&type=terms" scrolling="no" frameborder="0" width="100%" height="85"></iframe>
								</div>
								*/ ?>
								<?php echo View::factory('webshop/widget/accept_terms', array('accept_terms_field' => $accept_terms_field, 'accept_terms_error' => $accept_terms_error)); ?>
							<?php endif; ?>

							<button type="submit" class="btn btn-checkout btn-orange">
								<?php echo Arr::get($cmslabel, 'next_step'); ?>
							</button>
						<?php endif; ?>
					</div>
				</div>
			</div>
			<div class="step step4"><a class="step_link" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'review_order'); ?>"><?php echo Arr::get($cmslabel, 'step3'); ?></a></div>
		</div>

		<!-- Column 2 -->
		<div class="wc-col wc-col2 wc-step3-col2">
			<div class="wc-coupons">
				<div class="wc-col-cnt">
					<?php echo View::factory('webshop/widget/coupon', array('shopping_cart_info' => $shopping_cart_info)); ?>
				</div>
			</div>

			<?php echo View::factory('webshop/widget/loyalty_new', array('shopping_cart_info' => $shopping_cart_info)); ?>
			<div class="wc-priority-order">
				<div class="wc-col-cnt priority-order-col-cnt">
					<?php echo View::factory('webshop/widget/priority_order', ['class' => 'priority-order-checkout','shopping_cart_info' => $shopping_cart_info]); ?>
				</div>
			</div>
			<?php echo View::factory('webshop/widget/shopping_cart_items_small', array('shopping_cart_info' => $shopping_cart_info, 'products' => $products, 'products_status' => $products_status)); ?>		

			<div class="step3-footer">
				<?php echo View::factory('webshop/widget/shipping_bar', ['shopping_cart_info' => $shopping_cart_info]); ?>
				<div class="wc-col-cnt wc-col-totals">
					<?php echo View::factory('webshop/widget/total', array('shopping_cart_info' => $shopping_cart_info, 'mode' => 'preview')); ?>
				</div>
			</div>
		</div>
	</form>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('bottom'); ?> <?php $this->endblock('bottom'); ?>
<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>