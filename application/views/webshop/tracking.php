<!DOCTYPE html>
<html lang="<?php echo Arr::get(Kohana::config('app.html_lang'), $info['lang'], $info['lang']); ?>" data-default_lang="<?php echo Arr::get(Kohana::config('app.html_lang'), Kohana::config('app.language'), Kohana::config('app.language')); ?>" data-currency_code="<?php echo $currency['code']; ?>" data-currency_display="<?php echo $currency['display']; ?>" data-currency_exchange="<?php echo $currency['exchange']; ?>" data-webshop_min_order="<?php echo intval(Arr::get($shopping_cart, 'total_extra_shipping_min_total', '0')); ?>">
<head>
    <?php echo Google::tag_manager($info['gtagmanager_code'], 'head'); ?>
    <title><?php echo Arr::get($cmslabel, 'waiting_messages', 'Prič<PERSON>jte trenutak...'); ?>...</title>
    <meta name="robots" content="noindex, nofollow">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
</head>
<body>
    <?php echo Google::tag_manager($info['gtagmanager_code'], 'body'); ?>
    <?php echo Facebook::tracking('759023024186954', $info['lang'], 'PageView'); ?>
    <?php if ($order AND $order_first): ?>
        <?php echo View::factory('webshop/widget/ecommerce_tag_manager2_tracking', ['order' => $order, 'store_name' => 'Tvornica zdrave hrane', 'event' => 'orderComplete']); ?>
        <?php echo View::factory('webshop/widget/facebook_tracking', array('order' => $order)); ?>
    <?php endif; ?>
    <?php
    $logo_path = "/media/images/logo-mail.png";
    $logo_lang = [
        'hr' => '/media/images/logo-mail.png',
        'si' => '/media/images/logo-mail-sl.png',
        'en' => '/media/images/logo-mail-en.png',
        'de' => '/media/images/logo-mail-de.png',
    ];

    if (!empty($info['lang']) AND !empty(Arr::get($logo_lang, $info['lang'], ''))) {
        $logo_path = Arr::get($logo_lang, $info['lang'], $logo_path);
    }
    ?>

    <div style="text-align: center;"><br><img src="<?php echo $logo_path; ?>"><br><br>
    <form action="<?php echo $redirect_url; ?>" method="post" name="redirect_confirm">
        <input name="" type="submit"
               value="<?php echo Arr::get($cmslabel, 'waiting_messages', 'Pričekajte trenutak...'); ?>..."
               style="border: 0; background: #fff"/>
    </form>
</div>
<?php echo Html::media('jquery', 'js', false); ?>
<script>
    $(function () {
        setTimeout(function () {
            $('form[name="redirect_confirm"]').submit();
        }, 2000);
    });
</script>
</body>
</html>