<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> main-offset-sm page-checkout page-checkout-step2<?php $this->endblock('page_class'); ?>

<?php $this->block('extrahead'); ?>
	<?php echo Html::media('/media/cart.css', 'css'); ?>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('header_body'); ?>
	<div class="checkout-return">
		<a class="btn-return-home" href="<?php echo Utils::homepage($info['lang']); ?>"><?php echo Arr::get($cmslabel, 'return_to_homepage'); ?></a>
	</div>
<?php $this->endblock('header_body'); ?>

<?php $this->block('header_content'); ?>
	<h1 class="checkout-title"><?php echo Arr::get($cmslabel, 'checkout_title'); ?></h1>
<?php $this->endblock('header_content'); ?>

<?php $this->block('content_layout'); ?>
	<?php $location_request_country = Kohana::config('app.webshop.location_request_country'); ?>
	<form class="step2 df wc-row wc-form form-label ajax_siteform ajax_siteform_loading" action="#webshop_form" method="post" name="webshop" id="webshop_form" accept-charset="utf-8" data-location_request_country="<?php echo (!empty($autocomplete_countries)) ? $autocomplete_countries : "" ?>" data-webshop_autocomplete="zipcode_city_location" data-tracking_gtm_products="2|" data-tracking_gtm_checkout_advanced="2|<?php echo Utils::token_id(); ?>">
		<!-- Column 1 -->
		<div class="wc-col wc-col1 wc-step2-col1">
			<div class="wc-col-cnt">
				<h2 class="wc-title"><?php echo Arr::get($cmslabel, 'step1'); ?></h2>
				<?php echo Arr::get($cms_page, 'content'); ?>
				<div class="global_error global-error"<?php if ( ! count($errors)): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
				<?php $i = 1; ?>
				<?php foreach ($customer_fields as $field): ?>
					<div class="field field-checkout<?php if ($field == 'location'): ?> field-autocomplete<?php endif; ?> field-<?php echo $field; ?>">
						<?php $error = Valid::get_error($field, $errors); ?>
						<?php $label = (Arr::get($cmslabel, 'checkout_'.$field)) ? Arr::get($cmslabel, 'checkout_'.$field) : Arr::get($cmslabel, $field); ?>
						<?php if ($field == 'shipping'): ?>
							<?php echo Form::select_as_radio2($field, $available_shippings, $data->shipping->id); ?>
						<?php elseif($field == 'location'): ?>
							<input type="text" name="location" id="field-location" value="<?php echo Arr::get($customer_data, 'city') . (!empty($customer_data['zipcode']) ? ' (' . $customer_data['zipcode'] . ')' : ''); ?>" />
							<div class="phone-tooltip location-tooltip"><?php echo Arr::get($cmslabel, 'location_tooltip'); ?></div>
                        <?php elseif ($field == 'country'): ?>
                            <?php echo $data->input($field, 'form', ['attributes' => ['data-checkout_country_selector' => '1']]); ?>
						<?php else: ?>
							<?php echo $data->input($field, 'form'); ?>
						<?php endif; ?>
						<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo $label; ?></label>
						<?php if($field == 'phone'): ?><span class="phone-tooltip"><?php echo Arr::get($cmslabel, 'phone_tooltip', 'Samo za potrebe dostavne službe'); ?></span><?php endif; ?>
						<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
					</div>
					<?php $i++; ?>
				<?php endforeach; ?>

				<?php if (Kohana::config('app.webshop.bill_on_shipping_step')): ?>
					<div class="wc-col1-bottom-content">
						<?php if (sizeof($customer_bill_fields)): ?>
							<?php $b = 0; ?>
							<?php foreach ($customer_bill_fields as $field): ?>
								<?php $error = Valid::get_error($field, $errors); ?>
								<p class="field field-<?php echo $field; ?><?php if($b % 2 == 0): ?> last<?php endif; ?>">
									<?php echo $data->input($field, 'form'); ?>
									<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, substr($field, 2, strlen($field))); ?><?php if (in_array($field, $request_fields)): ?> *:<?php endif; ?></label>
									<?php if($field == 'phone'): ?><span class="phone-tooltip"><?php echo Arr::get($cmslabel, 'phone_tooltip', 'Samo za potrebe dostavne službe'); ?>"></span><?php endif; ?>
									<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
								</p>
								<?php $b++; ?>
							<?php endforeach; ?>
						<?php else: ?>
							<?php echo Arr::get($cmslabel, 'same_as_shipping', 'Podaci za dostavu računa jednaki su podacima za dostavu proizvoda'); ?>
						<?php endif; ?>
					</div>
				<?php endif; ?>
				<button class="btn btn-orange btn-checkout btn-step2-submit" type="submit"><span><?php echo Arr::get($cmslabel, 'next_step'); ?></span></button>
			</div>
			<div class="step step3"><a class="step_link" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>"><?php echo Arr::get($cmslabel, 'step2'); ?></a></div>
			<div class="step step4 last-step"><a class="step_link" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'review_order'); ?>"><?php echo Arr::get($cmslabel, 'step3'); ?></a></div>
		</div>

		<!-- Column 2 -->
		<div class="wc-col wc-col2 wc-step2-col2 wc-step2-shipping">
			<div class="wc-coupons">
				<div class="wc-col-cnt">
					<?php echo View::factory('webshop/widget/coupon', array('shopping_cart_info' => $shopping_cart_info)); ?>
				</div>
			</div>

			<?php echo View::factory('webshop/widget/loyalty_new', array('shopping_cart_info' => $shopping_cart_info)); ?>
			<div class="wc-priority-order">
				<div class="wc-col-cnt priority-order-col-cnt">
					<?php echo View::factory('webshop/widget/priority_order', ['class' => 'priority-order-checkout','shopping_cart_info' => $shopping_cart_info]); ?>
				</div>
			</div>
			<?php echo View::factory('webshop/widget/shopping_cart_items_small', array('shopping_cart_info' => $shopping_cart_info, 'products' => $products, 'products_status' => $products_status)); ?>		
			<?php echo View::factory('webshop/widget/shipping_bar', ['shopping_cart_info' => $shopping_cart_info, 'class' => 'step2-free-delivery-container']); ?>

			<div class="wc-col-cnt wc-col-totals">
				<?php echo View::factory('webshop/widget/total', array('shopping_cart_info' => $shopping_cart_info, 'mode' => 'preview')); ?>
			</div>
		</div>
	</form>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('bottom'); ?> <?php $this->endblock('bottom'); ?>
<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>