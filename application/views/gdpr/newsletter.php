<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-cms<?php $this->endblock('page_class'); ?>

<?php $this->block('h1'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
<?php $this->endblock('h1'); ?>

<?php $this->block('content'); ?>
	<div class="content-wrapper">
		<div class="content <?php if(empty($cms_page['main_image_2'])): ?> no-image<?php endif; ?>">
			<?php $message_type = (isset($message_type) AND $message_type) ? $message_type : Arr::get($info, 'message_type', ''); ?>
			<?php $message = (isset($message) AND $message) ? $message : Arr::get($info, 'message', ''); ?>
			<?php if ($message_type == 'success' AND $message): ?>
				<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}", "{$message_type}_{$message}"); ?></p>
			<?php else: ?>
				<?php echo Arr::get($cms_page, 'content'); ?>
				<?php if ($message_type == 'error' AND $message): ?>
					<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}", "{$message_type}_{$message}"); ?></p>
				<?php endif; ?>
				<form action="?" method="POST" class="form-inline">
					<div class="gdpr-mail-confirm">
						<div class="gdpr-mail-input">
							<label for="email"><?php echo Arr::get($cmslabel, 'gdpr_newsletter_enter_email'); ?>:</label>
							<input id="email" type="text" name="email" value="<?php echo $email; ?>" />
						</div>
						<?php if ($message_type == 'error' AND $message): ?>
							<p class="global-<?php echo $message_type; ?> global-gdpr-newsletter-error"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}", "{$message_type}_{$message}"); ?></p>
						<?php endif; ?>
						<input type="checkbox" name="gdpr_accept" id="field-gdpr_accept" value="1" />
						<label for="field-gdpr_accept"><?php echo $gdpr_accept_text; ?></label>
						<button type="submit" name="confirm" value="1" class="btn btn-primary"><?php echo Arr::get($cmslabel, 'gdpr_newsletter_confirm', 'Potvrdi'); ?></button>
					</div>
				</form>
			<?php endif; ?>
		</div>
	</div>
<?php $this->endblock('content'); ?>