<?php $this->extend('default'); ?>
<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> main-offset-sm page-auth page-signup<?php $this->endblock('page_class'); ?>

<?php $this->block('content_layout'); ?>
	<div class="df a-row">
		<div class="a-col a-col1">
			<?php if ($message_type AND $message): ?>
				<?php if (is_array($message)): ?>
					<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
				<?php else: ?>
					<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}"); ?></p>
				<?php endif; ?>
			<?php endif; ?>
            <?php
                $country_id = 1;
                $country_code = App::country_code();
                if (!empty($country_code)) {
                    $country_id = Webshop::country_by_code($country_code);
                }
                $country_id = (!empty($customer_data['country'])) ? $customer_data['country'] : $country_id;
            ?>

			<?php if ($message_type != 'success'): ?>
				<!-- SIGNUP FORM -->
				<form method="post" action="" id="signup-form" accept-charset="utf-8" enctype="multipart/form-data" name="signup" class="ajax_siteform form-label auth-form auth-signup-form" data-siteform_fb_event_tracking="CompleteRegistration" data-tracking_gtm_login="userLogin|Registracija">
					<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
					<div class="reg-section">
						<div class="a-subtitle"><?php echo Arr::get($cmslabel, 'login_info'); ?></div>
						<div class="reg-row">
                            <?php foreach ($customer_fields as $field): ?>
								<?php $error = Valid::get_error($field, $message); ?>
                                <?php if (Kohana::config('app.is_site_multidomain') AND ($field == 'lang' OR $field == 'site')): ?>
                                    <input type="hidden" id="field-<?php echo $field; ?>" name="<?php echo $field; ?>" value="<?php echo ($field == 'lang') ? $info['lang'] : $info['site_id']; ?>" />
                                    <?php continue; ?>
                                <?php endif; ?>
                                <?php if ($field == 'country'): ?>
                                    <input type="hidden" id="field-<?php echo $field; ?>" name="<?php echo $field; ?>" value="<?php echo $country_id; ?>" />
                                    <?php continue; ?>
                                <?php endif; ?>
								<?php if($field == 'first_name'): ?>
									</div>
										<div class="a-subtitle a-subtitle-personal"><?php echo Arr::get($cmslabel, 'personal_data'); ?></div>
									<div class="reg-row reg-<?php echo $field; ?>">
								<?php endif; ?>
								<?php if($field == 'newsletter'):?>
									</div>
									<div class="reg-row reg-<?php echo $field; ?>">
								<?php endif; ?>
								<?php if (in_array($field, array('accept_terms', 'newsletter'))): ?>
									<p class="field-<?php echo $field; ?>">
										<?php echo $item->input($field, 'form'); ?>
										<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field, $item->meta()->field($field)->label); ?><?php echo (in_array($field, $request_fields) ? '' : ''); ?></label>
										<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
									</p>
								<?php elseif($field == 'loyalty_request'):?>
									</div>	
										<div class="reg-row reg-<?php echo $field; ?>">
											<div class="a-loyalty"><?php echo Arr::get($cmslabel, 'auth_loyalty_title_nocard'); ?></div>
											<?php $options = Kohana::config('app.loyalty.user_join'); ?>
											<?php foreach ($options AS $option_id => $option_title): ?>
												<div class="field field-loyalty">	
													<span class="field-loyalty field-loyalty_request-<?php echo $option_id; ?>">
														<input type="radio" id="field-loyalty_request-<?php echo $option_id; ?>" name="loyalty_request" value="<?php echo $option_id; ?>" <?php if ($option_id == 'n'): ?>checked="checked"<?php endif; ?>>
														<label for="field-loyalty_request-<?php echo $option_id; ?>"><?php echo Arr::get($cmslabel, $field.'_'.$option_id, $option_title); ?></label>
														<div class="field-loyalty-note">
															<?php echo Arr::get($cmslabel, $field.'_'.$option_id.'_note'); ?>
															<?php if ($option_id == 'e'): ?>
																<label for="field-loyalty_code"><?php echo Arr::get($cmslabel, 'loyalty_code'); ?></label>
																<?php echo $item->input('loyalty_code', 'form'); ?>
																<span id="field-error-loyalty_code" class="field_error error" style="display: none"></span>
															<?php endif; ?>
														</div>
													</span>
												</div>
											<?php endforeach; ?>
								<?php else: ?>
									<div class="field field-<?php echo $field; ?>">
										<?php if($field == 'warehouse_location'): ?>
											<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field, $item->meta()->field($field)->label); ?><?php echo (in_array($field, $request_fields) ? '' : ''); ?></label>
											<?php echo $item->input($field, 'form'); ?>
										<?php else: ?>
											<?php echo $item->input($field, 'form'); ?>
											<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field, $item->meta()->field($field)->label); ?><?php echo (in_array($field, $request_fields) ? '' : ''); ?></label>
										<?php endif; ?>
										<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
									</div>
								<?php endif; ?>
							<?php endforeach; ?>
						</div>
					</div>
					<p class="submit">
                        <button class="btn btn-signup g-recaptcha" data-sitekey="<?php echo App::config_single('recaptcha_site_key_' . $info['site_id']);; ?>" data-callback='onSubmit' data-action='submit' data-before_submit type="submit"><?php echo Arr::get($cmslabel, 'confirm_signup'); ?></button>
                    </p>
				</form>
				<!-- / SIGNUP FORM -->
			<?php endif; ?>
		</div>

		<div class="a-col a-col2 lists">
			<?php echo Arr::get($cms_page, 'content'); ?>
			
			<div class="reg-have-acc">
				<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>"><?php echo Arr::get($cmslabel, 'already_register_login'); ?></a>
			</div>
			<div class="a-btns">
				<div class="submit">
					<a class="btn btn-auth-submit" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>"><?php echo Arr::get($cmslabel, 'login'); ?></a>
				</div>
				<div class="a-links a-signup-links">
					<a  class="a-links-forgotten" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>"><?php echo Arr::get($cmslabel, 'forgotten_password'); ?></a>
				</div>				
			</div>

			<?php echo View::factory('auth/widget/social_login'); ?>
		</div>
	</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>