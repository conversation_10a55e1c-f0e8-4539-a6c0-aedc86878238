<div class="aw">
	<a class="aw-login" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'my_profile'); ?></span></a>
	<?php if ($user): ?>
		<div class="aw-tooltip">				
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', FALSE); ?>"><?php echo Arr::get($cmslabel, 'my_profile'); ?></a>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', false); ?>"><?php echo Arr::get($cmslabel, 'my_orders'); ?></a>
			<?php if (Kohana::config('app.catalog.use_wishlists') AND $wishlist AND $wishlist['total_items'] > 0): ?>
				<a href="<?php echo $wishlist['url_'. $info['lang']]; ?>"><?php echo Arr::get($cmslabel, 'my_wishlist'); ?></a>
			<?php endif; ?>
			<?php if (!empty($user) AND !$user->b2b): ?>
				<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshopcoupon', FALSE); ?>"><?php echo Arr::get($cmslabel, 'my_coupons'); ?></a>
			<?php endif; ?>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE); ?>"><?php echo Arr::get($cmslabel, 'edit_profile'); ?></a>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE); ?>"><?php echo Arr::get($cmslabel, 'change_password'); ?></a>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'logout', FALSE); ?>?redirect=<?php echo $info['redirect_url']; ?>"><?php echo Arr::get($cmslabel, 'logout'); ?></a>
		</div>
	<?php else: ?>
		<?php /*
		<div class="aw-tooltip aw-quick-login">	
			<form action="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>?redirect=<?php echo $info['redirect_url']; ?>" method="post" accept-charset="utf-8" enctype="multipart/form-data" name="login" class="form-label form-quick-login ajax_siteform ajax_siteform_loading">
				<a class="aw-quick-login-close" href="javascript:toggleBox(['.aw']);">Zatvori</a>
				<div class="global_error global-error" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
				<div class="global_success global-success" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'success_forgotten_password'); ?></div>
				<div class="global_error global-error" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'error_forgotten_password'); ?></div>
				<div class="aw-quick-login-title"><?php echo Arr::get($cmslabel, 'email_signup'); ?></div>
				<div class="field field-quick field-quick-username">
					<label for="quick-email"><?php echo Arr::get($cmslabel, 'email'); ?></label>
					<input name="email" type="text" id="quick-email" tabindex="1" />
				</div>
				<div class="field field-quick field-quick-password">
					<label for="quick-password"><?php echo Arr::get($cmslabel, 'password'); ?></label>
					<input name="password" type="password" id="quick-password" tabindex="2" />
				</div>
				<div class="field field-quick field-quick-remember">
					<input type="checkbox" name="remember" id="quick-id_remember" value="1" checked />
					<label for="quick-id_remember"><?php echo Arr::get($cmslabel, 'remember'); ?></label>
				</div>
				<div class="df aic aw-quick-btns">
					<button type="submit" class="btn-quick-login"><?php echo Arr::get($cmslabel, 'login'); ?></button>
					<div class="fg1 aw-quick-links">
						<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>" class="button"><?php echo Arr::get($cmslabel, 'quick_forgotten_password', 'Zaboravio/la si lozinku?'); ?></a>
						<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>" class="button"><span><?php echo Arr::get($cmslabel, 'signup'); ?></span></a>
					</div>
				</div>
			</form>
			<?php echo View::factory('auth/widget/social_login', ['class' => 'quick-social-login']); ?>
		</div>
		*/ ?>
		<div class="aw-tooltip">
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>"><?php echo Arr::get($cmslabel, 'login'); ?></a>
			<a class="login" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>"><?php echo Arr::get($cmslabel, 'signup'); ?></a>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>"><?php echo Arr::get($cmslabel, 'header_forgotten_password'); ?></a>
		</div>
	<?php endif; ?>
</div>