<?php $this->extend('auth/default'); ?>

<?php $this->block('h1'); ?>
	<h1 class="a-title">
		<?php echo Arr::get($cms_page, 'seo_h1'); ?>
		<?php if($items_total > 0): ?>
			<span class="a-title-counter">(<?php echo $items_total; ?>)</span>
		<?php endif; ?>
	</h1>	
<?php $this->endblock('h1'); ?>

<?php $this->block('content2'); ?>
	<div id="items_widgetlist_webshop_layout">
		<?php echo View::factory('auth/widgetlist/webshoporder', ['items' => $items, 'pagination' => $pagination, 'items_total' => $items_total, 'items_filtered_total' => $items_filtered_total]); ?>
	</div>
<?php $this->endblock('content2'); ?>

<?php $this->block('extrabody'); ?>
<?php echo Html::media('cmsinfinitescroll', 'js'); ?>
<script type="text/javascript">
	$(function(){
		$('#items_widgetlist_webshop').CmsInfiniteScroll();
	});
</script>
<?php $this->endblock('extrabody'); ?>