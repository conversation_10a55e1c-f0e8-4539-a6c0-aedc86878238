<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-forgotten-password main-offset-sm<?php $this->endblock('page_class'); ?>

<?php $this->block('h1'); ?>
	
<?php $this->endblock('h1'); ?>

<?php $this->block('content'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	<?php echo Arr::get($cms_page, 'content'); ?>

	<?php if ($message_type AND $message): ?>
		<?php if (is_array($message)): ?>
			<p class="global-error global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
		<?php else: ?>
			<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}"); ?></p>
		<?php endif; ?>
	<?php endif; ?>

	<?php if ($message_type != 'success'): ?>
		<form method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="forgotten_password" class="ajax_siteform form-label auth-form auth-forgotten-password-form">
			<?php $error = ($message_type AND $message) ? "{$message_type}_{$message}" : ""; ?>
			<p class="field">
				<input type="text" name="email" id="id_email" />
				<label for="id_email"><?php echo Arr::get($cmslabel, 'email_addres'); ?></label>
				<span id="field-error-email" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
			</p>
			<div class="a-btns">
				<div class="submit"><button class="btn btn-auth-submit" type="submit"><span><?php echo Arr::get($cmslabel, 'send'); ?></span></button></div>
				<div class="a-links">
					<a class="back" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'back_to_login'); ?></span></a>
					<a class="signup" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'add_new_account'); ?></span></a>
				</div>
			</div>
		</form>
	<?php endif; ?>
<?php $this->endblock('content'); ?>

<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>