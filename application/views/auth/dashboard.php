<?php $this->extend('auth/default'); ?>


<?php $this->block('h1'); ?> <?php $this->endblock('h1'); ?>

<?php $this->block('content2'); ?>
	<div class="df a-intro">
		<div class="fg1 a-intro-left">
			<p class="a-intro-title"><?php echo Arr::get($cmslabel, 'you_can'); ?></p>
			<ul class="a-menu">
				<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', false), Arr::get($cmslabel, 'auth_view_orders')); ?></li>
				<?php if (Kohana::config('app.catalog.use_wishlists') AND !empty($wishlist)): ?>
                    <?php $wl_index = (isset($wishlist['url_'.$info['lang']])) ? 'url_'.$info['lang'] : 'url'; ?>
                    <li><?php echo str_replace("%LINK%", $wishlist[$wl_index], Arr::get($cmslabel, 'auth_view_wishlist')) ; ?></li>
				<?php endif; ?>
				<?php if (!empty($user) AND !$user->b2b): ?>
					<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'my_webshopcoupon', FALSE), Arr::get($cmslabel, 'auth_view_coupons')); ?></li>
				<?php endif; ?>
				<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE), Arr::get($cmslabel, 'auth_edit_profile')); ?></li>
				<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE), Arr::get($cmslabel, 'auth_change_password')); ?></li>
				<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'logout', FALSE) . '?redirect='.$info['redirect_url'], Arr::get($cmslabel, 'auth_logout')); ?></li>
			</ul>
		</div>

		<div class="a-intro-user">
			<p class="a-intro-title"><?php echo $user->first_name; ?> <?php echo $user->last_name; ?></p>
			<p>
				<span class="a-intro-email"><?php echo $user->email; ?></span><br>
				<?php echo $user->phone; ?>
			</p>
			<?php if($user->address): ?>
			<p>
				<?php echo $user->address; ?><br>
				<?php echo $user->zipcode; ?> <?php echo $user->city; ?>
			</p>
			<?php endif; ?>

			<p><a class="btn btn-gray btn-auth-edit" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'edit_profile'); ?></span></a></p>
		</div>
	</div>

	<!-- Orders -->
	<?php $orders = $user->get_webshoporder(TRUE, 1); ?>
	<?php if(count($orders)): ?>
		<div class="auth-box auth-box-orders orders-container" id="orders">
			<div class="a-section-title a-section-orders-title"><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', FALSE); ?>"><?php echo Arr::get($cmslabel, 'latest_orders'); ?></a></div>
			<?php echo View::factory('auth/widgetlist/webshoporder', array('items' => $orders, 'mode' => 'dashboard')); ?>
			<div class="auth-box-btns">
				<a class="btn" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'webshop_orders_show_all'); ?></span></a>
			</div>
		</div>
	<?php endif; ?>
<?php $this->endblock('content2'); ?>

<?php $this->block('breadcrumb'); ?> <?php $this->endblock('breadcrumb'); ?>