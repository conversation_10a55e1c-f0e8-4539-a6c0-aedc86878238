<?php $this->extend('auth/default'); ?>

<?php $this->block('h1'); ?>
	<h1 class="a-title">
		<?php echo Arr::get($cms_page, 'seo_h1'); ?>
		<?php if($items_total > 0): ?>
			<span class="a-title-counter">(<?php echo $items_total; ?>)</span>
		<?php endif; ?>
	</h1>
<?php $this->endblock('h1'); ?>

<?php $this->block('content2'); ?>
	<div class="ww-coupons ww-auth-coupons">
		<!-- Add Coupon Form -->
		<div class="ww-coupons-form ww-auth-coupons-form">
			<label class="ww-coupons-label" for="coupon_code"><?php echo Arr::get($cmslabel, 'coupon_have_coupon'); ?></label>
			<div class="ww-coupons-fields">
				<input class="ww-coupons-input" type="text" name="coupon_code" id="coupon_code" placeholder="<?php echo Arr::get($cmslabel, 'coupon_enter_code'); ?>" />
				<a class="ww-btn-add ww-coupons-add" href="javascript:cmscoupon.save_for_later('webshop_coupon')"><span><?php echo Arr::get($cmslabel, 'coupon_btn_add', 'Dodaj'); ?></span></a>
			</div>
			<div class="coupon_message" style="display: none"></div>
		</div>
	</div>

	<div id="items_widgetlist_webshop_layout">
		<?php echo View::factory('auth/widgetlist/webshopcoupon', ['items' => $items, 'pagination' => $pagination, 'items_total' => $items_total, 'items_filtered_total' => $items_filtered_total]); ?>
	</div>
<?php $this->endblock('content2'); ?>

<?php $this->block('extrabody'); ?>
<?php echo Html::media('cmsinfinitescroll', 'js'); ?>
<script type="text/javascript">
	$(function(){
		$('#items_widgetlist_webshop').CmsInfiniteScroll();
	});
</script>
<?php $this->endblock('extrabody'); ?>
