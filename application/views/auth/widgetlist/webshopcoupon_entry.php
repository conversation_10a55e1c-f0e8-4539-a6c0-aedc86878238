<?php foreach ($items as $item): ?>
	<div class="order-row w-table coupon-row">
		<div class="w-table-col col-coupon-num"><span class="label"><?php echo Arr::get($cmslabel, 'coupon_code'); ?>: </span><?php echo $item->code; ?></div>
		<div class="w-table-col col-coupon-desc"><span class="label"><?php echo Arr::get($cmslabel, 'coupon_description'); ?>: </span><?php echo ($item->description) ? $item->description : '-'; ?></div>
		<div class="w-table-col col-coupon-total">
			<span class="label"><?php echo Arr::get($cmslabel, 'coupon_value'); ?>: </span>
			<strong><?php echo ($item->type == 'f') ? Utils::currency_format($item->coupon_price, $currency['display']).Utils::get_second_pricetag_string($item->coupon_price, ['currency_code' => $currency['code'], 'display_format' => 'standard']) : ($item->coupon_percent * 100).'%'; ?></strong>
		</div>
		<div class="w-table-col col-coupon-valid">
			<?php if ($item->active): ?>		
				<?php if ($item->datetime_expire): ?>
					<span class="label"><?php echo Arr::get($cmslabel, 'coupon_valid_until'); ?>: </span>
					<?php echo date('d.m.Y, H:i', $item->datetime_expire); ?>
				<?php else: ?>
					-
				<?php endif; ?>
			<?php else: ?>
				<?php if ($item->used): ?>
					<?php echo Arr::get($cmslabel, 'coupon_used'); ?>
				<?php else: ?>
					<?php if ($item->datetime_expire AND $item->datetime_expire < time()): ?>istekao <?php echo date('d.m.Y', $item->datetime_expire); ?><?php else: ?>-<?php endif; ?>
				<?php endif; ?>
			<?php endif; ?>
		</div>
	</div>

<?php endforeach; ?>