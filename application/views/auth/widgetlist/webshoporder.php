<?php $mode = (isset($mode)) ? $mode : ''; ?>
<?php if (count($items)): ?>
	<div class="w-table w-table-head">
		<div class="w-table-col col-num"><?php echo Arr::get($cmslabel, 'webshop_order_number'); ?></div>
		<div class="w-table-col col-date"><?php echo Arr::get($cmslabel, 'date'); ?></div>
		<div class="w-table-col col-total"><?php echo Arr::get($cmslabel, 'amount'); ?></div>
		<div class="w-table-col col-status"><?php echo Arr::get($cmslabel, 'status'); ?></div>
		<div class="w-table-col col-btns"></div>
	</div>

	<div class="orders" <?php if (isset($pagination)): ?>id="items_widgetlist_webshop" data-infinitescroll_next_page="<?php echo $pagination->next_page; ?>" data-infinitescroll_total_pages="<?php echo $pagination->total_pages; ?>" data-infinitescroll_auto_trigger="2" <?php endif; ?>>
		<?php echo View::factory('auth/widgetlist/webshoporder_entry', ['items' => $items, 'mode' => $mode]); ?>
	</div>

	<?php if (isset($pagination)): ?>
		<?php echo $pagination; ?>
		<?php if ($pagination): ?><a href="javascript:void(0);" class="load-more" style="display: none"><?php echo Arr::get($cmslabel, 'load_more', 'Učitaj još'); ?></a><?php endif; ?>
	<?php endif; ?>
<?php else: ?>
	<div class="auth-no-orders"><?php echo Arr::get($cmslabel, 'no_orders', 'Nemate zabilježenu nijednu narudžbu.'); ?></div>
<?php endif; ?>