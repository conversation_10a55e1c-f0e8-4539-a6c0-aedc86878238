<?php foreach ($items as $item): ?>
	<div class="order-row<?php if($mode == 'dashboard'): ?> active<?php endif; ?>" id="order-<?php echo $item->number; ?>">
		<!-- Order Item -->
		<div class="w-table"<?php if($mode != 'dashboard'): ?> onClick="javascript:toggleBox(['#order-<?php echo $item->number; ?>', 'div#btn-order-<?php echo $item->number; ?>']);"<?php endif; ?>>
			<div class="w-table-col col-num"><span class="w-table-label"><?php echo Arr::get($cmslabel, 'webshop_order_number'); ?>:</span><?php echo $item->number; ?></div>
			<div class="w-table-col col-date"><span class="w-table-label"><?php echo Arr::get($cmslabel, 'date'); ?>:</span><?php echo Date::humanize($item->datetime_created); ?></div>
			<div class="w-table-col col-total">
				<span class="w-table-label"><?php echo Arr::get($cmslabel, 'amount'); ?>:</span>
				<span><?php echo Utils::currency_format($item->total * $item->exchange, $item->currency->display); ?></span>
                <?php echo Utils::get_second_pricetag_string($item->total * $item->exchange, ['currency_code' => $item->currency->code, 'display_format' => 'standard', 'order_created_date' => $item->datetime_created]); ?>
			</div>
			<?php /*
			<div class="table-col col-order-terms">
				<?php $terms_pdf = Utils::file_url($item->terms_pdf); ?>
				<?php if($terms_pdf): ?>
					<a class="btn-download btn-download-pdf btn-download-link" href="<?php echo $terms_pdf; ?>" target="_blank" title="<?php echo Arr::get($cmslabel, 'download_terms_pdf'); ?>"><span><?php echo Arr::get($cmslabel, 'download_terms_pdf'); ?></span></a>
				<?php endif; ?>
			</div>
			*/ ?>
			<div class="w-table-col col-status col-td-status">
				<?php echo $item->status->title($item->lang); ?>
				<div class="order-status">
					<?php
						$w = 30;
						if($item->status->code == 'ceka_uplatu'){
							$w = 40;
						}
						if($item->status->code == 'placeno'){
							$w = 55;
						}
						if($item->status->code == 'poslano'){
							$w = 75;
						}
						if($item->status->code == 'dostavljeno' OR $item->status->code == 'otkazano'){
							$w = 100;
						}					
					?>
					<span class="order-status-bar" style="width:<?php echo $w; ?>%;"></span>
				</div>
			</div>
			<?php if($mode != 'dashboard'): ?>
				<div class="w-table-col col-btns">
					<div class="btn-order-details" id="btn-order-<?php echo $item->number; ?>">
						<span class="btn-inactive"><?php echo Arr::get($cmslabel, 'webshop_btn_order_details', 'Prikaži detalje'); ?></span>
						<span class="btn-active"><?php echo Arr::get($cmslabel, 'webshop_btn_hide_order_details', 'Sakrij detalje'); ?></span>
						<span class="toggle-icon"></span>
					</div>
				</div>
			<?php endif; ?>
		</div>

		<!-- Order Item Details (subitems) -->
		<?php if ($item->items): ?>
			<?php  $items_qty = Catalog::products(array('lang' => $info['lang'], 'filters' => array('id' => $item->items->as_array(NULL, 'product_id')))); ?>
			<div class="order-details" id="order-detail-<?php echo $item->number; ?>">
				<div class="w-table-details">
					<?php foreach ($item->items AS $item_item): ?>
						<div class="wp wp-details">
							<div class="wp-image">
								<figure>
									<img <?php echo Thumb::generate($item_item->main_image, array('width' => 65, 'height' => 65, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-50.jpg')); ?> alt="<?php echo $item_item->title; ?>" data-product_main_image="<?php echo !empty($items_qty[$item_item->product_id]) ? $items_qty[$item_item->product_id]['shopping_cart_code'] : ''; ?>" />
								</figure>
							</div>
							<div class="wp-details-content">
								<div class="wp-cnt">
									<div class="wp-title" data-product_title="<?php echo !empty($items_qty[$item_item->product_id]) ? $items_qty[$item_item->product_id]['shopping_cart_code'] : ''; ?>"><?php echo $item_item->title; ?></div>
									<div class="wp-code"><span><?php echo Arr::get($cmslabel, 'code'); ?>:</span> <span data-product_code="<?php echo !empty($items_qty[$item_item->product_id]) ? $items_qty[$item_item->product_id]['shopping_cart_code'] : ''; ?>"><?php echo $item_item->code; ?></span></div>
								</div>
								<div class="wp-total">
									<div class="wp-price">
										<?php if ($item_item->basic_price > $item_item->price): ?>
											<span class="wp-old-price product_total_basic" data-product_basic_price="<?php echo !empty($items_qty[$item_item->product_id]) ? $items_qty[$item_item->product_id]['shopping_cart_code'] : ''; ?>">
												<span><?php echo Utils::currency_format($item_item->basic_price * $item->exchange, $item->currency->display); ?></span>
												<?php echo Utils::get_second_pricetag_string($item_item->basic_price * $item->exchange, ['currency_code' => $item->currency->code, 'display_format' => 'standard', 'order_created_date' => $item->datetime_created]); ?>
											</span>
											<span class="wp-current-price wp-price-discount product_total" data-product_price="<?php echo !empty($items_qty[$item_item->product_id]) ? $items_qty[$item_item->product_id]['shopping_cart_code'] : ''; ?>">
												<?php echo Utils::currency_format($item_item->price * $item->exchange, $item->currency->display); ?>
                                                <?php echo Utils::get_second_pricetag_string($item_item->price * $item->exchange, ['currency_code' => $item->currency->code, 'display_format' => 'standard', 'order_created_date' => $item->datetime_created]); ?>
											</span>
										<?php else: ?>
											<span class="wp-current-price product_total" data-product_price="<?php echo !empty($items_qty[$item_item->product_id]) ? $items_qty[$item_item->product_id]['shopping_cart_code'] : ''; ?>">
												<?php echo Utils::currency_format($item_item->price * $item->exchange, $item->currency->display); ?>
                                                <?php echo Utils::get_second_pricetag_string($item_item->price * $item->exchange, ['currency_code' => $item->currency->code, 'display_format' => 'standard', 'order_created_date' => $item->datetime_created]); ?>
											</span>
										<?php endif ?>
									</div>
									<div class="wp-qty-count" data-shoppingcart_product_qty_box="<?php echo $item_item->code; ?>" <?php if ((float) $item_item->qty == 1): ?> style="display: none;"<?php endif; ?>>
										<span class="product_qty"><?php echo $item_item->qty; ?></span> x 
										<?php echo Utils::currency_format($item_item->price * $item->exchange, $item->currency->display); ?>
                                        <?php echo Utils::get_second_pricetag_string($item_item->price * $item->exchange, ['currency_code' => $item->currency->code, 'display_format' => 'standard', 'order_created_date' => $item->datetime_created]); ?>
									</div>
								</div>
								<div class="wp-details-btns">
									<!-- Repeat Product Order -->
									<?php if (!empty($items_qty[$item_item->product_id]['is_available'])): ?>
										<a class="btn-order-again" href="javascript:cmswebshop.shopping_cart.add('<?php echo $items_qty[$item_item->product_id]['shopping_cart_code']; ?>', '', 'simple_loader', 'simple', 3)"><?php echo Arr::get($cmslabel, 'order_again', 'Ponovi narudžbu'); ?></a>
									<?php endif; ?>
								</div>
							</div>
						</div>
					<?php endforeach; ?>

					<div class="wp-total-sum">
						<div class="wp-total-row">
							<span class="label"><?php echo Arr::get($cmslabel, 'payment'); ?>:</span>
							<span class="value"><?php echo $item->payment->description($info['lang']); ?></span>
						</div>
						<div class="wp-total-row">
							<span class="label"><?php echo Arr::get($cmslabel, 'shipping'); ?>:</span> 
							<span class="value">
								<?php echo $item->shipping->description($info['lang']); ?> 
								<strong>(<?php echo Utils::currency_format($item->shipping_total * $item->exchange, $item->currency->display).Utils::get_second_pricetag_string($item->shipping_total * $item->exchange, ['currency_code' => $item->currency->code, 'display_format' => 'standard', 'order_created_date' => $item->datetime_created]); ?>)</strong>
							</span>
						</div>
						<?php if(!empty($item->loyalty_discount_percent)): ?>
							<div class="wp-total-row">
								<span class="label"><?php echo Arr::get($cmslabel, 'loyalty_discount'); ?>:</span> 
								<span class="value"><strong><?php echo $item->loyalty_discount_percent; ?>%</strong></span>
							</div>
						<?php endif; ?>				
						<div class="wp-total-row wp-total-row-total">
							<span class="label"><?php echo Arr::get($cmslabel, 'total'); ?></span>: 
							<span class="value">
								<?php echo Utils::currency_format($item->total * $item->exchange, $item->currency->display); ?>
                                <?php echo Utils::get_second_pricetag_string($item->total * $item->exchange, ['currency_code' => $item->currency->code, 'display_format' => 'standard', 'order_created_date' => $item->datetime_created]); ?>
							</span>
						</div>
					</div>
				</div>
			</div>
		<?php endif; ?>
	</div>
<?php endforeach; ?>