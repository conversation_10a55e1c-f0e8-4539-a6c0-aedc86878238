<?php $this->extend('default'); ?>
<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> main-offset-sm page-auth page-login<?php $this->endblock('page_class'); ?>

<?php $this->block('content_layout'); ?>
	<div class="df a-row">
		<div class="a-col a-col1">
			<div class="a-subtitle a-login-subtitle"><?php echo Arr::get($cmslabel, 'users_signup'); ?></div>
			<div class="a-cnt"><?php echo Arr::get($cmslabel, 'email_signup'); ?></div>
			<?php if ($message_type AND $message): ?>
				<?php if (is_array($message)): ?>
					<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
				<?php else: ?>
					<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}"); ?></p>
				<?php endif; ?>
			<?php endif; ?>

			<?php if ($message_type != 'success' OR (in_array($message, ['confirm_signup', 'reset_password', 'logout', 'forgotten_password']))): ?>
				<form method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" class="ajax_siteform form-label auth-form auth-form-login" data-tracking_gtm_login="userLogin|<?php echo Arr::get(Kohana::config('app.gtm.mapping_event_names'), 'userLogin', ''); ?>">
					<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
					<div class="global_success global-success" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'success_forgotten_password'); ?></div>
					<div class="global_error global-error" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'error_forgotten_password'); ?></div>
					<fieldset>
						<?php $error = ($message_type AND $message_type != 'success' AND $message) ? "{$message_type}_{$message}" : ""; ?>
						<p class="field form-inline field-checkout email">
							<input type="text" name="email" id="id_email" />
							<label for="id_email"><?php echo Arr::get($cmslabel, 'email_addres'); ?></label>
							<span id="field-error-email" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
						</p>
						<p class="field form-inline field-password">
							<input type="password" name="password" id="id_password" />
							<label for="id_password"><?php echo Arr::get($cmslabel, 'password'); ?></label>
						</p>
						<p class="field form-inline field-remember">
							<input type="checkbox" name="remember" id="id_remember" value="1" checked />
							<label for="id_remember"><?php echo Arr::get($cmslabel, 'remember'); ?></label>
						</p>
					</fieldset>
					<div class="a-btns">
						<div class="submit"><button class="btn btn-auth-submit" type="submit"><span><?php echo Arr::get($cmslabel, 'login'); ?></span></button></div>
						<div class="a-links">
							<a class="a-links-forgotten" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>"><?php echo Arr::get($cmslabel, 'forgotten_password'); ?></a>
						</div>
					</div>
				</form>

				<?php echo View::factory('auth/widget/social_login'); ?>
			<?php endif; ?>
		</div>
		
		<?php $pass_reset = Arr::get($cmslabel, 'existing_user_password_reset'); ?>
		<div class="a-col a-col2 lists">
			<?php /*if($pass_reset): ?>
				<div class="login-reset-pass"><?php echo $pass_reset; ?></div>
			<?php endif;*/ ?>

			<?php echo Arr::get($cms_page, 'content'); ?>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>" class="btn btn-login"><?php echo Arr::get($cmslabel, 'login_btn'); ?></a>
		</div>
	</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>