<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> main-offset-sm page-auth<?php $this->endblock('page_class'); ?>

<?php $this->block('breadcrumb'); ?> <?php $this->endblock('breadcrumb'); ?>

<?php $this->block('sidebar'); ?>
	<div class="a-sidebar-cnt">
		<div class="a-dashboard-title">
			<?php echo Arr::get($cmslabel, 'welcome', 'Pozdrav'); ?>, <span><?php echo $user->first_name; ?></span>
		</div>
		
		<?php if ($user): ?>
            <span data-ga4_user='<?php echo $user->id; ?>' style="display: none"></span>
			<ul class="nav-sidebar nav-auth">
				<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', '', FALSE) == $info['url']): ?> class="selected"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', FALSE); ?>"><?php echo Arr::get($cmslabel, 'my_profile'); ?></a></li>
				<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', false)== $info['url']): ?> class="selected"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', false); ?>#main"><?php echo Arr::get($cmslabel, 'my_orders'); ?></a></li>
				<?php if (Kohana::config('app.catalog.use_wishlists') AND $wishlist AND $wishlist['total_items'] > 0): ?>
					<li<?php if ($wishlist['url_'. $info['lang']] == $info['url']): ?> class="selected"<?php endif; ?>><a href="<?php echo $wishlist['url_'. $info['lang']]; ?>"><?php echo Arr::get($cmslabel, 'my_wishlist'); ?></a></li>
				<?php endif; ?>
				<?php if (!empty($user) AND !$user->b2b): ?>
					<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'my_webshopcoupon', FALSE) == $info['url']): ?> class="selected"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshopcoupon', FALSE); ?>"><?php echo Arr::get($cmslabel, 'my_coupons'); ?></a></li>
				<?php endif; ?>
				<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE) == $info['url']): ?> class="selected"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE); ?>"><?php echo Arr::get($cmslabel, 'edit_profile'); ?></a></li>
				<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE) == $info['url']): ?> class="selected"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE); ?>"><?php echo Arr::get($cmslabel, 'change_password'); ?></a></li>
				<li><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'logout', FALSE); ?>?redirect=<?php echo $info['redirect_url']; ?>"><?php echo Arr::get($cmslabel, 'logout'); ?></a></li>
			</ul>
		<?php endif; ?>
	</div>

	<?php if (!empty($user) AND !$user->b2b): ?>	
		<!-- Loyalty -->
		<div class="auth-box-loyalty<?php if ($user->loyalty_code): ?> has-loyalty-card<?php else: ?> no-loyalty-card<?php endif; ?>">
			<div class="auth-loyalty-card">
				<img src="/media/images/card-small-<?php echo $info['lang']; ?>.png" width="129" height="80" alt="">
				<span class="auth-loyalty-card-label"><?php echo Arr::get($cmslabel, 'auth_loyalty_title_nocard'); ?></span>
			</div>
			<?php if ($user->loyalty_code): ?>
				<div class="auth-loyalty-cnt">
					<?php
					$loyalty_display = Utils::barcode($user->loyalty_code);
					$barcode = (!empty($loyalty_display)) ? Html::barcode(true, $loyalty_display[0], $loyalty_display[1], 50, 'h', 2, true, true) : '';
					?>
					<p><span class="label"><?php echo Arr::get($cmslabel, 'card_number'); ?>:</span> <span class="value"><?php echo $user->loyalty_code; ?></span></p>
					<p><span class="label"><?php echo Arr::get($cmslabel, 'loyalty_points', 'Broj loyalty bodova'); ?>:</span> <span class="value"><?php echo $user->loyalty_point; ?></span></p>
					<p><span class="label"><?php echo Arr::get($cmslabel, 'loyalty_discount', 'Ostvareni popust'); ?>:</span> <span class="value"><?php echo $user->loyalty_discount_percent; ?>%</span></p>
					<?php echo Arr::get($cmslabel, 'about_loyalty'); ?>
				</div>
			<?php else: ?>
				<div class="auth-loyalty-nocard">
					<div class="auth-title-loyalty" id="loyalty_code_request"><?php echo Arr::get($cmslabel, 'auth_loyalty_title_nocard', 'Loyalty klub kartica'); ?></div>
					<div class="auth-loyalty-cnt"><?php echo Arr::get($cmslabel, 'auth_loyalty_subtitle'); ?></div>
					
					<div data-loyalty_request_hide="1">
						<div class="auth-loyalty-btns">
							<a class="btn btn-loyalty" href="javascript:cmsauth.loyalty_request('n');"><?php echo Arr::get($cmslabel, 'request_new_card'); ?></a>
							<?php echo Arr::get($cmslabel, 'about_loyalty'); ?>
						</div>
						
						<div class="auth-loyalty-field-container">
							<label for="loyalty_code"><?php echo Arr::get($cmslabel, 'add_loyalty_card'); ?></label>
							<div class="auth-loyalty-field-card">
								<input type="text" name="loyalty_code" id="loyalty_code" value="">
								<a class="btn btn-orange btn-loyalty btn-loyalty-add" href="javascript:cmsauth.loyalty_request('e');"><?php echo Arr::get($cmslabel, 'attach_card', 'Pridruži karticu'); ?></a>
								<span id="field-error-loyalty_code" class="error" style="display: none"></span>
							</div>
						</div>
					</div>
					<div id="loyalty_request_n_success" class="global-success auth-loyalty-success" style="display: none"><?php echo Arr::get($cmslabel, 'auth_loyalty_signup_success', 'Postali ste član kluba vjernosti.'); ?></div>
					<div id="loyalty_request_e_success" class="global-success auth-loyalty-success" style="display: none"><?php echo Arr::get($cmslabel, 'auth_loyalty_attach_card', 'Kartica vjernosti je uspješno pridružena.'); ?></div>
				</div>
			<?php endif; ?>
		</div>	
	<?php endif; ?>
<?php $this->endblock('sidebar'); ?>

<?php $this->block('content'); ?>
	<?php $this->block('h1'); ?>
		<h1 class="a-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	<?php $this->endblock('h1'); ?>
	
	<?php $message_type = (isset($message_type) AND $message_type) ? $message_type : Arr::get($info, 'message_type', ''); ?>
	<?php $message = (isset($message) AND $message) ? $message : Arr::get($info, 'message', ''); ?>
	<?php if ($message_type AND $message): ?>
		<?php if (is_array($message)): ?>
			<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
		<?php else: ?>
            <p class="global-<?php echo $message_type; ?>"<?php echo ($message_type.'_'.$message == 'success_confirm_signup') ? ' data-user_confirmed_registration="' . $info['user_id'] . '"' : ''; ?>><?php echo Arr::get($cmslabel, "{$message_type}_{$message}", "{$message_type}_{$message}"); ?></p>
		<?php endif; ?>
	<?php endif; ?>

	<?php echo Arr::get($cms_page, 'content'); ?>

	<?php $this->block('content2'); ?><?php $this->endblock('content2'); ?>
<?php $this->endblock('content'); ?>

<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>