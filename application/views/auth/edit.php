<?php $this->extend('auth/default'); ?>
<?php $this->block('page_class'); ?> main-offset-sm page-auth page-edit<?php $this->endblock('page_class'); ?>

<?php $this->block('content2'); ?>
	<form method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="edit" class="ajax_siteform form-label auth-form auth-form-edit" data-webshop_autocomplete="zipcode_city_location">
		<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
		<div class="a-edit-subtitle"><?php echo Arr::get($cmslabel, 'personal_data'); ?></div>
		<?php foreach ($customer_fields as $field): ?>
			<?php $error = Valid::get_error($field, $message); ?>
			<?php if($field == 'company_name'): ?>
				<div class="a-edit-subtitle"><?php echo Arr::get($cmslabel, 'auth_company_data', 'Informacije o tvrtki (za pravne osobe)'); ?></div>
			<?php endif; ?>
			<p class="field field-<?php echo $field; ?> field-edit-<?php echo $field; ?>">
				<?php if($field == 'warehouse_location'): ?>
					<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?></label>
					<?php echo $item->input($field, 'form'); ?>	
				<?php else: ?>
					<?php echo $item->input($field, 'form'); ?>	
					<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?></label>
				<?php endif; ?>
				<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
			</p>
		<?php endforeach; ?>
		<p class="submit"><button class="btn btn-auth-submit" type="submit"><?php echo Arr::get($cmslabel, 'save'); ?></button></p>
	</form>
<?php $this->endblock('content2'); ?>