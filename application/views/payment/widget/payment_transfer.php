<?php
$cms_label = isset($order_lang) ? Cms::labels($order_lang) : $cmslabel;

$description = Url::title(str_replace([
    '%NUMBER%',
    '%OIB%',
    '%ID%',
], [
    $number,
    $oib,
    (!empty($model_id) ? $model_id : $number),
], ($description  ? $description : 'Uplata')), ' ', true, false);

$reference = str_replace([
    '%NUMBER%',
    '%OIB%',
    '%ID%',
], [
    $number,
    $oib,
    (!empty($model_id) ? $model_id : $number),
], ($receive['reference_number'] ? $receive['reference_number'] : '%NUMBER%'));
$mode = (!empty($mode)) ? $mode : '';
?>

<?php if ($mode == '2d_barcode'): ?>
    <?php if (isset($order_country_code) AND $order_country_code == 'hr'): ?>
        <?php
        $hub3_path = HUB3::generate([
            'amount' => ($total * $total_exchange),
            'description' => $description,
            'purpose' => $purpose,
            's_name' => $sender['name'],
            's_street' => $sender['address'],
            's_place' => $sender['city'],
            'r_name' => $receive['name'],
            'r_street' => $receive['address'],
            'r_place' => $receive['city'],
            'r_iban' => str_replace(' ', '', $receive['bank_number']),
            'r_model' => $receive['model'],
            'r_reference' => $reference,
            'o_scale' => 1,
            'model' => (!empty($model)) ? $model : '',
            'model_id' => (!empty($model_id)) ? $model_id : '',
            'currency' => (!empty($currency_code)) ? $currency_code : 'EUR',
        ]);
        ?>
        <?php if ($hub3_path): ?>
            <img src="<?php echo $hub3_path; ?>" alt="" />
        <?php endif; ?>
    <?php else: ?>
        <p>
            <?php echo nl2br(Arr::get($receive, 'bank_account_data')); ?><br>
            <?php echo Arr::get($cms_label, 'bank_transfer_payment_model'); ?>: <?php echo $receive['model']; ?><br>
            <?php echo Arr::get($cms_label, 'bank_transfer_payment_reference_number'); ?>: <?php echo $reference; ?><br>
            <?php echo Arr::get($cms_label, 'bank_transfer_payment_description'); ?>: <?php echo $description; ?>
        </p>
    <?php endif; ?>

<?php else: ?>
	<?php if (isset($order_country_code) AND $order_country_code == 'hr'): ?>
		<div class="mail-payment-transfer" style="width:580px; background:#ffffff; color: #000000;">
			<table border="0" cellpadding="0" cellspacing="0" class="payment-transfer-template" style="width: 100%;">
				<tr>
					<td height="19" align="right" style="padding:0;color:red;font-size:13px;font-family: Arial, Helvetica, sans-serif;">
						<strong>UNIVERZALNI NALOG ZA PLACANJE</strong>
					</td>
				</tr>
			</table>
			<table border="0" cellpadding="0" cellspacing="0" style="width:100%;border:2px solid red;background:#ffffff;">
				<tbody>
					<tr>
						<td width="145" height="80" valign="top" style="padding:0;border-right: 2px solid red;width:35%;padding:0;padding-left:5px;">
							<span style="font-family:Arial, Helvetica;font-size:11px;color:red;display:block;"><strong>PLATITELJ</strong> (naziv/ime i adresa):</span>
							<span style="font-family:Geneva, Arial, Helvetica, sans-serif;font-size:12px;color:black">
								<?php echo Url::title($sender['name'], ' ', true, false); ?>
								<br /><?php echo Url::title($sender['address'], ' ', true, false); ?>
								<br /><?php echo Url::title($sender['city'], ' ', true, false); ?>
							</span>
						</td>
						<td valign="top" style="padding:0;background:#fcefe9; width:65%">
							<table width="100%" border="0" cellspacing="4" cellpadding="0" style="background:#fea78c;border-bottom:2px solid red; font-size:11px;line-height:10px;font-family:Arial, Helvetica, sans-serif;">
								<tr>
									<td height="19" width="9%" style="padding:0;padding-left:2px;font-size:11px;mso-line-height-rule:exactly;line-height:20px;">Hitno</td>
									<td height="19" width="6%" align="center" bgcolor="#FFFFFF" style="padding:0;mso-line-height-rule:exactly;line-height:20px;"></td>
									<td height="19" width="17%" style="padding:0;padding-left:10px;font-size:11px;mso-line-height-rule:exactly;line-height:10px;">Valuta<br />placanja:</td>
									<td height="19" width="12%" align="center" bgcolor="#FFFFFF" style="padding:0;mso-line-height-rule:exactly;line-height:20px;"><?php echo strtoupper($currency_code); ?></td>
									<td height="19" width="12%" style="padding:0;padding-left:10px;font-size:11px;mso-line-height-rule:exactly;line-height:20px;height:20px;">Iznos:</td>
									<td height="20" width="44%" align="right" bgcolor="#FFFFFF" style="padding:0;padding-right:10px;font-size:14px;mso-line-height-rule:exactly;line-height:20px;"><strong><?php echo Utils::price_format($total * $total_exchange); ?></strong></td>
								</tr>
							</table>
							<table width="100%" border="0" cellspacing="4" cellpadding="0" style="border-bottom:1px solid red;mso-line-height-rule:exactly;line-height:10px;font-family:Arial, Helvetica, sans-serif;">
								<tr>
									<td height="19" width="32%" style="padding:0;font-size:11px;mso-line-height-rule:exactly;line-height:10px;font-family:Arial, Helvetica, sans-serif;color:red;">IBAN ili broj racuna platitelja:</td>
									<td height="19" width="68%" align="center" bgcolor="#FFFFFF" style="padding:0;border:1px solid red;mso-line-height-rule:exactly;line-height:8px;">&nbsp;</td>
								</tr>
							</table>
							<table width="100%" border="0" cellspacing="4" cellpadding="0">
								<tr>
									<td height="19" width="17%" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;line-height:8px;">Model</td>
									<td height="19" width="6%" style="padding:0;"></td>
									<td height="19" width="77%" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;line-height:8px;">Poziv na broj platitelja</td>
								</tr>
								<tr>
									<td height="19" bgcolor="#FFFFFF" style="padding:0;font-size:11px;padding-left:10px;border:1px solid red;line-height:8px;"></td>
									<td height="19" style="padding:0;"></td>
									<td height="19" width="77%" align="center" bgcolor="#FFFFFF" style="padding:0;font-size:11px;line-height:8px;border:1px solid red;"></td>
								</tr>
							</table>
						</td>
					</tr>
				</tbody>
			</table>
			<table border="0" cellpadding="0" cellspacing="4" style="width:580px;mso-line-height-rule:exactly;line-height:10px;border:2px solid red;border-top:none;border-bottom:none;font-family:Arial, Helvetica, sans-serif;" bgcolor="#fcefe9">
				<tr>
					<td width="160" height="15" style="padding:0;padding-left:10px;font-size:11px;mso-line-height-rule:exactly;line-height:10px;font-family:Arial, Helvetica, sans-serif;color:red;">IBAN ili broj racuna<br />primatelja:</td>
					<td height="8" bgcolor="#FFFFFF" style="padding:0;padding-left:5px;border:1px solid red;font-size:13px;line-height:10px;"><strong><?php echo $receive['bank_number']; ?></strong></td>
				</tr>
			</table>
			<table border="0" cellpadding="0" cellspacing="0" style="width:580px;border-top:none;border-color:red;border-width:2px;border-style:solid;background:#ffffff;">
				<tbody>
					<tr>
						<td width="145" height="100" valign="top" style="font-family:Geneva, Arial, Helvetica, sans-serif;font-size:12px;padding:0;padding-left:5px;border-right-width:1px;border-bottom-width:1px;width:35%;color:#000;">
							<p style="font-size:11px;color:red;padding:0;margin:0;"><strong>PRIMATELJ</strong> (naziv/ime i adresa):</p>
							<p style="padding:0;margin:0;">
								<?php if (Kohana::config('app.sitename') == 'antikvarijat-studio.hr'): ?>
									<?php echo $receive['name']; ?>
								<?php else: ?>
									<?php echo Url::title($receive['name'], ' ', true, false); ?>
								<?php endif; ?>
								<br /><?php echo Url::title($receive['address'], ' ', true, false); ?>
								<br /><?php echo Url::title($receive['city'], ' ', true, false); ?>
							</p>
						</td>
						<td width="65%" valign="top" style="padding:0;background:#fcefe9;border-left:2px solid red;">
							<table width="100%" border="0" cellspacing="4" cellpadding="0" style="font-size:11px;line-height:10px;font-family:Arial, Helvetica, sans-serif;border-bottom:1px solid red;">
								<tr>
									<td height="8" width="17%" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;mso-line-height-rule:exactly;line-height:8px;">Model</td>
									<td height="8" width="6%" style="padding:0;"></td>
									<td height="8" width="77%" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;mso-line-height-rule:exactly;line-height:8px;">Poziv na broj primatelja</td>
								</tr>
								<tr>
									<td height="8" align="center" bgcolor="#FFFFFF" style="padding:0;border:1px solid red;line-height:8px;font-family:Arial, Helvetica, sans-serif;"><?php echo $receive['model']; ?></td>
									<td style="padding:0;padding-left:10px;"></td>
									<td height="19" width="77%" bgcolor="#FFFFFF" style="padding:0;padding-left:10px;border:1px solid red;line-height:8px;font-family:Arial, Helvetica, sans-serif;"><?php echo $reference; ?></td>
								</tr>
							</table>
							<table width="100%" border="0" cellspacing="4" cellpadding="0" style="font-size:11px;line-height:10px;font-family:Arial, Helvetica, sans-serif;">
								<tr>
									<td height="8" width="24%" valign="bottom" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;mso-line-height-rule:exactly;line-height:10px;">Sifra namjene:</td>
									<td width="12%" rowspan="2" valign="top" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;mso-line-height-rule:exactly;line-height:11px;">Opis placanja:</td>
									<td height="8" width="3%" rowspan="4" style="padding:0;padding-left:2px;"></td>
									<td height="8" width="61%" rowspan="4" align="center" valign="middle" bgcolor="#FFFFFF" style="padding:0;font-family:Arial, Helvetica, sans-serif;padding-left:2px;font-size:15px;border:1px solid red;"><?php echo $description; ?></td>
								</tr>
								<tr>
									<td height="19" bgcolor="#FFFFFF" style="padding:0;padding-left:2px;line-height:19px;border:1px solid red;font-family:Arial, Helvetica, sans-serif;"><?php echo $purpose; ?></td>
								</tr>
								<tr>
									<td height="8" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;mso-line-height-rule:exactly;line-height:8px;">Datum izvrsenja:</td>
									<td height="8" width="12%" style="padding:0;mso-line-height-rule:exactly;line-height:8px;"></td>
								</tr>
								<tr>
									<td height="19" colspan="2" bgcolor="#FFFFFF" style="padding:0;padding-left:2px;font-family:Arial, Helvetica, sans-serif;border:1px solid red;line-height:19px;"></td>
								</tr>
							</table>
						</td>
					</tr>
				</tbody>
			</table>

			<table border="0" cellpadding="0" cellspacing="0" style="width:580px;border-color:red;border-width:2px;border-style:solid;border-top:none;background:#ffffff;">
				<tbody>
					<tr>
						<td width="50%"  valign="top" style="padding:0;border-right-width:1px;border-bottom-width:1px;background:#fcefe9;">
							<table width="100%" border="0" cellspacing="2" cellpadding="0">
								<tr>
									<td height="8" valign="top" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;line-height:12px;">BIC i/ili naziv banke primatelja:</td>
									<td height="8" rowspan="2" valign="top" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;line-height:10px; padding-left:5px;">Primatelj<br />(osoba)</td>
									<td height="8" align="center" valign="top" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;line-height:12px;">Fizička</td>
									<td height="8" align="center" valign="top" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;line-height:12px;">Pravna</td>
								</tr>
								<tr>
									<td height="10" bgcolor="#FFFFFF" style="padding:5px;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;border:1px solid red;">
										<?php echo (!empty($receive['bank_name'])) ? $receive['bank_name'] : ''; ?>
									</td>
									<td height="10" align="center" bgcolor="#FFFFFF" style="padding:5px;border:1px solid red;"></td>
									<td height="10"align="center" bgcolor="#FFFFFF" style="padding:5px;border:1px solid red;"></td>
								</tr>
								<tr>
									<td height="35" colspan="4" bgcolor="#FFFFFF" style="padding:0;border:1px solid red;text-align: center">
										<?php if (isset($order_country_code) AND $order_country_code == 'hr'): ?>
											<?php
											$hub3_path = HUB3::generate([
												'amount' => ($total * $total_exchange),
												'description' => $description,
												'purpose' => $purpose,
												's_name' => $sender['name'],
												's_street' => $sender['address'],
												's_place' => $sender['city'],
												'r_name' => $receive['name'],
												'r_street' => $receive['address'],
												'r_place' => $receive['city'],
												'r_iban' => str_replace(' ', '', $receive['bank_number']),
												'r_model' => $receive['model'],
												'r_reference' => $reference,
												'model' => (!empty($model)) ? $model : '',
												'model_id' => (!empty($model_id)) ? $model_id : '',
												'currency' => (!empty($currency_code)) ? $currency_code : 'EUR',
											]);
											?>
											<?php if ($hub3_path): ?>
												<img src="<?php echo $hub3_path; ?>" alt="" />
											<?php endif; ?>
										<?php endif; ?>
									</td>
								</tr>
							</table>
							<table width="100%" border="0" cellspacing="2" cellpadding="0">
								<tr>
									<td height="8" width="16%" valign="top" style="padding:0;"></td>
									<td height="8" width="17%" valign="top" style="padding:0;"></td>
									<td height="8" width="9%" rowspan="2" valign="top" style="padding:0;"></td>
									<td height="8" width="23%" valign="top" style="padding:0;"></td>
									<td height="6" width="11%" align="center" valign="middle" style="padding:0;font-size:10px;font-family:Arial, Helvetica, sans-serif;color:red;line-height:6px;">BEN</td>
									<td height="6" width="12%" align="center" valign="middle" style="padding:0;font-size:10px;font-family:Arial, Helvetica, sans-serif;color:red;line-height:6px;">SHA</td>
									<td height="6" width="12%" align="center" valign="middle" style="padding:0;font-size:10px;font-family:Arial, Helvetica, sans-serif;color:red;line-height:6px;">OUR</td>
								</tr>
								<tr>
									<td width="16%" valign="top" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;line-height:9px;">Valuta<br />pokrica</td>
									<td width="17%" valign="top" bgcolor="#FFFFFF" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;border:1px solid red;">&nbsp;</td>
									<td width="23%" valign="top" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;line-height:9px;">Troskovna opcija</td>
									<td valign="top" bgcolor="#FFFFFF" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;border:1px solid red;">&nbsp;</td>
									<td width="12%" valign="top" bgcolor="#FFFFFF" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;border:1px solid red;">&nbsp;</td>
									<td width="12%" valign="top" bgcolor="#FFFFFF" style="padding:0;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;border:1px solid red;">&nbsp;</td>
								</tr>
							</table>
						</td>
						<td valign="top" style="padding:0;padding-left:5px;padding-top:2px;border-left:2px solid red;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;">Pecat korisnika PU</td>
						<td valign="top" style="padding:0;padding-left:5px;padding-top:2px;border-left:1px solid red;font-size:11px;font-family:Arial, Helvetica, sans-serif;color:red;">Potpis korisnika PU</td>
					</tr>
				</tbody>
			</table>
		</div>
	<?php else: ?>
		<h4><?php echo Arr::get($cmslabel, 'payment_slip_title'); ?></h4>
		<p>
			<strong><?php echo Arr::get($cmslabel, 'payment_slip_payer'); ?></strong>:<br>
			<?php echo $sender['name']; ?>
			<br /><?php echo $sender['address']; ?>
			<br /><?php echo $sender['city']; ?>
		</p>
		<p>
			<strong><?php echo Arr::get($cmslabel, 'payment_slip_recipient'); ?></strong>:<br>
			<?php echo $receive['name']; ?>
			<br /><?php echo $receive['address']; ?>
			<br /><?php echo $receive['city']; ?>
		</p>
		<p>
			<strong><?php echo Arr::get($cmslabel, 'payment_slip_account'); ?>:</strong> <?php echo $receive['bank_number']; ?><br>
			<strong><?php echo Arr::get($cmslabel, 'payment_slip_amount'); ?>:</strong> <?php echo Utils::price_format($total * $total_exchange); ?> <?php echo strtoupper($currency_code); ?><br>
			<?php if(!empty($receive['model'])): ?>
				<strong><?php echo Arr::get($cmslabel, 'payment_slip_model'); ?>:</strong> <?php echo $receive['model']; ?><br>
			<?php endif; ?>
			<?php if(!empty(Arr::get($cmslabel, 'payment_slip_number'))): ?>
				<strong><?php echo Arr::get($cmslabel, 'payment_slip_number'); ?>:</strong> <?php echo $reference; ?><br>
			<?php endif; ?>
			<strong><?php echo Arr::get($cmslabel, 'payment_slip_description'); ?>:</strong> <?php echo $description; ?>
		</p>		
	<?php endif; ?>
<?php endif; ?>