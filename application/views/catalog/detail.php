<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item, 'schema_org' => true]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('main'); ?>
<?php
$check_products = (Arr::get($info, 'controller', '') == 'webshop' AND Arr::get($info, 'action', '') == 'shipping');
$unavailable_items = [];
$country_code = '';
$shopping_cart_data = [];
if (!empty($customer_data['country']) AND $check_products) {
    $country = Webshop::countries([
        'lang' => $info['lang'],
        'single' => true,
        'filters' => [
            'id' => $customer_data['country'],
        ],
    ]);

    $country_code = Arr::get($country, 'code', '');
    $shopping_cart_data = Webshop::shopping_cart_data($info);
}
if ($check_products AND !empty($country_code)) {
    $unavailable_items = Webshop::get_unavailable_pickup_items($shopping_cart_data, $country_code);
}
?>

<?php
$filters = [];
if (!empty($customer_data['country'])) {
    $filters['id'] = $customer_data['country'];
} else {
    $filters['code'] = 'hr';
}

$selected_country = Webshop::countries([
    'lang' => $info['lang'],
    'filters' => $filters,
    'single' => true,
]);
?>
<?php

$webshop_only = false;
$webshop_only_warehouse_id = Kohana::config('app.catalog.webshop_only_warehouse_id');
if (!empty($webshop_only_warehouse_id)) {
    $warehouses_data = Arr::get($item, 'warehouses_ids', '');
    $warehouses_data = Text::db_to_array($warehouses_data);
    $item_warehouse_qtys = [];

    if (!empty($warehouses_data)) {
        foreach ($warehouses_data as $warehouse_data) {
            list($warehouse_id, $warehouse_qty) = explode('=', $warehouse_data);
            $item_warehouse_qtys[$warehouse_id] = $warehouse_qty;
        }

        $vp_warehoues_qty = (float)Arr::get($item_warehouse_qtys, $webshop_only_warehouse_id, 0);
        $webshop_only = (!empty($vp_warehoues_qty) AND $vp_warehoues_qty > 0);
        if (!empty($item_warehouse_qtys)) {
            foreach ($item_warehouse_qtys as $warehouse_id => $qty) {
                if ($warehouse_id != $webshop_only_warehouse_id AND (float)$qty > 0) {
                    $webshop_only = false;
                    break;
                }
            }
        }

    }
}
?>
	<?php $pickup_only = (!empty($item['type']) AND in_array($item['type'], Kohana::config('app.catalog.product_type.pickup_only'))); ?>
	<div class="cd-row">
		<div class="df cd-wrapper">
			<?php $images = Utils::get_files('catalogproduct', $item['id'], 'image', $info['lang']); ?>	
			<div class="cd-m-header-placeholder"></div>
			<div class="cd-col cd-col1<?php if (count($images) > 1): ?> multiple-images<?php endif; ?>">
				<div class="cd-container cd-container-images">
					<div class="cd-badges">
						<?php if($item['attributeitems_special']): ?>
							<?php foreach($item['attributeitems_special'] as $attr_special): ?>
								<?php if($attr_special['code'] == 'best_buy'): ?>
									<div class="cp-badge cp-badge-special cd-badge"><span><?php echo Arr::get($cmslabel, 'best_buy'); ?></span></div>
									<?php break; ?>
								<?php endif; ?>
							<?php endforeach; ?>
						<?php endif; ?>

						<!-- Priority badge (new products) -->
						<?php $product_priorities = Kohana::config('app.catalog.product_priorities'); ?>
						<?php $priority = Arr::get($product_priorities, $item['priority_2']); ?>
						<?php if ($priority): ?>
							<span class="cp-badge cp-badge-<?php echo Arr::get($priority, 'code'); ?> cd-badge cd-badge-<?php echo Arr::get($priority, 'code'); ?><?php if($item['attributeitems_special']): ?> special<?php endif; ?>"><?php echo Arr::get($cmslabel, 'priority_' . Arr::get($priority, 'code')); ?></span>
						<?php endif; ?>
					</div>

					<!-- Brand logo -->
					<?php if ($item['manufacturer_main_image']): ?>
						<div class="cd-brand">
							<a href="<?php echo $item['manufacturer_url']; ?>">
								<img <?php echo Thumb::generate($item['manufacturer_main_image'], array('width' => 250, 'height' => 30, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => TRUE, 'srcset' => '500x60r 2x')); ?> alt="<?php echo Text::meta($item['manufacturer_title']); ?>" />
							</a>
						</div>
					<?php endif; ?>

					<div class="cd-attr-container">
						<?php foreach ($item['attributes_special'] as $item_attr): ?>
							<?php if(!empty($item_attr) AND $item_attr['code'] != 'best_buy'): ?>
								<div class="cd-attr" >
									<span class="cd-attr-title"><?php echo $item_attr['title']; ?></span>
									<figure class="cd-attr-img">
										<img width="50" height="50" src="<?php echo Utils::file_url($item_attr['image']); ?>" alt="<?php echo $item_attr['title']; ?>">
									</figure>
								</div>
							<?php endif; ?>
						<?php endforeach; ?>
					</div>
			
					<div class="cd-images">
						<?php if (!empty($images) AND count($images) > 1): ?>
							<?php $t = 0; ?>
							<div class="cd-thumbs">
								<?php foreach ($images as $file): ?>
									<a class="cd-thumb<?php if($t == 0): ?> active<?php endif; ?>" data-slide-index="<?php echo $t; ?>" href="javascript:void(0);">
										<img <?php echo Thumb::generate($file['file'], array('width' => 120, 'height' => 120, 'srcset' => '250x250r 2x', 'default_image' => '/media/images/no-image-60.jpg', 'html_tag' => TRUE)); ?> alt="<?php echo Text::meta($file['description']); ?>" />
									</a>
									<?php $t++; ?>
								<?php endforeach; ?>
							</div>
						<?php endif; ?>
						<div class="cd-hero-image">
							<?php if(!empty($images)): ?>
								<?php $i = 0; ?>
								<div class="cd-hero-slider">
									<?php foreach ($images as $file): ?>
										<a class="fancybox" data-fancybox-group="gallery" href="<?php if($info['user_device'] == 'm'): ?>javascript:void(0);<?php else: ?><?php echo Utils::file_url($file['file']); ?><?php endif; ?>">
											<span><img <?php echo Thumb::generate($file['file'], ['width' => 740, 'height' => 740, 'default_image' => '/media/images/no-image-500.jpg', 'html_tag' => true]); ?><?php if(!empty($item['main_image_title'])): ?> title="<?php echo Text::meta($item['main_image_title']); ?>"<?php endif; ?> alt="<?php echo Text::meta($item['main_image_description']); ?>" /></span>
										</a>
										<?php $i++; ?>
									<?php endforeach; ?>
								</div>
							<?php else: ?>
								<img src="/media/images/no-image-500.jpg" width="500" height="500" alt="">
							<?php endif; ?>
						</div>
					</div>

					<?php $related_items = Widget_Publish::publishes(array('lang' => $info['lang'], 'related_code' => 'related', 'related_product_id' => $item['id'], 'category_code' => 'blog', 'limit' => 3)); ?>
					<?php $related_recipe_items = Widget_Publish::publishes(array('lang' => $info['lang'], 'related_code' => 'related', 'related_product_id' => $item['id'], 'category_code' => 'recipe', 'limit' => 3)); ?>
					<?php $list_recommendation = Widget_Catalog::products(['lang' => $info['lang'], 'related_code' => 'bought_together', 'related_item_id' => $item['id'], 'related_item_add' => $item, 'only_available' => TRUE, 'related_widget_data' => Arr::get($item, 'related_widget_data'), 'always_to_limit' => false, 'always_to_limit_on_empty_dataset' => true,]); ?>
					<?php $related_products = Widget_Catalog::products(array('lang' => $info['lang'], 'related_code' => 'related', 'related_item_id' => $item['id'], 'related_widget_data' => Arr::get($item, 'related_widget_data'), 'limit' => 10, 'always_to_limit' => TRUE)); ?>
					<?php $recommendation_total_selected = $item['price_custom']; ?>
					<?php $recommendation_products_selected = 1; ?>
					<?php $recommendation_products_total = 1; ?>
                    <?php $total_items = sizeof($list_recommendation); ?>

					<?php if(!empty($related_items) OR !empty($related_recipe_items) OR !empty($list_recommendation) OR !empty($item['content'])): ?>
						<ul class="cd-nav">
							<?php if(!empty($item['content'])): ?>
								<li class="cd-nav-m"><a href="#opis"><?php echo Arr::get($cmslabel, 'product_info'); ?></a></li>
							<?php endif; ?>
							<?php if(!empty($list_recommendation)): ?>
								<li><a href="#komplet"><?php echo Arr::get($cmslabel, 'buy_bundle'); ?></a></li>
							<?php endif; ?>
							<?php if(!empty($related_products)): ?>
								<?php $related_label = (Arr::get($cmslabel, 'product_related_products_link')) ? Arr::get($cmslabel, 'product_related_products_link') : Arr::get($cmslabel, 'product_related_products'); ?>
								<li><a href="#related"><?php echo $related_label; ?></a></li>
							<?php endif; ?>
							<?php if(!empty($related_items)): ?>
								<li><a href="#savjeti"><?php echo Arr::get($cmslabel, 'publish_related'); ?></a></li>
							<?php endif; ?>
							<?php if(!empty($related_recipe_items)): ?>
								<li><a href="#recepti"><?php echo Arr::get($cmslabel, 'publish_related_recipes'); ?></a></li>
							<?php endif; ?>
						</ul>
					<?php endif; ?>
				</div>

				<!-- Bought together -->			
				<?php if(!empty($list_recommendation)): ?>
					<?php
                    $recommendation_total = $list_recommendation['_basic'];
                    $recommendation_has_discount = (!empty($list_recommendation['_basic']['related_discount_percent']));
                    unset($list_recommendation['_basic']);
                    $ga4_recommended = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $list_recommendation, 'item_list_name' => Arr::get($cmslabel, 'label_bought_together'), 'item_list_id' => 'bought_together']);
					?>
					<div class="cd-related cd-bought-together" id="komplet" data-ga4_events_info='<?php echo $ga4_recommended; ?>'>
						<div class="cd-container" <?php if($pickup_only): ?><?php if (Arr::get($selected_country, 'title', '') != 'Hrvatska'): ?> style="display: none;"<?php endif; ?> data-selected_country-croatia-adtc<?php endif; ?>>
							<div class="subtitle bought-together-title"><?php echo Arr::get($cmslabel, 'bought_together'); ?></div>

							<div id="product_special_list_recommendation" class="items-list" data-tracking_gtm_bl_list="1">
								<div class="catalog-product-related-labels">
									<?php foreach ($list_recommendation as $related_item): ?>
                                        <span style="display: none;" data-tracking_gtm_bl="<?php echo $related_item['shopping_cart_code'] . '|' . $related_item['id'] . '|' . trim($related_item['title']) . '|' . trim($related_item['category_title']); ?>"></span>
										<?php $recommendation_total_selected += $related_item['price_custom']; ?>
										<?php $recommendation_products_selected += 1; ?>
										<?php $recommendation_products_total += 1; ?>
                                        <?php $ga4_data = Google4::create_event('select_item', ['cart_info' => $shopping_cart, 'items' => [$related_item]]); ?>
										<?php echo View::factory('catalog/index_entry_single', ['item' => $related_item, 'item_id' => $item['id'], 'product_priorities' => $product_priorities, 'class' => 'cp-bought-together no-shadow', 'mode' => 'bought_together', 'list' => strip_tags(Arr::get($cmslabel, 'bought_together')), 'recommendation_has_discount' => $recommendation_has_discount, 'ga4_data' => $ga4_data]); ?>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="cd-bought-together-footer">
								<div class="choosen-info">
									<div class="choosen-info-total">
										<span><?php echo Arr::get($cmslabel, 'choosen'); ?></span>
										<span class="counter">
											<span class="product_special_list_recommendation_products"><?php echo $recommendation_total['item_selected']; ?></span><span class="separator-red">/</span><span class="product_special_list_recommendation_products_total"><?php echo $recommendation_total['item_total']; ?></span>
										</span>
									</div>

									<input type="hidden" name="product_special_list_recommendation_item_price_basic" value="<?php echo $recommendation_total['item_price_basic']; ?>" />
									<input type="hidden" name="product_special_list_recommendation_item_price_all" value="<?php echo $recommendation_total['item_price_all']; ?>" />
									<input type="hidden" name="product_special_list_recommendation_related_discount_percent_general" value="<?php echo $recommendation_total['related_discount_percent']; ?>" />

									<?php // FIXME PROG aktivirati popuste kod kupovine u kompletu i testirati cijene u kombinaciji s loyalty-em i ostalim popustima ?>
									<?php if ($recommendation_total['item_price_saving'] > 0): ?>
										<div class="product_special_list_recommendation_nonediscount_box">
											<div class="total-choosen-cnt">
												<span class="label-total"><?php echo Arr::get($cmslabel, 'total'); ?>:</span>
												<strong class="product_special_list_recommendation_total total">
													<?php echo Utils::currency_format($recommendation_total['item_price_all'] * $currency['exchange'], $currency['display']); ?>
													<?php echo Utils::get_second_pricetag_string($recommendation_total['item_price_all'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
												</strong>
											</div>
										</div>
										<div class="product_special_list_recommendation_discount_box active">
											<div class="choosen-saving">
												<?php echo Arr::get($cmslabel, 'price_save'); ?> 
												<span class="product_special_list_recommendation_item_price_saving saving-label">
													<?php echo Utils::currency_format($recommendation_total['item_price_saving'] * $currency['exchange'], $currency['display']); ?>
													<?php echo Utils::get_second_pricetag_string($recommendation_total['item_price_saving'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
												</span>
											</div>
										</div>
									<?php else: ?>
										<div class="total-choosen-cnt">
											<span class="label-total"><?php echo Arr::get($cmslabel, 'total'); ?>:</span>
											<strong class="product_special_list_recommendation_total total">
												<?php echo Utils::currency_format($recommendation_total['item_price_all'] * $currency['exchange'], $currency['display']); ?>
												<?php echo Utils::get_second_pricetag_string($recommendation_total['item_price_all'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</strong>
										</div>
									<?php endif; ?>
								</div>
								<a onclick="javascript:cmswebshop.shopping_cart.gtm_add_bundle_list('recommendation','checkbox')" class="btn btn-orange btn-bought-together-add" href="javascript:cmswebshop.shopping_cart.add_special_list('recommendation:<?php echo $item['shopping_cart_code']; ?>|<?php echo $recommendation_total['list_id']; ?>', '', 1, 'input', 'simple_loader')"><span><?php echo Arr::get($cmslabel, 'add_choosen_to_shopping_cart'); ?></span></a>
							</div>

							<div class="cd-bought-together-list-message product_message_list"></div>
						</div>
					</div>
				<?php endif; ?>

				<?php if (!empty($related_items) OR !empty($related_recipe_items)): ?>
					<div class="cd-related">
						<div class="cd-container">
							<?php if (!empty($related_items)): ?>
								<!-- Related posts -->		
								<div class="cd-related-posts" id="savjeti">
									<div class="subtitle related-title"><?php echo Arr::get($cmslabel, 'publish_related'); ?></div>
									<?php echo View::factory('publish/index_entry', ['items' => $related_items, 'mode' => 'small', 'class' => 'pp-related gray-shadow cd-pp-related']); ?>
								</div>
							<?php endif; ?>

							<!-- Related recipes -->
							<?php if (!empty($related_recipe_items)): ?>	
								<div class="cd-related-recipes" id="recepti">
									<div class="subtitle related-title"><?php echo Arr::get($cmslabel, 'publish_related_recipes'); ?></div>
									<?php echo View::factory('publish/recipes/index_entry', ['items' => $related_recipe_items, 'mode' => 'list', 'class' => 'gray-shadow cd-pp-recipe']); ?>
								</div>
							<?php endif; ?>
						</div>
					</div>
				<?php endif; ?>	
			</div>
			
			<div class="cd-col cd-col2" data-tracking_gtm_impression="1|<?php echo $item['code']; ?>">
				<span style="display: none;" data-product_id="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['id']; ?></span>
				<span style="display: none;" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span>
				<span style="display: none;" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'manufacturer_title')); ?></span>
				<span style="display: none;" data-product_category_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'category_title')); ?></span>
				<span style="display: none;" data-product_category="<?php echo $item['shopping_cart_code']; ?>"><?php echo 'Kategorija ' . trim(Arr::get($item, 'category_title')); ?></span>
				<span style="display: none;" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price']).Utils::get_second_pricetag_string($item['price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>

				<?php $comment_status = (!empty($item['feedback_comment_widget'])) ? $item['feedback_comment_widget']['comments_status'] : 0; ?>
				<?php $rates_status = (!empty($item['feedback_rate_widget'])) ? $item['feedback_rate_widget']['rates_status'] : 0; ?>

				<div class="cd-header<?php if (!$item['is_available']): ?> not-available<?php endif; ?>">
					<div class="cd-container cd-container-header">	
						<div class="cd-m-header cd-m-header-top">
							<div class="bc bc-short cd-bc">
								<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
								<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
							</div>
			
							<div class="cd-info">
								<!-- Product code number -->
								<div class="cd-info-item cd-code<?php if (empty($item['feedback_rate_widget']) OR $comment_status <= 1): ?> no-feedback<?php endif; ?>"><span class="label"><?php echo Arr::get($cmslabel, 'code'); ?>:</span> <span data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span></div>
							
								<!-- Product rating -->
								<?php if (!empty($item['feedback_rate_widget'])): ?>
									<div class="cd-info-item cd-rates">
										<a href="#comments"><?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?></a>
									</div>
								<?php endif; ?>

								<?php if($comment_status > 1): ?>
									<div class="cd-info-item cd-info-link-comments"><a href="#comments"><?php echo Arr::get($cmslabel, 'view_comments'); ?></a> <span class="green counter">(<?php echo $item['feedback_comment_widget']['comments_by_lang']; ?>)</span></div>
								<?php endif; ?>
							</div>
							<div class="cd-mini-image">
								<img <?php echo Thumb::generate($item['main_image'], array('width' => 70, 'height' => 70, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> alt="" />
							</div>
							<h1 class="cd-title" data-product_title="1"><?php echo $item['seo_h1'] ?></h1>
							<div class="cd-category">
								<a href="<?php echo $item['category_url']; ?>"><?php echo $item['category_title']; ?></a>
							</div>
						</div>

						<!-- Product prices -->
						<?php if ($pickup_only): ?>
							<span class="cp-badge cp-badge-action cd-badge cd-badge-pickup"><?php echo Arr::get($cmslabel, 'pickup_only', 'Samo u poslovnici'); ?></span>
						<?php endif; ?>

						<div class="cd-price-container">
							<?php $is_loyalty = ($user AND Kohana::config('app.loyalty.use_loyalties') AND !empty($loyalty_user['active']) AND !empty($loyalty_user['discount_percent']) AND !($item['discount_percent'] > $loyalty_user['discount_percent'])) ? true : false; ?>
                            <?php $is_loyalty = ($user AND count((array)Kohana::config('app.loyalty.loyalty_countries')) > 0 AND !in_array(strtolower(Webshop::country_by_code($customer_data['country'], 'code', 'id')), (array)Kohana::config('app.loyalty.loyalty_countries'))) ? false : $is_loyalty; ?>
							<?php if (!empty($info['user_b2b'])): ?>
								<div class="cd-price">
									<div class="cd-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
										<?php echo Utils::currency_format($item['price_b2b_custom'] * $currency['exchange'], $currency['display']); ?>
										<?php echo Utils::get_second_pricetag_string($item['price_b2b_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
									</div>
								</div>
							<?php else: ?>
								<?php if($is_loyalty AND !in_array($item['type'], ['coupon'])): ?>
									<div class="cd-loyalty-price">
										<div class="cd-loyalty-club"><?php echo Arr::get($cmslabel, 'loyalty_club'); ?></div>
										<div class="cd-price">
											<div class="cd-current-price red" data-product_price_loyalty="<?php echo $item['shopping_cart_code']; ?>">
												<?php echo Utils::currency_format(((($info['user_b2b']) ? $item['price_b2b_custom'] : $item['basic_price_custom']) * (1 - ($loyalty_user['discount_percent'] / 100))) * $currency['exchange'], $currency['display']); ?>
												<?php echo Utils::get_second_pricetag_string((($info['user_b2b']) ? $item['price_b2b_custom'] : $item['basic_price_custom']) * (1 - ($loyalty_user['discount_percent'] / 100)), ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</div>
											<span class="cd-discount-badge">-<?php echo $loyalty_user['discount_percent']; ?>%</span>
											<div class="cd-save">
												<span class="label"><?php echo Arr::get($cmslabel, 'price_save'); ?></span> 
												<?php echo Utils::currency_format(($item['basic_price_custom'] - $item['loyalty_price']) * $currency['exchange'], $currency['display']); ?>
												<?php echo Utils::get_second_pricetag_string(($item['basic_price_custom'] - $item['loyalty_price']), ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</div>
											<div class="cd-old-price">
												<span><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
												<?php echo Utils::get_second_pricetag_string($item['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</div>
										</div>
										<div class="cd-tax"><?php echo Arr::get($cmslabel, 'tax_included', 'PDV uključen u cijenu'); ?></div>
										<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0 AND $item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']): ?>
											<div class="cd-tax cd-lowest-price">
												<?php echo Arr::get($cmslabel, 'lowest_price'); ?>:
												<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
												<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</div>
										<?php endif; ?>
									</div>
								<?php else: ?>
									<div class="cd-price">
										<?php if ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']): ?>
											<div class="cd-old-price"<?php if(!$is_loyalty): ?> data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>>
												<span><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
												<?php echo Utils::get_second_pricetag_string($item['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</div>
											<div class="cd-current-price red"<?php if(!$is_loyalty): ?> data-product_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>>
												<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
												<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</div>
											<span class="cd-discount-badge">-<?php echo $item['discount_percent']; ?>%</span>
											<div class="cd-save">
												<span class="label"><?php echo Arr::get($cmslabel, 'price_save'); ?> </span>
												<?php echo Utils::currency_format(($item['basic_price_custom'] - $item['price_custom']) * $currency['exchange'], $currency['display']); ?>
												<?php echo Utils::get_second_pricetag_string(($item['basic_price_custom'] - $item['price_custom']), ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</div>
										<?php else: ?>
											<div class="cd-current-price"<?php if(!$is_loyalty): ?> data-product_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>>
												<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
												<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</div>
										<?php endif; ?>
									</div>
									<div class="cd-tax"><?php echo Arr::get($cmslabel, 'tax_included', 'PDV uključen u cijenu'); ?></div>
									<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0 AND ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom'])): ?>
										<div class="cd-tax cd-lowest-price">
											<?php echo Arr::get($cmslabel, 'lowest_price'); ?>:
											<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
											<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
										</div>
									<?php endif; ?>
								<?php endif; ?>
							<?php endif; ?>
						</div>

						<!-- LOYALTY -->
						<?php if (empty($info['user_b2b']) AND Kohana::config('app.loyalty.use_loyalties') AND !empty($loyalty_user['discount_percent']) AND $item['is_available']): ?>
							<?php if (Kohana::config('app.loyalty.use_loyalties') AND !empty($loyalty_user['discount_percent']) AND empty($loyalty_user['active'])): ?>
								<?php if ($loyalty_user['discount_percent'] > $item['discount_percent'] AND !in_array($item['type'], ['coupon'])): ?>
									<?php $discount_price = $item['price_custom'] - (($item['basic_price_custom'] * (1 - ($loyalty_user['discount_percent'] / 100)))); ?>
									<?php $discount_price = Utils::currency_format($discount_price * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($discount_price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>

									<div class="cd-loyalty">
										<div class="loyalty-cnt">
											<?php echo str_replace('%AMOUNT%', $discount_price, Arr::get($cmslabel, 'join_loyalty')) ?>
											<?php if(!empty(Arr::get($cmslabel, 'loyalty_tooltip'))): ?>
												<div class="cd-tooltip cd-loyalty-tooltip">
													<?php echo Arr::get($cmslabel, 'loyalty_tooltip'); ?>
												</div>
											<?php endif; ?>
										</div>
										<div class="loyalty-checkbox">
											<input type="checkbox" name="loyalty_request_new" id="field-loyalty_request_new" value="1" <?php if (!empty($customer_data['loyalty_request_new'])): ?>checked<?php endif; ?>>
											<label for="field-loyalty_request_new"><?php echo str_replace('%AMOUNT%', $discount_price, Arr::get($cmslabel, 'join_loyalty_label', 'join_loyalty_label')) ?></label>
										</div>
									</div>
								<?php else: ?>
									<div class="cd-loyalty">
										<div class="loyalty-cnt">
											<?php echo Arr::get($cmslabel, 'join_loyalty_no_discount', 'join_loyalty_no_discount'); ?>
										</div>
										<div class="loyalty-checkbox">
											<input type="checkbox" name="loyalty_request_new" id="field-loyalty_request_new" value="1" <?php if (!empty($customer_data['loyalty_request_new'])): ?>checked<?php endif; ?>>
											<label for="field-loyalty_request_new"><?php echo Arr::get($cmslabel, 'loyalty_no_discount_confirm', 'loyalty_no_discount_confirm'); ?></label>
										</div>
									</div>
								<?php endif; ?>
							<?php endif; ?>
						<?php endif; ?>
						<!-- / LOYALTY -->

						<div class="cd-order-info<?php if (!$item['is_available']): ?> not-available<?php endif; ?>">
							<div class="cd-delivery">
								<?php echo str_replace('%AMOUNT%', Utils::currency_format($shopping_cart['total_extra_shipping_free_above'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_free_above'], ['currency_code' => $currency['code'], 'display_format' => 'standard']), Arr::get($cmslabel, (!empty($user->b2b)) ? 'free_delivery_b2b' : 'free_delivery')); ?>
							</div>
							<div class="cd-phone-order">
								<?php echo Arr::get($cmslabel, 'phone_order'); ?>
								<div class="cd-tooltip cd-phone-order-tooltip"><?php echo Arr::get($cmslabel, 'phone_order_tooltip'); ?></div>		
							</div>
						</div>

                        <?php if ($pickup_only): ?>
                            <?php /*
								$pickup_available_count = 0;
								$available_locations = '';
								$locations2 = [];
							?>
							<?php foreach ($item['warehouses'] as $location): ?>
								<?php $location_available_qty = (int)$location['available_qty']; ?>
								<?php if ($location_available_qty > 0): ?>
									<?php
										$pickup_available_count++;
										$locations2[] = $location['title'];
									?>
								<?php endif; ?>
							<?php endforeach; ?>
							<?php $available_locations = implode(', ', $locations2); */?>

                            <?php $pickup_available_count = 0; ?>
                            <?php $available_locations = ''; ?>
                            <?php foreach ($item['warehouses'] as $location): ?>
                                <?php $location_available_qty = (int)$location['available_qty']; ?>
                                <?php if ($location_available_qty > 0): ?>
                                    <?php $pickup_available_count++; ?>
                                    <?php $available_locations .= $location['title'] . ', '; ?>
                                <?php endif; ?>
                            <?php endforeach; ?>
                            <?php $available_locations = rtrim($available_locations,', ');?>

                            <div class="cd-item-status-stores">
                                <div class="status-info-label"><?php echo Arr::get($cmslabel, 'pickup_info'); ?></div>
                                <?php echo str_replace(['%COUNT%', '%LOCATIONS%'], [$pickup_available_count, $available_locations], Arr::get($cmslabel, 'available_only_in_office', '')); ?>
                            </div>
                        <?php endif; ?>

                        <?php if($selected_country === 'Hrvatska'): ?>
                            <div data-selected_country></div>
                        <?php endif; ?>

                        <?php if ($pickup_only): ?>
                            <div class="cd-change-delivery"<?php if (Arr::get($selected_country, 'title', '') == 'Hrvatska'): ?> style="display: none;"<?php endif; ?> data-selected_country-croatia>
                                <div class="cd-change-delivery-info"><?php echo Arr::get($cmslabel, 'cd_change_delivery', 'Dostava nije moguća za odabranu državu'); ?> <span class="bold2" data-selected_country><?php echo Arr::get($selected_country, 'title', ''); ?></span></div>
                                <a class="btn-autochange" href="javascript:setShippingCountry(1, true);"><?php echo Arr::get($cmslabel, 'cd_autochange_croatia', 'Promijeni državu dostave u: Hrvatska'); ?></a>
                            </div>
                        <?php endif; ?>

                        <div class="cd-add-container<?php if (!$item['is_available']): ?> not-available<?php endif; ?>" <?php if($pickup_only): ?><?php if (Arr::get($selected_country, 'title', '') != 'Hrvatska'): ?> style="display: none;"<?php endif; ?> data-selected_country-croatia-adtc<?php endif; ?>>
							<?php if ($item['is_available']): ?>
								<div class="add-to-cart-container<?php if((int) $item['available_qty'] <= 1): ?> cd-qty-limit<?php endif; ?>">
									<div class="wp-qty cd-qty">
										<?php $package_qty = (!empty($item['package_qty'])) ? $item['package_qty'] : 1; ?>
										<a class="wp-btn-qty wp-btn-dec" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', '-', 1, 0, <?php echo $package_qty; ?>, <?php echo (int) $item['available_qty']; ?>, <?php echo $package_qty; ?>);"><span class="toggle-icon"></span></a>
										<input type="text" name="qty[<?php echo $item['shopping_cart_code']; ?>]" class="wp-input-qty" value="<?php echo $package_qty; ?>" onchange="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', 'input', 1, 0, <?php echo $package_qty; ?>, <?php echo (int) $item['available_qty']; ?>, <?php echo $package_qty; ?>);" />
										<a class="wp-btn-qty wp-btn-inc" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', '+', 1, 0, <?php echo $package_qty; ?>, <?php echo (int) $item['available_qty']; ?>, <?php echo $package_qty; ?>);"><span class="toggle-icon"></span></a>
										<span class="wp-unit cd-unit"><?php echo Arr::get($cmslabel, 'product_qty', 'Kom'); ?></span>
									</div>

									<a class="btn btn-orange cd-btn-add" href="javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:detail', 'simple_loader', 'input', 3)">
										<span>
											<span class="cd-btn-add-l1"><?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?></span>
											<span class="cd-btn-add-l2"><?php echo Arr::get($cmslabel, 'add_to_cart'); ?></span>
										</span>
									</a>
								</div>
							<?php else: ?>
								<?php if (isset($item['feedback_notification_widget'])): ?>
									<!-- Qty notification form -->
									<?php $form_content = 'catalogproduct_'.$item['id'].'_'.$info['lang']; ?>
									<?php echo View::factory('feedback/notification_form', array('form_content' => $form_content)); ?>
								<?php endif; ?>
							<?php endif; ?>

							<!-- Wishlist -->
							<?php if (!empty($item['wishlist_widget'])): ?>
								<div class="cd-wishlist<?php if ($item['wishlist_widget']['active']): ?> active<?php endif; ?>" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>">
									<span>
										<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', '+', '', 2, '_tracking:detail');" class="cd-wishlist-btn cd-wishlist-add wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>"><span><?php echo Arr::get($cmslabel, 'add_to_wishlist'); ?></span></a>
										<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', '-', '', 2, '_tracking:remove');" class="cd-wishlist-btn cd-wishlist-remove wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>"><span><?php echo Arr::get($cmslabel, 'remove_from_wishlist'); ?></span></a>
									</span>
								</div>
							<?php endif; ?>
						</div>

						<?php if($info['lang'] == 'hr'): ?>
							<?php if($item['is_available'] AND !$pickup_only): ?>
								<?php if (empty($webshop_only)): ?>
									<a href="javascript:void(0);" class="cd-stores-availability cd-flyout-btn" data-flyout_code="stores">
										<span><?php echo Arr::get($cmslabel, 'warehouses_availability_status'); ?></span>
									</a>
								<?php else: ?>
									<div class="cd-webshop-only"><?php echo Arr::get($cmslabel, 'webshop_only_product'); ?></div>
								<?php endif; ?>
							<?php endif; ?>
						<?php endif; ?>

						<?php echo View::factory('catalog/widget/related_package', ['item' => $item, 'list' => 'related_package', 'is_loyalty' => $is_loyalty]); ?>
						<?php echo View::factory('catalog/widget/related_package', ['item' => $item, 'list' => 'related_flavor', 'is_loyalty' => $is_loyalty]); ?>
					</div>
				</div>
				<div class="cd-header-placeholder"></div>

				<div class="cd-cnt">
					<!-- Product description -->
					<div class="cd-container" id="opis">
						<div class="cd-tab cd-tab-content">
							<div class="cd-tab-cnt cd-tab-description lists">
								<?php if(!empty($item['element_tab_desc'])): ?>
									<?php echo $item['element_tab_desc']; ?>
								<?php endif; ?>

								<?php if(!empty($item['content'])): ?>
									<?php echo $item['content']; ?>
								<?php endif; ?>

								<!-- Related documents -->
								<?php $documents = Utils::get_files('catalogproduct', $item['id'], '-image', $info['lang']); ?>
								<?php if ($documents): ?>
									<ul class="cd-documents">
									<?php foreach ($documents as $file): ?>
										<li><a class="btn-download btn-download-link btn-download-pdf" href="<?php echo $file['url']; ?>" target="_blank" title="<?php echo Text::meta($file['description']); ?>"><span><?php echo Text::meta($file['title']); ?></span></a></li>
									<?php endforeach; ?>
									</ul>
								<?php endif; ?>

								<?php if(!empty($item['element_tab_extra'])): ?>
									<?php echo $item['element_tab_extra']; ?>
								<?php endif; ?>

								<?php if(!empty($item['element_video'])): ?>
									<?php echo $item['element_video']; ?>
								<?php endif; ?>
							</div>
						</div>

						<?php if(!empty($item['element_tab_nutritional'])): ?>
							<div class="cd-tab cd-tab-nutrition">
								<h2 class="cd-tab-title"><?php echo Arr::get($cmslabel, 'nutritional'); ?></h2>
								<div class="cd-tab-cnt"><?php echo $item['element_tab_nutritional']; ?></div>
							</div>
						<?php endif; ?>

						<?php if(!empty($item['seo_keywords_tags'])): ?>
							<div class="tags cd-tags">
								<?php foreach ($item['seo_keywords_tags'] as $tag): ?>
									<a href="<?php echo $tag['url'] ?>"><?php echo $tag['title'] ?></a><span class="comma">, </span>
								<?php endforeach ?>
							</div>
						<?php endif; ?>

						<?php echo View::factory('cms/widget/share', ['item' => isset($item) ? $item : [], 'class' => 'cd-share']); ?>
					</div>
				</div>

				<?php if($comment_status > 1): ?>
					<div class="comments cd-comments" id="comments">
						<div class="cd-container">
							<?php if($comment_status > 2): ?>
								<div class="comments-title cd-comments-title"><?php echo Arr::get($cmslabel, 'add_product_comment'); ?></div>
							<?php endif; ?>
							<?php echo View::factory('feedback/comments', ['item' => $item['feedback_comment_widget'], 'mode' => 'catalog']); ?>
						</div>
					</div>
				<?php endif; ?>

			</div>
		</div>
	
	<!-- Related products -->
	<?php if (!empty($related_products)): ?>
        <?php $ga4_related = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $related_products, 'item_list_name' => Arr::get($cmslabel, 'product_related_products'), 'item_list_id' => 'product_related_products']); ?>
		<div class="cd-rp" id="related">
			<div class="wrapper" data-ga4_events_info='<?php echo $ga4_related; ?>'>
				<div class="cd-rp-title"><?php echo Arr::get($cmslabel, 'product_related_products'); ?></div>
				<div class="cd-rp-slider slick-carousel slick-arrow3 blazy-container">
					<?php echo View::factory('catalog/index_entry', ['items' => $related_products, 'class' => 'no-shadow cp-rp', 'list' => strip_tags(Arr::get($cmslabel, 'product_related_products'))]); ?>
				</div>
			</div>
		</div>
	<?php endif; ?>

	<div class="fixed-footer" <?php if($pickup_only): ?><?php if (Arr::get($selected_country, 'title', '') != 'Hrvatska'): ?> style="display: none;"<?php endif; ?> data-selected_country-croatia-adtc<?php endif; ?>></div>
<?php $this->endblock('main'); ?>
<?php $this->block('after_main'); ?>
<?php if (empty($webshop_only)): ?>
    <?php echo View::factory('catalog/widget_detail/flyout_stores', array('item' => $item)); ?>
<?php endif; ?>
<?php $this->endblock('after_main'); ?>

<?php $this->block('page_class'); ?> page-catalog-detail header-spacing0<?php if(empty($related_products)): ?> no-related-products<?php endif; ?><?php $this->endblock('page_class'); ?>