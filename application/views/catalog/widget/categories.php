<?php $class = (isset($class)) ? ' '.$class : ''; ?>
<?php $categories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '1.2', 'hierarhy_by_position' => true)); ?>
<?php if(!empty($categories)): ?>
	<div class="wrapper categories<?php echo $class; ?>">
		<ul class="categories-widget">
			<?php foreach ($categories as $category): ?>
				<li>
					<a class="category-title" href="<?php echo $category['url']; ?>">
						<?php if(!empty($category['main_image'])): ?>
							<span class="category-image">
								<img src="<?php echo Utils::file_url($category['main_image']); ?>" loading="lazy" width="100" height="100" alt="<?php echo $category['title']; ?>">
							</span>
						<?php endif; ?>
						<span class="category-title-link"><?php echo $category['title']; ?></span>
						<span class="toggle-icon category-toggle-icon"></span>
					</a>
					<?php $subcategories = (!empty($category['children'])) ? $category['children'] : [] ?>
					<?php if(!empty($subcategories)): ?>
						<ul class="category-subnav">
							<?php foreach($subcategories as $subcategory): ?>
								<li><a href="<?php echo $subcategory['url']; ?>"><?php echo $subcategory['title']; ?></a></li>	
							<?php endforeach; ?>
							<?php if (!empty($category['total_new'])): ?>
								<li class="subcategory-new"><a href="<?php echo $category['url']; ?>?searchcode=basic&searchid=1&new=1&search_q="><?php echo Arr::get($cmslabel, 'new'); ?></a></li>
							<?php endif; ?>
							<?php if (!empty($category['total_discount'])): ?>
								<li class="subcategory-sale"><a href="<?php echo $category['url']; ?>?searchcode=basic&searchid=1&discount=1"><?php echo Arr::get($cmslabel, 'sale'); ?></a></li>
							<?php endif; ?>
							<li class="m-all-products"><a href="<?php echo $category['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all'); ?></a></li>
						</ul>
					<?php endif; ?>
				</li>
			<?php endforeach; ?>
		</ul>
	</div>
<?php endif; ?>