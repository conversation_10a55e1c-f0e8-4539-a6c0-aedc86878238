<?php $class = (isset($class)) ? ' '.$class : ''; ?>
<?php $mode = (isset($mode)) ? $mode : ''; ?>
<div class="cf<?php echo $class; ?>">
	<div class="cf-filter-header">
		<span><span class="filter-icon"></span><?php echo Arr::get($cmslabel, 'filters'); ?></span>
		<a href="javascript:void(0);" class="cf-filter-close"><span class="cf-filter-close-icon"></span></a>
	</div>
	
	<div class="cf-body">
		<!-- Selected filters -->
		<?php if ($active_filters): ?>
			<div class="cf-active">
				<div class="cf-active-title"><?php echo Arr::get($cmslabel, 'selected_filters'); ?></div>
				<?php foreach ($active_filters AS $active_filter_code => $active_filter): ?>
					<div class="cf-active-item">
						<a class="cf-active-item-link" href="<?php echo $active_filter['remove_url']; ?>">
							<?php echo $active_filter['title']; ?>
						</a>
					</div>
				<?php endforeach; ?>
				<div class="cf-active-btns">
					<a href="<?php echo (!empty($search_fields['_basic']['reset_url'])) ? $search_fields['_basic']['reset_url'] : '?';?>" class="btn-cf-active-clear"><span><?php echo Arr::get($cmslabel, 'clear_filtering'); ?></span></a>
				</div>
			</div>
		<?php endif; ?>
		
		<?php if(!empty($kind['parents'])): ?>
			<?php $start_position = explode('.', $kind['position_h'])[0]; ?>
			<?php $cat_title = (!empty($kind['parents'])) ? reset($kind['parents'])['title'] : $kind['title']; ?>
			<?php $categories_tree = Widget_Catalog::categories(['lang' => $info['lang'], 'level_range' => '2.3', 'start_position' => $start_position, 'generate_tree' => true, 'selected' => $kind['url'], 'layout_config' => ['title' => '%title_field%<span class="cf-counter">%total_field%</span>']]); ?>
			<div class="cf-item cf-item-categories active">
				<div class="cf-title cf-title-categories" onClick="toggleBox('.cf-item-categories');"><?php echo $cat_title; ?><span class="toggle-icon"></span></div>
				<div class="cf-item-wrapper scrollable cf-item-wrapper-categories-links">
					<ul class="nav-categories-sidebar">
						<?php echo $categories_tree; ?>
					</ul>
				</div>
			</div>
		<?php endif; ?>

		<form action="" method="get" id="attribute_filters_select" class="cf-form ajax_siteform">
			<?php echo $search_fields['_basic']['field']; ?>
			<div class="cf-items">
				<?php foreach ($search_fields AS $search_filter_field => $search_filter): ?>
					<?php $options = Arr::get($search_filter, 'options_details'); ?>
					<?php if (empty($options) OR !count($options)) {continue;} ?>
					<?php $template = Arr::get($search_filter, 'template'); ?>
					<?php if (!$options AND $search_filter_field !== 'categories' OR (isset($search_filter['options_total_available']) AND $search_filter['options_total_available'] == 0)) {continue;} ?>
					<?php if ($search_filter_field == 'categories' AND !empty($kind['parents']) AND $kind_content != 'manufacturer' AND !$q AND $mode != 'recipes'): ?>

					<?php else: ?>
						<div class="cf-item cf-item-<?php echo $search_filter_field; ?><?php if($search_filter['options_show']): ?> active<?php endif; ?>">

							<!-- Filter title  -->
							<div class="cf-title cf-title-<?php echo $search_filter_field; ?>"<?php if($mode != 'recipes'): ?> onClick="toggleBox('.cf-item-<?php echo $search_filter_field; ?>');"<?php endif; ?>>
								<?php $filter_title = Arr::get($cmslabel, 'f_filter_'.$search_filter_field); ?>
								<?php if($filter_title): ?>
									<?php echo $filter_title; ?>
								<?php else: ?>
									<?php echo Arr::get($search_filter, 'label', Arr::get($cmslabel, $search_filter_field, $search_filter_field)); ?>
								<?php endif; ?>
								<span class="toggle-icon"></span>
							</div>

							<div class="cf-item-wrapper scrollable cf-item-wrapper-<?php echo $search_filter_field; ?>">
								<?php $options_total = count($options); ?>
								<?php $i = 1; ?>
								<?php foreach ($options AS $option_id => $option): ?>
									<?php if ( ! $option_id) {$options_total--; continue;} ?>
									<div class="cf-row<?php if(!empty($option['level'])): ?> cf-row-level<?php echo $option['level']; ?><?php endif; ?><?php if($option['total'] <= 0): ?> cf-row-unavailable<?php endif; ?>">
										<input<?php if($option['total'] <= 0): ?> disabled<?php endif; ?> type="checkbox" id="search-<?php echo $search_filter_field; ?>-<?php echo $i; ?>" name="<?php echo $search_filter_field; ?>" value="<?php echo $option_id; ?>" <?php if ($option['selected']): ?> checked <?php endif; ?><?php if (Arr::get($search_filter, 'element_multivalue')): ?> data-element_multivalue="1"<?php endif; ?><?php if ($search_filter_field == 'categories'): ?> data-filter_hierarhy_position="<?php echo $option['position']; ?>"<?php endif; ?>>
										<label for="search-<?php echo $search_filter_field; ?>-<?php echo $i; ?>"><?php echo $option['title']; ?><span class="cf-counter"><?php echo $option['total']; ?></span></label>
									</div>
									<?php $i++; ?>
								<?php endforeach; ?>
							</div>
						</div>
					<?php endif; ?>
				<?php endforeach; ?>
			</div>
			<input type="hidden" name="search_q" value="<?php echo Arr::get($_GET, 'search_q', ''); ?>" />
			
			<div class="cf-btns<?php if($active_filters): ?> active<?php endif; ?>">
				<?php if($active_filters): ?>
					<a href="?" class="btn btn-gray btn-m-filter btn-m-cf-active-clear"><span><?php echo Arr::get($cmslabel, 'clear_selected'); ?></span></a>
				<?php endif; ?>

				<button class="btn btn-m-filter btn-m-cf-confirm" data-cmsfilter_manual_submit="1" data-cmsfilter_element="1" type="submit"><?php echo Arr::get($cmslabel, 'confirm_filters', 'Primijeni odabrano'); ?></button>
			</div>
		</form>
	</div>
</div>