<?php $list = (isset($list)) ? $list : 'related_package'; ?>

<?php $related_package = Widget_Catalog::products(array('lang' => $info['lang'], 'related_code' => $list, 'related_item_id' => $item['id'], 'related_widget_data' => Arr::get($item, 'related_widget_data'), 'limit' => 100, 'always_to_limit' => TRUE, 'always_to_limit_strict_rules' => true)); ?>
<?php if(!empty($related_package)): ?>

	<div class="cd-related-package<?php if($list == 'related_flavor'): ?> cd-related-flavor<?php endif; ?>">
		<div class="cd-related-package-title"><?php echo Arr::get($cmslabel, $list, 'Povezani'); ?></div>
		<div class="cd-related-package-items">
			<?php foreach ($related_package as $related_package_item): ?>
			<?php
				$attributes = CmsAttribute::attributeitems([
					'lang' => $info['lang'],
					'module' => 'catalog',
					//'mode' => 'id',
					'filters' => [
						'id' => Text::db_to_array($related_package_item['attributes_ids']),
					],
				]);
				?>
				<a href="<?php echo $related_package_item['url']; ?>" class="cd-related-package-item">
					<div class="cd-related-package-item-inner">
						<?php if ($list == 'related_flavor'): ?>
							<div class="cd-related-package-image">
								<img <?php echo Thumb::generate($related_package_item['main_image'], array('width' => 60, 'height' => 60, 'default_image' => '/media/images/no-image-60.jpg', 'placeholder' => '/media/images/no-image-60.jpg')); ?> alt="" />
							</div>
							<?php
							if (!empty($attributes)) {
								foreach ($attributes as $related_package_attr) {
									if ($related_package_attr['attribute_code'] === 'okus') { ?>
										<div class="cd-related-package-unit"><?php echo $related_package_attr['title']; ?></div>
									<?php }
								}
							}
							?>
						<?php else: ?>
							<?php if (!empty($related_package_item['unitd_qty'])): ?>
								<div class="cd-related-package-unit"><?php echo $related_package_item['unitd_qty'] ?><?php echo $related_package_item['unitd_unit'] ?></div>
							<?php endif ?>
						<?php endif; ?>
						<?php if (!empty($related_package_item['attributes_special']) AND $list != 'related_flavor'): ?>
							<?php foreach ($related_package_item['attributes_special'] as $related_package_attr): ?>
								<?php if ($related_package_attr['code'] == 'organic'): ?>
									<div class="cd-related-package-attr cd-related-package-attr-organic">
										<?php echo explode(" ", $related_package_attr['title'])[0]; ?>
									</div>
									<?php break; ?>
								<?php endif; ?>
							<?php endforeach; ?>
						<?php endif; ?>
						<div class="cd-related-package-price">
							<?php if (!empty($info['user_b2b'])): ?>
								<div class="cd-related-package-current-price">
									<div class="cd-related-main-price"><?php echo Utils::currency_format($related_package_item['price_b2b_custom'] * $currency['exchange'], $currency['display']); ?></div>
								</div>
							<?php else: ?>
								<?php if($is_loyalty AND $related_package_item['loyalty_price'] < $related_package_item['basic_price'] AND !in_array($related_package_item['type'], ['coupon'])): ?>
									<div class="cd-related-main-price">
										<span class="cd-related-package-old-price"><?php echo Utils::currency_format($related_package_item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
										<span class="cd-related-package-discount-price"><?php echo Utils::currency_format($related_package_item['loyalty_price'] * $currency['exchange'], $currency['display']); ?></span>
									</div>
								<?php elseif ($related_package_item['discount_percent_custom'] > 0 OR $related_package_item['price_custom'] < $related_package_item['basic_price']): ?>
									<div class="cd-related-main-price">
										<span class="cd-related-package-old-price"><?php echo Utils::currency_format($related_package_item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
										<span class="cd-related-package-discount-price"><?php echo Utils::currency_format($related_package_item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
									</div>
								<?php else: ?>
									<div class="cd-related-package-current-price">
										<div class="cd-related-main-price"><?php echo Utils::currency_format($related_package_item['price_custom'] * $currency['exchange'], $currency['display']); ?></div>
									</div>
								<?php endif; ?>
							<?php endif; ?>
						</div>
					</div>
				</a>
			<?php endforeach; ?>
		</div>
	</div>
<?php endif; ?>