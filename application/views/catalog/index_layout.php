<?php if (sizeof($items)): ?>
	<div id="items_catalog" class="c-items c-items4 c-items-main" data-infinitescroll="items_catalog" data-infinitescroll_next_page="<?php echo $pagination->next_page; ?>" data-infinitescroll_total_pages="<?php echo $pagination->total_pages; ?>" data-infinitescroll_auto_trigger="2">
		<?php
			$gtm_product_list = '';
			if(!empty($kind)) {
				$gtm_product_list = 'Kategorija ' . $kind['title'];
			}
			if(!empty($q)) {
				$gtm_product_list = 'Rezultati pretrage';
			}
		?>
		<?php echo View::factory('catalog/index_entry'.$items_layout_sufix, array('items' => $items, 'list' => $gtm_product_list, 'item_list_id' => $item_list_id, 'item_list_name' => $item_list_name)); ?>
	</div>

	<?php echo $pagination; ?>
	<?php if($pagination): ?>
	<div class="load-more-container">
		<a href="javascript:void(0);" class="btn load-more btn-load-more btn-load-more-catalog" style="display: none;"><?php echo str_replace(array('%PER_PAGE%', '%TOTAL%', '%NEXT_PAGE_FIRST_ITEM%', '%NEXT_PAGE_LAST_ITEM%'), array($pagination->items_per_page, $pagination->total_items, $pagination->next_page_first_item, $pagination->next_page_last_item), Arr::get($cmslabel, 'load_more_catalog', 'Učitaj još proizvoda')); ?></a>
	</div>	
	<?php endif; ?>
<?php else: ?>
	<!-- IS SEARCH -->
	<?php if ($q): ?>
		<div class="c-empty"><?php echo Arr::get($cmslabel, 'search_no_products'); ?></div>
	<?php else: ?>
		<div class="c-empty"><?php echo Arr::get($cmslabel, 'no_products'); ?></div>
	<?php endif; ?>
<?php endif; ?>