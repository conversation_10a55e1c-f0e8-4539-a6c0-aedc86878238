<?php $mode = (isset($mode))? $mode : ''; ?>
<?php $class = (isset($class))? ' '.$class : ''; ?>
<?php $is_loyalty = ($user AND Kohana::config('app.loyalty.use_loyalties') AND !empty($loyalty_user['discount_percent']) AND !(!empty($item['discount_percent']) AND $item['discount_percent'] > $loyalty_user['discount_percent'])) ? true : false; ?>
<?php $country_id = ($user AND $user->country->id) ? $user->country->id : Webshop::country_by_code($info['country_code']); ?>
<?php $country_id = (!empty($customer_data['country'])) ? $customer_data['country'] : $country_id; ?>
<?php $is_loyalty = ($user AND count((array)Kohana::config('app.loyalty.loyalty_countries')) > 0 AND !in_array(strtolower(Webshop::country_by_code($country_id, 'code', 'id')), (array)Kohana::config('app.loyalty.loyalty_countries'))) ? false : $is_loyalty; ?>
<?php $i = (isset($i)) ? $i : 0; ?>
<?php $list = (isset($list)) ? $list : ''; ?>
<?php $recommendation_has_discount = (isset($recommendation_has_discount) AND !empty($recommendation_has_discount)) ? $recommendation_has_discount : false; ?>
<?php
if ($mode == 'bought_together' AND $recommendation_has_discount) {
    $is_loyalty = false;
}
?>
<?php $pickup_only = (!empty($item['type']) AND in_array($item['type'], Kohana::config('app.catalog.product_type.pickup_only'))); ?>
<?php
	$filters = [];
	if (!empty($customer_data['country'])) {
		$filters['id'] = $customer_data['country'];
	} else {
		$filters['code'] = 'hr';
	}

	$selected_country = Webshop::countries([
		'lang' => $info['lang'],
		'filters' => $filters,
		'single' => true,
	]);
?>
<div
	<?php if($mode == 'bought_together'): ?> id="product_special_list_recommendation-descriptions-<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>
	class="cp
	<?php if($mode == 'instashop' AND $info['user_device'] == 'm'): ?>
		cp-list
	<?php else: ?>
		<?php if($mode == 'list' OR $mode == 'bought_together'): ?> cp-list<?php else: ?> cp-grid<?php endif; ?><?php echo $class; ?>
	<?php endif; ?><?php if(!$item['is_available']): ?> cp-unavailable<?php endif; ?>"
	<?php if ($mode == 'wishlist' AND !empty($item['wishlist_widget']['content'])): ?> id="wishlistitem_details_<?php echo $item['wishlist_widget']['content']; ?>" data-wishlistitem_details="<?php echo $item['wishlist_widget']['content']; ?>"<?php endif; ?>
	<?php if($mode == 'instashop'): ?> id="<?php echo $item['id']; ?>"<?php endif; ?>
    data-tracking_gtm_impression="<?php echo $i; ?>|<?php echo $item['code']; ?>|<?php echo $list; ?>"
>
	<?php if ($mode == 'list'): ?>
		<input type="checkbox" name="product_special_list_related" id="product_special_list_related-<?php echo $item['shopping_cart_code']; ?>" value="<?php echo $item['shopping_cart_code']; ?>" checked="checked" style="display: none" />
		<input type="hidden" name="price[<?php echo $item['shopping_cart_code']; ?>]" value="<?php echo $item['price_custom']; ?>" />
	<?php endif; ?>

    <span style="display: none;" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span>
    <span style="display: none;" data-product_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['title']; ?></span>
    <span style="display: none;" data-product_price="<?php echo $item['shopping_cart_code'];; ?>"><?php echo Utils::currency_format($item['price']); ?></span>
    <span style="display: none;" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'manufacturer_title', '')); ?></span>
    <span style="display: none;" data-product_category_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'category_title')); ?></span>
    <span style="display: none;" data-product_category="<?php echo $item['shopping_cart_code']; ?>"><?php echo 'Kategorija ' . trim(Arr::get($item, 'category_title')); ?></span>

	<div class="cp-col cp-col1">
		<?php if($mode != 'bought_together' AND !empty($item['attributes_special'])): ?>
			<div class="cp-attr-container">
				<?php $as = 1; ?>
				<?php foreach ($item['attributes_special'] as $item_attr): ?>
					<?php if(!empty($item_attr) AND $item_attr['code'] != 'best_buy'): ?>
						<div class="cp-attr">
							<span class="cp-attr-title"><?php echo $item_attr['title']; ?></span>
							<span class="cp-attr-image"><img src="<?php echo Utils::file_url($item_attr['image']); ?>" alt="<?php echo $item_attr['title']; ?>" loading="lazy" width="50" height="50"></span>
						</div>
						<?php if($as == 3) break; ?>
						<?php $as++; ?>
					<?php endif; ?>
				<?php endforeach; ?>
			</div>
		<?php endif; ?>

		<?php if(!empty($item['manufacturer_main_image'])): ?>
            <div class="cp-brand">
                <img <?php echo Thumb::generate($item['manufacturer_main_image'], array('width' => 110, 'height' => 40, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> loading="lazy" alt=""/>
            </div>
		<?php endif; ?>
		
		<div class="cp-image">
			<div class="cp-badges">
				<?php if(!empty($info['user_b2b']) AND $item['discount_percent_b2b_custom'] > 0 OR $item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']): ?>
					<?php if (!empty($info['user_b2b'])): ?>
						<?php if ($item['discount_percent_b2b_custom'] > 0): ?>
							<span class="cp-badge cp-badge-discount<?php if($item['attributeitems_special']): ?> special<?php endif; ?>">-<?php echo (1 - ($item['price_b2b_custom'] / $item['basic_price_b2b_custom'])) * 100; ?>%</span>
						<?php endif; ?>
					<?php else: ?>
						<?php if($is_loyalty AND $item['loyalty_price'] < $item['basic_price']): ?>
							<span class="cp-badge cp-badge-discount">-<?php echo $loyalty_user['discount_percent']; ?>%</span>
						<?php else: ?>
							<?php if ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']): ?>
								<span class="cp-badge cp-badge-discount">-<?php echo $item['discount_percent']; ?>%</span>
							<?php endif; ?>
						<?php endif; ?>
					<?php endif; ?>
				<?php elseif(!empty($item['attributeitems_special'])): ?>
					<span class="cp-badge cp-badge-special"><span><?php echo Arr::get($cmslabel, 'best_buy'); ?></span></span>
				<?php else: ?>
					<?php $product_priorities = Kohana::config('app.catalog.product_priorities'); ?>
					<?php $priority = Arr::get($product_priorities, $item['priority_2']); ?>
					<?php if ($priority): ?>
						<span class="cp-badge cp-badge-<?php echo Arr::get($priority, 'code'); ?><?php if($item['attributeitems_special']): ?> special<?php endif; ?>"><?php echo Arr::get($cmslabel, 'priority_' . Arr::get($priority, 'code')); ?></span>
					<?php endif; ?>
				<?php endif; ?>
                <?php if ($pickup_only):?>
                    <?php $pickup_available_count = 0; ?>
                    <?php foreach ($item['warehouses'] AS $location): ?>
                        <?php $location_available_qty = (int)$location['available_qty']; ?>
                        <?php if ($location_available_qty > 0): ?>
                            <?php $pickup_available_count++; ?>
                        <?php endif; ?>
                    <?php endforeach; ?>
                    <span class="cp-badge cp-badge-action cp-badge-pickup"><span><?php echo Arr::get($cmslabel, 'pickup_only', 'Samo u poslovnici'); ?> (<?php echo $pickup_available_count; ?>)</span></span>
                <?php endif; ?>
			</div>

			<a class="cp-main-image" href="<?php echo $item['url']; ?>" target="_parent" data-product_id="<?php echo $item['id']; ?>" data-create_ga4_event='<?php echo $ga4_data; ?>'>
				<img loading="lazy" data-lazy="<?php echo Thumb::generate($item['main_image'], 300, 295, false, 'thumb', TRUE, '/media/images/no-image-300.jpg'); ?>" <?php echo Thumb::generate($item['main_image'], array('width' => 300, 'height' => 295, 'default_image' => '/media/images/no-image-300.jpg', 'html_tag' => true, 'srcset' => '600x600r 2x')); ?><?php if(!empty($item['main_image_title'])): ?> title="<?php echo Text::meta($item['main_image_title']); ?>"<?php endif; ?> alt="<?php echo ($item['main_image_description']) ? Text::meta($item['main_image_description']) : $item['title']; ?>" data-product_main_image="<?php echo $item['shopping_cart_code']; ?>" />
			</a>
		</div>
	</div>
	
	<div class="cp-col cp-col2">
		<?php if (!empty($item['wishlist_widget']) AND $mode != 'bought_together'): ?>
			<div class="cp-wishlist<?php if ($item['wishlist_widget']['active']): ?> active<?php endif; ?>" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>">
				<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', '+', '', 2, '_tracking:index');" class="cp-wishlist-btn cp-wishlist-add wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>"><span><?php echo Arr::get($cmslabel, 'add_to_wishlist'); ?></span></a>
				<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', 'remove', '', 2, '_tracking:remove');" class="cp-wishlist-btn cp-wishlist-remove wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>"><span><?php echo Arr::get($cmslabel, 'remove_from_wishlist'); ?></span></a>
				<span class="product-in-wishlist wishlist_message wishlist_message_<?php echo $item['wishlist_widget']['content']; ?> wishlist-message wishlist-message-<?php echo $item['id']; ?>" style="display: none;"></span>
			</div>
		<?php endif; ?>
		<div class="cp-cnt">
			<!-- Rating -->
			<div class="cp-rate <?php if (isset($item['feedback_comment_widget']) AND Arr::get($item['feedback_comment_widget'], 'comments', 0) > 0): ?> has-comments<?php endif; ?>">
				<?php if(!empty($item['feedback_rate_widget']) AND $item['feedback_rate_widget']['rates_status'] > 1): ?>
					<?php if (isset($item['feedback_rate_widget'])): ?>
						<?php if($item['feedback_rate_widget']['rates_votes'] > 0): ?>
							<?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
						<?php endif; ?>
					<?php endif; ?>
					<?php if(!empty($item['feedback_comment_widget']['comments_by_lang']) AND $item['feedback_comment_widget']['comments_by_lang'] > 0): ?>
                        <?php $max_per_page = Kohana::config('app.feedback.comments.per_page'); ?>
						<div class="cp-rate-counter">(<?php echo $item['feedback_comment_widget']['comments_by_lang']; ?>)</div>
					<?php endif; ?>
				<?php endif; ?>
			</div>
			
			<a href="<?php echo $item['category_url']; ?>" target="_parent" class="cp-category"><?php echo $item['category_title']; ?></a>		
			<div class="cp-title"><a href="<?php echo $item['url']; ?>" target="_parent" data-product_title="<?php echo $item['shopping_cart_code']; ?>" data-product_id="<?php echo $item['id']; ?>" data-create_ga4_event='<?php echo $ga4_data; ?>'><?php echo $item['title']; ?> </a></div>

			<?php if($mode == 'bought_together'): ?>
				<div class="cp-bought-together-message product_message product_message_<?php echo $item['shopping_cart_code']; ?>" style="display: none"></div>
			<?php endif; ?>
		</div>

		<div class="cp-footer">
			<div class="cp-price">
				<?php if (!empty($info['user_b2b'])): ?>
					<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
						<?php echo Utils::currency_format($item['price_b2b_custom'] * $currency['exchange'], $currency['display']); ?>
						<?php echo Utils::get_second_pricetag_string($item['price_b2b_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
					</div>
				<?php else: ?>
					<?php if($is_loyalty AND $item['loyalty_price'] < $item['basic_price'] AND !in_array($item['type'], ['coupon'])): ?>
						<div class="cp-old-price" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>">
							<span><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
							<?php echo Utils::get_second_pricetag_string($item['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
						<div class="cp-discount-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
							<?php echo Utils::currency_format($item['loyalty_price'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($item['loyalty_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
						<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0 AND ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom'])): ?>
							<div class="cp-lowest-price">
								<?php echo Arr::get($cmslabel, 'lowest_price'); ?>:
								<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
						<?php endif; ?>
					<?php elseif ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price']): ?>
						<div class="cp-old-price" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>">
							<span><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
							<?php echo Utils::get_second_pricetag_string($item['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
						<div class="cp-discount-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
							<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
						<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
							<div class="cp-lowest-price">
								<?php echo Arr::get($cmslabel, 'lowest_price'); ?>:
								<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
						<?php endif; ?>
					<?php else: ?>
						<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
							<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
					<?php endif; ?>
				<?php endif; ?>
			</div>
		
			<?php $package_qty = (!empty($item['package_qty'])) ? $item['package_qty'] : 1; ?>
			<div class="cp-addtocart<?php if($item['available_qty'] <= $package_qty): ?> cp-addtocart-single<?php endif; ?> <?php if($pickup_only): ?><?php if (Arr::get($selected_country, 'title', '') != 'Hrvatska'): ?> pickup-not-visible<?php endif; ?><?php endif; ?>"<?php if($pickup_only): ?><?php if (Arr::get($selected_country, 'title', '') != 'Hrvatska'): ?> style="display: none;"<?php endif; ?> data-selected_country-croatia-adtc<?php endif; ?>>
				<?php if($item['is_available']): ?>	
					<div class="cp-qty<?php if($item['available_qty'] <= $package_qty): ?> cp-qty-single<?php endif; ?>">
						<a href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', '-', 1, 0, <?php echo $package_qty; ?>, <?php echo (int) $item['available_qty']; ?>, <?php echo $package_qty; ?>);" class="wp-btn-qty wp-btn-dec"><span class="toggle-icon"></span></a>
						<input type="text" name="qty[<?php echo $item['shopping_cart_code']; ?>]" class="wp-input-qty" value="<?php echo $package_qty; ?>" />
						<a href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', '+', 1, 0, <?php echo $package_qty; ?>, <?php echo (int) $item['available_qty']; ?>, <?php echo $package_qty; ?>);" class="wp-btn-qty wp-btn-inc"><span class="toggle-icon"></span></a>
					</div>

					<?php if($mode == 'bought_together'): ?>
						<div class="cp-checkbox">
							<input type="hidden" name="price[<?php echo $item['shopping_cart_code']; ?>]" value="<?php echo $item['price_custom']; ?>" />
							<input type="checkbox"<?php if (!empty($item_id) AND $item_id == $item['id']): ?> disabled<?php endif; ?> name="product_special_list_recommendation" id="product_special_list_recommendation-<?php echo $item['shopping_cart_code']; ?>" value="<?php echo $item['shopping_cart_code']; ?>" onclick="javascript:cmswebshop.shopping_cart.calculate_special_list('recommendation');" checked="checked" />
							<label for="product_special_list_recommendation-<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($cmslabel, 'bought_together_label'); ?></label>
						</div>
					<?php else: ?>
                        <?php $ga4_code = $item['id'] . '_' . $item_list_id; ?>
						<a class="btn btn-orange cp-btn-addtocart<?php if($item['available_qty'] <= $package_qty): ?> cp-btn-addtocart-single<?php endif; ?>" data-ga4_code="<?php echo $ga4_code; ?>" title="<?php echo Arr::get($cmslabel, 'add_to_cart'); ?>"  href="javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:index', 'simple_loader', 'input', 1)">
							<span><?php echo Arr::get($cmslabel, 'add_to_cart'); ?></span>
						</a>
						<?php if($mode == 'instashop' AND $info['user_device'] == 'm'): ?>
							<span class="cp-add-success product_message product_message_<?php echo $item['shopping_cart_code']; ?>" style="display: none"></span>
						<?php endif; ?>
					<?php endif; ?>
				<?php else: ?>
					<span class="cp-unavailable-label"><?php echo Arr::get($cmslabel, 'unavailable'); ?></span>
					<a class="btn cp-btn-detail" href="<?php echo $item['url']; ?>"><span></span></a>
				<?php endif; ?>
			</div>
		</div>
	</div>
</div>