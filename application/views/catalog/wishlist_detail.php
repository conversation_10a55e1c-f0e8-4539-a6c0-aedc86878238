<?php if (!empty($user)): ?>
	<?php $this->extend('auth/default'); ?>
	
	<?php $this->block('h1'); ?>
		<h1 class="a-title">
			<?php echo Arr::get($cmslabel, 'my_wishlist'); ?>
			<?php if (!empty($wishlist) AND $wishlist['total_items'] > 0): ?>
				<span class="a-title-counter a-title-wishlist-counter<?php if ($wishlist AND $wishlist['total_items'] > 0): ?> active<?php endif; ?>" data-wishlist_active="1">(<span class="wishlist_count"><?php echo $item['total_items']; ?></span>)</span>
			<?php endif; ?>
		</h1>
	<?php $this->endblock('h1'); ?>

	<?php $this->block('content2'); ?>
		<?php if ($item['total_items'] > 0): ?>
			<div id="view_wishlist" class="auth-wishlist-items">
				<?php if (!empty($wishlist['url_delete'])): ?>
					<div class="wishlist-auth-btns wishlist-auth-btns-top">
						<a class="btn btn-white btn-wishlist-delete btn-auth-wishlist-delete" href="javascript:cmswishlist.remove('catalog_catalogproduct')"><span><?php echo Arr::get($cmslabel, 'wishlist_delete'); ?></span></a>
					</div>
				<?php endif; ?>

				<div id="items_wishlist" class="cart-wishlist-items">
					<?php echo View::factory('catalog/index_entry_wishlist', ['items' => $item['items'], 'class' => 'wp-auth-wishlist', 'mode' => 'auth-wishlist', 'list' => 'Lista želja']); ?>
				</div>

				<?php if ($item['total_items'] > 6 AND !empty($wishlist['url_delete'])): ?>
					<div class="wishlist-auth-btns wishlist-auth-btns-bottom">
						<a class="btn btn-white btn-wishlist-delete btn-auth-wishlist-delete" href="javascript:cmswishlist.remove('catalog_catalogproduct')"><span><?php echo Arr::get($cmslabel, 'wishlist_delete'); ?></span></a>
					</div>
				<?php endif; ?>
			</div>

			<div class="c-empty wishlist-empty" id="empty_wishlist" style="display: none;">
				<div class="no-wishlist-title"><?php echo Arr::get($cmslabel, 'wishlist_no_products'); ?></div>
			</div>
		<?php else: ?>
			<div class="c-empty wishlist-empty">
				<div class="no-wishlist-title"><?php echo Arr::get($cmslabel, 'wishlist_no_products'); ?></div>
			</div>
		<?php endif; ?>
	<?php $this->endblock('content2'); ?>
<?php else: ?>
	<?php $this->extend('default'); ?>
	
	<?php $this->block('page_class'); ?> page-wishlist white-bg<?php $this->endblock('page_class'); ?>

	<?php $this->block('after_header'); ?>
		<div class="wrapper wishlist-header">
			<h1 class="wishlist-title"><?php echo Arr::get($cmslabel, 'wishlist'); ?> <span class="wishlist-title-counter"><span class="wishlist_count<?php if (!empty($item['total_items']) AND $item['total_items'] > 0): ?> active<?php endif; ?>" data-wishlist_active="1"><?php echo $item['total_items']; ?></span></span></h1>
			<?php if (!empty($item['total_items']) AND $item['total_items'] > 0 AND !empty($wishlist['url_delete'])): ?>
				<a class="btn btn-white btn-wishlist-delete" href="javascript:cmswishlist.remove('catalog_catalogproduct')"><span><?php echo Arr::get($cmslabel, 'wishlist_delete'); ?></span></a>
			<?php endif; ?>
		</div>
	<?php $this->endblock('after_header'); ?>

	<?php $this->block('main'); ?>
		<div class="wrapper wrapper-wishlist">
			<?php if ($item['total_items'] > 0): ?>
				<div id="view_wishlist">
					<div class="c-items c-items5 wishlist-items">
						<?php echo View::factory('catalog/index_entry', ['items' => $item['items'], 'mode' => 'wishlist', 'list' => 'Lista želja']); ?>
					</div>

					<?php if (!empty($wishlist['url_delete']) AND $item['total_items'] > 5): ?>
						<div class="wishlist-btns">
							<a class="btn btn-white btn-wishlist-delete" href="javascript:cmswishlist.remove('catalog_catalogproduct')"><span><?php echo Arr::get($cmslabel, 'wishlist_delete'); ?></span></a>
						</div>
					<?php endif; ?>
				</div>

				<div class="c-empty wishlist-empty" id="empty_wishlist" style="display: none;">
					<div class="no-wishlist-title"><?php echo Arr::get($cmslabel, 'wishlist_no_products'); ?></div>
				</div>
			<?php else: ?>
				<div class="c-empty wishlist-empty">
					<div class="no-wishlist-title"><?php echo Arr::get($cmslabel, 'wishlist_no_products'); ?></div>
				</div>
			<?php endif; ?>
		</div>
	<?php $this->endblock('main'); ?>	
<?php endif; ?>

<?php $this->block('title'); ?><?php echo $cmslabel['my_wishlist']; ?><?php $this->endblock('title'); ?>