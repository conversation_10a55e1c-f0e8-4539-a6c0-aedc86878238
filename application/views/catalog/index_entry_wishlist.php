<?php $mode = (isset($mode))? $mode : ''; ?>
<?php $class = (isset($class))? ' '.$class : ''; ?>
<?php $list = (isset($list)) ? $list : ''; ?>
<?php $i = 1;
$ga4_data = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $items, 'item_list_name' => (!empty($ga4_list) ? $ga4_list : $list), 'item_list_id' => (!empty($ga4_list) ? Url::title($ga4_list, '-', true) : Url::title($list, '-', true))]); ?>
<div style="display: none" data-ga4_events_info='<?php echo $ga4_data;?>'></div>
<?php foreach ($items as $item): ?>
	<?php $is_loyalty = ($user AND Kohana::config('app.loyalty.use_loyalties') AND !empty($loyalty_user['discount_percent']) AND !($item['discount_percent'] > $loyalty_user['discount_percent'])) ? true : false; ?>
    <?php $is_loyalty = ($user AND count((array)Kohana::config('app.loyalty.loyalty_countries')) > 0 AND !in_array(strtolower(Webshop::country_by_code($customer_data['country'], 'code', 'id')), (array)Kohana::config('app.loyalty.loyalty_countries'))) ? false : $is_loyalty; ?>
    <?php $content = (!empty($item['wishlist_widget']['content'])) ? $item['wishlist_widget']['content'] : ''; ?>
    <article class="wp<?php if (!$item['is_available']): ?> unavailable<?php endif; ?><?php echo $class; ?>" id="wishlistitem_details_<?php echo $content; ?>" data-wishlistitem_details="<?php echo $item['shopping_cart_code']; ?>" data-tracking_gtm_impression="<?php echo $i; ?>|<?php echo $item['code']; ?>|<?php echo $list; ?>">
		<?php if ($item['is_available']): ?>	
			<input type="hidden" name="price[<?php echo $item['shopping_cart_code']; ?>]" value="<?php echo $item['price_custom']; ?>" />
			<input type="hidden" name="product_special_list_wishlist" value="<?php echo $item['shopping_cart_code']; ?>" />
		<?php endif; ?>
		<span style="display: none;" data-product_id="<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($item, 'product_id', ''); ?></span>
		<span style="display: none;" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span>
		<span style="display: none;" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'manufacturer_title', '')); ?></span>
		<span style="display: none;" data-product_category_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'category_title')); ?></span>
		<span style="display: none;" data-product_category="<?php echo $item['shopping_cart_code']; ?>"><?php echo 'Kategorija ' . trim(Arr::get($item, 'category_title')); ?></span>

		<div class="wp-image">
			<figure>
				<a href="<?php echo $item['url']; ?>">
					<img <?php echo Thumb::generate($item['main_image'], array('width' => 70, 'height' => 70, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?><?php if(!empty($item['main_image_title'])): ?> title="<?php echo Text::meta($item['main_image_title']); ?>"<?php endif; ?> alt="<?php echo ($item['main_image_description']) ? Text::meta($item['main_image_description']) : $item['title']; ?>" data-product_main_image="<?php echo $item['shopping_cart_code']; ?>" />
				</a>
			</figure>
		</div>
		
		<div class="wp-content">
			<div class="wp-cnt">			
				<div class="cp-title"><a href="<?php echo $item['url']; ?>" data-product_title="<?php echo $item['shopping_cart_code']; ?>" data-product_id="<?php echo $item['id']; ?>"><?php echo $item['title']; ?></a></div>	
				<?php if($mode == 'auth-wishlist'): ?>
					<?php if(!empty($item['product_code'])): ?><div class="cp-code"><?php echo Arr::get($cmslabel, 'code'); ?>: <?php echo $item['product_code']; ?></div><?php endif; ?>
				<?php else: ?>
					<?php if(!empty($item['code'])): ?><div class="cp-code"><?php echo Arr::get($cmslabel, 'code'); ?>: <?php echo $item['code']; ?></div><?php endif; ?>
				<?php endif; ?>
				<div class="wp-btns">
					<?php if (!empty($item['wishlist_widget'])): ?>
						<a class="wp-btn wp-btn-delete wishlist_set_<?php echo $content; ?> wishlist_active" href="javascript:cmswishlist.set('<?php echo $content; ?>', 'remove', '', 1, '_tracking:remove');">
							<?php echo Arr::get($cmslabel, 'remove_from_wishlist'); ?>
						</a>
					<?php endif; ?>
					
					<?php $product_in_cart = Webshop::get_product_status($item['shopping_cart_code'], $shopping_cart_status); ?>
					<?php if($item['is_available']): ?>
						<a class="wp-btn wp-btn-delete wp-btn-add<?php if($product_in_cart): ?> active<?php endif; ?>" data-shoppingcart_product_active="<?php echo $item['shopping_cart_code']; ?>" title="<?php echo Arr::get($cmslabel, 'add_to_cart'); ?>"  href="javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:index', 'simple_loader', 'input', 1)">
							<span class="a"><?php echo Arr::get($cmslabel, 'move_to_cart'); ?></span>
							<span class="b"><?php echo Arr::get($cmslabel, 'product_in_cart'); ?></span>
						</a>
					<?php endif; ?>
				</div>
			</div>

			<div class="wp-total">
				<?php if (!empty($info['user_b2b'])): ?>
					<?php if ($item['discount_percent_b2b_custom'] > 0): ?>
						<div class="wp-old-price" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>">
							<?php echo Utils::currency_format($item['basic_price_b2b_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($item['basic_price_b2b_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
						<div class="wp-discount-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
							<?php echo Utils::currency_format($item['price_b2b_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($item['price_b2b_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
						<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
							<div class="wp-lowest-price">
								<?php echo Arr::get($cmslabel, 'lowest_price'); ?>:
								<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
						<?php endif; ?>
					<?php else: ?>
						<div class="wp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
							<?php echo Utils::currency_format($item['price_b2b_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($item['price_b2b_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
					<?php endif; ?>
				<?php else: ?>
					<?php if($is_loyalty AND $item['loyalty_price'] < $item['basic_price']): ?>
						<div class="wp-old-price" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>">
							<span><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
							<?php echo Utils::get_second_pricetag_string($item['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
						<div class="wp-discount-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
							<?php echo Utils::currency_format($item['loyalty_price'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($item['loyalty_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
						<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
							<div class="wp-lowest-price">
								<?php echo Arr::get($cmslabel, 'lowest_price'); ?>:
								<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
						<?php endif; ?>
					<?php elseif ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price']): ?>
						<div class="wp-old-price" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>">
							<span><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
							<?php echo Utils::get_second_pricetag_string($item['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
						<div class="wp-discount-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
							<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
						<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
							<div class="wp-lowest-price">
								<?php echo Arr::get($cmslabel, 'lowest_price'); ?>:
								<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
						<?php endif; ?>
					<?php else: ?>
						<div class="wp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
							<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						</div>
					<?php endif; ?>
				<?php endif; ?>
			</div>
		</div>
	</article>
	<?php $i++; ?>
<?php endforeach; ?>