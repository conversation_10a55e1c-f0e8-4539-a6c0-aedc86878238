<div class="cd-tab-info-body cd-tab-body-warehouse flyout cd-flyout" data-flyout="stores">
    <div class="cd-flyout-close"></div>
    <div class="cd-flyout-title"><?php echo Arr::get($cmslabel, 'cd_availability_title'); ?></div>
    <div class="cd-flyout-content" id="cdStores">
        <div class="cd-flyout-content-intro"><?php echo Arr::get($cmslabel, 'cd_availability_info'); ?></div>
        <div class="cd-stores">
            <?php $locations = $item['warehouses']; ?>
            <?php foreach ($locations as $location): ?>
                <?php $location_available_qty = (int)$location['available_qty']; ?>
                <article class="cd-store<?php if ($location_available_qty === 0): ?> cd-store-notavailable<?php elseif ($location_available_qty > 0): ?> cd-store-available<?php endif; ?>">
                    <div class="cd-store-title"><span><?php echo(!empty($location['title2']) ? $location['title2'] : $location['title']); ?></span></div>
                    <div class="cd-store-content">
                        <div class="cd-store-availability<?php if ($location_available_qty === 1): ?> last<?php elseif ($location['available_qty'] > 0): ?> available<?php else: ?> unavailable<?php endif; ?>">
                            <?php if(!empty($location['address'])): ?><div class="cd-store-i cd-store-address"><?php echo $location['address']; ?></div><?php endif; ?>
                            <?php if(!empty($location['business_hour'])): ?><div class="cd-store-i cd-store-business-hour"><?php echo $location['business_hour']; ?></div><?php endif; ?>
                            <?php if(!empty($location['contact'])): ?><div class="cd-store-i cd-store-contact"><?php echo $location['contact']; ?></div><?php endif; ?>
                        </div>
                </article>
            <?php endforeach; ?>
        </div>

        <?php if (!empty($cmslabel['item_stores_note'])): ?>
            <div class="cd-store-note special"><?php echo Arr::get($cmslabel, 'item_stores_note'); ?></div>
        <?php endif; ?>

        <div class="cd-flyout-bottom">
            <div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
        </div>
    </div>
</div>