<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> header-spacing0 white-bg page-brands<?php $this->endblock('page_class'); ?>

<?php $this->block('main'); ?>
	<div class="wrapper m-header">
		<div class="bc bc-brands">
			<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url']); ?>
			<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
		</div>	
		<h1 class="m-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
		<div class="page-content"><?php echo Arr::get($cms_page, 'content'); ?></div>
	</div>

	<?php echo View::factory('catalog/widget/manufacturers', ['class' => 'm-brands']); ?>

	<div class="ma">
		<div class="df aic wrapper wrapper-ma">
			<div class="ma-col ma-col1">
				<div class="ma-title"><?php echo Arr::get($cmslabel, 'brands_search'); ?></div>
			</div>
			<div class="ma-col ma-col2">
				<?php $manufacturers_alphabet = Widget_Catalog::manufacturers(array('lang' => $info['lang'], 'limit' => 0, 'sort' => 'title', 'hierarhy_by_alphabet' => true)); ?>
				<?php if(!empty($manufacturers_alphabet)): ?>
					<div class="ma-items">
						<?php foreach($manufacturers_alphabet as $alphabet => $manufacturers): ?>
							<a class="ma-item" href="#<?php echo $alphabet; ?>"><span><?php echo $alphabet; ?></span></a>
						<?php endforeach; ?>
					</div>	
				<?php endif; ?>
			</div>
		</div>
	</div>
	
	<div class="wrapper wrapper-m-items">		
		<div class="m-items">
			<?php if (sizeof($manufacturers_alphabet)): ?>
				<?php $i = 1; ?>
				<div class="m-row">
					<?php foreach($manufacturers_alphabet as $alphabet => $manufacturers): ?>
						<div class="m-column">
							<div class="m-letter" id="<?php echo $alphabet; ?>"><span><?php echo $alphabet; ?></span></div>
							<div class="m-list-section">
								<ul class="m-list">
									<?php foreach($manufacturers as $manufacturer): ?>
										<li><a href="<?php echo $manufacturer['url']; ?>"><?php echo $manufacturer['title']; ?></a></li>
									<?php endforeach; ?>
								</ul>
							</div>
						</div>
						<?php if($i % 4 == 0 AND $i != count($manufacturers_alphabet)): ?></div><div class="m-row"><?php endif; ?>
						<?php $i++; ?>
					<?php endforeach; ?>
				</div>
			<?php else: ?>
				<?php echo Arr::get($cmslabel, 'no_manufacturers'); ?>
			<?php endif; ?>
		</div>
	</div>
<?php $this->endblock('main'); ?>