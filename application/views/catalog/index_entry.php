<?php $product_priorities = (isset($product_priorities)) ? $product_priorities : Kohana::config('app.catalog.product_priorities'); ?>
<?php $mode = (isset($mode))? $mode : ''; ?>
<?php $class = (isset($class))? ' '.$class : ''; ?>
<?php $list = (isset($list)) ? $list : ''; ?>
<?php $i = 1; ?>
<?php $item_index = 0; ?>
<?php foreach ($items as $item): ?>
	<?php
	$item_list_name = !empty($item_list_name) ? $item_list_name : '';
	$item_list_id = !empty($item_list_id) ? $item_list_id : '';
	$item['position_index'] = $item_index;
	$ga4_data = Google4::create_event('select_item', ['cart_info' => $shopping_cart, 'items' => [$item], 'item_list_name' => $item_list_name, 'item_list_id' => $item_list_id]);
	$item_index++;
	?>
	<?php echo View::factory('catalog/index_entry_single', ['item' => $item, 'product_priorities' => $product_priorities, 'mode' => $mode, 'class' => $class, 'i' => $i, 'list' => $list, 'ga4_data' => $ga4_data, 'item_list_id' => $item_list_id]); ?>
	<?php $i++; ?>
<?php endforeach; ?>