<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title'])) ? $kind['seo_title'] : Arr::get($cms_page, 'seo_title')).((!empty($pagination->current_page) AND $pagination->current_page > 1) ? sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages) : '')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['cms_page' => (!empty($cms_page) ? $cms_page : []), 'kind' => $kind, 'extra_kind' => (!empty($extra_kind) ? $extra_kind : []), 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-catalog white-bg<?php if(sizeof($items) < 1): ?> no-items<?php endif; ?><?php if(!empty($kind['level'])): ?> page-catalog-level<?php echo $kind['level']; ?><?php else: ?> page-catalog-level0<?php endif; ?><?php if($q): ?> page-search page-search-catalog<?php endif; ?><?php $this->endblock('page_class'); ?>

<?php if($kind_content == 'manufacturer'): ?>
	<?php $this->block('page_class'); ?> page-brand<?php $this->endblock('page_class'); ?>
<?php endif; ?>
<?php $search_fields = Widget_Catalog::search_filters(array('lang' => $info['lang'], 'filters' => $filters, 'hide_total_0' => TRUE, 'search_id' => ((isset($kind['search_id']) AND $kind['search_id']) ? $kind['search_id'] : 1), 'price_display' => $currency['display'], 'price_exchange' => $currency['exchange'])); ?>
<?php $active_filters = (isset($search_fields['_basic']['selected']) AND $search_fields['_basic']['selected']) ? $search_fields['_basic']['selected'] : array(); ?>

<?php $this->block('after_header'); ?>
	<?php if(isset($_GET['special_view']) AND $_GET['special_view'] == 'wishlist'): ?>
		<div class="wrapper wishlist-header">
			<h1 class="wishlist-title"><?php echo Arr::get($cmslabel, 'wishlist'); ?> <span class="wishlist-title-counter"></span></h1>
		</div>
	<?php endif; ?>

	<?php if(empty($kind) AND !$q AND !isset($_GET['special_view'])): ?>
		<div class="c-level0-header">
			<div class="bc bc-catalog">
				<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, ($kind ? $kind['breadcrumbs'] : [])); ?>
				<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
			</div>
			<h1 class="c-title c-title-level0"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
		</div>

		<?php echo View::factory('catalog/widget/categories', ['class' => 'c-categories']); ?>
	<?php endif; ?>

	<?php if($q): ?>
		<?php 
			$search_url = Utils::app_absolute_url($info['lang'], 'search'); 
			$search_totals = Widget_Search::totals($info['lang'], $q);
			$blog_category = Widget_Publish::category($info['lang'], 'blog');
			$blog_url = (!empty($blog_category['url'])) ? $blog_category['url'] : '';
			$recipes_category = Widget_Publish::category($info['lang'], 'recipe');
			$recipes_url = (!empty($recipes_category['url'])) ? $recipes_category['url'] : '';
			$blog_category = Widget_Publish::category($info['lang'], 'blog');
			$blog_url = (!empty($blog_category['url'])) ? $blog_category['url'] : '';
			if ((int) Arr::get($search_totals, 'catalog', 0) === 0) {
				if ((int) Arr::get($search_totals, 'publish.01', 0) > 0) {
					Request::current()->redirect($blog_url . '?search_q=' . $q);
				} elseif ((int) Arr::get($search_totals, 'publish.02', 0) > 0) {
					Request::current()->redirect($recipes_url . '?search_q=' . $q);
				} elseif ((int) Arr::get($search_totals, 'cms', 0) > 0) {
					Request::current()->redirect($search_url . '?search_q=' . $q . '&search_content=cms');
				} 
			}
		?>
		<div class="s-header-wrapper">
			<h1 class="s-h1"><span class="s-headline"><?php echo Arr::get($cmslabel, 'search_headline'); ?><?php echo (isset($extra_kind) AND $extra_kind AND $extra_kind['seo_title']) ? ' - '.Text::meta($extra_kind['seo_title']) : ''; ?><?php if($q): ?></span><span class="s-keyword"><?php echo $q; ?></span><?php endif; ?></h1>
			
			<ul class="s-nav">
				<li class="selected"><a href="?search_q=<?php echo $q; ?>"><?php echo Arr::get($cmslabel, "search_catalog"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'catalog', 0); ?>)</span></a></li>
				
				<?php if (!empty($blog_url)): ?>
					<li><a href="<?php echo $blog_url; ?>?search_q=<?php echo $q; ?>"><?php echo Arr::get($cmslabel, "search_publish"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish.01', 0); ?>)</span></a></li>
				<?php endif; ?>

				<?php if (!empty($recipes_url)): ?>
					<li><a href="<?php echo $recipes_url; ?>?search_q=<?php echo $q; ?>"><?php echo Arr::get($cmslabel, "search_recipes"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish.02', 0); ?>)</span></a></li>
				<?php endif; ?>
				<li><a href="<?php echo $search_url; ?>?search_q=<?php echo $q; ?>&search_content=cms"><?php echo Arr::get($cmslabel, "search_cms"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'cms', 0); ?>)</span></a></li>
			</ul>
		</div>
	<?php endif; ?>
<?php $this->endblock('after_header'); ?>

<?php $this->block('main'); ?>
	<?php if(isset($_GET['special_view']) AND $_GET['special_view'] == 'wishlist'): ?>
		<div class="wrapper wrapper-wishlist">
			<div class="c-empty wishlist-empty" id="empty_wishlist">
				<div class="no-wishlist-title"><?php echo Arr::get($cmslabel, 'wishlist_no_products'); ?></div>
			</div>
		</div>
	<?php else: ?>
		<div class="df wrapper wrapper-catalog">
			<div class="c-col c-col1">
				<?php if($kind_content == 'manufacturer'): ?>
					<div class="m-logo"><img src="<?php echo Utils::file_url($kind['main_image']); ?>" alt=""></div>
				<?php endif; ?>
				
				<?php echo View::factory('catalog/widget/filter', ['search_fields' => $search_fields, 'active_filters' => $active_filters, 'class' => 'cf-products', 'kind_content' => $kind_content, 'q' => $q, 'kind' => $kind]); ?>		
			</div>

			<div class="c-col c-col2">		
				<?php if(!empty($kind) AND !$q): ?>
					<div class="bc bc-catalog">
						<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, ($kind ? $kind['breadcrumbs'] : [])); ?>
						<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
					</div>

					<div class="c-header">
						<?php if(!empty($kind['main_image']) AND $kind_content != 'manufacturer' AND $kind_content != 'list'): ?>
							<img src="<?php echo Utils::file_url($kind['main_image']); ?>" alt="">
						<?php endif; ?>				

						<div class="c-header-col">
							<h1 class="c-title<?php if(!empty($kind['level'])): ?> c-title-level<?php echo $kind['level']; ?><?php endif; ?><?php if(!empty($kind_content) AND $kind_content == 'manufacturer'): ?> c-title-brand<?php endif; ?>"><?php echo ($kind) ? $kind['seo_h1'] : Arr::get($cms_page, 'seo_h1'); ?><?php echo (isset($extra_kind) AND $extra_kind AND $extra_kind['seo_title']) ? ' - '.Text::meta($extra_kind['seo_title']) : ''; ?></h1>

							<!-- Category content -->
							<?php if (($kind AND $kind['content']) OR Arr::get($cms_page, 'content')): ?>
								<div class="c-desc<?php if($kind_content == 'manufacturer'): ?> has-border<?php endif; ?>">
									<div class="c-desc-cnt lists">
										<?php echo ($kind) ? $kind['content'] : Arr::get($cms_page, 'content'); ?>
									</div>
								</div>
							<?php endif; ?>
						</div>
					</div>

					<?php $bestsellers = Widget_Catalog::products(['lang' => $info['lang'], 'list_code' => 'bestsellers', 'sort' => 'list_position', 'limit' => 0]); ?>
					<?php 
						$bestseller_items = [];
						if (!empty($bestsellers) AND !empty($kind['position_h'])) {
							$s = 0;
							foreach ($bestsellers AS $bestseller_item_id => $bestseller_item) {
								if($s < 4) {
									if (Text::starts_with($bestseller_item['category_position_h'], $kind['position_h'])) {
										$bestseller_items[$bestseller_item_id] = $bestseller_item;
										$s++;
									}
								}
							}
						}
					?>
					<?php if(!empty($bestseller_items)): ?>
                        <?php $ga4_data = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $bestseller_items, 'item_list_name' => Arr::get($cmslabel, 'catalog_special_title'), 'item_list_id' => 'catalog_special_title']); ?>
						<div class="c-special" data-ga4_events_info='<?php echo $ga4_data; ?>'>
							<div class="c-special-title"><?php echo Arr::get($cmslabel, 'catalog_special_title'); ?></div>
							<div class="c-special-items-slider">
								<div class="c-items c-items4 c-special-items blazy-container">
									<?php echo View::factory('catalog/index_entry', ['items' => $bestseller_items, 'item_list_name' => Arr::get($cmslabel, 'catalog_special_title'), 'item_list_id' => 'catalog_special_title']); ?>
								</div>
							</div>
						</div>
					<?php endif; ?>
				<?php endif; ?>

				<!-- Catalog toolbar -->
				<div class="c-toolbar<?php if(!empty($bestseller_items)): ?> no-border<?php endif; ?><?php if(empty($kind)): ?> c-toolbar-level0<?php endif; ?>">
					<?php /*
					<div class="c-counter">
						<strong><?php echo $items_total; ?></strong> <?php echo Arr::get($cmslabel, 'found_products'); ?>
					</div>
					*/ ?>
					
					<?php /*if($items_total > 0 OR !empty($_GET['discount'])): ?>
						<div class="c-filter c-discount">
							<?php $discount_base_url = Url::query($_GET, FALSE, 'page,searchcode,searchid,discount'); ?>
							<?php $discount_base_url .= ($discount_base_url)  ? '&' : '?'; ?>
							<?php if ((int) Arr::get($_GET, 'discount') == 1): ?>
								<a class="active" href="<?php echo $discount_base_url; ?>searchcode=basic&searchid=1"><?php echo Arr::get($cmslabel, 'sale'); ?></a>
							<?php else: ?>
								<a href="<?php echo $discount_base_url; ?>searchcode=basic&searchid=1&discount=1"><?php echo Arr::get($cmslabel, 'sale'); ?></a>
							<?php endif; ?>
						</div>
					<?php endif;*/ ?>
					
					<a class="btn-toggle-filter btn-toggle-catalog-filter" href="javascript:void(0);"><span><span class="filter-icon"></span><?php echo Arr::get($cmslabel, 'filters'); ?></span></a>
					
					<!-- Sort options -->
					<?php if ($items_total > 1): ?>
						<div class="sort c-sort">
							<?php $sort_base_url = Url::query($_GET, FALSE, 'page,sort'); ?>
							<?php $sort_base_url .= ($sort_base_url)  ? '&' : '?'; ?>
							<?php $selected_sort = Arr::get($_GET, 'sort', ''); ?>
							<label><?php echo Arr::get($cmslabel, 'ordering'); ?></label>
							<select onchange="window.location.href=this.options[this.selectedIndex].value">
								<option value="<?php echo $sort_base_url; ?>"<?php if (!$selected_sort): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_recommended', 'Predloženo'); ?></option> 
								<option value="<?php echo $sort_base_url; ?>sort=new"<?php if ($selected_sort == 'new'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_recent', 'Najnovije'); ?></option> 
								<option value="<?php echo $sort_base_url; ?>sort=old"<?php if ($selected_sort == 'old'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_older', 'Najstarije'); ?></option> 
								<option value="<?php echo $sort_base_url; ?>sort=expensive"<?php if ($selected_sort == 'expensive'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_expensive', 'Najskuplje'); ?></option>
								<option value="<?php echo $sort_base_url; ?>sort=cheaper"<?php if ($selected_sort == 'cheaper'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_cheaper', 'Najjeftinije'); ?></option>
							</select>
						</div>
					<?php endif; ?>
				</div>

				<div id="items_catalog_layout">
                    <?php
                        $template_data = [
                            'cms_page' => $cms_page,
                            'kind' => $kind,
                            'extra_kind' => $extra_kind,
                            'q' => $q,
                            'items' => $items,
                            'items_per_page' => $items_per_page,
                            'items_all' => $items_all,
                            'items_layout_sufix' => $items_layout_sufix,
                            'items_total' => $items_total,
                            'pagination' => $pagination,
                            'selected_sort' => $selected_sort,
                            'item_list_name' => (!empty($item_list_name) ? $item_list_name : ''),
                            'item_list_id' => (!empty($item_list_id) ? $item_list_id : ''),
                        ];
                    ?>
					<?php echo View::factory('catalog/index_layout', $template_data); ?>
				</div>			
			</div>

        </div>
	<?php endif; ?>
<?php $this->endblock('main'); ?>