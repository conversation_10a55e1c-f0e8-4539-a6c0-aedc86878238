<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-catalog-index page-auth-wishlist<?php $this->endblock('page_class'); ?>

<?php $this->block('main_layout'); ?>
	<div class="wrapper">
		<?php if ($items): ?>
			<?php foreach ($items AS $wishlist): ?>
				<div class="wishlist-item">
					<div class="wishlist-header">
						<h1 class="wishlists-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?><?php if($wishlist['total_items'] > 0): ?> <span class="wishlist-title-counter"><?php echo $wishlist['total_items']; ?></span><?php endif; ?></h1>
						<?php if ($wishlist['can_delete'] AND !empty($wishlist['items'])): ?>
							<a class="btn btn-border btn-wishslit-delete" href="<?php echo $wishlist['url_delete']; ?>"><span><?php echo Arr::get($cmslabel, 'wishlist_delete'); ?></span></a>
						<?php endif; ?>
					</div>
			
					<?php if(!empty($wishlist['items'])): ?>
						<div class="c-items wishlist-items">
							<?php echo View::factory('catalog/index_entry', ['items' => $wishlist['items'], 'mode' => 'wishlist', 'list' => 'Lista želja']); ?>
						</div>
					<?php else: ?>
						<div class="c-empty wishlist-empty"><?php echo Arr::get($cmslabel, 'wishlist_no_products_title'); ?></div>
					<?php endif; ?>		
				</div>
			<?php endforeach; ?>
		<?php else: ?>
			<div class="wishlist-header">
				<h1 class="wishlists-title"><?php echo Arr::get($cmslabel, 'wishlists'); ?></h1>
			</div>			
			<div class="c-empty wishlist-empty"><?php echo Arr::get($cmslabel, 'wishlist_no_products_title'); ?></div>
		<?php endif; ?>
	</div>
<?php $this->endblock('main_layout'); ?>