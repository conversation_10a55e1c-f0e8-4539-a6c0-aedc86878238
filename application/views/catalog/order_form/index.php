<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(($kind) ? $kind['seo_title'] . ((isset($kind['parents']) AND $kind['parents']) ? ' - ' . implode(' - ', array_map(function ($element) {
			return $element['title'];
		}, $kind['parents'])) : '') : Arr::get($cms_page, 'seo_title')); ?><?php echo (isset($extra_kind) AND $extra_kind AND $extra_kind['seo_title']) ? ' - ' . Text::meta($extra_kind['seo_title']) : ''; ?><?php if ($pagination->current_page > 1): ?><?php echo sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages); ?><?php endif; ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', array('cms_page' => isset($cms_page) ? $cms_page : array(), 'kind' => $kind, 'pagination' => $pagination)); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-quickorder white-bg<?php $this->endblock('page_class'); ?>

<?php $this->block('after_header'); ?>
	<div class="wrapper quickorder-header">
		<h1 class="title-quickorder"><?php echo Arr::get($cmslabel, 'quick_order'); ?></h1>
	</div>
<?php $this->endblock('after_header'); ?>

<?php $this->block('main'); ?>
	<div class="df wrapper qo-wrapper">
		<div class="qo-col qo-col1">
			<form class="qo-form" action="" method="POST" id="orderform" data-orderform_mode="single">
				<div class="clone-element-content">
					<div class="qo-field">
						<label for="field-product"><?php echo Arr::get($cmslabel, 'quick_order_top_title'); ?></label>
						<input data-orderform_layout="3" class="field-autocomplete" id="field-product" type="text" name="product-0" placeholder="<?php echo Arr::get($cmslabel, 'title_product'); ?>">
					</div>
					<div class="qo-field-tip"><?php echo Arr::get($cmslabel, 'quick_order_tip'); ?></div>
					<div class="qo-row-header" data-orderform_active="1">
						<div class="qo-row-header-products"><?php echo Arr::get($cmslabel, 'quick_order_products', 'Proizvod/i'); ?></div>
						<div class="qo-row-header-qty"><?php echo Arr::get($cmslabel, 'quantity', 'Količina'); ?></div>
						<div class="qo-row-header-total"><?php echo Arr::get($cmslabel, 'total_value', 'Ukupno'); ?></div>
					</div>
					<?php for ($i = -1; $i < 3; $i++): ?>
						<div class="wp clone-element qo-row fz0 product_info_clone" style="display:none" data-clone_element_box="product-<?php echo $i; ?>">
							<div class="qo-field">
								<input type="hidden" name="product-<?php echo $i; ?>-shopping_cart_code">
								<input type="hidden" name="product-<?php echo $i; ?>-price">
							</div>
							<div class="wp-image">
								<img class="image" data-product-<?php echo $i; ?>-image="1">
							</div>
							<div class="wp-content">
								<div class="wp-cnt">
									<div class="wp-title" data-product-<?php echo $i; ?>-label="1"></div>
									<div class="wp-code" data-product-<?php echo $i; ?>-code="1"></div>
								</div>
								<div class="wp-qty-container">
									<span class="wp-qty-label" data-product-<?php echo $i; ?>-unit="1"></span>
									<div class="wp-qty">
										<a class="wp-btn-qty wp-btn-dec" data-operation="-" data-change_qty="product-<?php echo $i; ?>" href="javascript:void(0);"><span class="toggle-icon"></span></a>
										<input class="wp-input-qty product_qty_input wp-qty" type="number" name="product-<?php echo $i; ?>-qty" data-decimal_points="0" data-clone_element_tracking="product-<?php echo $i; ?>">
										<a class="wp-btn-qty wp-btn-inc" data-operation="+" data-change_qty="product-<?php echo $i; ?>" href="javascript:void(0);"><span class="toggle-icon"></span></a>
									</div>
								</div>
								<div class="wp-total">
									<div class="ww-old-price product_total_basic" data-product-<?php echo $i; ?>-price="1"></div>
									<div class="wp-current-price product_total_basic_" data-product_price_total="product-<?php echo $i; ?>"></div>
								</div>
								<div class="wp-btns remove_element">
									<a class="wp-btn wp-btn-delete btn-qo-clear" href="javascript:;" data-clone_element_clear="product-<?php echo $i; ?>" tabindex="1000"><?php echo Arr::get($cmslabel, 'clear', 'Obriši'); ?></a>
								</div>
							</div>
						</div>
					<?php endfor; ?>
				</div>
				<div class="qo-container"></div>
				<div style="display:none" class="more-fields qo-more-fields">
					<a href="javascript:void(0);" data-clone_mode="single" class="clone-element-link"><span><?php echo Arr::get($cmslabel, 'show_more_fields', 'Dodaj još proizvoda'); ?></span></a>
				</div>
				<span data-orderform_info="total" style="display: none;"></span>
			</form>
		</div>

		<div class="qo-col qo-col2">
			<div class="qo-col-body">
				<div class="qo-col-cnt qo-col-cnt-desc">
					<?php $rotator_items = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'quick_order_desc', 'limit' => 1]); ?>
					<?php if (!empty($rotator_items)): ?>
						<?php foreach ($rotator_items as $item): ?>
							<div class="qo-col-title"
								 onclick="toggleBox('.qo-col-cnt-desc');"><?php echo $item['title']; ?></div>
							<div class="qo-col-desc">
								<?php echo $item['content']; ?>
								<a class="btn-qo-toggle-desc"
								   href="javascript:toggleBox('.qo-col-cnt-desc');"><?php echo Arr::get($cmslabel, 'hide_content'); ?></a>
							</div>
						<?php endforeach; ?>
					<?php endif; ?>
				</div>

				<div class="qo-col-cnt qo-col-cnt-totals" data-orderform_active="1">
					<div class="qo-col-totals">
						<div class="qo-totals-row qo-totals-row-total">
							<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'total', 'Ukupno za platiti'); ?>:</span>
							<span class="w-totals-value value cart_info_total" data-orderform_info="total_price"></span>
						</div>
					</div>

					<div class="qo-buttons">
						<a href="javascript:cmswebshop.shopping_cart.add_orderform_3(1)" class="btn btn-orange btn-add-to-cart-quick"><?php echo Arr::get($cmslabel, 'add_to_cart_detail', 'Dodaj sve u košaricu'); ?></a>
					</div>
				</div>
			</div>

			<div class="qo-col2-footer">
				<div class="qo-free-shipping">
					<?php echo str_replace('%AMOUNT%', Utils::currency_format($shopping_cart['total_extra_shipping_free_above'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_free_above'], ['currency_code' => $currency['code'], 'display_format' => 'standard']), Arr::get($cmslabel, (!empty($user->b2b)) ? 'free_delivery_b2b' : 'free_delivery')); ?>
				</div>
			</div>
		</div>
	</div>
<?php $this->endblock('main'); ?>
<?php $this->block('extrabody'); ?>
<?php echo Html::media('cmswebshoporderform'); ?>
<?php $this->endblock('extrabody'); ?>
