<?php $mode = (isset($mode))? $mode : ''; ?>
<div class="sw<?php if($mode == 'special_search'): ?> activate<?php endif; ?>">
	<a href="javascript:void(0);" class="sw-toggle"></a>
	<form class="sw-form" action="<?php echo Utils::app_absolute_url($info['lang'], 'catalog'); ?>" method="get">
		<input class="sw-input" name="search_q" id="search_q<?php echo ($mode) ? '_'.$mode : ''; ?>" type="text" placeholder="<?php echo Arr::get($cmslabel, 'enter_search_term', 'Pretraživanje...'); ?>" autocomplete="off" />
		<button class="sw-btn" type="submit"><?php echo Arr::get($cmslabel, 'search'); ?></button>

		<div id="field-search_q-autocomplete_position" class="autocomplete-container" style="display: none" data-show-products="<?php echo Arr::get($cmslabel, 'show_all_products'); ?>">
			<div class="autocomplete-wrapper">
				<div class="autocomplete-col autocomplete-col1 catalogproduct">
					<ul class="ui-autocomplete ui-autocomplete1" data-autocomplete_contenttype="catalogproduct"></ul>
				</div>
				<div class="autocomplete-col autocomplete-col2 catalogcategory">
					<div class="autocomplete-col-item" data-autocomplete_contenttype_box="catalogcategory">
						<div class="autocomplete-title"><?php echo Arr::get($cmslabel, 'autocomplete_categories'); ?></div>
						<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all" data-autocomplete_contenttype="catalogcategory"></ul>
					</div>
					<div class="autocomplete-col-item publish.02" data-autocomplete_contenttype_box="publish.02">
						<div class="autocomplete-title"><?php echo Arr::get($cmslabel, 'autocomplete_recipes'); ?></div>
						<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all" data-autocomplete_contenttype="publish.02"></ul>	
					</div>
					<div class="autocomplete-col-item publish.01" data-autocomplete_contenttype_box="publish.01">
						<div class="autocomplete-title"><?php echo Arr::get($cmslabel, 'autocomplete_publishes'); ?></div>
						<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all" data-autocomplete_contenttype="publish.01"></ul>	
					</div>
					<div class="autocomplete-col-item catalogmanufacturer" data-autocomplete_contenttype_box="catalogmanufacturer">
						<div class="autocomplete-title"><?php echo Arr::get($cmslabel, 'autocomplete_brands'); ?></div>
						<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all" data-autocomplete_contenttype="catalogmanufacturer"></ul>	
					</div>
				</div>
			</div>
		</div>
	</form>
</div>