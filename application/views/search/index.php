<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> white-bg page-search page-search-<?php echo $search_content; ?><?php $this->endblock('page_class'); ?>

<?php 
$catalog_search_url = Utils::app_absolute_url($info['lang'], 'catalog');
$recipes_category = Widget_Publish::category($info['lang'], 'recipe');
$recipes_url = (!empty($recipes_category['url'])) ? $recipes_category['url'] : '';
$search_url = Utils::app_absolute_url($info['lang'], 'search');
$search_content = Arr::get($_GET, 'search_content');
$search_totals = Widget_Search::totals($info['lang'], $query, $search_content);
$other_results = array_sum($search_totals); 
$blog_category = Widget_Publish::category($info['lang'], 'blog');
$blog_url = (!empty($blog_category['url'])) ? $blog_category['url'] : '';
?>
<?php $this->block('after_header'); ?>
	<div class="s-header-wrapper">
		<h1 class="s-h1"><span class="s-headline"><?php echo Arr::get($cmslabel, 'search_headline'); ?></span><span class="s-keyword"><?php echo $query; ?></span></h1>
		
		<ul class="s-nav">
			<li><a href="<?php echo $catalog_search_url; ?>?search_q=<?php echo $query; ?>"><?php echo Arr::get($cmslabel, "search_catalog"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'catalog', 0); ?>)</span></a></li>
			<?php if (!empty($blog_url)): ?>
				<li><a href="<?php echo $blog_url; ?>?search_q=<?php echo $query; ?>"><?php echo Arr::get($cmslabel, "search_publish"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish.01', (($items AND $search_content == 'publish.01') ? count($items['publish.01']) : 0)); ?>)</span></a></li>
			<?php endif; ?>
			<?php if (!empty($recipes_url)): ?>
				<li><a href="<?php echo $recipes_url; ?>?search_q=<?php echo $query; ?>"><?php echo Arr::get($cmslabel, "search_recipes"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish.02', (($items AND $search_content == 'publish.02') ? count($items['publish.02']) : 0)); ?>)</span></a></li>
			<?php endif; ?>
			<li<?php if($search_content == 'cms'): ?> class="selected"<?php endif; ?>><a href="<?php echo $search_url; ?>?search_q=<?php echo $query; ?>&search_content=cms"><?php echo Arr::get($cmslabel, "search_cms"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'cms', (($items AND $search_content == 'cms') ? count($items['cms']) : 0)); ?>)</span></a></li>
		</ul>
	</div>
<?php $this->endblock('after_header'); ?>

<?php $this->block('main'); ?>
	<div class="s-wrapper">
		<?php echo Arr::get($cms_page, 'content'); ?>

		<?php if (sizeof($items) > 0 ): ?>
			<?php foreach ($items as $module => $results): ?>
				<div class="search-cnt">
					<?php foreach ($results as $item): ?>
						<article class="s-item">
							<h2 class="s-item-title"><a href="<?php echo $item['url']; ?>"><?php echo $item['title']; ?></a></h2>
							<?php if (isset($item['content']) AND $item['content']): ?><div class="s-item-content"><?php echo Text::limit_words(strip_tags($item['content']), 50, '...'); ?></div><?php endif; ?>
						</article>
					<?php endforeach; ?>
				</div>
			<?php endforeach; ?>
		<?php else: ?>
			<div class="s-no-results"><?php echo Arr::get($cmslabel, 'nothing_found', 'Nema rezultata za traženi pojam'); ?></div>
		<?php endif; ?>
	</div>
<?php $this->endblock('main'); ?>