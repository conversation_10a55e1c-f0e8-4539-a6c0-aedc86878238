<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-locations-detail nw-main<?php $this->endblock('page_class'); ?>

<?php $this->block('content_layout'); ?>
	<div class="df ld-row">
		<div class="ld-col ld-col1">
			<div class="bc bc-ld">
				<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
				<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>		
			</div>
			<a class="btn-all-stores" href="<?php echo Utils::app_absolute_url($info['lang'], 'location', ''); ?>"><?php echo Arr::get($cmslabel, 'all_locations'); ?></a>
			<h1 class="ld-title"><?php echo Arr::get($item, 'seo_h1'); ?></h1>
			<?php if(!empty($item['business_hour'])): ?>
				<div class="ld-icon ld-address">
					<?php echo $item['address']; ?>
				</div>
			<?php endif; ?>
			<?php if(!empty($item['business_hour'])): ?>
				<div class="ld-icon ld-hours">
					<?php echo $item['business_hour']; ?>
				</div>
			<?php endif; ?>
			<?php if(!empty($item['contact'])): ?>
				<div class="ld-icon ld-contact">
					<?php echo $item['contact']; ?>
				</div>
			<?php endif; ?>
			<div class="ld-cnt"><?php echo Arr::get($item, 'content'); ?></div>
		</div>
		<div class="ld-col ld-col2">
			<div class="ld-images slick-arrow2">
				<?php $images = Utils::get_files('locationpoint', $item['id'], 'image', $info['lang']); ?>
				<?php if(!empty($images)): ?>
					<?php foreach ($images as $file): ?>
						<a href="<?php echo $file['url']; ?>" class="fancybox lloader" rel="gallery" title="<?php echo Text::meta($file['title']); ?><?php if($file['description']): ?> - <?php endif; ?><?php echo Text::meta($file['description']); ?>">
							<span><img <?php echo Thumb::generate($file['file'], ['width' => 700, 'height' => 520, 'crop' => true, 'default_image' => '/media/images/no-image-300.jpg', 'placeholder' => '/media/images/no-image-300.jpg']); ?> alt="<?php echo Text::meta($file['description']); ?>" /></span>
						</a>
					<?php endforeach; ?>	
				<?php else: ?>
					<div><img src="/media/images/no-image-300.jpg" width="300" height="300" alt=""></div>
				<?php endif; ?>
			</div>
		</div>
	</div>

	<?php if (!empty($item['gmap'])): ?>
		<div id="map" class="map l-map">
			<div id="map_canvas" style="width:100%;height:100%;"></div>
		</div>
		<div style="display: none;"
			data-gmap_object_id="<?php echo $item['id']; ?>"
			data-gmap_object_lat="<?php echo $item['gmap']['lat']; ?>"
			data-gmap_object_lon="<?php echo $item['gmap']['lon']; ?>"
			data-gmap_object_tooltip="_content"
			data-id="<?php echo Text::meta($item['id']); ?>">
				<span class="infoBox-cnt">
					<span class="title"><?php echo $item['title']; ?></span>
					<span class="address"><?php echo $item['address']; ?></span>		
					<?php if(!empty($item['business_hour'])): ?>
						<span class="business-hour">
							<?php echo $item['business_hour']; ?>
						</span>
					<?php endif; ?>
					<span class="contact"><?php echo $item['contact']; ?></span>
				</span>
		</div>
	<?php endif; ?>	

	<?php echo View::factory('newsletter/widget/manage', ['class' => 'nw-bottom-main']); ?>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>

<?php $this->block('extrabody'); ?>
	<?php echo Html::media('cmslocation,infobox', 'js'); ?>
	<script>
		$('.ld-images').slick({
			infinite: true,
			slidesToShow: 1,
			slidesToScroll: 1,
			fade: true,
			dots: false,
			arrows: true,
			lazyLoad: 'ondemand',
			draggable: false
		});
	</script>
<?php $this->endblock('extrabody'); ?>