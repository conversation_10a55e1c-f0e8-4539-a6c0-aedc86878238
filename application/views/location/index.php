<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-locations nw-main<?php $this->endblock('page_class'); ?>

<?php $this->block('content_layout'); ?>
    <?php $items = Widget_Location::points(array('lang' => $info['lang'])); ?>
	<?php echo View::factory('location/widget/map', ['map_points' => $items]); ?>

	<div class="locations-header">
		<div class="pos-r wrapper2">
			<div class="bc bc-locations">
				<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url']); ?>
				<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
			</div>
			<h1 class="locations-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>

			<?php if (!empty($locations)): ?>
				<ul class="nav-locations">
					<?php foreach ($locations AS $location): ?>
						<li class="nav-location-item"><a href="#<?php echo $location['code']; ?>"><span><?php echo $location['title']; ?></span></a></li>
					<?php endforeach; ?>
				</ul>
			<?php endif; ?>
		</div>
	</div>
	
	<?php if(!empty(Arr::get($cms_page, 'content'))): ?>
		<div class="wrapper2 l-content"><?php echo Arr::get($cms_page, 'content'); ?></div>
	<?php endif; ?>
    <?php $items = Widget_Location::points(['lang' => $info['lang'], 'mode' => 'full']); ?>

	<?php if ($items): ?>
		<div class="l-items" id="mappoints">
			<?php $title = ''; ?>
			<?php $i = 0; ?>
			<div class="l-section">
				<div class="wrapper2">
					<?php 
						$cities = array_map(function($element){
						    return $element['location_code'];
						}, $items);
						$city_counter = (array_count_values($cities));
					?>

					<?php foreach ($items AS $item): ?>
						<?php if($title != $item['location_title']): ?>	
							<?php if($i != 0): ?>
								</div></div><div class="l-section"><div class="wrapper2">
							<?php endif; ?>
							<h2 class="l-title l-title<?php echo $i; ?>" id="<?php echo $item['location_code']; ?>"><?php echo $item['location_title']; ?> <span class="l-counter">(<?php echo $city_counter[$item['location_code']]; ?>)</span></h2>
						<?php endif; ?>

						<div class="lp" id="<?php echo $item['location_code']; ?>">
							<div class="lp-left">
								<?php $images = Utils::get_files('locationpoint', $item['id'], 'image', $info['lang'], 0); ?>
								<?php if ($images): ?>
									<div class="lp-images slick-arrow2<?php if(count($images) <= 1): ?> single<?php endif; ?>">
										<?php foreach ($images as $file): ?>
											<a href="<?php echo $item['url']; ?>" class="fancybox lloader" rel="gallery-<?php echo $i; ?>" title="<?php echo Text::meta($file['title']); ?><?php if($file['description']): ?> - <?php endif; ?><?php echo Text::meta($file['description']); ?>">
												<span><img data-lazy="<?php echo Thumb::generate($file['file'], 480, 360, true, 'thumb', TRUE, '/media/images/no-image-480.jpg'); ?>" <?php echo Thumb::generate($file['file'], array('width' => 480, 'height' => 360, 'crop' => true, 'default_image' => '/media/images/no-image-480.jpg', 'placeholder' => '/media/images/no-image-480.jpg')); ?> alt="<?php echo Text::meta($file['description']); ?>" /></span>
											</a>
										<?php endforeach; ?>
									</div>
								<?php else: ?>
									<a href="<?php echo $item['url']; ?>"><img src="/media/images/no-image-480.jpg" width="480" height="360" alt=""></a>
								<?php endif; ?>
							</div>
							<div class="lp-right">
								<div class="lp-cnt">
									<h3 class="lp-title"><a href="<?php echo $item['url']; ?>"><?php echo $item['seo_h1']; ?></a></h3>
									<?php if(!empty($item['address'])): ?>
										<div class="lp-address"><a href="#map"><?php echo $item['address']; ?></a></div>
									<?php endif; ?>
									<?php if(!empty($item['business_hour'])): ?>
										<div class="lp-hours">
											<?php echo $item['business_hour']; ?>
										</div>
									<?php endif; ?>
									<?php if(!empty($item['contact'])): ?>
										<div class="lp-contact"><?php echo $item['contact']; ?></div>
									<?php endif; ?>
									<a class="btn btn-location-detail" href="<?php echo $item['url']; ?>"><?php echo Arr::get($cmslabel, 'more_info'); ?></a>
								</div>
							</div>
						</div>
						<?php $title = $item['location_title']; ?>
						<?php $i++; ?>
					<?php endforeach; ?>
				</div>	
			</div>
		</div>
	<?php else: ?>
		<?php echo Arr::get($cmslabel, 'no_locations'); ?>
	<?php endif; ?>

	<?php echo View::factory('newsletter/widget/manage', ['class' => 'nw-bottom-main']); ?>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>

<?php $this->block('extrabody'); ?>
	<?php echo Html::media('cmslocation,infobox', 'js'); ?>
	<script>
		$('.lp-images').slick({
			infinite: true,
			slidesToShow: 1,
			slidesToScroll: 1,
			fade: true,
			dots: false,
			arrows: true,
			lazyLoad: 'ondemand',
			draggable: false
		});
	</script>
<?php $this->endblock('extrabody'); ?>