<!DOCTYPE html>
<html lang="<?php echo Arr::get(Kohana::config('app.html_lang'), $info['lang'], $info['lang']); ?>" data-lang="<?php echo Arr::get(Kohana::config('app.html_lang'), $info['lang'], $info['lang']); ?>" data-default_lang="<?php echo Arr::get(Kohana::config('app.html_lang'), Kohana::config('app.language'), Kohana::config('app.language')); ?>" data-currency_code="<?php echo $currency['code']; ?>" data-currency_display="<?php echo $currency['display']; ?>" data-currency_exchange="<?php echo $currency['exchange']; ?>" data-i18n_lang="<?php echo Arr::get(Kohana::config('app.i18n_lang'), $info['lang'], $info['lang']); ?>" data-webshop_min_order="<?php echo intval(Arr::get($shopping_cart, 'total_extra_shipping_min_total', '0')); ?>"
      data-kne_use_double_pricetags="<?php echo (Kohana::config('app.utils.kn_euro_conversion.use_double_pricetags')) ?? 0; ?>"
      data-kne_conversion_executed="<?php echo ((isset($kn_euro_conversion_executed)) ? (int)$kn_euro_conversion_executed : (Kohana::config('app.utils.kn_euro_conversion.conversion_executed')));?>"
      data-kne_conversion_exchange="<?php echo (Kohana::config('app.utils.kn_euro_conversion.conversion_exchange')) ?? 7.5345; ?>"
      data-kne_conversion_date="<?php echo (strtotime((!empty($kn_euro_conversion_date)) ? $kn_euro_conversion_date : '2023-01-01 00:00:00')) * 1000; ?>"
      data-kne_double_prices_begin_date="<?php echo (strtotime((Kohana::config('app.utils.kn_euro_conversion.double_prices_begin_date')) ?? '2022-09-05 00:00:00')) * 1000 ; ?>"
      data-kne_double_prices_end_date="<?php echo (strtotime((Kohana::config('app.utils.kn_euro_conversion.double_prices_end_date')) ?? '2023-12-31 23:59:59')) * 1000 ; ?>"
      data-kne_kn_format="<?php echo Catalog::currency('HRK', 'display') ?? '%s kn'; ?>"
      data-kne_euro_format="<?php echo Catalog::currency('EUR', 'display') ?? '%s €'; ?>"
      data-kne_display_format="<?php echo (Kohana::config('app.utils.kn_euro_conversion.display_format.standard')) ?? ''; ?>"
      data-kne_second_price_to_front="<?php echo Kohana::config('app.utils.kn_euro_conversion.converted_price_show_in_front'); ?>"
      data-cartitem_use_loyalty_price="<?php echo Kohana::config('app.webshop.orderitem_use_loyalty_price'); ?>"
>
<head>
	<?php 
		$lang = $info['lang']; 
		$cookiebot_id = '0678daa3-47bb-4d6a-90d7-fc702811b908';
		if($lang == 'si') {
			$cookiebot_id = '7fd78573-b58d-4c38-a9cb-b227f90a9fd9';
		}
		if($lang == 'en') {
			$cookiebot_id = 'e3663285-1e6a-4ee0-a89f-346c385cb81e';
		}
		if($lang == 'de') {
			$cookiebot_id = '17e36d90-e6b4-432c-a6d4-5302ed315656';
		}
		if($info['site_url'] == 'https://www.bio-fabrik.at') {
			$cookiebot_id = 'ff1b6418-93ed-4253-adb7-aafcdef6e053';
		}
	?>
	<script id="Cookiebot" src="https://consent.cookiebot.com/uc.js" data-cbid="<?php echo $cookiebot_id; ?>" data-consentmode="disabled" type="text/javascript" async></script>
	<?php if (Kohana::$environment === 1): ?>
		<?php echo Google::tag_manager($info['gtagmanager_code'], 'head'); ?>
        <?php if (Kohana::config('app.recommend_api.enabled')): ?>
            <?php RecommendTracking::persistRecommendData(); ?>
        <?php endif; ?>
	<?php endif; ?>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<meta name="apple-mobile-web-app-status-bar-style" content="#232323">
	<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
	<link rel="manifest" href="/site.webmanifest">
	<link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
	<meta name="msapplication-TileColor" content="#da532c">
	<meta name="theme-color" content="#ffffff">
	<meta name="google-site-verification" content="V36VfsSiPicQ_8_oiDEUOWLsNU-xd545sSlciCsjA6s" />
	<meta name="facebook-domain-verification" content="hbbh2l3hjp3amveg41qsnkdoz3eoio" />
	<meta name="facebook-domain-verification" content="9gcq8ba1pyq5hhzfweo22gnf3xyyfp" />
	<title><?php $this->block('title'); ?><?php $this->endblock('title'); ?></title>
	<?php if (Kohana::$environment !== 1): ?><meta name="robots" content="noindex, nofollow"><?php endif; ?>
	<?php $this->block('seo'); ?><?php $this->endblock('seo'); ?>
	<?php //echo Html::media('fancybox,standard', 'css'); ?>
	<?php echo Html::media('css_gdefault'); ?>
	<style>
		a#CybotCookiebotDialogPoweredbyCybot,div#CybotCookiebotDialogPoweredByText {display: none;}
		#CookiebotWidget .CookiebotWidget-body .CookiebotWidget-main-logo {display: none;}
	</style>
	<?php if (Kohana::$environment === 1): ?>
		<?php echo Facebook::tracking('759023024186954', $info['lang'], 'PageView'); ?>
		<?php /*
		<script>
			(function(h,o,t,j,a,r){
				h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
				h._hjSettings={hjid:911266,hjsv:6};
				a=o.getElementsByTagName('head')[0];
				r=o.createElement('script');r.async=1;
				r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
				a.appendChild(r);
			})(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
		</script>
		*/ ?>
	<?php endif; ?>
	<!-- Start of  Zendesk Widget script -->
	<script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key=377b60e3-102e-4c2e-ad35-0b90b9070c1a"> </script>
	<script type="text/javascript">
		zE('webWidget', 'setLocale', '<?php echo Arr::get(Kohana::config('app.html_lang'), $info['lang'], $info['lang']); ?>');
		window.zESettings = {
			webWidget: {
				color: { 
					theme: '#ABC075',
					launcherText: '#FFFFFF'
				},
				launcher: {
					label: {'*': '<?php echo Arr::get($cmslabel, 'livechat_launcher_label', 'Korisnička podrška'); ?>'}
				},
				chat: {
					title: {'*': '<?php echo Arr::get($cmslabel, 'livechat_chat_title', 'Pomoć'); ?>'},
					offlineForm: {
						greeting: {
							'*': '<?php echo Arr::get($cmslabel, 'livechat_chat_offline'); ?>',
						}
					}
				},
				position: {'horizontal': 'left'}
			}
		};
	</script>
	<!-- End of  Zendesk Widget script -->
    <?php if (!empty($info['controller']) AND $info['controller'] == 'webshop'): ?>
        <?php echo Html::init_map($info['lang']); ?>
    <?php endif; ?>
	<?php $this->block('extrahead'); ?><?php $this->endblock('extrahead'); ?>

    <script src="https://www.google.com/recaptcha/api.js"></script>
</head>
<?php
$check_products = (Arr::get($info, 'controller', '') == 'webshop' AND Arr::get($info, 'action', '') == 'shipping');
$unavailable_items = [];
$country_code = '';
$shopping_cart_data = [];
if (!empty($customer_data['country']) AND $check_products) {
    $country = Webshop::countries([
        'lang' => $info['lang'],
        'single' => true,
        'filters' => [
            'id' => $customer_data['country'],
        ],
    ]);

    $country_code = Arr::get($country, 'code', '');
    $shopping_cart_data = Webshop::shopping_cart_data($info);
}
if ($check_products AND !empty($country_code)) {
    $unavailable_items = Webshop::get_unavailable_pickup_items($shopping_cart_data, $country_code);
    $check_products = (!empty($unavailable_items));
}
?>
<body data-ga4_tracking_with_no_gdpr="1" class="<?php echo $info['page_class']; ?><?php $this->block('page_class'); ?><?php $this->endblock('page_class'); ?> <?php if ($check_products): ?> body-country-select<?php endif; ?>" data-shopping_cart_preview_active="1" data-module="<?php echo $info['controller']; ?>" data-tracking_only_visible="1" data-logged_in="<?php echo !empty($info['user_id']) ? $info['user_id'] : 0; ?>" data-loyalty_percent_format="1">
    <?php $this->block('extrabody_top'); ?><?php $this->endblock('extrabody_top'); ?>
    <?php echo View::factory('cms/ga4_tracking'); ?>
	<?php echo Facebook::init('1441785865884488', $info['lang']); ?>

	<div class="page-wrapper" id="tab-top">
		<?php $this->block('header'); ?>
			<?php $hello_bar_rotator = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'hello_bar', 'limit' => 1, 'ignore_hidden_element' => true]); ?>
			<?php if(!empty($hello_bar_rotator)): ?>
				<?php foreach ($hello_bar_rotator as $hello_bar): ?>
					<?php
						$coupon_activated_class = '';
						if(isset($shopping_cart['total_extra_coupon_code']) AND in_array($hello_bar['coupon_code'], $shopping_cart['total_extra_coupon_code'])){
							$coupon_activated_class = 'active';
						}
					?>
					<style>
						<?php if(!empty($hello_bar['image'])): ?>
							.hello-bar-live{background-image: url(<?php echo Utils::file_url($hello_bar['image']); ?>); background-repeat: no-repeat;}
							@media (max-width: 950px){
								<?php if(!empty($hello_bar['image_3'])): ?>
									.hello-bar-live{background-image: url(<?php echo Utils::file_url($hello_bar['image_3']); ?>); background-repeat: no-repeat;}
								<?php endif; ?>
							}
						<?php elseif(!empty($hello_bar['color2']) && strlen($hello_bar['color']) === 7): ?>
							.hello-bar-live{background: <?php echo $hello_bar['color2']; ?>;}
						<?php endif; ?>

						<?php if(!empty($hello_bar['color']) && strlen($hello_bar['color']) === 7): ?>
							.heb-content, .heb-content a, .heb-right,.activated-coupon-note{color: <?php echo $hello_bar['color']; ?>;}
						<?php endif; ?>
					</style>
					<div class="hello-bar hello-bar-live<?php if($hello_bar['coupon_code'] != null AND !empty($user->b2b)) : ?> hello-bar-none<?php endif; ?><?php if($hello_bar['coupon_code'] == null AND (empty($hello_bar['datetime_counter']) AND empty($hello_bar['date_active_to']))) : ?> hello-bar-clear<?php endif; ?> <?php echo $coupon_activated_class; ?>" data-coupon_active="webshop_coupon">
						<div class="wrapper">
							<?php if(!empty($hello_bar['image_2'])): ?>
								<div class="heb-image">
									<span >
										<?php $fileExtension = pathinfo($hello_bar['image_2'], PATHINFO_EXTENSION); ?>
										<?php if($fileExtension == 'svg'): ?>
											<img loading="lazy" src="<?php echo Utils::file_url($hello_bar['image_2']); ?>" alt="">
										<?php else: ?>
											<img loading="lazy" <?php echo Thumb::generate($hello_bar['image_2'], array('width' => 205, 'height' => 120, 'default_image' => '/media/images/no-image-205.jpg', 'html_tag' => TRUE)); ?> alt="" />
										<?php endif; ?>
									</span>
								</div>
							<?php endif; ?>
							<div class="heb-content<?php if(empty($hello_bar['image_2'])): ?> heb-content-noimage<?php endif; ?>">
								<?php if(!empty($hello_bar['link'])): ?>
									<a href="<?php echo $hello_bar['link']; ?>">
								<?php endif; ?>
									<div class="heb-title">
										<?php echo $hello_bar['title']; ?>
									</div>
									<div class="heb-subtitle">
										<?php echo $hello_bar['title2']; ?>
									</div>
								<?php if(!empty($hello_bar['link'])): ?>
									</a>
								<?php endif; ?>
							</div>
							<?php if($hello_bar['coupon_code'] != null) : ?>
								<div class="heb-right-container">
									<?php  if($hello_bar['coupon_code'] != null) : ?>
										<input type="hidden" data-hellobar_coupon_code="<?php echo $hello_bar['coupon_code']; ?>" />
										<?php $hellobar_message_button = ''; ?>
										<?php $hellobar_message_content = ''; ?>
										<?php
											if(!empty($hello_bar['element_hellobar_message_button_link']) AND !empty($hello_bar['element_hellobar_message_button_content']) AND !empty($hello_bar['element_hellobar_message_content'])){
												$hellobar_message_button = '<a class="btn" href="'.$hello_bar['element_hellobar_message_button_link'].'">'.$hello_bar['element_hellobar_message_button_content'].'</a>';
											}
											if (!empty($hello_bar['element_hellobar_message_content'])) {
												$hellobar_message_content = '<p>' . $hello_bar['element_hellobar_message_content'] . $hellobar_message_button . '</p>';
											}
										?>
										<div class="hellobar-coupon-message" data-hellobar_message="message" data-hellobar_message_value="<?php echo htmlentities($hellobar_message_content); ?>">
										</div>
									<?php endif; ?>


									<div class="activated-coupon-note"><?php echo $hello_bar['element_hellobar_message_content'] ?></div>
									<a class="btn btn-blue hellobar-coupon-btn" href="javascript:cmscoupon.set('webshop_coupon', '<?php echo $hello_bar['coupon_code']; ?>')" data-list_link="<?php if(!empty($hello_bar['element_hellobar_message_button_link'])): ?><?php echo $hello_bar['element_hellobar_message_button_link']; ?><?php endif; ?>">
										<span class="i"><?php echo Arr::get($cmslabel, 'hellobar_coupon_title_inactive'); ?></span>
										<span class="a"><?php echo Arr::get($cmslabel, 'hellobar_coupon_title_active'); ?></span>
									</a>
								</div>
							<?php endif; ?>
							<?php if(!empty($hello_bar['datetime_counter']) OR !empty($hello_bar['date_active_to'])): ?>
								<div class="heb-right-timer">
									<?php if(!empty($hello_bar['datetime_counter'])): ?>
										<div class="heb-right" data-countdown="<?php echo date('Y/m/d H:i:s', $hello_bar['datetime_counter']); ?>"></div>
									<?php elseif(!empty($hello_bar['date_active_to'])): ?>
										<div class="heb-right" data-countdown="<?php echo date('Y/m/d H:i:s', $hello_bar['date_active_to']); ?>"></div>
									<?php endif; ?>
								</div>
							<?php endif; ?>
						</div>
						<!-- <div class="heb-close"></div> -->
					</div>
				<?php endforeach; ?>
			<?php endif; ?>


			<?php if (!empty($user->loyalty_code)): ?>
				<?php 
					$loyalty_display = Utils::barcode($user->loyalty_code); 
					$barcode = (!empty($loyalty_display)) ? Html::barcode(true, $loyalty_display[0], $loyalty_display[1], 50, 'h', 2, true, true) : '';
				?>
				<?php if($barcode): ?>
					<div class="loyalty-quick">
                        <a class="btn-loyalty-header" href="javascript:toggleBox(['.btn-loyalty-header', '.loyalty-header']);"><span class="s">Prikaži</span><span class="h">Sakrij</span> loyalty klub karticu</a>
                        <div class="loyalty-header">
                            <img src="<?php echo Utils::site_url(Kohana::config('app.language')) . $barcode; ?>" alt="">
                            <div style="width:100%;text-align: center;letter-spacing: 0.55em;padding-left:0.55em"><?php echo $loyalty_display[0]; ?></div>
                        </div>
                    </div>
				<?php endif; ?>
			<?php endif; ?>

			<header class="header">
				<div class="header-placeholder"></div>
				<div class="header-body">
					<?php $this->block('header_body'); ?><?php $this->endblock('header_body'); ?>
					<div class="wrapper pos-r wrapper-header">
						<span class="m-nav-title"><?php echo Arr::get($cmslabel, 'menu'); ?></span>
						<a href="<?php echo Utils::homepage($info['lang']); ?>" class="logo"></a>
						<div class="header-contact"><?php echo Arr::get($cmslabel, 'user_support'); ?></div>
						<?php $this->block('header_content'); ?>
							<a class="btn-toggle-nav" href="javascript:void(0);"><span></span></a>
							<?php $active_menu_item = Utils::active_urls($info['lang'], $info['cmspage_url']); ?>
							<?php $menu = Widget_Cms::menu(array('lang' => $info['lang'], 'generate_tree' => true, 'code' => 'main', 'level_range' => '1.2', 'selected' => $active_menu_item)); ?>
							<ul class="nav">
								<?php echo $menu; ?>
							</ul>
							
							<a class="btn btn-orange header-categories" href="javascript:void(0);"><span><span></span><?php echo Arr::get($cmslabel, 'catalog_categories'); ?></span></a>
							<?php $categories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '1.2', 'hierarhy_by_position' => true)); ?>
							<?php if(!empty($categories)): ?>
								<?php $menu_products = Widget_Catalog::products(['lang' => $info['lang'], 'sort' => 'list_position', 'list_code' => 'menu', 'limit' => 3]); ?>
								<?php $category_promo = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'category_promo', 'limit' => 1]); ?>

								<div data-tracking_gtm_categories_list="1" class="categories-container<?php if($menu_products OR $category_promo): ?> has-extra-content<?php endif; ?>">
									<div class="categories-container-body">
										<?php $i = 1; ?>
										<ul class="nav-categories">
											<?php foreach ($categories as $category): ?>
												<?php $subcategories = (!empty($category['children'])) ? $category['children'] : [] ?>
												<li class="<?php if($subcategories): ?>has-children<?php endif; ?><?php if($i == 1): ?> active<?php endif; ?>"><a data-category="<?php echo $category['code']; ?>" href="<?php echo $category['url']; ?>"><span><?php echo $category['title']; ?></span></a></li>
												<?php $i++; ?>
											<?php endforeach; ?>
											<li><a href="<?php echo Utils::app_absolute_url($info['lang'], 'catalog', 'manufacturer'); ?>"><span><?php echo Arr::get($cmslabel, 'manufacturers'); ?></span></a></li>
											<?php $instashop_category = Widget_Publish::category($info['lang'], 'instashop'); ?>
											<?php if(!empty($instashop_category)): ?>
												<li><a href="<?php echo $instashop_category['url']; ?>"><span><?php echo $instashop_category['title']; ?></span></a></li>
											<?php endif; ?>
										</ul>

										<?php $s = 1; ?>
										<?php foreach ($categories as $category): ?>
											<?php $subcategories = (!empty($category['children'])) ? $category['children'] : [] ?>
											<?php if(!empty($subcategories)): ?>
												<div class="nav-categories-right<?php if($s == 1): ?> active<?php endif; ?>" data-category="<?php echo $category['code']; ?>">
													<ul class="subcategory-list">
														<?php foreach ($subcategories as $subcategory): ?>
															<li><a href="<?php echo $subcategory['url']; ?>"><?php echo $subcategory['title']; ?></a></li>
														<?php endforeach; ?>
														
														<?php if (!empty($category['total_new'])): ?>
															<li class="subcategory-new"><a href="<?php echo $category['url']; ?>?searchcode=basic&searchid=1&new=1&search_q="><?php echo Arr::get($cmslabel, 'new'); ?></a></li>
														<?php endif; ?>

														<?php if (!empty($category['total_discount'])): ?>
															<li class="subcategory-sale"><a href="<?php echo $category['url']; ?>?searchcode=basic&searchid=1&discount=1"><?php echo Arr::get($cmslabel, 'sale'); ?></a></li>
														<?php endif; ?>
														<li class="m-all-products"><a href="<?php echo $category['url']; ?>"><?php echo Arr::get($cmslabel, 'all_products'); ?></a></li>
													</ul>
												</div>
											<?php endif; ?>
											<?php $s++; ?>
										<?php endforeach; ?>	

										<?php if($info['user_device'] != 't' AND $info['user_device'] != 'm'): ?>
											<?php if(!empty($menu_products)): ?>
												<div class="category-container-products">
													<div class="category-container-products-title"><?php echo Arr::get($cmslabel, 'most_sale'); ?></div>
													<div class="category-container-product-items">
														<?php echo View::factory('catalog/index_entry', ['items' => $menu_products, 'list' => 'Izbornik']); ?>
													</div>
												</div>
											<?php else: ?>
												<?php if(!empty($category_promo)): ?>	
													<div class="category-container-promo">
														<?php foreach ($category_promo as $category_promo_item): ?>
															<a href="<?php echo $category_promo_item['url']; ?>" class="lloader">
																<picture>
																	<?php if($category_promo_item['image_2']): ?>
																		<source srcset="<?php echo Thumb::generate($category_promo_item['image_2'], 430, 430, true, 'thumb', TRUE, '/media/images/no-image-250.jpg'); ?>" media="(max-width: 1550px)">
																	<?php endif; ?>
																	<img <?php echo Thumb::generate($category_promo_item['image'], array('width' => 700, 'height' => 430, 'crop' => true, 'default_image' => '/media/images/no-image-250.jpg', 'placeholder' => '/media/images/no-image-250.jpg')); ?> alt="" />
																</picture>
															</a>
														<?php endforeach; ?>
													</div>
												<?php endif; ?>
											<?php endif; ?>
										<?php endif; ?>
									</div>
								</div>
							<?php endif; ?>
							
							<?php echo View::factory('search/widget/form'); ?>
							<a class="btn quick-order" href="<?php echo Utils::app_absolute_url($info['lang'], 'catalog'); ?>?special_view=order_form"><span><span class="btn-label"><?php echo Arr::get($cmslabel, 'quick_order'); ?></span></span></a>
                            <div class="w-lang">
                                <?php //echo View::factory('cms/widget/language'); ?>

                                <?php $languages = Utils::languages('', $info); ?>
                                <?php $active_alternative_code = ''; ?>
                                <?php foreach ($languages as $lang => $value): ?>
                                    <?php $active_alternative_code = (!empty($value['alternative_code']) AND $info['site_id'] == $value['id']) ? $value['alternative_code'] : ''; ?>
                                <?php endforeach; ?>

                                <?php
                                $filters = [];
                                if (!empty($customer_data['country'])) {
                                    $filters['id'] = $customer_data['country'];
                                } else {
                                    $filters['code'] = 'hr';
                                }

                                $selected_country = Webshop::countries([
                                    'lang' => $info['lang'],
                                    'filters' => $filters,
                                    'single' => true,
                                ]);
                                ?>
                                <div class="w-lang-span">
                                    <div class="btn-w-toggle"><span class="flag <?php echo (!empty($active_alternative_code)) ? $active_alternative_code : $info['lang']; ?>"></span><span><?php echo (!empty($active_alternative_code)) ? $active_alternative_code : $info['lang']; ?></span></div>
                                    <div class="delivery-to"><?php echo Arr::get($cmslabel, 'shipping_country_select'); ?> <span class="bold2" data-selected_country><?php echo Arr::get($selected_country, 'title', ''); ?></span></div>
                                </div>

                                <div class="w-list">
                                    <a href="javascript:void(0);" class="ww-preview-close w-lang-mobile-close"></a>
                                    <div class="w-list-lang">
                                        <?php foreach ($languages as $lang => $value): ?>
                                            <a class="<?php echo $value['country_code']; ?><?php if($info['lang'] == $value['code'] AND $value['id'] == $info['site_id']): ?> active<?php endif; ?>" href="<?php echo Arr::get($value, 'lang_url', $value['base_url']); ?>"><span class="flag <?php echo (!empty($value['alternative_code'])) ? $value['alternative_code'] : $value['country_code']; ?>"></span><?php echo (!empty($value['alternative_code'])) ? $value['alternative_code'] : $value['country_code']; ?></a>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="w-delivery-change-cnt">
                                        <div><?php echo Arr::get($cmslabel, 'shipping_country_select'); ?> <span class="bold2" data-selected_country><?php echo Arr::get($selected_country, 'title', ''); ?></span></div>
                                        <a class="w-delivery-change" href="javascript:openCountrySelectModal()"><?php echo Arr::get($cmslabel, 'change_shipping_country_btr'); ?></a>
                                    </div>
                                </div>
                            </div>
							<?php echo View::factory('auth/widget/user_box'); ?>
							<?php echo View::factory('catalog/widget/wishlist'); ?>
							<?php echo View::factory('webshop/widget/shopping_cart'); ?> 
						<?php $this->endblock('header_content'); ?>
					</div>
				</div>
				<div class="sw-placeholder"></div>
				<?php $this->block('after_header'); ?><?php $this->endblock('after_header'); ?>
			</header>
			<div class="m-nav">
				<div class="m-nav-support">
					<div class="m-nav-support-col">
						<?php echo Arr::get($cmslabel, 'support_mobile'); ?>
					</div>
					<div class="social social-m-nav">
						<?php echo Arr::get($cmslabel, 'contact_social'); ?>
					</div>
				</div>
			</div>
		<?php $this->endblock('header'); ?>

		<?php $this->block('main'); ?>
			<div class="main">
				<?php $this->block('content_layout'); ?>
					<div class="df main-wrapper">
						<div class="main-body">
							<div class="fg1 lists main-content">
								<?php $this->block('breadcrumb'); ?>
									<div class="bc">
										<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url']); ?>
										<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
									</div>
								<?php $this->endblock('breadcrumb'); ?>
								<?php $this->block('content'); ?><?php $this->endblock('content'); ?>
							</div>
						</div>
						<aside class="sidebar">
							<?php $this->block('sidebar'); ?>
								<?php $sidebar = Widget_Cms::menu(['lang' => $info['lang'], 'code' => 'cms_menu', 'selected' => $active_menu_item]); ?>
								<ul class="nav-sidebar">
									<?php foreach ($sidebar as $sidebar_item): ?>
										<li <?php if($info['basic_url'] == $sidebar_item['url']): ?>class="selected"<?php endif ?>>
											<a href="<?php echo $sidebar_item['url']; ?>" title="<?php echo $sidebar_item['anchor_text']; ?>"><?php echo $sidebar_item['title']; ?></a>
										</li>
									<?php endforeach; ?>
								</ul>
								<?php $this->block('sidebar_bottom'); ?>
									<div class="sidebar-cnt">
										<div class="support support-sidebar">
											<p class="support-title"><?php echo Arr::get($cmslabel, 'footer_user_support'); ?></p>
											<?php echo Arr::get($cmslabel, 'support'); ?>
										</div>
										<div class="social social-sidebar">
											<?php echo Arr::get($cmslabel, 'contact_social'); ?>
										</div>
									</div>
								<?php $this->endblock('sidebar_bottom'); ?>
							<?php $this->endblock('sidebar'); ?>
						</aside>				
					</div>
					<?php $this->block('after_main_wrapper'); ?><?php $this->endblock('after_main_wrapper'); ?>
				<?php $this->endblock('content_layout'); ?>
			</div>
		<?php $this->endblock('main'); ?>

		<?php $this->block('after_main'); ?><?php $this->endblock('after_main'); ?>

		<?php $this->block('newsletter'); ?>
			<?php $newsletter_form = Widget_Newsletter::form(['lang' => $info['lang'], 'code' => 'list']); ?>
			<?php echo View::factory('newsletter/widget/manage', ['newsletter_form' => $newsletter_form]); ?>
		<?php $this->endblock('newsletter'); ?>

		<?php $this->block('bottom'); ?>
			<div class="bottom" data-css="lazyload" data-original="/media/images/footer.jpg">
				<div class="df wrapper wrapper-bottom">
					<div class="bottom-col bottom-col1">
						<div class="bottom-title"><?php echo Arr::get($cmslabel, 'footer_info'); ?></div>
						<div class="bottom-col-cnt">
							<?php $footer_col1 = Widget_Cms::menu(['lang' => $info['lang'], 'code' => 'footer_col1', 'selected' => $active_menu_item]); ?>
							<ul class="nav-bottom">
								<?php foreach ($footer_col1 as $footer_col1_item): ?>
									<li <?php if($info['basic_url'] == $footer_col1_item['url']): ?>class="selected"<?php endif ?>>
										<a href="<?php echo $footer_col1_item['url']; ?>" title="<?php echo $footer_col1_item['anchor_text']; ?>"><?php echo $footer_col1_item['title']; ?></a>
									</li>
								<?php endforeach; ?>
							</ul>
						</div>
					</div>
					<div class="bottom-col bottom-col2">
						<div class="bottom-title"><?php echo Arr::get($cmslabel, 'footer_terms'); ?></div>
						<div class="bottom-col-cnt">
							<?php $footer_col2 = Widget_Cms::menu(['lang' => $info['lang'], 'code' => 'footer_col2', 'selected' => $active_menu_item]); ?>
							<ul class="nav-bottom">
								<?php foreach ($footer_col2 as $footer_col2_item): ?>
									<li <?php if($info['basic_url'] == $footer_col2_item['url']): ?>class="selected"<?php endif ?>>
										<a href="<?php echo $footer_col2_item['url']; ?>" title="<?php echo $footer_col2_item['anchor_text']; ?>"><?php echo $footer_col2_item['title']; ?></a>
									</li>
								<?php endforeach; ?>
								<li><a href="javascript: Cookiebot.renew()" class="gdpr_configurator_button" id="gdpr_configurator_button"><?php echo Arr::get($cmslabel, 'gdpr_edit', 'Uredi privole'); ?></a></li>
							</ul>
						</div>
					</div>
					<div class="bottom-col bottom-col3">
						<div class="bottom-title"><?php echo Arr::get($cmslabel, 'footer_col3_title'); ?></div>
						<div class="bottom-col-cnt">
							<?php echo Arr::get($cmslabel, 'footer_payment'); ?>
						</div>
					</div>
					<div class="bottom-col bottom-col4">
						<div class="bottom-title"><?php echo Arr::get($cmslabel, 'footer_bussines_time_title'); ?></div>
						<div class="bottom-col-cnt">
							<?php $locations = Widget_Location::points(['lang' => $info['lang']]); ?>
							<?php if(!empty($locations)): ?>
								<ul class="bottom-locations">
									<?php foreach($locations as $location): ?>
										<li><a href="<?php echo $location['url']; ?>"><?php echo (!empty($location['headline']) ? $location['headline'] : $location['title']); ?></a></li>
									<?php endforeach; ?>
								</ul>
							<?php endif; ?>
						</div>
					</div>
				</div>
			</div>
		<?php $this->endblock('bottom'); ?>

		<footer class="footer">
			<div class="df wrapper wrapper-footer">
				<div class="footer-col footer-col1">
					<div class="copy"><?php echo str_replace('%YEAR%', date('Y'), Arr::get($cmslabel, 'copyright')); ?></div>
				</div>
				<div class="footer-col footer-col2">
					<div class="dev"><?php echo Kohana::config('signature.webshop.'.$info['lang']); ?></div>
				</div>
				<div class="footer-col footer-col3">
					<?php echo Arr::get($cmslabel, 'footer_cards'); ?>
				</div>
			</div>
			<a class="ontop" href="#tab-top"></a>
		</footer>
	</div>

    <div class="country-select-modal" data-country_select_alert_main_el style="<?php if (empty($unavailable_items)): ?>display:none;<?php endif; ?>">
        <div class="csm-title"><?php echo Arr::get($cmslabel, 'pick_country_title'); ?></div>
        <div data-shipping_country_box style="display: none;">
            <?php $countries = Webshop::countries(['lang' => $info['lang']]); ?>
            <?php $selected_country = Arr::get($customer_data, 'country', ''); ?>
            <div class="shipping-country-select-cnt">
                <select class="shipping-country-select" name="shipping_country_select" data-shipping_country_select_el>
                    <?php foreach ($countries as $country_id => $country): ?>
                        <option value="<?php echo $country_id; ?>" <?php if ($country_id == $selected_country): ?>selected<?php endif; ?>><?php echo $country['title']; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="shipping-country-select-note"><?php echo Arr::get($cmslabel, 'shipping_country_note'); ?></div>
            <button class="btn btn-orange shipping-country-submit" name="submit_shipping_country" data-shipping_country_select_btn><?php echo Arr::get($cmslabel, 'country_select_confirm_btn'); ?></button>
        </div>


        <div class="ww-cart-items" <?php if (empty($unavailable_items)): ?>style="display:none;"<?php endif; ?> data-country_change_response_el>
            <div class="wp-pickup-products">
                <div class="status-info-label"><?php echo Arr::get($cmslabel, 'pickup_info'); ?></div>
                <div class="wp-pickup-label"><?php echo Arr::get($cmslabel, 'country_select_shipping_note', 'U košarici se nalaze proizvodi za koje je moguća samo Wolt dostava (Hrvatska) ili osobno preuzimanje u odabranoj dostupnoj poslovnici'); ?></div>
            </div>
            <div data-unavailable_items_el <?php if (Arr::get($info, 'controller', '') == 'webshop'): ?> data-empty_cart_redirect_url="<?php echo Utils::app_absolute_url($info['lang'], 'webshop'); ?>" <?php endif; ?>>
                <?php $shopping_cart_data = Webshop::shopping_cart_data($info); ?>
                <?php $products_status = Arr::get($shopping_cart_data, 'products_status', []); ?>
                <?php $product_codes = []; ?>
                <?php if (!empty($shopping_cart_data['products'])): ?>
                    <?php $i = 1; ?>
                    <?php foreach ($shopping_cart_data['products'] as $shopping_cart_code => $product_data): ?>
                        <?php $product_type = Arr::get($product_data, 'type', ''); ?>
                        <?php $product_status = Arr::get($products_status, $shopping_cart_code, []); ?>
                        <?php if (!in_array($product_type, Kohana::config('app.catalog.product_type.pickup_only')) OR empty($product_status)) {continue;} ?>
                        <?php array_push($product_codes, $shopping_cart_code); ?>
                        <?php if ($check_products): ?>
                            <div class="ww-cart-items-inner" data-pickup_cart_item="<?php echo $shopping_cart_code; ?>" <?php if (empty($unavailable_items)): ?>style="display:none;"<?php endif; ?>>
                                <?php echo View::factory('webshop/shopping_cart_entry', ['product_status' => $product_status, 'product_code' => $shopping_cart_code, 'product_data' => $product_data, 'i' => $i, 'mode' => 'checkout']); ?>
                            </div>
                        <?php endif; ?>
                        <?php $i++; ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <div class="country-cart-cnt">
                <a class="btn btn-orange" style="pointer-events: none;" data-unavailable_items_remove_btn href="javascript:cmswebshop.shopping_cart.remove_all('<?php echo implode(',', $product_codes); ?>');" class="btn"><span><?php echo Arr::get($cmslabel, 'clear_cart_pickup_items'); ?></span></a>
                <a class="btn-autochange" href="javascript:setShippingCountry(1, true);"><?php echo Arr::get($cmslabel, 'autochange_croatia', 'ili promjeni državu dostave u: Hrvatska'); ?></a>
            </div>
        </div>
        <a title="Close" class="fancybox-item fancybox-close shipping-country-close" href="javascript:;"></a>
    </div>

	<?php echo View::factory('newsletter/widget/manage_leaving', ['newsletter_form' => $newsletter_form]); ?>
	<?php echo View::factory('admin/widget_fe/toolbar'); ?>
	<?php echo View::factory('cms/widget/support_details'); ?>
	<?php echo Html::media('js_gdefault'); ?>
	<?php $this->block('extrabody'); ?><?php $this->endblock('extrabody'); ?>
	<?php
	$page_type = 'other';
	$items_ids = [];
	if (Arr::get($info, 'page_class') == 'page-cms-homepage') {
		$page_type = 'home';
	} elseif (Arr::get($info, 'controller') == 'catalog' AND Arr::get($info, 'action') == 'detail') {
		$page_type = 'offerdetail';
		$items_ids = [$this->item['id']];
	} elseif (Arr::get($info, 'controller') == 'catalog' AND Arr::get($info, 'action') == 'index' AND !empty(Arr::get($_GET, 'search_q'))) {
		$page_type = 'searchresults';
	} elseif (Arr::get($info, 'controller') == 'webshop' AND in_array(Arr::get($info, 'action'), ['shopping_cart', 'login', 'payment'])) {
		$page_type = 'conversionintent';
		if (!empty($this->products)) {
			$items_ids = array_values(array_map(function ($element) {
				return $element['id'];
			}, $this->products));
		}
	} elseif (Arr::get($info, 'controller') == 'webshop' AND Arr::get($info, 'action') == 'thank_you') {
		$page_type = 'conversion';
		if (!empty($this->order->items)) {
			$items_ids = array_map(function ($element) {
				return $element['product_id'];
			}, $this->order->items->as_array());
		}
	}
	?>

	<?php if (Kohana::$environment === 1): ?>
		<?php echo Google::dynamic_remarketing('958641031', $items_ids, $page_type); ?>
	<?php endif; ?>
	<script defer id="mcjs">!function(c,h,i,m,p){m=c.createElement(h),p=c.getElementsByTagName(h)[0],m.async=1,m.src=i,p.parentNode.insertBefore(m,p)}(document,"script","https://chimpstatic.com/mcjs-connected/js/users/e3ddb1a991ccc8888b24de8ee/8d73a2fdf5834aa3f6f385439.js");</script>
	<!--
	<script> 
		_linkedin_partner_id = "1001641";
		window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
		window._linkedin_data_partner_ids.push(_linkedin_partner_id);
	</script>
	<script>(function(){var s = document.getElementsByTagName("script")[0]; var b = document.createElement("script"); b.type = "text/javascript";b.async = true; b.src = "https://snap.licdn.com/li.lmsanalytics/insight.min.js"; s.parentNode.insertBefore(b, s);})(); </script> 
	<noscript><img height="1" width="1" style="display:none;" alt="" src="https://dc.ads.linkedin.com/collect/?pid=1001641&fmt=gif" /></noscript>
	-->
</body>
</html>