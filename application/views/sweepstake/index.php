<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php if ($pagination->current_page > 1): ?><?php echo sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages); ?><?php endif; ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', array('cms_page' => isset($cms_page) ? $cms_page : array(),'pagination' => $pagination)); ?><?php $this->endblock('seo'); ?>

<?php $this->block('content'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	<?php echo Arr::get($cms_page, 'content'); ?>

	<?php foreach ($items as $item): ?>
		<div class="sweepstake">
			<h2 class="sweepstake-title"><a href="<?php echo $item['url']; ?>"><?php echo $item['title']; ?></a></h2>
			<p class="date">
			<?php if ($item['datetime_active_from']): ?>
				<strong><?php echo Arr::get($cmslabel, 'vote_active_from'); ?>:</strong> <?php echo Date::humanize($item['datetime_active_from']); ?> &nbsp;
			<?php endif; ?>
			<?php if ($item['datetime_active_to']): ?>
				<strong><?php echo Arr::get($cmslabel, 'vote_active_to'); ?>:</strong> <?php echo Date::humanize($item['datetime_active_to']); ?> &nbsp;
			<?php endif; ?>
			</p>
		</div>
	<?php endforeach; ?>

	<?php echo $pagination; ?>
<?php $this->endblock('content'); ?>