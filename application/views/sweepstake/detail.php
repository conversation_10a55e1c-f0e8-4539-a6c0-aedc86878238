<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', array('item' => $item)); ?><?php $this->endblock('seo'); ?>

<?php $this->block('breadcrumb'); ?>
	<div class="bc">
		<?php $breadcrumb = Cms::breadcrumb($info['lang'], NULL, TRUE, $item['breadcrumbs']); ?>
		<?php $last_breadcrumb = @array_pop(array_keys($breadcrumb)); ?>
		<?php foreach ($breadcrumb as $link => $title): ?>
			<?php if ($link === $last_breadcrumb): ?>
				<?php echo $title; ?>
			<?php else: ?>
				<a href="<?php echo $link; ?>"><?php echo $title; ?></a>
			<?php endif; ?>
		<?php endforeach; ?>
	</div>
<?php $this->endblock('breadcrumb'); ?>

<?php $this->block('content'); ?>
	<h1><?php echo $item['seo_h1']; ?></h1>
	<div class="cms-content">	
		<div class="sweepstake-date">
			<?php if ($item['datetime_active_from']): ?>
				<strong><?php echo Arr::get($cmslabel, 'vote_active_from'); ?>:</strong> <?php echo Date::humanize($item['datetime_active_from']); ?> &nbsp;
			<?php endif; ?>
			 &nbsp;
			<?php if ($item['datetime_active_to']): ?>
				<strong><?php echo Arr::get($cmslabel, 'vote_active_to'); ?>:</strong> <?php echo Date::humanize($item['datetime_active_to']); ?> &nbsp;
			<?php endif; ?>
		</div>
		
		<?php if(!empty($item['content'])): ?>	
			<div class="sweepstake-cnt">
				<?php echo $item['content']; ?>
			</div>
		<?php endif; ?>

		<?php if ($can_vote): ?>
			<form action="<?php echo $item['vote_url']; ?>" name="sweepstake_form" id="sweepstake_form" method="POST" class="ajax_siteform form-label" data-siteform_response="show_hide" data-tracking_gtm_survey="userRating|<?php echo Arr::get(Kohana::config('app.gtm.mapping_event_names'), 'userRating', ''); ?>">
				<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>

				<div class="sweepstake-questions">
					<?php foreach ($item['questions'] AS $question_id => $question): ?>
						<div class="sweepstake-field field-<?php echo "question_{$question['id']}"; ?> clear">
							<?php if (!empty($question['main_image'])): ?>
								<span class="question-image"><img <?php echo Thumb::generate($question['main_image'], array('width' => 100, 'height' => 100, 'html_tag' => TRUE)); ?> alt="" /></span>
							<?php endif; ?>
							<h2 class="sweepstake-question-title"><?php echo $question['title']; ?><?php if ($question['requested']): ?> *<?php endif; ?></h2>
							<?php if (!empty($question['tips'])): ?>
								<div class="sweepstake-note"><?php echo $question['tips']; ?></div>
							<?php endif; ?>
							<div class="sweepstake-question-fields">
								<?php if ($question['type'] == 'input'): ?>
									<div class="sweepstake-input-field">
										<?php echo Form::input("question_{$question_id}", Arr::get($_POST, "question_{$question_id}", '')); ?>
									</div>
								<?php elseif ($question['type'] == 'selectm'): ?>
									<?php echo Form::select_as_checkbox("question_{$question_id}", $question['answers_option'], Arr::get($_POST, "question_{$question_id}", '')); ?>
								<?php elseif ($question['type'] == 'text'): ?>
									<?php echo Form::textarea("question_{$question_id}", Arr::get($_POST, "question_{$question_id}", '')); ?>
								<?php else: ?>
									<?php echo Form::select_as_radio2("question_{$question_id}", $question['answers_option'], Arr::get($_POST, "question_{$question_id}", '')); ?>
								<?php endif; ?>
								<?php $error = Valid::get_error("question_{$question['id']}", $errors); ?>
								<div id="field-error-<?php echo "question_{$question['id']}"; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></div>
							</div>

							<?php if (!empty($question['use_comment'])): ?>
								<?php echo Form::textarea("questioncomment_{$question_id}", Arr::get($_POST, "questioncomment_{$question_id}", '')); ?>
							<?php endif; ?>
						</div>
					<?php endforeach; ?>
				</div>

				<?php if (!empty($participant_feedback_items)): ?>
					<div class="sweepstake-rate-body">
						<div class="sweepstake-rate-title"><?php echo Arr::get($cmslabel, 'sweepstake_rate_title'); ?></div>
						
						<?php foreach ($participant_feedback_items AS $participant_feedback_item_id => $participant_feedback_item): ?>
							<div class="sweepstake-rate-item">
								<div class="sweepstake-rate-col sweepstake-rate-image">
									<img <?php echo Thumb::generate($participant_feedback_item['main_image'], array('width' => 80, 'height' => 80, 'default_image' => '/media/images/no-image-80.jpg', 'html_tag' => true)); ?> alt="" />
								</div>
								<div class="sweepstake-rate-col">
									<div class="sweepstake-rate-item-title"><?php echo $participant_feedback_item['title']; ?></div>
									<div class="sweepstake-rate-item-score">
										<?php if (isset($participant_feedback_item['rates_status'])): ?>
											<?php if (!empty($participant_feedback_item['rates_enable'])): ?>
												<p>
													<?php for ($rate = 1; $rate <= 5; $rate++): ?>
														<input type="radio" id="comment-rate-<?php echo $rate; ?>-<?php echo $participant_feedback_item_id; ?>" name="rate-<?php echo $participant_feedback_item_id; ?>" value="<?php echo $rate ?>">
														<label for="comment-rate-<?php echo $rate; ?>-<?php echo $participant_feedback_item_id; ?>"><?php echo $rate ?></label>
													<?php endfor; ?>
													<?php $error = Valid::get_error("rate-{$participant_feedback_item_id}", $errors); ?>
													<span id="field-error-rate-<?php echo $participant_feedback_item_id; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
												</p>
											<?php else: ?>
												<div><?php echo Arr::get($cmslabel, 'comment_disabled', 'Nije moguće ocjeniti'); ?></div>
											<?php endif; ?>
										<?php endif; ?>
									</div>

									<?php if (isset($participant_feedback_item['comments_status'])): ?>
										<?php if (!empty($participant_feedback_item['comments_enable'])): ?>
											<div class="sweepstake-rate-item-comment">
												<textarea id="field-comment" name="comment-<?php echo $participant_feedback_item_id; ?>" id="field-comment-<?php echo $participant_feedback_item_id; ?>" cols="40" rows="4" class="field_text field_text_message" placeholder="<?php echo Arr::get($cmslabel, 'comment', 'Komentar'); ?>"></textarea>
												<?php $error = Valid::get_error("comment-{$participant_feedback_item_id}", $errors); ?>
												<span id="field-error-comment-<?php echo $participant_feedback_item_id; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
											</div>
										<?php else: ?>
											<div class="sweepstake-rate-item-comment"><?php echo Arr::get($cmslabel, 'comment_disabled', 'Nije moguće komentirati'); ?></div>
										<?php endif; ?>
									<?php endif; ?>
								</div>
							</div>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>

				<?php if (!empty($participant_extra_data['webshoporder'])): ?>

				<?php else: ?>
					<div class="sweepstake-userfields">
						<h2 class="sweepstake-question-title"><?php echo Arr::get($cmslabel, 'sweepstake_user', 'Podaci za prijavu'); ?></h2>
						<?php foreach ($user_fields as $user_field): ?>
							<?php if (isset($participant_extra_data['webshoporder']) AND $participant_extra_data['webshoporder'] AND $user_field == 'email') {continue;} ?>
							<?php if (!empty($participant_extra_data['subscriber_active']) AND $user_field == 'newsletter') {continue;} ?>
							<?php $error = Valid::get_error($user_field, $errors); ?>
							<p class="field field-<?php echo $user_field; ?>">	
								<?php echo $user_participant->input($user_field, 'form'); ?>
								<label for="field-<?php echo $user_field; ?>" class="label-<?php echo $user_field; ?>"><?php echo Arr::get($cmslabel, $user_field, $user_participant->meta()->field($user_field)->label); ?><?php echo (in_array($user_field, $user_request_fields) ? ' *' : ''); ?></label>
								<span id="field-error-<?php echo $user_field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
							</p>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>

				<button type="submit" class="btn-sweepstake"><?php echo Arr::get($cmslabel, 'vote'); ?></button>
			</form>
			<div id="sweepstake_form_success" class="sweepstake-success" style="display:none;">
				<?php echo Arr::get($cmslabel, 'already_vote'); ?>
			</div>
		<?php else: ?>
			<?php if (!$item['vote_url']): ?>
				<?php echo Arr::get($cmslabel, 'votes_closed'); ?>
			<?php elseif ( ! $can_vote): ?>
				<?php echo Arr::get($cmslabel, 'already_vote'); ?>
			<?php endif; ?>	
		<?php endif; ?>
	</div>
<?php $this->endblock('content'); ?>