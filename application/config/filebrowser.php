<?php

defined('SYSPATH') or die('No direct script access.');

return [
    'generate_thumb' => [
        'extra_big' => ['width' => 1920, 'height' => NULL, 'crop' => FALSE],
        'big' => ['width' => 980, 'height' => NULL, 'crop' => FALSE],
        'big_crop' => ['width' => 980, 'height' => 700, 'crop' => FALSE],
        'medium' => ['width' => 600, 'height' => NULL, 'crop' => FALSE],
        'medium_crop' => ['width' => 600, 'height' => 560, 'crop' => TRUE],
        'small_crop' => ['width' => 110, 'height' => 110, 'crop' => TRUE],
    ],
    'generate_thumb_upload' => [
		'rotatorelement-image' => [
			['width' => 1480, 'height' => 280, 'crop' => TRUE],
			['width' => 1480, 'height' => 550, 'crop' => TRUE],
			['width' => 720, 'height' => 280, 'crop' => TRUE],
			['width' => 467, 'height' => 280, 'crop' => TRUE],
			['width' => 730, 'height' => 430, 'crop' => TRUE],
			['width' => 700, 'height' => 770, 'crop' => TRUE],
			['width' => 180, 'height' => 180, 'crop' => TRUE],
		],
		'rotatorelement-image_2' => [
			['width' => 105, 'height' => 20, 'crop' => TRUE],
			['width' => 760, 'height' => 1500, 'crop' => TRUE],
			['width' => 276, 'height' => 276, 'crop' => FALSE],
			['width' => 205, 'height' => 120, 'crop' => FALSE],
		],
		'rotatorelement-image_3' => [
			['width' => 100, 'height' => 100, 'crop' => TRUE],
		],
		'publishfile-file' => [
			['width' => 800, 'height' => 800, 'crop' => TRUE],
			['width' => 355, 'height' => 355, 'crop' => TRUE],
			['width' => 470, 'height' => 240, 'crop' => TRUE],
			['width' => 260, 'height' => 135, 'crop' => TRUE],
			['width' => 715, 'height' => 370, 'crop' => TRUE],
			['width' => 800, 'height' => 410, 'crop' => TRUE],
			['width' => 1230, 'height' => 630, 'crop' => TRUE],
			['width' => 740, 'height' => 740, 'crop' => FALSE],
			['width' => 320, 'height' => 165, 'crop' => TRUE],
		],
		'locationpointfile-file' => [
			['width' => 480, 'height' => 360, 'crop' => TRUE],
			['width' => 300, 'height' => 140, 'crop' => TRUE],
			['width' => 700, 'height' => 520, 'crop' => TRUE],
		],
		'catalogcategory-image_2' => [
			['width' => 505, 'height' => 505, 'crop' => FALSE],
		],
		'catalogproductfile-file' => [
			['width' => 300, 'height' => 295, 'crop' => FALSE],
			['width' => 120, 'height' => 120, 'crop' => FALSE],
			['width' => 740, 'height' => 740, 'crop' => FALSE],
			['width' => 144, 'height' => 144, 'crop' => TRUE],
			['width' => 70, 'height' => 70, 'crop' => TRUE],
			['width' => 65, 'height' => 65, 'crop' => FALSE],
		],
		'catalogmanufacturer-image' => [
			['width' => 120, 'height' => 64, 'crop' => FALSE],
			['width' => 110, 'height' => 40, 'crop' => FALSE],
			['width' => 250, 'height' => 30, 'crop' => FALSE],
		],
		'staticcontentpageitem-image' => [
			['width' => 960, 'height' => 550, 'crop' => TRUE],
			['width' => 730, 'height' => 380, 'crop' => TRUE],
			['width' => 480, 'height' => 250, 'crop' => TRUE],
            ['width' => 300, 'height' => 295, 'crop' => FALSE],
            ['width' => 120, 'height' => 120, 'crop' => FALSE],
            ['width' => 715, 'height' => 370, 'crop' => TRUE],
            ['width' => 810, 'height' => 410, 'crop' => TRUE],
            ['width' => 260, 'height' => 135, 'crop' => TRUE],
            ['width' => 470, 'height' => 240, 'crop' => TRUE],
            ['width' => 355, 'height' => 355, 'crop' => TRUE],
            ['width' => 800, 'height' => 800, 'crop' => TRUE],
		],
		'staticcontentpageitem-image2' => [
			['width' => 150, 'height' => 50, 'crop' => FALSE],
		],
		/*
		'catalogmanufacturer-image' => [
			['width' => 300, 'height' => 200, 'crop' => FALSE],
			['width' => 150, 'height' => 25, 'crop' => TRUE],
		],
		'catalogproductfile-file' => [
			['width' => 500, 'height' => 500, 'crop' => FALSE],
			['width' => 700, 'height' => 700, 'crop' => FALSE],
			['width' => 150, 'height' => 150, 'crop' => FALSE],
		],*/
	],
];
