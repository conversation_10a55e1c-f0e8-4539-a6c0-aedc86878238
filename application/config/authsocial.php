<?php
/*!
* HybridAuth
* http://hybridauth.sourceforge.net | https://github.com/hybridauth/hybridauth
* (c) 2009-2011 HybridAuth authors | hybridauth.sourceforge.net/licenses.html
* HybridAuth Config file: http://hybridauth.sourceforge.net/userguide/Configuration.html
*/

return array(
	'providers' => array ( 
		//'OpenID' => array (
		//	'enabled' => TRUE
		//),
		'Facebook' => array ( 
			'enabled' => TRUE,
			'keys'    => array ( 
				'id' => '408366688080020',
				'secret' => 'e5391bdf4b89b72ab5fdf0502965d081'
				),
		),
		'Google' => array ( 
			'enabled' => TRUE,
			'keys'    => array ( 
				'id' => '************-5a0ensulqe4181i2t7aksmnq8j2gjlq9.apps.googleusercontent.com',
				'secret' => '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'
				),
		),
		'Twitter' => array ( 
			'enabled' => TRUE,
			'keys'    => array ( 
				'key' => '*************************',
				'secret' => 'AqnNwwk4aR84opKd0HYaej5Kqx5pN1n9hW9gqrjf230G4SXD7w'
				) 
		),
	),
    'version' => 2,
);
